<script setup>
import { ref } from "vue";
// import { useRouter } from 'vue-router';
import { Form, Field, ErrorMessage } from "vee-validate";
import { ScheduleMeetingsValidation } from '@/validationSchema/session';
import Multiselect from 'vue-multiselect';
import { UserStore } from '../../store/index';
import Modal from "../common/Modal/Modal.vue";
import Spinner from "../common/Spinner.vue";
import DatePicker from "../UIElements/DatePicker.vue";
import VueDatePicker from '@vuepic/vue-datepicker';

const emits = defineEmits(['closeModal', 'schedule', 'startSession']);
const props = defineProps({
  loader: Boolean,
  calenderStartTime: String,
  calenderEndTime: String,
  availableSlots: Object,
});
// const router = useRouter();
const Store = UserStore();
// Form fields
// const name = ref();
const dp = ref();
// const email = ref("");
// const slot = ref("30 min");
const date = ref("");
// const initialDate = ref('');
const startTime = ref("");
const initialStartTime = ref("");
const endTime = ref("");
const initialEndTime = ref("");
const selectedSlot = ref("");
const AvailableSlots = ref(['30 min', '60 min', '120 min']);

function formatTime (dateString) {
  const date = new Date(dateString);
  const hours = String(date.getHours()).padStart(2, '0'); // Ensure 2 digits
  const minutes = String(date.getMinutes()).padStart(2, '0'); // Ensure 2 digits
  return `${hours}:${minutes}`;
}
console.log("oooo", props.calenderEndTime);
console.log("availableSlots", props.availableSlots);

if (props.calenderStartTime){
  const initialDate = new Date(props.calenderStartTime);
  date.value = `${initialDate.getFullYear()}-${String(initialDate.getMonth() + 1).padStart(2, '0')}-${String(initialDate.getDate()).padStart(2, '0')}`;
  console.log("dateee", date.value);

  startTime.value = formatTime(props.calenderStartTime);
  initialStartTime.value = formatTime(props.calenderStartTime);
}
if (props.calenderEndTime){
  endTime.value = formatTime(props.calenderEndTime);
  initialEndTime.value = formatTime(props.calenderEndTime);
}
// Form submission handler
const handleForm = async (values) => {
  console.log("Form Submitted:", values);
  emits('schedule', values);
};

const handleStartTime = (time, setFieldValue) => {
  const timeString = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
  startTime.value = timeString;
  setFieldValue('startTime', timeString);

};
const handleEndTime = (time, setFieldValue) => {
  const timeString = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
  endTime.value = timeString;
  setFieldValue('endTime', timeString);
};
const handleDate = (val) => {
  date.value = val.split("T")[0];
};
</script>

<template>
  <Modal :open="true" :preventOverflow="true">
    <div class="z-[10]  bg-white  " :class="Store.isMobile?'w-full h-full':'rounded-lg h-fit w-[50%] xl:w-[25%] max-lg:w-[45%] relative top-[5%] px-3 py-3'">
        <div v-if="Store.isMobile" class="flex w-full  gap-5 items-center border-b-2 h-[9%] py-1 px-2">
        <span class="w-8 flex justify-center bg-gray-100 p-2 rounded-md" @click="emits('closeModal')">
              <svg width="6" height="14" viewBox="0 0 6 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path id="Back" d="M0 7.0039C0 7.16006 0.0446808 7.29279 0.140426 7.4021L5.24681 13.3438C5.32979 13.4453 5.4383 13.5 5.55958 13.5C5.80851 13.5 6 13.2736 6 12.9613C6 12.8129 5.94894 12.6802 5.87872 12.5865L1.07872 7.0039L3.47872 4.21261L5.87872 1.42132C5.94894 1.31982 6 1.18709 6 1.03874C6 0.734234 5.80851 0.5 5.55958 0.5C5.4383 0.5 5.32979 0.554655 5.24681 0.656156L0.140426 6.60571C0.0446808 6.71501 0 6.84775 0 7.0039Z" fill="black"/>
              </svg>
        </span>
            <p class="text-xl font-bold">Schedule Session</p>
        </div>
      <div :class="Store.isMobile?'bg-gray-50 h-[91%]':''">
        <div class="w-full h-[5%] flex items-center justify-start" :class="Store.isMobile?'px-3 relative top-[15px]':''">
          <p class="text-[#111928] text-xl font-bold">Enter Contact</p>
        </div>
        <div v-if="!Store.isMobile" class="absolute top-6 right-6 cursor-pointer" @click="emits('closeModal')">
            <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="x" clip-path="url(#clip0_707_6588)">
                <path id="Vector" d="M5.90741 5L9.30409 1.60332C9.36538 1.54412 9.41427 1.47331 9.4479 1.39502C9.48153 1.31672 9.49924 1.23252 9.49998 1.14731C9.50072 1.0621 9.48448 0.977595 9.45221 0.898729C9.41995 0.819863 9.3723 0.748212 9.31204 0.687958C9.25179 0.627705 9.18014 0.580054 9.10127 0.547787C9.0224 0.515521 8.9379 0.499284 8.85269 0.500024C8.76748 0.500765 8.68328 0.518468 8.60498 0.5521C8.52669 0.585733 8.45588 0.634621 8.39668 0.695913L5 4.09259L1.60332 0.695913C1.48229 0.579017 1.32019 0.514333 1.15193 0.515796C0.983666 0.517258 0.822712 0.584748 0.70373 0.70373C0.584748 0.822712 0.517258 0.983666 0.515796 1.15193C0.514333 1.32019 0.579017 1.48229 0.695913 1.60332L4.09259 5L0.695913 8.39668C0.634621 8.45588 0.585733 8.52669 0.5521 8.60498C0.518468 8.68328 0.500765 8.76748 0.500024 8.85269C0.499284 8.9379 0.515521 9.0224 0.547787 9.10127C0.580054 9.18014 0.627705 9.25179 0.687958 9.31204C0.748212 9.3723 0.819863 9.41995 0.898729 9.45221C0.977595 9.48448 1.0621 9.50072 1.14731 9.49998C1.23252 9.49924 1.31672 9.48153 1.39502 9.4479C1.47331 9.41427 1.54412 9.36538 1.60332 9.30409L5 5.90741L8.39668 9.30409C8.51771 9.42098 8.67981 9.48567 8.84807 9.4842C9.01633 9.48274 9.17729 9.41525 9.29627 9.29627C9.41525 9.17729 9.48274 9.01633 9.4842 8.84807C9.48567 8.67981 9.42098 8.51771 9.30409 8.39668L5.90741 5Z" fill="#6B7280"/>
                </g>
                <defs>
                <clipPath id="clip0_707_6588">
                <rect width="10" height="10" fill="white"/>
                </clipPath>
                </defs>
            </svg>
        </div>
        <Form :validation-schema="ScheduleMeetingsValidation" v-slot="{ setFieldValue }"  @submit="handleForm" :class="Store.isMobile?'px-3 m-0 relative top-[18px]':'m-0'">
          <!-- Name -->
          <div :class="Store.isMobile?'h-[90px]':'h-[85px]'">
            <label for="name" class="text-[14px] font-medium text-black">Lead Name*</label>
            <Field type="text" name="name" class="input-primary w-full !bg-gray-50" placeholder="Full Name" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-0 capitalize" name="name" />
          </div>
          <!-- Phone -->
          <div v-if="Store.isMobile" :class="Store.isMobile?'h-[90px]':'h-[85px]'">
            <label for="phone" class="text-[14px] font-medium text-black">Phone*</label>
            <Field type="text" name="phone" class="input-primary w-full !bg-gray-50" placeholder="Phone" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-0 capitalize" name="phone" />
          </div>

          <!-- Email -->
          <div :class="Store.isMobile?'h-[90px]':'h-[85px]'">
            <label for="email" class="text-[14px] font-medium text-black">Email ID*</label>
            <Field type="email" name="email" class="input-primary w-full !bg-gray-50" placeholder="Email ID" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-0 capitalize" name="email" />
          </div>

          <!-- Slot & Date -->

          <div class="flex gap-3" :class="Store.isMobile?'h-[90px]':'h-[85px]'">
            <div class="w-[50%]  relative">
            <label for="slot" class="text-[14px] font-medium text-black">Date</label>
                  <Field name="date"  v-model="date" v-slot="{ field }">
                    <DatePicker v-bind="field" :initalValue="date" class="h-[40px] bg-gray-50 rounded-md"   @select-date="(selectedDate) => handleDate(selectedDate)"  ref="dp" label="Enter Date" css_id="picker" :required="true" :showLabelHigh="false" />
                  </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-0 capitalize" name="date" />
            </div>
            <div class="w-[53%]">
              <label for="slot" class="text-[14px] font-medium text-black">Slot</label>
              <Field name="slot" v-model="selectedSlot" v-slot="{field}">
                  <Multiselect v-model="selectedSlot" :options="AvailableSlots" :searchable="false" :close-on-select="true" :show-labels="false"
                  placeholder="Select a Slot" aria-label="Select a Slot" v-bind="field" class="!bg-gray-50 !rounded-lg !text-left" maxHeight="100">
                  </Multiselect>
              </Field>
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-0 capitalize" name="slot" />
            </div>
          </div>

          <!-- Start & End Time -->
          <div class="flex gap-3" :class="Store.isMobile?'h-[90px]':'h-[85px]'">
            <div class="w-1/2 relative">
              <label for="startTime" class="text-[14px] font-medium text-black">Start</label>
                <Field name="startTime" :model-value="initialStartTime" v-slot="{field}" class="w-full p-2 border border-black rounded !bg-gray-50">
                  <VueDatePicker
                  class="!bg-gray-50 border rounded-lg h-[44px] flex items-center relative"
                    v-bind="field"
                    v-model="startTime"
                    :format="()=>startTime"
                    @update:modelValue="(time) => handleStartTime(time, setFieldValue)"
                    time-picker
                  />
                  <label v-if="!startTime" class="absolute top-[2.7rem] left-10">- -:- -</label>
                </Field>
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-0 capitalize" name="startTime" />
            </div>

            <div class="w-1/2 relative">
              <label for="endTime" class="text-[14px] font-medium text-black">End</label>
                <Field name="endTime" :model-value="initialEndTime" v-slot="{field}" class="w-full p-2 border border-black rounded !bg-gray-50">
                  <VueDatePicker
                  class="!bg-gray-50 border rounded-lg h-[44px] flex items-center relative"
                    v-bind="field"
                    v-model="endTime"
                    :format="()=>endTime"
                    @update:modelValue="(time) => handleEndTime(time, setFieldValue)"
                    time-picker
                  />
                  <label v-if="!endTime" class="absolute top-[2.7rem] left-10">--:--</label>
                </Field>
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-0 capitalize" name="endTime" />
            </div>
          </div>
          <!-- Submit Button -->
          <div class=" w-full flex items-center justify-between" :class="Store.isMobile?'h-[45px] gap-3':'h-[60px]'">
            <div @click="emits('closeModal')" v-if="Store.isMobile" class=" hover:bg-[#1a56db] border border-gray-400 bg-white text-black active:!bg-gray-100 px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
                rounded-lg flex flex-row justify-center items-center gap-2" :class="Store.isMobile?'h-full w-[50%]':'h-[90px] w-full'">
              Cancel
            </div>
            <button type="submit" class=" hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
                rounded-lg flex flex-row justify-center items-center gap-2" :class="Store.isMobile?'h-full w-[50%]':'h-[90px] w-full'">
              Schedule
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </ModaL>
</template>

<style>
.multiselect__single{
    background-color: #f9fafb;
}
</style>
