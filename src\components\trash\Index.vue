<script setup>
import { ref, onMounted } from 'vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import Spinner from '../common/Spinner.vue';
import NotfoundImage from '../common/NotfoundImage.vue';

const listOfTrash = ref([]);
const loader = ref(null);

async function fetchTrashData () {
  await GetAllTrash().then((res) => {
    listOfTrash.value = res;
  });
}

function extractType (type) {
  const typeParts = type.split('_');
  return typeParts[typeParts.length - 1];
}

async function restore (item) {
  const typeMapping = {
    svgs: { entity: 'projectSVG', action: 'restoreSVG' },
    scenes: { entity: 'projectScene', action: 'restoreScenes' },
    sidebar: { entity: 'sidebar', action: 'restoreSidebar' },
    gallerys: { entity: 'gallery', action: 'restoreGallery' },
    communitys: { entity: 'community', action: 'restoreCommunity' },
    amenitys: { entity: 'amenity', action: 'restoreAmenity' },
    buildings: { entity: 'building', action: 'restoreBuilding' },
    virtualTour: { entity: 'virtualTour', action: 'restoreVirtualTour' },
    landmarks: { entity: 'projectLandmark', action: 'restoreLandmark' },
    units: { entity: 'unit', action: 'restoreUnit' },
    unitplans: { entity: 'unitplan', action: 'restoreUnitplan' },
  };
  const typeParts = item.type.split('_');
  const projectId = typeParts[0];
  const typeKey = typeParts[1];
  const typeConfig = typeMapping[typeKey];
  if (!typeConfig) {
    console.error(`Unknown type: ${typeKey}`);
    return;
  }
  const payload = {
    trash_id: item._id,
  };
  loader.value = item._id;
  try {
    await RestoreTrash(payload, projectId, typeConfig.entity, typeConfig.action);
    await fetchTrashData();
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

onMounted(() => {
  fetchTrashData();
});
</script>

<template>
    <div>
        <div class="flex items-center justify-between w-full mb-6">
            <p class="text-txt-50 dark:text-txt-1000 text-2xl font-semibold">
                Trash
            </p>
        </div>
        <div v-if="listOfTrash && Object.keys(listOfTrash).length !==0">
            <table class="w-full rounded-lg bg-transparent">
                <tbody>
                    <tr
                        v-for="(item, key) in listOfTrash"
                        :key="key"
                        class="even:bg-bg-1000 odd:bg-stone-50 even:dark:bg-bg-200 dark:odd:bg-bg-50 capitalize"
                    >
                        <td class="p-3">{{ extractType(item.type) }}</td>
                        <td class="p-3 text-right">
                            <div
                                class="flex justify-end items-center gap-2 cursor-pointer"
                                @click="restore(item)"
                            >
                                Restore
                                <Spinner v-if="loader === item._id" />
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div v-else class="w-full">
            <div class="w-fit m-auto">
                <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <rect width="300" height="286" fill="url(#pattern0)"/>
                    <NotfoundImage/>
                </svg>
            </div>
            <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Trashed Data Available</div>
        </div>
    </div>
</template>
