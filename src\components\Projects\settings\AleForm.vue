<script setup>
import { ref, watch } from 'vue';
import { UpdateProjectSettings, uploadSettingFiles } from '../../../api/projects/settings/index.ts';
import { AleSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import Multiselect from 'vue-multiselect';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ctaTypeList, languages } from '../../../helpers/constants.ts';
import { ProjectStore } from '../../../store/project.ts';
import { getAllScenes } from '../../../api/masterScene/index.ts';
import { getListofScenes } from '../../../api/projects/scene/index.ts';
import { ShortenUrl } from '../../../helpers/helpers.ts';
import { getCookie } from '../../../helpers/domhelper';
import Spinner from '@/components/common/Spinner.vue';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const shortenUrlRes = ref(null);
const org_url_domain = import.meta.env.VITE_UI_LIBRARY_STAG;
const sceneTypeList = ['project Scene', 'master Scene'];
const initialSceneTypeRef = ref(null);
const initialSceneIdRef = ref(null);
const intitialCtaType = ref(null);
const initialSceneIdList = ref(null);
const initial_default_language = ref(null);
const supported_languages = ref([]);
const loader = ref(false);
const aleVideoRef = ref({
  preview: null,
  fileData: null,
});
const aleThumbnailRef = ref({
  preview: null,
  fileData: null,
});

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

async function handleGenerate () {
  const current_organization = getCookie('organization');

  const org_url = `${org_url_domain}${current_organization}/projectscene/${project_Id.value}`; // org url
  const title = projectStore.settings?.name || ''; // name
  const description = projectStore.settings?.description || ''; // description
  const image = previousData.value?.thumbnail || ''; // thumbnail of ale

  const res = await ShortenUrl(org_url, title, description, image);
  if (res) {
    shortenUrlRes.value = res;
    isEdit.value = false;
  }
}

const handleCreateObjectURL = (val) => {
  return URL.createObjectURL(val); // Local blob url
};

async function sceneIdApiCallBack (val) {

  return new Promise((resolve, reject) => {
    if (val.split(' ')[0] === 'master') {
      // Master Scene
      getAllScenes().then((res) => {
        if (Object.keys(res).length > 0) {
          initialSceneIdList.value = Object.values(res);
        } else {
          initialSceneIdList.value = [];
        }
        resolve(res);
      }).catch((error) => {
        reject(error);
      });
    } else {
      // Projects
      getListofScenes(project_Id.value).then((res) => {
        if (Object.keys(res).length > 0) {
          initialSceneIdList.value = Object.values(res);
        } else {
          initialSceneIdList.value = [];
        }
        resolve(res);
      });
    }
    initialSceneIdRef.value = null;
  });

}

watch(
  () => previousData.value,
  () => supported_languages.value,
  () => {
    handleGenerate();
  },
  { deep: true },
);

watch(initialSceneTypeRef, (val) => {
  sceneIdApiCallBack(val);  // Api Callback
});

const getLanguageByCode = (code) => {
  return languages.find((lang) => lang.code === code) || null;
};

const setupDataCallBack = (values) => {

  if (values) {

    const data = values;

    // Previous Data
    previousData.value =  {
      is_enabled: data.projectSettings?.ale?.is_enabled ? data.projectSettings?.ale?.is_enabled : false,
      currency_support: data.projectSettings?.ale?.currency_support ? data.projectSettings?.ale?.currency_support : false,
      is_cta_enabled: data.projectSettings?.ale?.is_cta_enabled ? data.projectSettings?.ale?.is_cta_enabled : false,
      initial_scene_type: (data.projectSettings?.ale?.initial_scene_type ? data.projectSettings.ale.initial_scene_type : null),
      initial_scene_id: (data.projectSettings?.ale?.initial_scene_id ? data.projectSettings?.ale?.initial_scene_id : null),
      cta_name: (data.projectSettings?.ale?.cta_name ? data.projectSettings?.ale?.cta_name : null),
      cta_type: (data.projectSettings?.ale?.cta_type ? data.projectSettings?.ale?.cta_type : null),
      video: (data.projectSettings?.ale?.welcome_video ? data.projectSettings?.ale?.welcome_video : null),
      thumbnail: (data.projectSettings?.ale?.welcome_thumbnail ? data.projectSettings?.ale?.welcome_thumbnail : null),
      initial_default_language: (data.projectSettings?.ale?.default_language ? getLanguageByCode(data.projectSettings?.ale?.default_language) : null ),
      supported_languages: (data.projectSettings?.ale?.supported_languages  ?  data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code))   : null ),
    };

    // Form Initial Values
    initialData.value = {
      is_enabled: (data.projectSettings?.ale?.is_enabled ? data.projectSettings?.ale?.is_enabled : false),
      is_cta_enabled: (data.projectSettings?.ale?.is_cta_enabled ? data.projectSettings?.ale?.is_cta_enabled : false),
      cta_name: data.projectSettings?.ale?.cta_name,
      currency_support: (data.projectSettings?.ale.currency_support ? data.projectSettings?.ale.currency_support: false),
      supported_languages: (data.projectSettings?.ale?.supported_languages ? data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code)) : []),
    };

    if (data.projectSettings?.ale?.initial_scene_type) {
      initialSceneTypeRef.value = `${data.projectSettings.ale.initial_scene_type} Scene`;
    }

    if (data.projectSettings?.ale?.default_language) {
      initial_default_language.value = (data.projectSettings?.ale?.default_language ? getLanguageByCode(data.projectSettings?.ale?.default_language) : null);
    }

    if (data.projectSettings?.ale?.supported_languages) {
      supported_languages.value = (data.projectSettings?.ale?.supported_languages ? data.projectSettings?.ale?.supported_languages.map((code) => getLanguageByCode(code))   : null);
    }
    if (data.projectSettings?.ale?.cta_type) {
      intitialCtaType.value = data.projectSettings?.ale?.cta_type;
    }

    if (data.projectSettings?.ale?.initial_scene_type) {
      sceneIdApiCallBack(`${data.projectSettings.ale.initial_scene_type} Scene`).then((result) => {
        initialSceneIdRef.value = result[data.projectSettings.ale.initial_scene_id];
      });
    }

    aleVideoRef.value.preview = data.projectSettings?.ale?.welcome_video;
    aleThumbnailRef.value.preview = data.projectSettings?.ale?.welcome_thumbnail;
  }
};
const saveSettings = async (payload) => {
  return UpdateProjectSettings(payload).then((result) => {
    aleVideoRef.value.fileData = null;
    aleThumbnailRef.value.fileData = null;
    if (result) {
      loader.value = false;
      isEdit.value = false;
      projectStore.settings.projectSettings[projectSettingsFormTypes.ALE] = result.projectSettings[projectSettingsFormTypes.ALE];
      setupDataCallBack(result);
      resolve(result);
    }
  });
};
const handleSubmit = async (val) => {
  return new Promise((resolve) => {
    loader.value = true;
    const prevData = { ...previousData.value }; // prevData track source
    const newCompareObj = { ...val }; // form values

    // delete previous
    delete prevData.video;
    delete prevData.thumbnail;
    delete newCompareObj.video;
    delete newCompareObj.thumbnail;

    // object changes
    prevData.initial_default_language = prevData.initial_default_language ? prevData.initial_default_language.code : null;
    prevData.supported_languages = prevData.supported_languages ? prevData.supported_languages.map((languages) => languages.code) : null;
    newCompareObj.initial_scene_type = newCompareObj.initial_scene_type.split(' ')[0];
    newCompareObj.initial_scene_id = newCompareObj.initial_scene_id ? newCompareObj.initial_scene_id.sceneData._id : null;
    newCompareObj.initial_default_language = newCompareObj.initial_default_language ? newCompareObj.initial_default_language.code : null;
    newCompareObj.supported_languages = newCompareObj.supported_languages ? newCompareObj.supported_languages.map((languages) => languages.code) : null;

    const payload = {}; // Object to store values to be sent
    payload.project_id = project_Id.value;
    payload.query = {
      [projectSettingsFormTypes.ALE]: {},
    };

    const parms = frameParms(prevData, newCompareObj);

    if (Object.keys(parms).length > 0) {
      parms.is_enabled !== undefined ? payload.query[projectSettingsFormTypes.ALE].is_enabled = parms.is_enabled : '';
      parms.currency_support !== undefined ? payload.query[projectSettingsFormTypes.ALE].currency_support = parms.currency_support : '';
      if (parms.initial_scene_type) {
        payload.query[projectSettingsFormTypes.ALE].initial_scene_type = parms.initial_scene_type;
      }
      if (parms.initial_scene_id) {
        payload.query[projectSettingsFormTypes.ALE].initial_scene_id = parms.initial_scene_id;
      }
      if (parms.is_cta_enabled !== undefined) {
        payload.query[projectSettingsFormTypes.ALE].is_cta_enabled = parms.is_cta_enabled;
      }
      if (parms.cta_type) {
        payload.query[projectSettingsFormTypes.ALE].cta_type = parms.cta_type;
      }
      if (parms.cta_name) {
        payload.query[projectSettingsFormTypes.ALE].cta_name = parms.cta_name;
      }
      if (parms.initial_default_language) {
        payload.query[projectSettingsFormTypes.ALE].default_language = parms.initial_default_language;
      }

      if (parms.supported_languages) {
        payload.query[projectSettingsFormTypes.ALE].supported_languages = [...parms.supported_languages];
      }
    }

    if (Object.keys(parms).length > 0 || (val.video !== null && val.video !== undefined) || (val.thumbnail !== null && val.thumbnail !== undefined)) {

      const formData = new FormData();
      formData.append('project_id', project_Id.value);
      val.video && formData.append('welcome_video', val.video);
      val.thumbnail && formData.append('welcome_thumbnail', val.thumbnail);
      if (val.video || val.thumbnail){
        (async () => {
          const res  = await uploadSettingFiles(formData);
          if (res){
            if (res.welcome_thumbnail) {
              payload.query[projectSettingsFormTypes.ALE].welcome_thumbnail = res.welcome_thumbnail;
            }
            if (res.welcome_video) {
              payload.query[projectSettingsFormTypes.ALE].welcome_video = res.welcome_video;
            }
            await saveSettings(payload);
          }
        })(); // IIFE
      } else {
        saveSettings(payload);
      }
    } else {
      aleVideoRef.value.fileData = null;
      aleThumbnailRef.value.fileData = null;
      resolve();
    }
  });
};

// Initialize
if (projectStore.settings){
  console.log("Intialize Project Settings");
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
      <div class="flex flex-col justify-start items-start my-3">

            <!-- Headers -->
            <div class="flex justify-between items-center w-full mb-4">

            <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> ALE Settings: </p>

            <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
                @handle-click="() => isEdit = !isEdit">
                <template v-slot:svg>
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                    d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                    fill="white" />
                </svg>
                </template>
            </Button>

            <div v-if="isEdit" class="flex justify-start items-center gap-3">
                <Button title="Reset" type="button" theme="secondary"
                @handle-click="() => isEdit = !isEdit"> </Button>
                <label for="editAleSettings"
                :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer']">
                Save
                <Spinner v-if="loader" /></label>

            </div>

            </div>

            <!-- View -->
            <div v-if="!isEdit" class="grid grid-cols-3 gap-8 w-full mt-3 mb-5">

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Enabled:</label>
                <p v-if="previousData?.is_enabled"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.is_enabled }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Initial scene Type: </label>
                <p class="font-medium text-sm text-txt-default"> {{
                previousData?.initial_scene_type ?
                    previousData?.initial_scene_type : '-' }} </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Initial Scene Id (Project Scene / Master Scene ID):
                </label>
                <p class="font-medium text-sm text-txt-default"> {{
                previousData?.initial_scene_id
                    ? previousData.initial_scene_id : '-' }} </p>

            </div>
            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50">cta Enabled:</label>
                <p v-if="previousData?.is_cta_enabled"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.is_cta_enabled }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> cta Type </label>
                <p class="font-medium text-sm text-txt-default"> {{
                previousData?.cta_type ?
                    previousData?.cta_type : '-' }} </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> cta Name
                </label>
                <p class="font-medium text-sm text-txt-default"> {{
                previousData?.cta_name
                    ? previousData.cta_name : '-' }} </p>

            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Video: </label>
                <video v-if="previousData?.video" controls
                :src="previousData?.video"
                class="object-fill h-48 w-96 rounded-md"></video>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Thumbnail: </label>
                <img v-if="previousData?.thumbnail"
                :src="previousData?.thumbnail" class="w-96  h-48 rounded-md" />
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <!-- shortenUrlRes -->
            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> ShortUrl: </label>
                <a v-if="shortenUrlRes" :href="shortenUrlRes" target="_blank" class="font-medium text-sm text-blue-500">
                {{ shortenUrlRes }}
                </a>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Default language: </label>
                <p class="font-medium text-sm text-txt-default"> {{
                previousData?.initial_default_language ?
                previousData?.initial_default_language?.name : '-' }} </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50">Supported language:</label>
                <div v-if="previousData?.supported_languages && previousData?.supported_languages.length">
                    <p class="font-medium text-sm text-txt-default">
                    <span v-for="(language, index) in previousData?.supported_languages" :key="index">
                        {{ language.name }}<span v-if="index < previousData?.supported_languages.length - 1">, </span>
                    </span>
                    </p>
                </div>
                <p v-else class="font-medium text-sm text-txt-default">-</p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Currency Support:</label>
                <p v-if="previousData?.currency_support"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.currency_support }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            </div>

            <!-- Form -->
            <Form v-else class="grid grid-cols-3 gap-3 mt-3 w-full"
            @submit="(val) => handleSubmit(val).then(() => isEdit = false)"
            :initial-values="initialData" :validation-schema="AleSettingsSchema">

            <div class="relative">

                <Field v-slot="{ field }" name="is_enabled" type="checkbox" :value="true" :unchecked-value="false">
                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" />
                    Enable <strong>*</strong>
                </label>
                </Field>

                <ErrorMessage name="is_enabled" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="relative mb-7">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Initial Scene Type
                <strong>*</strong> </label>

                <Field name="initial_scene_type" :model-value="initialSceneTypeRef" v-slot="{ field }">

                <Multiselect :allow-empty="false" v-bind="field" v-model="initialSceneTypeRef" :searchable="false"
                    :close-on-select="true" :show-labels="false" placeholder="Choose" :options="sceneTypeList"
                    maxHeight="250">
                </Multiselect>

                </Field>

                <ErrorMessage name="initial_scene_type" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="relative mb-7" v-if="initialSceneIdList !== null">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Initial Scene Id
                <strong>*</strong> </label>

                <Field name="initial_scene_id" :model-value="initialSceneIdRef" v-slot="{ field }">

                <Multiselect :allow-empty="false" v-bind="field" v-model="initialSceneIdRef" :searchable="false"
                    :close-on-select="true" :show-labels="false" :custom-label="(val) => val.sceneData.name"
                    placeholder="Choose" :options="initialSceneIdList" maxHeight="250">
                </Multiselect>
                </Field>

                <ErrorMessage name="initial_scene_id" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>
            <div class="relative">

                <Field v-slot="{ field }" name="is_cta_enabled" type="checkbox" :value="true" :unchecked-value="false">
                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" />
                   cta Enable <strong>*</strong>
                </label>
                </Field>

                <ErrorMessage name="is_cta_enabled" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="relative mb-7">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> cta Type
                <strong>*</strong> </label>

                <Field name="cta_type" :model-value="intitialCtaType" v-slot="{ field }">

                <Multiselect :allow-empty="false" v-bind="field" v-model="intitialCtaType" :searchable="false"
                    :close-on-select="true" :show-labels="false" placeholder="Choose" :options="ctaTypeList"
                    maxHeight="250">
                </Multiselect>

                </Field>

                <ErrorMessage name="cta_type" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="relative mb-7">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> cta Name
                <strong>*</strong> </label>

                <Field name="cta_name" class="input-primary" type="text" placeholder="cta Name"/>

                <ErrorMessage name="cta_name" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="flex flex-col gap-4 mb-5 items-start justify-start w-full col-span-1">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0"> Video </label>

                <div class="flex flex-row justify-start gap-2 items-start w-full h-64">

                <div class="w-96 h-full overflow-hidden p-[1px] rounded-md ">
                    <!-- https://archive.org/download/Popeye_forPresident/Popeye_forPresident_512kb.mp4 -->
                    <video v-if="aleVideoRef.preview" controls :src="aleVideoRef.preview"
                    class="object-fill h-[inherit] w-full"></video>
                    <div v-else
                    class="flex justify-center items-center gap-2  rounded-md h-full bg-gray-50 border-[1px] border-gray-300">
                    <p class="text-txt-black text-base font-medium"> Preview Area </p>
                    </div>
                </div>

                <div class="w-96 h-full mb-0">

                    <Field name="video" :model-value="aleVideoRef.fileData" v-slot="{ field }">
                    <div v-bind="field" class="h-full">
                        <input type="file" id="video" class="hidden" @input="(e) => {
                        aleVideoRef.fileData = e.target.files[0];
                        aleVideoRef.preview = handleCreateObjectURL(e.target.files[0]);
                        }
                        ">
                        <label for="video"
                        class="text-txt-50 border-[1px] border-dashed border-bg-550 rounded-md h-[inherit] flex flex-col justify-center items-center cursor-pointer">
                        <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                            d="M31.4999 16C31.5062 18.3815 30.7336 20.6997 29.2999 22.6013C29.2209 22.7063 29.1221 22.7948 29.0089 22.8617C28.8958 22.9285 28.7705 22.9724 28.6404 22.9909C28.5103 23.0093 28.3778 23.002 28.2505 22.9692C28.1233 22.9365 28.0037 22.879 27.8986 22.8C27.7936 22.7211 27.7051 22.6222 27.6383 22.5091C27.5714 22.3959 27.5275 22.2707 27.5091 22.1406C27.4906 22.0105 27.4979 21.878 27.5307 21.7507C27.5634 21.6234 27.6209 21.5038 27.6999 21.3988C28.8734 19.8441 29.5056 17.9479 29.4999 16C29.4999 13.6131 28.5517 11.3239 26.8638 9.63607C25.176 7.94825 22.8868 7.00004 20.4999 7.00004C18.1129 7.00004 15.8238 7.94825 14.1359 9.63607C12.4481 11.3239 11.4999 13.6131 11.4999 16C11.4999 16.2653 11.3945 16.5196 11.207 16.7071C11.0195 16.8947 10.7651 17 10.4999 17C10.2347 17 9.98031 16.8947 9.79278 16.7071C9.60524 16.5196 9.49988 16.2653 9.49988 16C9.4994 14.9909 9.63778 13.9865 9.91113 13.015C9.77489 13 9.63738 13 9.49988 13C7.90859 13 6.38246 13.6322 5.25724 14.7574C4.13203 15.8826 3.49988 17.4087 3.49988 19C3.49988 20.5913 4.13203 22.1175 5.25724 23.2427C6.38246 24.3679 7.90859 25 9.49988 25H12.4999C12.7651 25 13.0195 25.1054 13.207 25.2929C13.3945 25.4805 13.4999 25.7348 13.4999 26C13.4999 26.2653 13.3945 26.5196 13.207 26.7071C13.0195 26.8947 12.7651 27 12.4999 27H9.49988C8.4001 27.0003 7.31208 26.7738 6.30377 26.3347C5.29546 25.8955 4.38853 25.2532 3.6396 24.4478C2.89068 23.6425 2.31585 22.6913 1.95103 21.6538C1.58621 20.6163 1.43923 19.5147 1.51926 18.4179C1.59929 17.321 1.90462 16.2524 2.41619 15.2788C2.92775 14.3053 3.63455 13.4477 4.49245 12.7595C5.35035 12.0714 6.34092 11.5675 7.40229 11.2794C8.46366 10.9913 9.57304 10.9251 10.6611 11.085C11.7691 8.86898 13.5928 7.09188 15.8368 6.04158C18.0808 4.99127 20.6136 4.72928 23.025 5.29804C25.4365 5.8668 27.5853 7.233 29.1234 9.17535C30.6616 11.1177 31.4989 13.5224 31.4999 16ZM20.2074 15.2925C20.1145 15.1996 20.0042 15.1258 19.8828 15.0755C19.7614 15.0252 19.6313 14.9992 19.4999 14.9992C19.3685 14.9992 19.2383 15.0252 19.1169 15.0755C18.9955 15.1258 18.8853 15.1996 18.7924 15.2925L14.7924 19.2925C14.6995 19.3854 14.6258 19.4957 14.5755 19.6171C14.5252 19.7385 14.4993 19.8686 14.4993 20C14.4993 20.1314 14.5252 20.2615 14.5755 20.3829C14.6258 20.5043 14.6995 20.6146 14.7924 20.7075C14.98 20.8952 15.2345 21.0006 15.4999 21.0006C15.6313 21.0006 15.7614 20.9747 15.8828 20.9244C16.0042 20.8741 16.1145 20.8004 16.2074 20.7075L18.4999 18.4138V26C18.4999 26.2653 18.6052 26.5196 18.7928 26.7071C18.9803 26.8947 19.2347 27 19.4999 27C19.7651 27 20.0195 26.8947 20.207 26.7071C20.3945 26.5196 20.4999 26.2653 20.4999 26V18.4138L22.7924 20.7075C22.8853 20.8004 22.9956 20.8741 23.117 20.9244C23.2384 20.9747 23.3685 21.0006 23.4999 21.0006C23.6313 21.0006 23.7614 20.9747 23.8828 20.9244C24.0042 20.8741 24.1145 20.8004 24.2074 20.7075C24.3003 20.6146 24.374 20.5043 24.4243 20.3829C24.4746 20.2615 24.5004 20.1314 24.5004 20C24.5004 19.8686 24.4746 19.7385 24.4243 19.6171C24.374 19.4957 24.3003 19.3854 24.2074 19.2925L20.2074 15.2925Z"
                            fill="#5B616E" />
                        </svg>

                        Video
                        <span v-if="aleVideoRef.fileData" class="text-txt-50 font-semibold text-center">
                            {{ aleVideoRef.fileData.name }}
                        </span>
                        </label>
                    </div>
                    </Field>

                    <ErrorMessage name="video" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

                </div>

                </div>

            </div>

            <div class="flex flex-col gap-4 mb-5 items-start justify-start w-full">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0"> Thumbnail <strong>*</strong>
                </label>

                <div class="flex flex-row justify-start gap-2 items-start w-full h-64">

                <div class="w-96 h-full overflow-hidden">
                    <img v-if="aleThumbnailRef.preview" :src="aleThumbnailRef.preview" alt=""
                    class=" h-[inherit] rounded-md bg-gray-100 w-full">
                    <div v-else
                    class="flex justify-center items-center gap-2  rounded-md h-full bg-gray-50 border-[1px] border-gray-300">
                    <p class="text-txt-black text-base font-medium"> Preview Area </p>
                    </div>
                </div>

                <div class="w-96 h-full mb-0">

                    <Field name="thumbnail" :model-value="aleThumbnailRef.fileData" v-slot="{ field }">
                    <div v-bind="field" class="h-full">
                        <input type="file" id="thumbnail" class="hidden" @input="(e) => {
                        aleThumbnailRef.fileData = e.target.files[0];
                        aleThumbnailRef.preview = handleCreateObjectURL(e.target.files[0]);
                        }">
                        <label for="thumbnail"
                        class="text-txt-50 border-[1px] border-dashed border-bg-550 rounded-md h-[inherit] flex flex-col justify-center items-center cursor-pointer">
                        <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                            d="M31.4999 16C31.5062 18.3815 30.7336 20.6997 29.2999 22.6013C29.2209 22.7063 29.1221 22.7948 29.0089 22.8617C28.8958 22.9285 28.7705 22.9724 28.6404 22.9909C28.5103 23.0093 28.3778 23.002 28.2505 22.9692C28.1233 22.9365 28.0037 22.879 27.8986 22.8C27.7936 22.7211 27.7051 22.6222 27.6383 22.5091C27.5714 22.3959 27.5275 22.2707 27.5091 22.1406C27.4906 22.0105 27.4979 21.878 27.5307 21.7507C27.5634 21.6234 27.6209 21.5038 27.6999 21.3988C28.8734 19.8441 29.5056 17.9479 29.4999 16C29.4999 13.6131 28.5517 11.3239 26.8638 9.63607C25.176 7.94825 22.8868 7.00004 20.4999 7.00004C18.1129 7.00004 15.8238 7.94825 14.1359 9.63607C12.4481 11.3239 11.4999 13.6131 11.4999 16C11.4999 16.2653 11.3945 16.5196 11.207 16.7071C11.0195 16.8947 10.7651 17 10.4999 17C10.2347 17 9.98031 16.8947 9.79278 16.7071C9.60524 16.5196 9.49988 16.2653 9.49988 16C9.4994 14.9909 9.63778 13.9865 9.91113 13.015C9.77489 13 9.63738 13 9.49988 13C7.90859 13 6.38246 13.6322 5.25724 14.7574C4.13203 15.8826 3.49988 17.4087 3.49988 19C3.49988 20.5913 4.13203 22.1175 5.25724 23.2427C6.38246 24.3679 7.90859 25 9.49988 25H12.4999C12.7651 25 13.0195 25.1054 13.207 25.2929C13.3945 25.4805 13.4999 25.7348 13.4999 26C13.4999 26.2653 13.3945 26.5196 13.207 26.7071C13.0195 26.8947 12.7651 27 12.4999 27H9.49988C8.4001 27.0003 7.31208 26.7738 6.30377 26.3347C5.29546 25.8955 4.38853 25.2532 3.6396 24.4478C2.89068 23.6425 2.31585 22.6913 1.95103 21.6538C1.58621 20.6163 1.43923 19.5147 1.51926 18.4179C1.59929 17.321 1.90462 16.2524 2.41619 15.2788C2.92775 14.3053 3.63455 13.4477 4.49245 12.7595C5.35035 12.0714 6.34092 11.5675 7.40229 11.2794C8.46366 10.9913 9.57304 10.9251 10.6611 11.085C11.7691 8.86898 13.5928 7.09188 15.8368 6.04158C18.0808 4.99127 20.6136 4.72928 23.025 5.29804C25.4365 5.8668 27.5853 7.233 29.1234 9.17535C30.6616 11.1177 31.4989 13.5224 31.4999 16ZM20.2074 15.2925C20.1145 15.1996 20.0042 15.1258 19.8828 15.0755C19.7614 15.0252 19.6313 14.9992 19.4999 14.9992C19.3685 14.9992 19.2383 15.0252 19.1169 15.0755C18.9955 15.1258 18.8853 15.1996 18.7924 15.2925L14.7924 19.2925C14.6995 19.3854 14.6258 19.4957 14.5755 19.6171C14.5252 19.7385 14.4993 19.8686 14.4993 20C14.4993 20.1314 14.5252 20.2615 14.5755 20.3829C14.6258 20.5043 14.6995 20.6146 14.7924 20.7075C14.98 20.8952 15.2345 21.0006 15.4999 21.0006C15.6313 21.0006 15.7614 20.9747 15.8828 20.9244C16.0042 20.8741 16.1145 20.8004 16.2074 20.7075L18.4999 18.4138V26C18.4999 26.2653 18.6052 26.5196 18.7928 26.7071C18.9803 26.8947 19.2347 27 19.4999 27C19.7651 27 20.0195 26.8947 20.207 26.7071C20.3945 26.5196 20.4999 26.2653 20.4999 26V18.4138L22.7924 20.7075C22.8853 20.8004 22.9956 20.8741 23.117 20.9244C23.2384 20.9747 23.3685 21.0006 23.4999 21.0006C23.6313 21.0006 23.7614 20.9747 23.8828 20.9244C24.0042 20.8741 24.1145 20.8004 24.2074 20.7075C24.3003 20.6146 24.374 20.5043 24.4243 20.3829C24.4746 20.2615 24.5004 20.1314 24.5004 20C24.5004 19.8686 24.4746 19.7385 24.4243 19.6171C24.374 19.4957 24.3003 19.3854 24.2074 19.2925L20.2074 15.2925Z"
                            fill="#5B616E" />
                        </svg>

                        Thumbnail
                        <span v-if="aleThumbnailRef.fileData" class="text-txt-50 font-semibold text-center">
                            {{ aleThumbnailRef.fileData.name }}
                        </span>
                        </label>
                    </div>
                    </Field>

                    <ErrorMessage name="thumbnail" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

                </div>

                </div>

            </div>
            <!-- generate link -->
            <div class="relative mb-7">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> ShortUrl </label>

                <div class="w-full flex flex-col gap-2">

                    <button type="button" @click="handleGenerate()" class="bg-blue-500 text-white px-4 py-2 rounded-md mt-2">
                    Generate Shorten Link
                    </button>
                </div>

            </div>

            <div class="relative mb-7">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Default Language
                <strong>*</strong> </label>

                <Field name="initial_default_language" :model-value="initial_default_language" v-slot="{ field }">
                <multiselect v-model="initial_default_language" v-bind="field" :options="languages" :custom-label="name" placeholder="Select one" label="name"
                    track-by="name" :close-on-select="true"></multiselect>
                </Field>

                <ErrorMessage name="initial_default_language" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="relative mb-7">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Supported Language
                    <strong>*</strong> </label>

                <Field name="supported_languages" v-slot="{ field }">
                    <multiselect v-bind="field" :options="languages" :custom-label="name" placeholder="Select one" label="name"
                    track-by="name" :multiple="true" maxHeight="250"></multiselect>
                </Field>

                <ErrorMessage name="supported_languages" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="relative">

                <Field v-slot="{ field }" name="currency_support" type="checkbox" :value="true" :unchecked-value="false">
                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" />
                    Currency Support <strong>*</strong>
                </label>
                </Field>

                <ErrorMessage name="currency_support" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <Button id="editAleSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>

            </Form>

        </div>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}
</style>
