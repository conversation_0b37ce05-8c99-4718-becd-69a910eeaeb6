<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const deleteToolTip = ref(false);
const saveToolTip = ref(false);
const OrientationToolTip = ref(false);
const ReplaceToolTip = ref(false);

defineProps([ 'index', 'data']);

const emit = defineEmits(['orientation', 'selectedImageId', 'deleteButton']);

const deleteImg = (value) => {
  console.log('clicked', value);
  emit('deleteButton', value);
};
const changeOrientation = (id) => {
  const activateOrientation = ref(true);
  emit('orientation', id, activateOrientation);
};

const showSaveToolTip = () => {
  saveToolTip.value = true;
};

const hideSaveToolTip = () => {
  saveToolTip.value = false;
};

const showDeleteToolTip = () => {
  deleteToolTip.value = true;
};

const hideDeleteToolTip = () => {
  deleteToolTip.value = false;
};

const showOrientationToolTip = () => {
  OrientationToolTip.value = true;
};

const hideOrientationToolTip = () => {
  OrientationToolTip.value = false;
};

const showReplaceToolTip = () => {
  ReplaceToolTip.value = true;
};

const hideReplaceToolTip = () => {
  ReplaceToolTip.value = false;
};

const emitSelectedImageId = (Id) => {
  emit('selectedImageId', Id);
};

</script>

<template>
    <div class="w-[16.25rem] h-[13.125rem] flex flex-col" >

        <div class="w-full h-[8.125rem] mt-[1.25rem] mb-[1.25rem] skeleton-loader "  @click="emitSelectedImageId(data._id)">
            <img class=" object-fill w-full h-full" :src="data.thumbnail"/>
         </div>

        <div class="flex justify-between items-center mb-2.5">
            <div class="flex justify-center items-center">
                <p class="inline-block w-32 whitespace-nowrap overflow-hidden text-ellipsis rounded p-1 pl-3 text-txt-default text-base ">{{ data.name }}
                </p>
            </div>

            <div class="flex gap-2 relative">
                <p class="overlay text-xs " v-if="OrientationToolTip">change Orientation</p>
                <svg @mouseover="showOrientationToolTip" @mouseleave="hideOrientationToolTip"
                    @click="changeOrientation(data.name)" class="w-5 h-5 cursor-pointer eva eva-compass-outline"
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#413f48" onclick="resetOrientation()">
                    <g data-name="Layer 2">
                        <g data-name="compass">
                            <rect width="24" height="24" opacity="0"></rect>
                            <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z">
                            </path>
                            <path
                                d="M15.68 8.32a1 1 0 0 0-1.1-.25l-4.21 1.7a1 1 0 0 0-.55.55l-1.75 4.26a1 1 0 0 0 .18 1h.05A1 1 0 0 0 9 16a1 1 0 0 0 .38-.07l4.21-1.7a1 1 0 0 0 .55-.55l1.75-4.26a1 1 0 0 0-.21-1.1zm-4.88 4.89l.71-1.74 1.69-.68-.71 1.74z">
                            </path>
                        </g>
                    </g>
                </svg>

                <p class="overlay text-xs" v-if="saveToolTip">Edit Title</p>
                <svg @mouseover="showSaveToolTip" @mouseleave="hideSaveToolTip"
                        @click="() => { router.push(`edit_title/${data._id}`); }" class="w-5 h-5 cursor-pointer" width="24"
                    height="24" viewBox="0 0 24 24" fill="#413f48" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M21.5303 6.93757L17.0624 2.46874C16.9139 2.32013 16.7375 2.20225 16.5433 2.12182C16.3492 2.0414 16.1411 2 15.931 2C15.7208 2 15.5128 2.0414 15.3186 2.12182C15.1245 2.20225 14.9481 2.32013 14.7995 2.46874L2.46899 14.8003C2.31978 14.9483 2.20147 15.1245 2.12096 15.3187C2.04045 15.5128 1.99934 15.721 2.00001 15.9312V20.4001C2.00001 20.8244 2.16857 21.2313 2.46862 21.5314C2.76867 21.8314 3.17562 22 3.59995 22H8.06878C8.27896 22.0007 8.48718 21.9595 8.68134 21.879C8.87549 21.7985 9.0517 21.6802 9.19973 21.531L21.5303 9.20048C21.6789 9.05191 21.7968 8.87552 21.8772 8.68138C21.9576 8.48724 21.999 8.27916 21.999 8.06903C21.999 7.85889 21.9576 7.65081 21.8772 7.45667C21.7968 7.26253 21.6789 7.08614 21.5303 6.93757ZM8.06878 20.4001H3.59995V15.9312L12.3996 7.13156L16.8684 11.6004L8.06878 20.4001ZM17.9994 10.4684L13.5306 6.00061L15.9305 3.6007L20.3993 8.06853L17.9994 10.4684Z"
                        fill="#413f48" />
                </svg>

                <p class="overlay text-xs" v-if="ReplaceToolTip">Replace Image</p>
                <svg @mouseover="showReplaceToolTip" @mouseleave="hideReplaceToolTip"  @click="() => {
                    router.push({name:'Replace_image',params:{image_id:data._id}});
                }"
                    class="w-5 h-5 cursor-pointer eva eva-compass-outline" xmlns="http://www.w3.org/2000/svg"
                    width="20px" height="20px" viewBox="0 0 24 24" fill="#413f48" data-target="#add_media_modal"
                    data-toggle="modal"
                    >
                    <g data-name="Layer 2">
                        <g data-name="flip-2">
                            <rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"></rect>
                            <path
                                d="M6.09 19h12l-1.3 1.29a1 1 0 0 0 1.42 1.42l3-3a1 1 0 0 0 0-1.42l-3-3a1 1 0 0 0-1.42 0 1 1 0 0 0 0 1.42l1.3 1.29h-12a1.56 1.56 0 0 1-1.59-1.53V13a1 1 0 0 0-2 0v2.47A3.56 3.56 0 0 0 6.09 19z">
                            </path>
                            <path
                                d="M5.79 9.71a1 1 0 1 0 1.42-1.42L5.91 7h12a1.56 1.56 0 0 1 1.59 1.53V11a1 1 0 0 0 2 0V8.53A3.56 3.56 0 0 0 17.91 5h-12l1.3-1.29a1 1 0 0 0 0-1.42 1 1 0 0 0-1.42 0l-3 3a1 1 0 0 0 0 1.42z">
                            </path>
                        </g>
                    </g>
                </svg>

                <p class="overlay text-xs" v-if="deleteToolTip">Delete Image</p>
                <svg @mouseover="showDeleteToolTip" @mouseleave="hideDeleteToolTip" @click="deleteImg(data._id)"
                    class="w-5 h-5 cursor-pointer" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M11.9904 3.50077C11.1614 3.50077 10.4894 4.17267 10.4894 5.00149L10.4894 5.00812H13.4914L13.4914 5.00149C13.4914 4.17267 12.8194 3.50077 11.9904 3.50077ZM8.98836 5.00149L8.98837 5.00812H3.75369C3.33744 5.00812 3 5.34556 3 5.76181C3 6.17806 3.33744 6.5155 3.75369 6.5155H5.00399V19.0001C5.00399 20.6569 6.34713 22.0001 8.00399 22.0001H16.0079C17.6648 22.0001 19.0079 20.6569 19.0079 19.0001V6.5155H20.2363C20.6526 6.5155 20.99 6.17806 20.99 5.76181C20.99 5.34556 20.6526 5.00812 20.2363 5.00812H14.9924L14.9925 5.00149C14.9925 3.34381 13.6484 2 11.9904 2C10.3324 2 8.98836 3.34381 8.98836 5.00149ZM13.4912 9.75192C13.4912 9.3371 13.828 9.00083 14.2428 9.00083C14.6576 9.00083 14.9944 9.33711 14.9944 9.75192V17.2979C14.9944 17.7127 14.6576 18.049 14.2428 18.049C13.828 18.049 13.4912 17.7127 13.4912 17.2979V9.75192ZM9.00057 9.75194C9.00057 9.33712 9.33737 9.00085 9.75218 9.00085C10.167 9.00085 10.5038 9.33713 10.5038 9.75194V17.298C10.5038 17.7128 10.167 18.049 9.75218 18.049C9.33737 18.049 9.00057 17.7128 9.00057 17.298V9.75194Z"
                    fill="#413f48" />
                </svg>

            </div>

        </div>
    </div>

</template>

<style scoped>
::placeholder {
    font-size: 12px;
    font-weight: 500;
}

.overlay {
    background-color: #000000;
    color: #ffffff;
    padding: 5px;
    border-radius: 5px;
    position: absolute;
    z-index: 1;
    white-space: nowrap;
    top: 25px;
    right: 5px;
}

.overlay:hover {
    opacity: 1;
}

.skeleton-loader {
        background-color: #ffffff;
        /* background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, .5) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #262626; */
        background: linear-gradient(90deg, rgba(255,255,255,1) 90%, rgba(1,7,8,1) 150%);
        background-size: 200% 100%;
        background-position-x: 180%;
        animation:  1s loading ease-in-out infinite;
    }

    @keyframes loading {
        to {
          background-position-x: -20%;
        }
    }

</style>
