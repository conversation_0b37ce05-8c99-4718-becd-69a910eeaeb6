import * as yup from 'yup';

import { fileValidation, imageSizeValidation } from '../../helpers/validationSchemaHelpers';
export const createCommunitySchema = yup.object({
  name: yup.string().required(),
  category: yup.object().required(),
  file: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
});

export const editCommunitySchema = yup.object({
  name: yup.string().required(),
  category: yup.object().required(),
  file: yup.mixed().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
});
