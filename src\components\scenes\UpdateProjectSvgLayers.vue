<script setup>

import Spinner from '../common/Spinner.vue';
import { useRoute } from 'vue-router';
import Multiselect from 'vue-multiselect';
import { Field, Form, ErrorMessage} from 'vee-validate';
import { ref, watch } from 'vue';
import projectSchema from '../../validationSchema/scene/updateProjectLayerSchema';
import { validationSchema as ProjectValidationSchema } from '../../validationSchema/scene/updateProjectLayerSchema';
import { validationSchema as MasterValidationSchema } from '../../validationSchema/scene/updateMasterLayerSchema';
import masterFormSchema from '../../validationSchema/scene/updateMasterLayerSchema';
import { removeUndefinedAndNullInObject } from '../../helpers/domhelper';
import { Org_Store } from '../../store/organization';
import { ProjectStore } from '../../store/project';
import { updateLayers, updateLayersVideoTag } from '../../api/projects/scene/svg/index';
import { updateLayers as updateMasterLayers, updateLayersVideoTag as updateMasterLayersVideoTag } from '../../api/masterScene/svg/index';
import { isMasterScenePath } from '../../helpers/helpers';
import {  getScene as getMasterScene } from '../../api/masterScene/index';
import { getScene } from '@/api/projects/scene';
import { unitplanTypeList } from '@/helpers/constants';

const route = useRoute();
const props = defineProps({
  landmarks: Object,
  categoryList: Array,
});
const isMasterScene = ref(isMasterScenePath(route.fullPath));
console.log(isMasterScene.value);

const formSchema = isMasterScene.value ? masterFormSchema : projectSchema;
console.log(formSchema);
const loader = ref(false);
const fileKey = ref(0);
const svgId = ref(route.query.svgId);
const sceneId = ref(route.params.scene_id);
const layerId = ref(route.query.layerId);
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const project_id  = route.params.project_id;
const componentLoaded = ref(false);

const showVideoTagUpload = ref(false);
const showOutputVideoTag = ref(false);
const valueInVideoTag = ref();
const showCustomError = ref(false);
const setToNull = ref(false);
const finishedUploading = ref(false);
// const videoTagUploadRef = ref(null);
const sceneDetails = ref();
const showGroupUnits = ref(false);
const groupUnitsName = ref('');
const bedrooms = ref('');

const getSceneById = () => {

  if (isMasterScene.value){
    getMasterScene(sceneId.value).then((res) => {
      sceneDetails.value = res;
    });
  } else {
    getScene(project_id, sceneId.value).then((res) => {
      sceneDetails.value = res;
    });
  }
};

getSceneById();

// Find the route new
const findTypeRoute = () => {
  const routeList = [];
  Object.values(sceneDetails.value.svgData).forEach((svg) => {

    Object.values(svg.layers).forEach((layer) => {
      if (layer.type === 'route') {
        routeList.push({ _id: layer.layer_id, name: layer.name });
      }
    });
  });
  return routeList;
};

const handleGetScene = async () => {

  if (isMasterScene.value && sceneId.value){
    console.log("api called");
    await getMasterScene(sceneId.value).then((res) => {
      sceneDetails.value = res;
    });
  } else {
    if (!project_id || !sceneId.value) {
      return;
    }
    await getScene(project_id, sceneId.value).then((res) => {
      sceneDetails.value = res;
      console.log("sceneDetails.value", sceneDetails.value);
    });
  }
};

handleGetScene(); // Initialize
projectStore.Refr;

const handleChange = (val) => {

  if (val === 'project') {
    if (formSchema.project_id.options.length === 0) {
      formSchema.project_id.options = Object.values(newOrganizationStore.projects).map((project) => ({ '_id': project._id, 'name': project.name }));
    }
  }
  if (val === 'scene' || val === 'pin' || val === 'building' || val === 'floor' || val==='community') {
    if (formSchema.scene_id.options.length === 0) {
      formSchema.scene_id.options = isMasterScene.value ? Object.values(newOrganizationStore.masterScenes).map(({ sceneData }) => ({ '_id': sceneData._id, 'name': sceneData.name })) : Object.values(projectStore.scenes).map(({ sceneData }) => ({ '_id': sceneData._id, 'name': sceneData.name }));
    }
  }

  if (!isMasterScene.value){
    if (val === 'landmark') {
      if (formSchema.landmark_id.options.length === 0) {
        formSchema.landmark_id.options = Object.values(props.landmarks).map((landmark) => ({ '_id': landmark._id, 'name': landmark.name }));
      // Console.log(`output->landmark`,props.landmarks)
      }
      // Finding layers with type route
      formSchema.route_id.options = findTypeRoute();
    }
    if (val === 'building') {

      formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
    }
    if (val === 'community') {

      formSchema.community_id.options = Object.values(projectStore.communities).map((community) => ({ '_id': community._id, 'name': community.name }));
    }
    if (val === 'floor') {
      if (formSchema.building_id.options.length === 0) {
        formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
      watch(formSchema.building_id.ref, (val) => {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[val].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      });
      if (formSchema.building_id.ref.value) {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[formSchema.building_id.ref.value].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      }

    }
    if (val === 'units') {
      if (formSchema.units.options.length === 0) {
        formSchema.units.options = Object.values(projectStore.units).map((unit) => ({ '_id': unit._id, 'name': (unit.name + " " + (unit.building_id?projectStore.buildings[unit.building_id].name:"") + " " + (unit.community_id?projectStore.communities[unit.community_id].name:"")   ) }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenity') {
      if (formSchema.amenity_id.options.length === 0) {
        formSchema.amenity_id.options = Object.values(projectStore.amenities).map((amenity) => ({ '_id': amenity._id, 'name': amenity.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenitycategory') {
      if (formSchema.amenity_category.options.length === 0) {
        formSchema.amenity_category.options = Object.values(props.categoryList).map((elem) => ({ '_id': elem.name, 'name': elem.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'grouped_units'){
      if (formSchema.showLabel.ref.value){
        showGroupUnits.value = true;
      } else {
        showGroupUnits.value = false;
      }
    } else {
      showGroupUnits.value = false;
    }
  }
};

const handleInitialValues = () => {
  componentLoaded.value = false;
  layerId.value = route.query.layerId;
  // Testing
  const layer = sceneDetails.value.svgData[svgId.value].layers[layerId.value];

  formSchema.type.ref.value = layer.type ? layer.type : sceneDetails.value.svgData[svgId.value].type;
  formSchema.project_id.ref.value = layer.project_id ? layer.project_id : null;
  formSchema.scene_id.ref.value = layer.scene_id ? layer.scene_id : null;
  formSchema.image_id.ref.value = layer.image_id ? layer.image_id : null;

  if (!isMasterScene.value){
    formSchema.landmark_id.ref.value = layer.landmark?.landmark_id ? layer.landmark.landmark_id : null;
    formSchema.route_id.ref.value = layer.landmark?.route_id ? layer.landmark.route_id : null;
    formSchema.building_id.ref.value = layer.building_id ? layer.building_id : sceneDetails.value.svgData[svgId.value].building_id? sceneDetails.value.svgData[svgId.value].building_id : null;
    formSchema.community_id.ref.value = layer.community_id ? layer.community_id : null;
    formSchema.floor_id.ref.value = layer.floor_id ? layer.floor_id : null;
    formSchema.amenity_id.ref.value = layer.amenity_id ? layer.amenity_id : null;
    formSchema.amenity_category.ref.value = layer.amenity_category ? layer.amenity_category : null;
    formSchema.units.ref.value = layer.units ? layer.units.map((elem) => {
      return { '_id': elem, 'name': projectStore.units?.[elem]?.name };
    }) : null;
    formSchema.showLabel.ref.value = layer.showLabel ? layer.showLabel : false;
    if (layer.showLabel){
      showGroupUnits.value = true;
    } else {
      showGroupUnits.value = false;
    }
    if (layer.group_name !== '' & layer.group_name !== undefined & layer.group_name !== null){
      groupUnitsName.value = layer.group_name;
    } else {
      groupUnitsName.value = '';
    }
    if (layer.bedrooms !== null){
      bedrooms.value = layer.bedrooms;
    } else {
      bedrooms.value = null;
    }
  }
  formSchema.labelName.ref.value = layer.name ? layer.name : null;
  formSchema.title.ref.value = layer.title ? layer.title : null;
  formSchema.category.ref.value = layer.category ? layer.category : null;

  layer.video_tag ? console.log("Video tag in Layer", layer.video_tag) : console.log("No Video tag in Layer");

  formSchema.outputVideoTag.outputRef.value = layer.video_tag ? layer.video_tag : undefined;
  valueInVideoTag.value = formSchema.outputVideoTag.outputRef.value ? formSchema.outputVideoTag.outputRef.value : false;

  if (formSchema.outputVideoTag.outputRef.value !== undefined){
    console.log("Video Tag is Present in ref");
    console.log("value inside outputref", formSchema.outputVideoTag.outputRef.value);
    console.log("value inside ref", valueInVideoTag.value);
    showVideoTagUpload.value = false;
    showOutputVideoTag.value = true;
  } else {
    console.log("No Video Tag in ref");
    showVideoTagUpload.value = true;
    showOutputVideoTag.value = false;
  }

  console.log("Whether to show upload button", showVideoTagUpload.value);
  console.log("Whether to show output VideoTag field", showOutputVideoTag.value);

  if (formSchema.video_tag.ref.value){
    console.log("Need to clear file button");
    formSchema.video_tag.ref.value = null;
    fileKey.value += 1;   // reset the file upload button
  } else {
    console.log("No need oto clear file button");
  }
  setToNull.value = false;
  finishedUploading.value = false;
  handleChange(formSchema.type.ref.value);

  componentLoaded.value = true;
};

const areValuesEqual = (obj1, obj2) => {
  if (!obj1){
    return true;
  }
  if (!obj2){
    return true;
  }
  const keys1 = Object.keys(obj1).sort();
  const keys2 = Object.keys(obj2).sort();
  if (keys1.length !== keys2.length) {
    return false;
  }
  for (let i = 0; i < keys1.length; i++) {
    // console.log(keys1[i],"===",keys2[i]);
    if (keys1[i] !== keys2[i]) {
      return false;
    }
  }
  // Compare values by sorted keys
  for (let i = 0; i < keys1.length; i++) {
    const key = keys1[i];
    // console.log(obj1[key],"===",obj2[key]);
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }
  return true;
};

const  extractFileName=(url) => {
  if (!url) {
    console.error("URL is undefined or null");
    return null;
  }

  // Match only the filename portion, immediately before the final underscore and ".mp4"
  const match = url.match(/([^/]+?)_\d+\.mp4/);

  if (match) {
    const decodedFileName = decodeURIComponent(match[1]); // Decode %20 to spaces
    console.log("Extracted File Name:", decodedFileName);
    const fileName = decodedFileName.match(/[^/]+$/)[0];
    return `${fileName}.mp4`;
  }

  console.warn("No matching file name found in the URL");
  return null;
};

watch(() => route.query.svgId, () => {
  svgId.value = route.query.svgId;
});

watch(() => route.query.layerId, () => {
  handleInitialValues();
});

watch(() => {
  const sceneData = !isMasterScene.value ? projectStore?.scenes[sceneId.value]?.svgData : newOrganizationStore.masterScenes?.[sceneId.value].svgData;
  return sceneData;
});

watch(() =>
  sceneDetails.value, () => {
  handleInitialValues();
});

// Resetting the videoTag When Type is changed
// watch(
//   ()=>formSchema.type.ref.value,
//   (newType)=>{
//     const layer = sceneDetails.value.svgData[svgId.value].layers[layerId.value];
//     if(layer.type !== newType){
//         console.log("type is changed");
//         if(layer.video_tag){
//           delete layer.video_tag
//         }
//         console.log("layer video tag after changing type",layer);
//         valueInVideoTag.value = false;
//         showVideoTagUpload.value = true;
//     }
//   });

// watch(()=>formSchema.type.ref.value,
//   (newType)=>{
//       const layer = sceneDetails.value.svgData[svgId.value].layers[layerId.value];
//       if(layer.type !== newType){
//         if(layer.video_tag){
//           valueInVideoTag.value = layer.video_tag
//           // formSchema.video_tag.ref.value = layer.video_tag;
//         }
//       }
//   })

// Preventing from uploading the same file again
watch(
  () => formSchema.video_tag.ref.value,
  (newFile) => {
    if (newFile instanceof File) {
      const layer =  sceneDetails.value.svgData[svgId.value].layers[layerId.value];
      const { video_tag } = layer;
      if (video_tag){
        const OldVideoTag = extractFileName(video_tag);
        console.log("OldVideoTag", OldVideoTag);
        console.log("NewVideoTag", newFile.name);
        console.log("Are equal", areValuesEqual(OldVideoTag, newFile.name));

        if (areValuesEqual(OldVideoTag, newFile.name)) {
        // formSchema.videoTag.ref.value = null;
          showCustomError.value = true;
        } else {
          showCustomError.value = false;
        }
      }

    }
  },
);
watch(formSchema.type.ref, (val) => {
  console.log("formSchema.type.ref", val);
  handleChange(val);
});

const updateLayersData = (obj) => {
  return new Promise((resolve, reject) => {
    const dataWithoutVideoTag = Object.fromEntries(
      Object.entries(obj).filter(([key]) => key !== 'video_tag'),
    );
    console.log(dataWithoutVideoTag);
    const objectToUpdate = {
      layer_id: route.query.layerId,
      ...( !isMasterScene.value && {project_id: route.params.project_id}),
      svg_id: route.query.svgId,
      query: dataWithoutVideoTag,
    };

    if (isMasterScene.value){
      updateMasterLayers(objectToUpdate).then(() => {
        resolve(true);
      }).catch((err) => {
        console.log('Error');
        console.log('output->err in updateLayersData', err);
        reject(false);
      });
    } else {
      updateLayers(objectToUpdate).then(() => {
        resolve(true);
      }).catch((err) => {
        console.log('Error');
        console.log('output->err in updateLayersData', err);
        reject(false);
      });
    }
  });

};
const  updateLayersFiles = (obj) => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('layer_id', route.query.layerId);
    if (!isMasterScene.value){
      formData.append('project_id', route.params.project_id);
    }
    formData.append('scene_id', sceneId.value);
    formData.append('svg_id', route.query.svgId);
    formData.append('video_tag', obj.video_tag);

    if (isMasterScene.value){
      updateMasterLayersVideoTag(formData).then(() => {
        console.log("success");
        resolve(true);
      }).catch((err) => {
        console.log('Error');
        console.log('output->err in updateMasterSceneLayer', err);
        reject(false);
      });
    } else {
      updateLayersVideoTag(formData).then(() => {
        console.log("success");
        resolve(true);
      }).catch((err) => {
        console.log('Error');
        console.log('output->err in updateProjectSceneLayer', err);
        reject(false);
      });
    }

  });

};

function getChangedValues (layer, query){
  console.log("oldValue", layer);
  console.log("newValue", query);
  const changedFields = {};
  if (query.units){
    changedFields.units = [];
    if (layer.units){
      let count = 0;
      for (const subkey in layer.units){
        if (subkey in query.units && (layer.units[subkey].name !== query.units[subkey].name)){
          changedFields.units[count] = query.units[subkey];
          count ++;
        }
      }
      let count1 = 0;
      for (const subkey in query.units){
        if (!(subkey in layer.units)){
          changedFields.units[count1] = query.units[subkey];
          count1 ++;
        }
      }
    } else {
      for (const subkey in query.units){
        if (query.units[subkey].name){
          console.log("111", query.units[subkey]);
          changedFields.units[subkey] = query.units[subkey];
          console.log("222", changedFields.units[subkey]);
        }
      }
    }
  } else {
    for (const key in layer){
      if (key in query && layer[key] !== query[key]){
        changedFields[key] = query[key];
      }
    }

    for (const key in query){
      if (!(key in layer)){
        changedFields[key] = query[key];
      }
    }
  }
  if (Object.keys(changedFields).length > 0){
    changedFields.type = query.type;
  }
  return changedFields;
}
const handleUpdateSvgLayer = async (query) => {
  console.log("query", query);
  // Retrieve Old Data
  const layer = sceneDetails.value.svgData[svgId.value].layers[layerId.value];
  console.log("in db ", layer);
  const {...queryObj} = query;
  if (layer.video_tag){
    const videoTagFilename = extractFileName(layer.video_tag);
    layer.video_tag = videoTagFilename;
  }
  if (query.video_tag){
    queryObj.video_tag = queryObj.video_tag.name;
  }
  const existingValues = Object.fromEntries(
    Object.entries(layer).filter(([key]) => key !== 'layer_id'),
  );
  console.log("existing values", existingValues);

  const fieldsToUpdate = getChangedValues(existingValues, queryObj);
  console.log("to be stored", fieldsToUpdate);
  const otherFields = {};
  console.log("check null", setToNull.value);

  if (Object.keys(fieldsToUpdate).length > 0){
    loader.value = true;
  }

  if (setToNull.value && layer.video_tag !== null && layer.video_tag && !query.video_tag){
    loader.value = true;
    console.log("delete videoTag");
    const resetVideoTag = {
      video_tag: null,
    };
    await updateLayersFiles(resetVideoTag).then((res) => {
      if (res){
        loader.value = false;
        finishedUploading.value = true;
      }
    });
  }
  if (fieldsToUpdate.video_tag){
    fieldsToUpdate.video_tag = query.video_tag;
    console.log("files updating");
    const fields = Object.fromEntries(
      Object.entries(fieldsToUpdate).filter(([key]) => key !== 'video_tag' && key !== 'type'),
    );
    console.log("fields", fields);

    Object.assign(otherFields, fields);
    // await updateVideoLayers(null,query,'update');
    await updateLayersFiles(fieldsToUpdate).then((res) => {
      if (res){
        finishedUploading.value = true;
      }
    });
  } else {
    Object.assign(otherFields, fieldsToUpdate);
  }
  console.log("otherfiels", otherFields);
  console.log("check length", Object.keys(otherFields).length);

  if (Object.keys(otherFields).length > 0){
    console.log("data updating");

    await updateLayersData(query).then((result) => {
      if (result){
        loader.value = false;
        if (fieldsToUpdate.video_tag){
          loader.value = true;
        }
        if (!fieldsToUpdate.video_tag && !setToNull.value){
          handleGetScene();
        }

      }
    });
  }
  if (finishedUploading.value || setToNull.value){
    if (isMasterScene.value){
      newOrganizationStore.ForceRefreshMasterScenes();  // master scene
      handleGetScene();
    } else {
      // projectStore.ForceRefreshScenes(project_id); // project scene
      handleGetScene();
    }
    loader.value = false;
  }
};

const handleSubmit = (values) => {
  if (!isMasterScene.value){
    if (values.units && values.units.length !== 0) {
      values.units = values.units.map((elem) => elem._id);
    }
  }
  const obj = removeUndefinedAndNullInObject(values);
  handleUpdateSvgLayer(obj);
};
// const handleFileUpload = (event) => {
//   const input = event.target.files[0];
//   console.log("input", input);
//   if (input) {
//     formSchema.video_tag.ref.value = input;
//     console.log("inside dnd updaload", formSchema.video_tag.ref.value);
//   }
// };
// const handleChangeVideotag = () => {
//   showVideoTagUpload.value = true;
//   valueInVideoTag.value = false;
//   console.log("ooo inside upload", videoTagUploadRef.value);
//   videoTagUploadRef.value[0]?.click();
// };

if (sceneDetails.value){
  handleInitialValues();
}
if (!isMasterScene.value){
  watch(formSchema.showLabel.ref, (newVal) => {
    if (newVal && formSchema.type.ref.value === 'grouped_units'){
      showGroupUnits.value = true;
    } else {
      showGroupUnits.value = false;
    }
  });
}
// Add the new method for handling unit removal
const handleUnitRemoval = (field, index) => {
  const newValue = [...formSchema[field].ref.value];
  newValue.splice(index, 1);
  formSchema[field].ref.value = newValue;
};

</script>

<template>
  <div
    class="relative">
    <div v-if="!componentLoaded" class="pb-2">
      <div
              class="mx-2">
              <div
              class="skeleton-loader w-16 h-4 rounded-sm"> </div>

              <div
              class="skeleton-loader mt-2 w-full h-7 flex rounded-md">
              </div>
            </div>
      <div
              class="mx-2 mt-2">
              <div
              class="skeleton-loader w-20 h-4 rounded-sm "> </div>

              <div
              class="skeleton-loader mt-2 w-full h-7 flex rounded-md">

              </div>
            </div>
      <div class="flex items-center justify-between gap-3 mx-2 mt-2" >
                  <div  class="skeleton-loader h-5 w-full flex-1  rounded-sm"></div>
                    <div class="skeleton-loader   w-9 h-5  rounded-sm"></div>
                </div>
      <div
              class="mx-2">

              <div
              class="skeleton-loader mt-2 w-full h-6 flex rounded-md">

              </div>
            </div>
    </div>
      <Form v-else @submit="handleSubmit"
        :validation-schema="isMasterScene ? MasterValidationSchema : ProjectValidationSchema">
        <div class="">
          <div class="flex flex-col gap-2">

             <!-- name -->

             <div class="hidden">
                      <label
                    class="label-primary">
                     Name
                  </label>
                  <Field
                    v-model="formSchema.labelName.ref.value"
                    as="input"
                    type="text"
                    id="name"
                    class="input-primary text-black"
                    name="name"
                    placeholder="update Layer's Name">
                  </Field>

                  <ErrorMessage
                    name="name"
                    as="p"
                    class="text-sm text-rose-500 mt-1" />
            </div>

            <!-- Type -->
            <div v-if="formSchema.type !== null"
              class="mx-2">
              <p
              class="text-xs font-medium text-gray-500 ">{{
                  formSchema.type.label }}</p>

              <label :for="formSchema.type.name" class="w-full bg-white relative flex justify-center items-center gap-2 p-2 mt-1 mb-0 rounded-md ">
                <p class="w-full text-gray-500 text-xs font-medium">{{`${formSchema.type.ref.value?formSchema.type.ref.value:formSchema.type.label}`}}</p>
                <svg class="w-4 h-4 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>

              <Field
              v-model="formSchema.type.ref.value"
                v-if="formSchema.type.as.toLowerCase() === 'select'"
                :as="formSchema.type.as"
                :id="formSchema.type.name"
                :name="formSchema.type.name"
                class="absolute inset-0 opacity-0 cursor-pointer w-full h-full flex rounded-lg text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                <option value="" disabled> Choose </option>
                <option value="" disabled
                  v-if="formSchema.type.options === null || formSchema.type.options.length === 0">
                  No Data found ! </option>
                <option v-else :value="option.value"
                  v-for="option, index in  formSchema.type.options"
                  :key="index" class="text-black"> {{
                    option.value }} </option>
              </Field>
            </label>
              <ErrorMessage :name="formSchema.type.name"
                class="text-xs text-rose-500 mt-1" as="p" />
            </div>

            <!-- Others -->
            <div v-if="formSchema.type.ref.value" class="flex flex-col gap-2">
              <div
                v-for="items in formSchema.type.options[formSchema.type.ref.value]?.toShow"
                :key="items.field">
                <div
                  v-if="formSchema[items.field]?.type === 'multiselect'"
                  class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                  <label
                    class="label-primary">{{
                      formSchema[items.field].label }}</label>
                  <Field as="input"
                    v-model="formSchema[items.field].ref.value"
                    class="sr-only"
                    :name="formSchema[items.field].name">
                  </Field>
                  <Multiselect
                  class="multiselect-Update-Layer"
                    v-model="formSchema[items.field].ref.value"
                    :options="formSchema[items.field].options"
                    :searchable="true" :multiple="true"
                    :taggable="false" placeholder="units name?"
                    :close-on-select="false" label="name"
                    track-by="_id" open-direction="bottom"
                    deselectLabel="remove" selectLabel="">

                        <!-- Custom Option Template (showing just selected items count) -->
                                <template #selection="{ values, isOpen }">
        <span class=""
              v-if="values.length"
              v-show="!isOpen">{{ values.length }} units selected </span>
      </template>

      <!-- No Search Results Message -->
      <template v-slot:noResult>Oops! No Units found.</template>
      </Multiselect>
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1" />
                    <!-- Selected Units Tag -->
                                <div name="unit_lists" class="">
                        <div class="outside-tags mt-2 flex flex-wrap gap-2 max-h-24 py-1 px-2 overflow-y-auto">
      <div
        class="bg-[#10b981] py-1 px-2 rounded-md text-white text-xs w-fit h-fit flex items-center gap-2"
        v-for="(unit, index) in formSchema[items.field].ref.value"
        :key="unit._id"
      >
        {{ unit.name }}
        <button class="remove-tag" @click="() => handleUnitRemoval(items.field, index)">
          <span class="sr-only">Remove</span>
<svg class="h-4 w-4 fill-[#f6f6f6]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
        </div>
    </div>
    </div>

                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'dropdown'"
                  class="mx-2">
             <p
              class="text-xs font-medium text-gray-500 ">{{
                  formSchema[items.field].label }}</p>

<label :for="formSchema[items.field].name" class="w-full bg-white relative flex justify-center items-center gap-2 p-2 mt-1 rounded-md">
                <p class="w-full text-gray-500 text-xs font-medium overflow-hidden text-ellipsis whitespace-nowrap">{{`${formSchema[items.field].ref.value?
                formSchema[items.field].name==='category' ?formSchema[items.field].ref.value
                :formSchema[items.field].options.find((item)=>item._id=== formSchema[items.field].ref.value)?.name
                :formSchema[items.field].label}`}}</p> <svg class="w-4 h-4 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>
                  <Field
                    v-model="formSchema[items.field].ref.value"
                    v-if="formSchema[items.field].as.toLowerCase() === 'select'"
                    :as="formSchema[items.field].as"
                    :id="formSchema[items.field].name"
                    :name="formSchema[items.field].name"
                    class="absolute inset-0 opacity-0 cursor-pointer w-full h-full flex rounded-lg text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                    {{ formSchema[items.field].ref.value }}
                    <option value='' class="text-gray-500">
                      Choose </option>
                    <option value="" disabled
                      v-if="formSchema[items.field].options === null || formSchema[items.field].options.length === 0">
                      No Data found ! </option>
                    <option v-else
                      :value="option._id ? option._id : option"
                      v-for="option, index in formSchema[items.field].options"
                      :key="index" class="text-black"> {{
                        option.name ? option.name : option }}
                    </option>
                  </Field>
                </label>
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-xs text-rose-500 mt-1"
                    as="p" />
                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'text'"
                  class="mx-2">
                  <label :for="formSchema[items.field].name"
                    class="text-xs font-medium text-gray-500 mb-0">{{
                      formSchema[items.field].label }}</label>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    :type="formSchema[items.field].ref.type"
                    v-if="formSchema[items.field].as.toLowerCase() === 'input'"
                    :name="formSchema[items.field].name"
                    :id="formSchema[items.field].name"
                    class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-xs placeholder:text-left placeholder:text-gray-500"
                    :placeholder="`enter ${formSchema[items.field].label}`" />
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-xs text-rose-500 mt-1"
                    as="p" />
                </div>
                <div
                  v-show="formSchema[items.field]?.name === 'video_tag' && valueInVideoTag"
                >
                 <label
                 class="label-primary">
                 video_tag</label>
                 <div
                 class="flex justify-between items-center">
                 <Field
                  readonly
                  type="text"
                  :v-model="valueInVideoTag"
                  name="outputVideoTag"
                  id="outputVideoTag"
                  class="select-primary !w-[220px]"
                  :placeholder=" `${valueInVideoTag}` "
                 />

                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-rose-500 cursor-pointer" viewBox="0 0 24 24"  @click="handleDeleteVideoTag">
                          <g data-name="Layer 2">
                            <g data-name="trash-2">
                              <rect width="24" height="24" opacity="0" />
                              <path
                                d="M21 6h-5V4.33A2.42 2.42 0 0 0 13.5 2h-3A2.42 2.42 0 0 0 8 4.33V6H3a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2zM10 4.33c0-.16.21-.33.5-.33h3c.29 0 .5.17.5.33V6h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8h12z" />
                              <path d="M9 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                              <path d="M15 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                            </g>
                          </g>
                  </svg>
                   </div>
                </div>
                 <!-- <div
                  v-show="formSchema[items.field]?.type === 'file' && showVideoTagUpload"
                  class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                  <label :for="formSchema[items.field].name"
                    class="label-primary">{{
                      formSchema[items.field].label }}</label>
                  <Field name="video_tag" id="video_tag" :model-value="formSchema.video_tag.ref.value">
                  <input
                      ref="videoTagUploadRef"
                      :key="fileKey"
                      type="file"
                      name="video_tag"
                      @change="(e) => handleFileUpload(e)"
                     />
                    </Field>

                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                  <p v-if="showCustomError" class="text-sm text-rose-500 mt-1">
                    Choose a Different Video
                  </p>

                </div> -->

                <div v-if ="formSchema[items.field]?.type === 'checkbox'" class="flex items-center justify-between mx-2" >
                  <label :for="formSchema[items.field].name" class="mb-0 text-sm font-medium text-gray-900">
                    Show Label</label>

                    <div
                      class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                      <div
                       class="relative mb-0 p-0">
                  <Field
                  class="sr-only peer"
                  as="input"
                  v-model="formSchema.showLabel.ref.value"
                  :name="formSchema[items.field].name"
                  :type="formSchema[items.field].type"
                  :id="formSchema[items.field].name"
                  :value="true"
                  :unchecked-value="false"
                  />
                  <label :for="formSchema[items.field].name"
                                            class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                  </label>
                </div>
              </div>

                <ErrorMessage :name="formSchema[items.field].name" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

                </div>
                <div
                 v-if="showGroupUnits && formSchema.type.ref.value === 'grouped_units'"

                  class="mx-2">
                  <label for="group_name"
                    class="text-xs font-medium text-gray-500 mb-0">Group Name </label>

                  <Field
                    v-model="groupUnitsName"
                    type="text"
                    name="group_name"
                    id="group_name"
                    class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-xs placeholder:text-left placeholder:text-gray-500"
                    :placeholder="`enter name`" />
                    <ErrorMessage name="group_name" as="p" v-slot="{ message }"
                class="w-full flex justify-start items-center gap-2 mb-2 ">
                <span class=" text-xs font-normal text-red-600 capitalize "> {{ message }}</span>
                  </ErrorMessage>
                </div>
            <div v-if="formSchema.type.ref.value === 'grouped_units'" class="mx-2">
                  <p
              class="text-xs font-medium text-gray-500 ">Bedrooms</p>

              <label for="bedrooms" class="w-full bg-white relative flex justify-center items-center gap-2 p-2 mt-1 mb-0 rounded-md ">
                <p class="w-full text-gray-500 text-xs font-medium">{{`${bedrooms?bedrooms:'Choose'}`}}</p>
                <svg class="w-4 h-4 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>

              <Field
                v-model="bedrooms"
                as="select"
                id="bedrooms"
                name="bedrooms"
                class="absolute inset-0 opacity-0 cursor-pointer w-full h-full flex rounded-lg text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                <option value="" disabled> Choose </option>
                <option value="" disabled
                  v-if="unitplanTypeList.length === null || unitplanTypeList.length === 0">
                  No Data found ! </option>
                <option v-else :value="option"
                  v-for="option,index in unitplanTypeList"
                  :key="index" class="text-black"> {{
                    option }} </option>
              </Field>
            </label>
                  <ErrorMessage
                    name="bedrooms"
                    class="text-xs text-rose-500 mt-1"
                    as="p" />
            </div>
              </div>
            </div>

            <div
              class="text-center flex justify-end items-center">
              <button type="submit"
                :disabled="loader"
                class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">
                {{!loader ?'Save Changes':""}}
                <Spinner class="fill-white text-gray-200" v-if="loader" />
              </button>
            </div>
          </div>
        </div>
      </Form>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
/* width */
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

</style>

<style>

.multiselect-Update-Layer .multiselect__tags .multiselect__tags-wrap{
   display: flex;
   justify-content: start;
   align-items: start;
   gap: 2px;
   overflow-x: auto;
}
.multiselect-Update-Layer .multiselect__tag span{
  text-overflow: ellipsis;
    min-width: auto;
    display: block;
    max-width: 54px;
    overflow: hidden;
}

.multiselect-Update-Layer .multiselect__tags .multiselect__tags-wrap .multiselect__tag{
 overflow: visible !important ;
}

.skeleton-loader{
  background-color: #5a5757;
        background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, 0.468) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #e5e7eb;
        background-size: 200% 100%;
        background-position-x: 180%;
        animation: 1s loading ease-in-out infinite;
}

@keyframes loading {
        to {
          background-position-x: -20%;
        }
    }
</style>
