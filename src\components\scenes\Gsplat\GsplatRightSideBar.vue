<script setup>

import Spinner from '....//common/Spinner.vue';
import { useRoute } from 'vue-router';
import Multiselect from 'vue-multiselect';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { onMounted, ref, watch } from 'vue';
import projectSchema from '../../../validationSchema/scene/updateProjectLayerSchema';
import masterFormSchema from '../../../validationSchema/scene/updateMasterLayerSchema';
import { gsplatFieldValidatteSchema } from '../../../validationSchema/scene/updateProjectLayerSchema';
import { gsplatFieldValidatteSchema as masterGsplatFieldValidatteSchema } from '../../../validationSchema/scene/updateMasterLayerSchema';
import { Org_Store } from '../../../store/organization';
import { ProjectStore } from '../../../store/project';
import { createLayers, updatesvgLayer } from '../../../api/projects/scene/svg/index';
import { createLayers as masterCreateLayers, updatesvgLayer as masterUpdatesvgLayer  } from '../../api/masterScene/svg/index';
import router from '../../../router';
import { isMasterScenePath } from '../....//helpers/helpers';

const emit = defineEmits(['closeModal', 'updateGsplatLayers']);
const route = useRoute();
const props = defineProps({
  type: String,
  landmarks: Object,
  svgData: Object,
  scenes: Object,
  projects: Object,
  projectId: String,
  defaultPostion: Object,
  defaultScale: Object,
  categoryList: Array,
  isPlane: Boolean,
});
const loader = ref(false);
const svgId = ref(Object.keys(props.svgData)[0]);
const sceneId = ref(route.params.scene_id);
const layerId = ref(route.query.layerId);
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const formSchema = isMasterScene.value ? masterFormSchema : projectSchema;
console.log(formSchema);
const hotSpotNameRef = ref(null);
const xposition = ref(null);
const yposition = ref(null);
const zposition = ref(null);
const width = ref(null);
const height = ref(null);
const previousData = ref({});

/* Methods */

// Find the route new
const findTypeRoute = () => {
  const routeList = [];
  Object.values(props.svgData).forEach((svg) => {

    Object.values(svg.layers).forEach((layer) => {
      if (layer.type === 'route') {
        routeList.push({ _id: layer.layer_id, name: layer.layer_id });
      }
    });
  });
  return routeList;
};

const handleChange = (val) => {

  if (val === 'project') {
    if (formSchema.project_id.options.length === 0) {
      formSchema.project_id.options = Object.values(newOrganizationStore.projects).map((project) => ({ '_id': project._id, 'name': project.name }));
    }
  }
  if (val === 'scene' || val === 'pin' || val === 'building' || val === 'floor' || val==='community') {
    if (formSchema.scene_id.options.length === 0) {
      formSchema.scene_id.options = Object.values(props.scenes).map(({ sceneData }) => ({ '_id': sceneData._id, 'name': sceneData.name }));
    }
  }

  if (!isMasterScene.value){
    if (val === 'landmark') {
      if (formSchema.landmark_id.options.length === 0) {
        formSchema.landmark_id.options = Object.values(props.landmarks).map((landmark) => ({ '_id': landmark._id, 'name': landmark.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
      // Finding layers with type route
      formSchema.route_id.options = findTypeRoute();

    }
    if (val === 'building') {

      formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
    }
    if (val === 'community') {
      console.log(projectStore.communities);

      formSchema.community_id.options = Object.values(projectStore.communities).map((community) => ({ '_id': community._id, 'name': community.name }));
    }

    if (val === 'floor') {
      if (formSchema.building_id.options.length === 0) {
        formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
      watch(formSchema.building_id.ref, (val) => {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[val].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      });
      if (formSchema.building_id.ref.value) {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[formSchema.building_id.ref.value].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      }

    }
    if (val === 'units') {
      if (formSchema.units.options.length === 0) {
        formSchema.units.options = Object.values(projectStore.units).map((unit) => ({ '_id': unit._id, 'name': (unit.name + ' ' + (unit.building_id?projectStore.buildings[unit.building_id].name:'') + ' ' + (unit.community_id?projectStore.communities[unit.community_id].name:'')   ) }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenity') {
      if (formSchema.amenity_id.options.length === 0) {
        formSchema.amenity_id.options = Object.values(projectStore.amenities).map((amenity) => ({ '_id': amenity._id, 'name': amenity.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenitycategory') {
      if (formSchema.amenity_category.options.length === 0) {
        formSchema.amenity_category.options = Object.values(props.categoryList).map((elem) => ({ '_id': elem.name, 'name': elem.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
  }

};

const handleInitialValues = () => {
  layerId.value = route.query.layerId;
  if (props.svgData[svgId.value]?.layers[layerId.value]){
    // Testing
    const layer = props.svgData[svgId.value].layers[layerId.value];
    const {position, scale, name, ...query} =  layer;
    if (!props.isPlane){
      previousData.value.name = name;
    }
    previousData.value.xposition = position.x;
    previousData.value.yposition = position.y;
    previousData.value.zposition = position.z;

    previousData.value.query = query;
    hotSpotNameRef.value = layer.name;
    xposition.value = layer.position.x;
    yposition.value = layer.position.y;
    zposition.value = layer.position.z;
    if (props.isPlane){
      previousData.value.width = scale.width;
      previousData.value.height = scale.height;
      width.value = layer.scale.width;
      height.value = layer.scale.height;
    }
    formSchema.type.ref.value = layer.type ? layer.type : null;
    formSchema.project_id.ref.value = layer.project_id ? layer.project_id : null;
    formSchema.scene_id.ref.value = layer.scene_id ? layer.scene_id : null;
    formSchema.image_id.ref.value = layer.image_id ? layer.image_id : null;

    if (!isMasterScene.value){
      formSchema.landmark_id.ref.value = layer.landmark?.landmark_id ? layer.landmark.landmark_id : null;
      formSchema.route_id.ref.value = layer.landmark?.route_id ? layer.landmark.route_id : null;
      formSchema.building_id.ref.value = layer.building_id ? layer.building_id : null;
      formSchema.community_id.ref.value = layer.community_id ? layer.community_id : null;
      formSchema.floor_id.ref.value = layer.floor_id ? layer.floor_id : null;
      formSchema.amenity_id.ref.value = layer.amenity_id ? layer.amenity_id : null;
      formSchema.units.ref.value = layer.units ? layer.units.map((elem) => {
        return { '_id': elem, 'name': projectStore.units[elem].name };
      }) : null;
      formSchema.title.ref.value = layer.title ? layer.title : null;
      formSchema.category.ref.value = layer.category ? layer.category : null;
    }

  }

  /*   If(props.defaultPostion){
        // position initial values
  } */

  handleChange(formSchema.type.ref.value);

};

const comparePreviousValues = (compareObj, sourceObj) => {
  const newObject = {}; // Return Object
  const comparisonObject = {...compareObj};
  const queryObject = compareObj.query;
  delete queryObject.layer_id;
  delete queryObject.svg_url;
  delete queryObject.scene_id;
  const objectQuery = {};
  Object.keys(comparisonObject).forEach((key) => {
    if (key !== 'xposition' && key !== 'yposition' && key !== 'zposition' && key !== 'name' && key !== 'position' ){
      // Query object
      if (typeof comparisonObject[key] === 'object'){
        // Extract the object and get the keys and values
        Object.keys(comparisonObject[key]).forEach((secondKey) => {

          if (typeof comparisonObject[key][secondKey] === 'object'){
            Object.keys(comparisonObject[key][secondKey]).forEach((thirdkey) => {
              objectQuery[thirdkey] = comparisonObject[key][secondKey][thirdkey]; // Append
            });
          } else {
            objectQuery[secondKey] = comparisonObject[key][secondKey]; // Append
          }

        });
      }
    }
  });
  comparisonObject.query = {...objectQuery};
  const {name, xposition, yposition, zposition, width, height, svgFile, ...query} = sourceObj;
  const newSourceObj = {
    name: name,
    xposition: xposition,
    yposition: yposition,
    zposition: zposition,
    ...( props.isPlane && ({width: width,
      height: height})),
    query: {...query},
  };

  Object.keys(newSourceObj).forEach((key) => {
    if (key !== 'query'){

      if (comparisonObject[key] !== newSourceObj[key]){
        if (key ===  'xposition' || key === 'zposition' ||  key === 'yposition'  ){
          newObject.xposition = newSourceObj.xposition;
          newObject.zposition = newSourceObj.zposition;
          newObject.yposition = newSourceObj.yposition;
        } else if ( key === 'width' || key === 'height' ){
          newObject.width = newSourceObj.width;
          newObject.height = newSourceObj.height;
        } else {
          newObject[key] = newSourceObj[key];
        }
      }

    } else {
      // Type & its related
      if (comparisonObject[key].type === newSourceObj[key].type){
        let anyChange = false;
        Object.keys(newSourceObj[key]).forEach((secondKey) => {
          if (comparisonObject[key][secondKey] !== newSourceObj[key][secondKey]){
            newObject[secondKey] = newSourceObj[key][secondKey];
            anyChange = true;
          }
        });
        if (anyChange){
          newObject.type = newSourceObj[key].type;
        }
      } else {
        // Type is not same or undefined
        Object.keys(newSourceObj[key]).forEach((querykey) => {
          newObject[querykey] = newSourceObj[key][querykey];
        });
        newObject.type = newSourceObj[key].type;
      }

    }
  });

  if (svgFile){
    newObject.svgFile =  svgFile;
  }

  console.log(newObject);
  return newObject;
};

const frameParms = (values) => {
  const formData = new FormData();
  if (!isMasterScene.value){
    formData.append('project_id', props.projectId);
  }
  formData.append('svg_id', svgId.value);
  if (props.type === 'edit'){
    formData.append('layer_id', layerId.value);
  }
  const {name, xposition, yposition, zposition, width, height, svgFile, ...query} = values;
  console.log(values);

  name && formData.append('name', name);
  if (props.type === 'edit'){
    Object.keys(query).length > 0 && formData.append('query', JSON.stringify(query));
  } else {
    const newquery = Object.values(query)[0]; // create
    console.log(Object.values(query));
    Object.keys(query).length > 0 && formData.append('query', JSON.stringify(newquery));
  }
  const position = {
    ...( (xposition !== null && xposition !== undefined )&& {x: xposition}),
    ...( (yposition !== null && yposition !== undefined) && {y: yposition}),
    ...( (zposition !== null && zposition !== undefined) && {z: zposition}),
  };
  Object.keys(position).length > 0 && formData.append('position', JSON.stringify(position));
  if (props.isPlane){
    const scale = {
      ...( (width !== null && width !== undefined )&& {'width': width}),
      ...( (height !== null && height !== undefined) && {'height': height}),
    };
    console.log(scale);
    Object.keys(scale).length > 0 && formData.append('scale', JSON.stringify(scale));
  }

  svgFile && formData.append('svgFile',  svgFile);

  return formData;
};

watch(() => route.query.svgId, () => {
  svgId.value = route.query.svgId;
});

watch(() => route.query.layerId, () => {
  handleInitialValues();
});

watch(formSchema.type.ref, (val) => {
  console.log(val);
  handleChange(val);
});

// Watch position
watch(() => props.defaultPostion, () => {
  console.log(props.defaultPostion);
  if (Object.keys(props.defaultPostion).length > 0){
    xposition.value = props.defaultPostion.x;
    yposition.value = props.defaultPostion.y;
    zposition.value = props.defaultPostion.z;
  }

}, {deep: true});

watch(() => props.defaultScale, () => {

  if (Object.keys(props.defaultScale).length > 0){
    width.value = props.defaultScale.width;
    height.value = props.defaultScale.height;
  }

}, {deep: true});

const handelChangeFile = (e) => {
  const file = e.target.files[0];
  const svgFormats = ['image/svg+xml'];
  if (svgFormats.includes(file.type)){
    if (layerId.value){
      emit('updateGsplatLayers', {
        'id': layerId.value,
        'svg_url': URL.createObjectURL(file),
      });
    }
  }
};

const handelSubmit = (values) => {
  if (!isMasterScene.value){
    if (values.units && values.units.length !== 0) {
      values.units = values.units.map((elem) => elem._id);
    }
  }
  /*  Const obj = removeUndefinedAndNullInObject(values);  */
  loader.value = true;
  console.log(values);
  if (props.type === 'add'){
    // Add
    const {name, xposition, yposition, zposition, width, height, svgFile, ...query} = values;
    const newObj = {
      'name': name,
      'xposition': xposition,
      'yposition': yposition,
      'zposition': zposition,
      'width': width,
      'height': height,
      'svgFile': svgFile,
      'query': query,
    };
    console.log(newObj);

    if (isMasterScene.value){
      masterCreateLayers(frameParms(newObj)).then(() => {
        loader.value = false;
        window.location = `/masterscenes/${sceneId.value}/gsplat`;
      }).catch(() => {
      }).finally(() => {
        loader.value = false;
      });
    } else {
      createLayers(frameParms(newObj)).then(() => {
        loader.value = false;
        window.location = `/projects/${props.projectId}/scenes/${sceneId.value}/gsplat`;
      }).catch((err) => {
        console.log('output->err in updateProjectSceneLayer', err);
      }).finally(() => {
        loader.value = false;
      });
    }
  } else {
    // Edit
    console.log(previousData.value);
    console.log(comparePreviousValues(previousData.value, values));
    console.log(frameParms(comparePreviousValues(previousData.value, values)));
    for (const pair of frameParms(comparePreviousValues(previousData.value, values)).entries()) {
      console.log(pair[0], pair[1]);
    }
    if (Object.keys(comparePreviousValues(previousData.value, values)).length > 0) {

      if (isMasterScene.value){
        masterUpdatesvgLayer(frameParms(comparePreviousValues(previousData.value, values))).then(() => {
          console.log(frameParms(comparePreviousValues(previousData.value, values)));
          loader.value = false;
          //   Window.location = `/masterscenes/${sceneId.value}/gsplat`;
        }).catch((err) => {
          console.log('output->err in updateProjectSceneLayer', err);
        }).finally(() => {
          loader.value = false;
        });
      } else {

        updatesvgLayer(frameParms(comparePreviousValues(previousData.value, values))).then(() => {
          console.log(frameParms(comparePreviousValues(previousData.value, values)));
          loader.value = false;
        //  Window.location = `/projects/${props.projectId}/scenes/${sceneId.value}/gsplat`;
        }).catch((err) => {
          console.log('output->err in updateProjectSceneLayer', err);
        }).finally(() => {
          loader.value = false;
        });
      }

    } else {
      if (isMasterScene.value){
        router.push(`/masterscenes/${sceneId.value}/gsplat`);
      } else {
        router.push(`/projects/${props.projectId}/scenes/${sceneId.value}/gsplat`);
      }
    }
  }
};

/* Hooks */
onMounted(() => {
  if (props.type === 'edit'){
    handleInitialValues();
  }
});

</script>

<template>
  <div
    class="relative transform overflow-hidden rounded-t-2xl rounded-b-none sm:rounded-t-lg sm:rounded-b-lg  text-left  transition-all sm:my-1 w-full h-full sm:max-w-md">

    <div class="p-3 sm:p-6">
      <h2 class="text-black text-lg mb-4 text-capitalize"> {{ type }} {{isPlane ?  'Plane' : 'Hotspot'}} </h2>

      <Form @submit="handelSubmit"
        :validation-schema="isMasterScene ? masterGsplatFieldValidatteSchema(type,isPlane) : gsplatFieldValidatteSchema(type, isPlane)">
        <div class="">
          <div class="flex flex-col gap-2 text-black">

            <!-- Name -->

            <div class="mb-2" v-if="!isPlane">
              <label for=""
                class="label-primary">Name</label>

              <Field v-model="hotSpotNameRef" as="input" type="text"
                    id="name"
                    class="input-primary text-black"
                    name="name">
              </Field>

              <ErrorMessage name="name"
                class="text-sm text-rose-500 mt-1" as="p" />
            </div>

            <!-- Type -->
            <div v-if="formSchema.type !== null"
              class="select-primary">
              <label :for="formSchema"
                class="label-primary">{{
                  formSchema.type.label }}</label>
              <Field v-model="formSchema.type.ref.value"
                v-if="formSchema.type.as.toLowerCase() === 'select'"
                :as="formSchema.type.as"
                :id="formSchema.type.name"
                :name="formSchema.type.name"
                class="select-primary">
                <option value="" disabled> Choose </option>
                <option value="" disabled
                  v-if="formSchema.type.options === null || formSchema.type.options.length === 0">
                  No Data found ! </option>
                <option v-else :value="option.value"
                  v-for="option, index in  formSchema.type.options"
                  :key="index" class="text-black"> {{
                    option.value }} </option>
              </Field>
              <ErrorMessage :name="formSchema.type.name"
                class="text-sm text-rose-500 mt-1" as="p" />
            </div>

            <!-- Others -->
            <div v-if="formSchema.type.ref.value">
              <div
                v-for="items in formSchema.type.options[formSchema.type.ref.value]?.toShow"
                :key="items.field" class="w-full">

                <div
                  v-if="formSchema[items.field]?.type === 'multiselect'"
                  class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                  <label
                    class="label-primary">{{
                      formSchema[items.field].label }}</label>
                  <Field as="input"
                    v-model="formSchema[items.field].ref.value"
                    class="sr-only"
                    :name="formSchema[items.field].name">
                  </Field>
                  <Multiselect
                    v-model="formSchema[items.field].ref.value"
                    :options="formSchema[items.field].options"
                    :searchable="true" :multiple="true"
                    :taggable="true" placeholder="units name?"
                    :close-on-select="false" label="name"
                    track-by="_id" open-direction="bottom"
                    deselectLabel="remove" selectLabel="">
                  </Multiselect>
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1" />

                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'dropdown'"
                  class="select-primary">
                  <label :for="formSchema[items.field].name"
                    class="label-primary">{{
                      formSchema[items.field].label }}</label>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    v-if="formSchema[items.field].as.toLowerCase() === 'select'"
                    :as="formSchema[items.field].as"
                    :id="formSchema[items.field].name"
                    :name="formSchema[items.field].name"
                    class="select-primary">
                    {{ formSchema[items.field].ref.value }}
                    <option value='' class="text-gray-500">
                      Choose </option>
                    <option value="" disabled
                      v-if="formSchema[items.field].options === null || formSchema[items.field].options.length === 0">
                      No Data found ! </option>
                    <option v-else
                      :value="option._id ? option._id : option"
                      v-for="option, index in formSchema[items.field].options"
                      :key="index" class="text-black"> {{
                        option.name ? option.name : option }}
                    </option>
                  </Field>
                  {{ formSchema[items.field].ref.value }}
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'text'"
                  class="select-primary">
                  <label :for="formSchema[items.field].name"
                    class="label-primary">{{
                      formSchema[items.field].label }}</label>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    :type="formSchema[items.field].ref.type"
                    v-if="formSchema[items.field].as.toLowerCase() === 'input'"
                    :name="formSchema[items.field].name"
                    :id="formSchema[items.field].name"
                    class="block p-2 w-full rounded-md border-1 bg-[transparent] py-1.5 text-white shadow-sm ring-1 ring-inset ring-[#737373]  focus:ring-2 focus:ring-inset focus:ring-indigo-500 sm:text-sm sm:leading-6 placeholder:text-start placeholder:text-sm placeholder:text-gray-500"
                    :placeholder="`enter ${formSchema[items.field].label}`" />

                  {{ formSchema[items.field].ref.value }}
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>

              </div>
            </div>

            <!-- Positions -->
            <div class="relative">

                      <label
                    class="label-primary">
                      X position
                  </label>
                  <Field
                    v-model="xposition"
                   disabled
                    as="input"
                    type="number"
                    id="xposition"
                    class="input-primary text-black"
                    name="xposition">
                  </Field>

                  <ErrorMessage
                    name="xposition"
                    as="p"
                    class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative">
                          <label
                        class="label-primary">
                          y position
                      </label>
                      <Field
                      v-model="yposition"
                      disabled
                          as="input"
                          id="yposition"
                        class="input-primary text-black"
                        name="yposition">
                      </Field>

                      <ErrorMessage
                        name="yposition"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative">
                          <label
                        class="label-primary">
                          z position
                      </label>
                      <Field v-model="zposition" as="input"
                      disabled
                        id="zposition"
                        class="input-primary text-black"
                        name="zposition">
                      </Field>

                      <ErrorMessage
                        name="zposition"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative" v-if="isPlane">

            <label
            class="label-primary">
             Width
            </label>
            <Field
            v-model="width"
            disabled
            as="input"
            type="number"
            id="width"
            class="input-primary text-black"
            name="width">
            </Field>

            <ErrorMessage
            name="width"
            as="p"
            class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative" v-if="isPlane">
                <label
              class="label-primary">
                Height
            </label>
            <Field
            v-model="height"
            disabled
                as="input"
                id="height"
              class="input-primary text-black"
              name="height">
            </Field>

            <ErrorMessage
              name="height"
              as="p"
              class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative" v-if="!isPlane">
                  <label
                        class="label-primary">
                        Icon
                      </label>
                      <Field :onchange="(e) => handelChangeFile(e)" type="file"
                                        name="svgFile"
                                        id="svgFile"
                                        autocomplete="svgFile"
                                        class="block p-2 w-full rounded-md border-1 bg-[transparent] py-1.5 text-white shadow-sm ring-1 ring-inset ring-[#737373] cursor-pointer  sm:text-sm sm:leading-6 placeholder:text-start placeholder:text-sm placeholder:text-gray-500"
                                        placeholder="Upload gspalt zip" />

                      <ErrorMessage
                        name="svgFile"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>

            <div
              class="text-center mt-3 flex justify-end items-center">
              <!-- <button @click="handleCloseModal()"
                            class="w-1/2 sm:w-fit h-11 sm:h-10 rounded-full sm:rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent m-0 px-3 text-xs text-white font-semibold leading-6">Discard</button> -->
              <button id="submit" type="submit"
               :disabled="loader"
                class="inline-flex justify-center items-center gap-2 ml-3 w-1/2 sm:w-fit h-11 sm:h-9 rounded-full  sm:rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-3 text-xs text-white font-semibold leading-6">Save
                Changes
                <Spinner v-if="loader" />
              </button>
            </div>
          </div>
        </div>
      </Form>
    </div>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
/* width */
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>
