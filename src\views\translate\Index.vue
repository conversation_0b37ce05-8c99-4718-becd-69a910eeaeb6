<script setup>
import SideNavBar from '../../components/common/SideNavBar.vue';
import Navbar from '../../components/common/Navbar.vue';
import Translate from '../../components/translate/Index.vue';
import { UserStore } from '../../store/index';
const userStore = UserStore();
</script>

<template>
     <div class="w-full h-full overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col">
        <Navbar/>
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
            <Translate/>
        </div>
    </div>
</div>
</template>
