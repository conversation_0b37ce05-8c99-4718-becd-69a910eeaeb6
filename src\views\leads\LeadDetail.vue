<script setup>
import Navbar from '../../components/common/Navbar.vue';
import SideNavBar from '../../components/common/SideNavBar.vue';
import { UserStore } from '../../store/index';
const userStore = UserStore();
</script>

<template>
    <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar/>
    <div class="h-screen w-full flex justify-start items-start overflow-hidden bg-[#f3f4f6]">
        <SideNavBar />
        <div v-if="userStore.user_data"  class="px-8 pt-0 bg-transparent h-full overflow-y-auto w-full">

        </div>
    </div>
</div>
</template>
