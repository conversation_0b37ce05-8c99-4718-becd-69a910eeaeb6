<script setup>
import SideNavBar from '../../components/common/SideNavBar.vue';
import LeadsView from '../../components/leads/Index.vue';
import Navbar from '../../components/common/Navbar.vue';
import { UserStore } from '../../store/index';
const userStore = UserStore();

</script>

<template>
     <div class="w-full h-full overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar/>
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
                <LeadsView class="pb-[80px] sm:pb-0"></LeadsView>
            <router-view name="modal"></router-view>
        </div>
    </div>
</div>
</template>
