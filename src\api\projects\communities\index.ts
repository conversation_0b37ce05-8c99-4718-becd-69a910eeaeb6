import type { community, createCommunity, moveCommunityToTrash, updateCommunity } from '@/types/community';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function getListOfCommunities (projectId : string) : Promise<Record<string, community>> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({url: `${api_url}/community/getCommunities/${projectId}`}).then((res) => {
      resolve(res as Record<string, community>);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function createCommunity (values : createCommunity ) :Promise <community | void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/community/createCommunity`, body: values}).then((res) => {
      resolve(res as community);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function updateCommunity (payload : updateCommunity) : Promise<community | void> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/community/updateCommunity`, body: payload}).then((res) => {
      resolve(res as community);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function moveCommunityToTrash (values: moveCommunityToTrash, project_id:string): Promise<community | void> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/community/moveToTrash/${project_id}`, body: values}).then((res) => {
      resolve(res as community);
    }).catch((error) => {
      reject(error);
    });
  });

}
