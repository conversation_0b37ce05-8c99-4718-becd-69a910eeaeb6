<script setup>
import { computed, ref, watch } from 'vue';
import router from '@/router';
import { useRoute } from 'vue-router';
import { getListofUnitplan } from '@/api/projects/unitplans';

const route = useRoute();

const projectId = ref(route.params.project_id);
const selectedBedroom = ref(''), expandedVillaId = ref(null), selectedUnitplan = ref(route.params.unitplan_id);
const unitplansArray = ref({});

const bedroomTypes = computed(() => {
  const types = Object.values(unitplansArray.value)
    .filter((unit) => unit.unit_type === 'villa' || unit.unit_type === 'flat')
    .map((unit) => unit.bedrooms);
  return [...new Set(types)];
});

watch(() => route.params.params, (newId, oldId) => {
  console.log('Route param changed:', oldId, '=>', newId);
});

const props = defineProps({
  selectedMenu: String,
});

watch(() => props.selectedMenu, (newVal) => {
  if (newVal === 'unitplan') {
    getListofUnitplan(projectId.value).then((res) => {
      unitplansArray.value = res;
    });
  }
}, { immediate: true });

const getUnitplansByBedroom = (bedroom) => {
  return Object.values(unitplansArray.value).filter((unit) => unit.bedrooms === bedroom  && (unit.unit_type === 'villa' || unit.unit_type === 'flat'));
};

const toggleVilla = (id) => {
  expandedVillaId.value = expandedVillaId.value === id ? null : id;
  selectedUnitplan.value = id;
};

function handleUnitClick (unitId) {
  selectedUnitplan.value = unitId;
  router.push({
    name: 'design_unitplan',
    params: {
      project_id: route.params.project_id,
      unitplan_id: unitId,
    },
    query: route.query,
  });
}

function handlVRTourClick (unitId) {
  router.push({
    name: 'design_tours_Tourview',
    params: {
      project_id: route.params.project_id,
      tour_id: unitId,
    },
    query: route.query,
  });
}

watch(() => route.params.unitplan_id, (newVal) => {
  selectedUnitplan.value = newVal;
});

</script>

<template>
    <!-- Pages -->
    <div class="h-full bg-gray-100  flex justify-between">
    <div v-if="selectedMenu==='unitplan'" class="w-[200px] h-full  bg-white rounded-t-lg p-2 flex flex-col">
      <div class="flex justify-between items-center w-full">
        <div class="w-full">
          <h2 class=" text-gray-500 text-lg font-semibold ml-1">
            Interior
          </h2>
          <div class="flex flex-col ml-1 pt-3 gap-2 w-full">
            <div v-for="types in bedroomTypes" :key="types">
              <div class="flex" @click="selectedBedroom = selectedBedroom === types ? null : types">
                <span class="flex items-center gap-2 cursor-pointer w-full">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M5.45117 2.04118L5.45117 3.80005C5.45117 4.21426 5.78696 4.55005 6.20117 4.55005C6.61539 4.55005 6.95117 4.21426 6.95117 3.80005L6.95117 2.00203C7.27752 2 7.62652 2 8.00021 2C10.8287 2 12.243 2 13.1217 2.8787C13.6835 3.44049 13.8861 4.22117 13.9592 5.4504L11.6006 5.4504C11.1864 5.4504 10.8506 5.78618 10.8506 6.2004C10.8506 6.61461 11.1864 6.9504 11.6006 6.9504L13.9984 6.9504C14.0004 7.27694 14.0004 7.62618 14.0004 8.00014C14.0004 10.8286 14.0004 12.2429 13.1217 13.1216C12.6318 13.6115 11.9754 13.8283 11.0003 13.9241L8.75008 13.9813C8.76053 12.7476 9.7639 11.7506 11.0001 11.7506C11.4143 11.7506 11.7501 11.4148 11.7501 11.0006C11.7501 10.5864 11.4143 10.2506 11.0001 10.2506C8.92937 10.2506 7.2506 11.929 7.25 13.9996C7.14833 13.9993 7.04874 13.9989 6.95117 13.9983L6.95117 6.9504L9.20026 6.9504C9.61447 6.9504 9.95026 6.61461 9.95026 6.2004C9.95026 5.78618 9.61447 5.4504 9.20026 5.4504L6.20117 5.4504L2.04122 5.4504C2.1143 4.22117 2.31692 3.44049 2.87871 2.8787C3.44061 2.31681 4.22151 2.11422 5.45117 2.04118ZM2.00203 6.9504C2 7.27694 2 7.62618 2 8.00014C2 10.8286 2 12.2429 2.87871 13.1216C3.44061 13.6835 4.22151 13.8861 5.45117 13.9591L5.45117 6.9504L2.00203 6.9504Z" fill="#6B7280"/>
                  </svg>
                  <p class="font-medium capitalize" :class="selectedBedroom === types ? 'text-black' : 'text-[#6B7280]'">
                    {{ types }}
                  </p>
                </span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" :class="[
                  'transition-transform duration-200',
                  selectedBedroom === types ? 'rotate-0' : 'rotate-180'
                ]">
                  <path d="M8.01229 5.44444C7.74709 5.4445 7.49278 5.54188 7.30529 5.71519L3.30529 9.41132C3.20978 9.49656 3.1336 9.59852 3.08119 9.71126C3.02878 9.82399 3.00119 9.94524 3.00004 10.0679C2.99888 10.1906 3.02419 10.3123 3.07447 10.4259C3.12475 10.5394 3.199 10.6426 3.29289 10.7294C3.38679 10.8161 3.49844 10.8847 3.62133 10.9312C3.74423 10.9777 3.87591 11.001 4.00869 11C4.14147 10.9989 4.27269 10.9734 4.39469 10.925C4.5167 10.8766 4.62704 10.8062 4.71929 10.7179L8.01229 7.67506L11.3053 10.7179C11.4939 10.8862 11.7465 10.9794 12.0087 10.9773C12.2709 10.9751 12.5217 10.878 12.7071 10.7066C12.8925 10.5353 12.9977 10.3036 13 10.0613C13.0022 9.81901 12.9014 9.58559 12.7193 9.41132L8.71929 5.71519C8.5318 5.54188 8.27748 5.4445 8.01229 5.44444Z" fill="#111928"/>
                </svg>
              </div>
              <div v-if="selectedBedroom === types" class="mt-2">
                <div v-for="unit in getUnitplansByBedroom(types)" :key="unit._id"  class="flex flex-col w-full cursor-pointer rounded-lg transition duration-150 capitalize">
                  <div class="flex items-center gap-2 text-sm py-2 px-2" :class="selectedUnitplan === unit._id && 'bg-blue-50 rounded-lg'" @click="toggleVilla(unit._id)">
                    <template v-if="unit.unit_type === 'villa' && unit.floor_unitplans.length" >
                      <div class="relative flex flex-col items-center" >
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6" fill="none">
                          <path d="M0.266744 1.35016L5.35589 5.76842C5.52673 5.9167 5.75842 6 6 6C6.24158 6 6.47327 5.9167 6.64411 5.76842L11.7333 1.35016C11.8606 1.23954 11.9474 1.09862 11.9825 0.945213C12.0176 0.791806 11.9996 0.632798 11.9307 0.488291C11.8617 0.343785 11.745 0.220267 11.5952 0.133353C11.4454 0.0464399 11.2693 3.33974e-05 11.0891 0L0.910859 0C0.730698 3.33974e-05 0.554593 0.0464399 0.404807 0.133353C0.255021 0.220267 0.138279 0.343785 0.0693395 0.488291C0.000400064 0.632798 -0.0176418 0.791806 0.0174953 0.945213C0.0526324 1.09862 0.139371 1.23954 0.266744 1.35016Z" fill="#6B7280"/>
                        </svg>
                        <div
                          v-if="expandedVillaId === unit._id"
                          class="absolute top-0 left-1/2 h-[calc(100%+47px)] w-[0.5px] bg-gray-500"
                        ></div>
                      </div>
                    </template>
                    <span @click="unit.unit_type !== 'villa' && handleUnitClick(unit._id)">{{ unit.name }}</span>
                    <svg v-if="unit.tour_id" @click="handlVRTourClick(unit.tour_id)" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path d="M6.75 3H14.25C15.6519 3 16.3528 3 16.875 3.30144C17.2171 3.49892 17.5011 3.78296 17.6986 4.125C18 4.64711 18 5.34808 18 6.75C18 8.15192 18 8.85285 17.6986 9.375C17.5011 9.71708 17.2171 10.0011 16.875 10.1986C16.3528 10.5 15.6519 10.5 14.25 10.5H13.9547C13.4396 10.5 13.1819 10.5 12.9409 10.4465C12.6183 10.3748 12.3154 10.2329 12.0539 10.031C11.8584 9.88005 11.6935 9.6822 11.3637 9.28643C11.1009 8.97105 10.9695 8.8134 10.8187 8.7426C10.6168 8.64788 10.3832 8.64788 10.1813 8.7426C10.0305 8.8134 9.8991 8.97105 9.6363 9.28643C9.30653 9.6822 9.1416 9.88005 8.94617 10.031C8.68457 10.2329 8.38169 10.3748 8.05907 10.4465C7.81805 10.5 7.56046 10.5 7.04527 10.5H6.75C5.34808 10.5 4.64711 10.5 4.125 10.1986C3.78295 10.0011 3.49892 9.71708 3.30144 9.375C3 8.85285 3 8.15192 3 6.75C3 5.34808 3 4.64711 3.30144 4.125C3.49892 3.78296 3.78295 3.49892 4.125 3.30144C4.64711 3 5.34808 3 6.75 3Z" fill="#6B7280" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M10.5755 15L9.06023 13.5M10.5755 15L9.06023 16.5M10.5755 15C6.78804 15 3.60667 13.5 3 12M12.8483 14.8283C15.4518 14.4235 17.4497 13.3419 18 12" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>

                  <div v-if="expandedVillaId === unit._id && unit.unit_type === 'villa'" class="ml-8 relative">
                    <div v-for="childId in unit.floor_unitplans" :key="childId" class="text-sm py-2 transition" :class="selectedUnitplan === childId && 'bg-blue-50 rounded-lg pl-2'">
                      <div class="absolute left-[-1.1rem] top-[85%] w-[11px] h-px bg-gray-500"></div>
                      <span @click="{selectedUnitplan=childId; handleUnitClick(childId)}">{{ unitplansArray[childId]?.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

</template>

<style scoped>
</style>
