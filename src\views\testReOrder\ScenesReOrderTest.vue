<script setup>
import { ref } from 'vue';
import sceneTmpData from "./sceneReOrderData.json";
import SceneReOrder from '../../components/scenes/SceneReOrder.vue';

const dumpData = ref(null);

/* ---------------- Methods --------------------- */

function createCategoryBasedNestedSceneData (data) {
  // Create a map for faster lookups during processing
  const sceneMap = {};
  const categoryMap = {};
  let randomCategoryCounter = 1;

  // First pass: extract only the essential information
  for (const sceneId in data) {
    const sceneData = data[sceneId].sceneData;

    // Assign a random category if none exists
    const category = sceneData.category || `RANDOM_${randomCategoryCounter++}`;

    // Create a simplified object with only essential properties
    sceneMap[sceneId] = {
      id: sceneId,
      name: sceneData.name,
      linked_scenes: [],
      type: sceneData.type,
      category: category,
      isRoot: sceneData.root === true,
      originalParent: sceneData.parent, // Keep track of original parent for hierarchy
    };

    // Initialize category in categoryMap if it doesn't exist
    if (!categoryMap[category]) {
      categoryMap[category] = {
        id: `category-${category}`,
        name: `Category ${category}`,
        category: category,
        linked_scenes: [],
      };
    }
  }

  // Identify all root nodes (nodes without parents)
  for (const sceneId in sceneMap) {
    const scene = sceneMap[sceneId];
    if (!scene.originalParent || scene.isRoot) {
      // This is a root scene - add to its category's linked_scenes
      const categoryObj = categoryMap[scene.category];
      // Create a deep copy to avoid circular references later
      const sceneCopy = JSON.parse(JSON.stringify(scene));
      delete sceneCopy.originalParent; // Clean up temporary property
      categoryObj.linked_scenes.push(sceneCopy);
    }
  }

  // Recursive function to build the nested structure
  function buildNestedStructure (node) {
    // Find all children of this node
    const children = [];
    for (const sceneId in sceneMap) {
      if (sceneMap[sceneId].originalParent === node.id) {
        // Check if the child has a different category
        if (sceneMap[sceneId].category !== node.category) {
          // This child belongs to a different category
          // Create a reference copy with minimal info
          const referenceNode = {
            id: sceneId,
            name: sceneMap[sceneId].name,
            category: sceneMap[sceneId].category,
            isReference: true, // Mark as reference
            referencePath: `categories.${sceneMap[sceneId].category}`,
          };

          children.push(referenceNode);

          // Ensure the actual node is added to its category if it's not already a child of another node
          const alreadyAddedAsChild = Object.values(sceneMap).some((s) =>
            s.originalParent === sceneId && s.category === sceneMap[sceneId].category,
          );

          if (!alreadyAddedAsChild) {
            // Deep copy to avoid circular references
            const childCopy = JSON.parse(JSON.stringify(sceneMap[sceneId]));
            delete childCopy.originalParent; // Clean up temporary property

            // Check if this node is already in the category's linked_scenes
            const existsInCategory = categoryMap[childCopy.category].linked_scenes.some(
              (s) => s.id === childCopy.id,
            );

            if (!existsInCategory) {
              categoryMap[childCopy.category].linked_scenes.push(childCopy);
            }
          }
        } else {
          // Same category, include directly
          // Create a deep copy of the child to avoid circular references
          const childCopy = JSON.parse(JSON.stringify(sceneMap[sceneId]));
          delete childCopy.originalParent; // Clean up temporary property

          // Recursively build the structure for this child
          buildNestedStructure(childCopy);
          children.push(childCopy);
        }
      }
    }

    // Set the linked_scenes to the children array
    node.linked_scenes = children;
  }

  // Build the nested structure for each category's root scenes
  for (const category in categoryMap) {
    categoryMap[category].linked_scenes.forEach(buildNestedStructure);
  }

  // Convert categoryMap to an array of categories
  const result = Object.entries(categoryMap).map(([, value]) => ({
    ...value,
  }));

  return result;
}

/* Setup Data */

// setupData (Modify based on your needs)
const setupData = (data) => {
  console.log("setupData", data);
  if (data){
    // Structuring your data's
    const convertedData = createCategoryBasedNestedSceneData(data);

    console.log(convertedData);

    // Update the your component reference
    dumpData.value = convertedData;
  }
};

// Initial
setupData(sceneTmpData);

/* Emits Handler */

// Child Sort
const handleChildSortEmit = (val) => {
  console.log("-----------------------------");
  console.log('Parent~ChildSort', val);
  console.log("-----------------------------");
};

// Child Reparenting Sort
const handleChildReparentingSortEmit = (val) => {
  console.log("-----------------------------");
  console.log('Parent~ChildReparentingSort', val);
  console.log("-----------------------------");
};

</script>

<template>
    <div class="w-full overflow-y-auto p-2">
      <div class="!w-[60%] bg-teal-300 p-2 rounded" v-if="dumpData">
                <!-- v-model ----- :modelValue="parentList" @update:modelValue="parentList = $event"  -->
                  <SceneReOrder v-model="dumpData" groupName="scene" :allowChildReparenting="true" :allowChildSort="true" uniqueKey="id" nestedChildKey="linked_scenes" ghostClass="sampe_ghost" animationMilliSec="450" @handleChildSort="(val) => handleChildSortEmit(val)" @handleChildReparentingSort="(val) => handleChildReparentingSortEmit(val)">
                      <template #default="{item}">
                          <div class="w-full text-left p-2 bg-teal-600 rounded capitalize mb-2">
                                {{ item.name }} ({{ item.id }})
                          </div>
                      </template>
                  </SceneReOrder>
      </div>
    </div>
</template>

<style scoped>
.sampe_ghost {
  opacity: 0.1;
 background-color: rebeccapurple !important;
}
</style>
