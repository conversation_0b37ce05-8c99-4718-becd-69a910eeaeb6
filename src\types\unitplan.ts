export enum unitplan_type {
  STUDIO = 'studio',
  ONEBHK = '1BHK',
  TWOBHK = '2BHK',
  THREEBHK = '3BHK',
  FOURBHK = '4BHK',
  FIVEBHK = '5BHK',
  SIXBHK = '6BHK',
  SEVENBHK = '7BHK',
  EIGHTBHK = '8BHK',
  NINEBHK = '9BHK',
  TENBHK = '10BHK',
  PENTHOUSE = 'penthouse',
  TOWNHOUSE = 'townhouse',
  SUITE = 'suite',
  // PLOT = 'plot',
  DUPLEX = 'duplex',
  ONEPOINTFIVEBHK = '1.5BHK',
  TWOPOINTFIVEBHK ='2.5BHK',
  THREEPOINTFIVEBHK = '3.5BHK',
  FOURPOINTFIVEBHK = '4.5BHK',
  FIVEPOINTFIVEBHK = '5.5BHK',
  SIXPOINTFIVEBHK = '6.5BHK',
  SEVENPOINTFIVEBHK = '7.5BHK',
  EIGHTPOINTFIVEBHK = '8.5BHK',
  NINEPOINTFIVEBHK = '9.5BHK',
  TENPOINTFIVEBHK = '10.5BHK'
}
export enum measurementType {
  SQFT = 'sqft',
  SQMT = 'sqmt'
}
export enum unitType {
  VILLA = 'villa',
  FLAT = 'flat',
  VILLA_FLOOR = 'villa_floor'
}
export type unitplanType = {
  _id: string;
  project_id: string;
  type?: string;
  name: string;
  thumbnail: string;
  image_url: string;
  measurement?:number;
  measurement_type?: measurementType;
  tour_id: string;
  bedrooms: unitplan_type;
  bathrooms:number;
  is_furnished:boolean;
  is_commercial:boolean;
}

export type createUnitplanType = {
  project_id: string;
  type?: string;
  name: string;
  lowRes: File | Blob;
  highRes: File | Blob;
  measurement?:number;
  measurement_type?: measurementType;
  tour_id: string;
  bedrooms: unitplan_type;
  bathrooms:number;
  is_furnished:boolean;
  unit_type: string;
  balcony_measurement: string;
  balcony_measurement_type: string;
  is_commercial:boolean;
}

export enum exterior_type {
  SCENE = 'scene',
  GALLERY = 'gallery'
}
export type updateUnitplanInterface = {
  // Define the properties of the unit plan that can be edited
  type?: string;
  name?: string;
  measurement?: number;
  measurement_type?: string;
  tour_id?: string;
  bedrooms?: number;
  bathrooms?: number;
  is_furnished?: boolean;
  unit_type?:string,
  exterior_type?:string,
  scene_id?:string,
  gallery_id?:string,
  floor_unitplans?:string,
  building_id?: string,
  style?:string,
  project_id:string,
  unitplan_id:string,
  is_commercial?:boolean;

}

export type unitplanTrashType = {
  unitplan_id : string[],
  timetimeStamp: string
}

export type createHotspots = {
  project_id : string,
  unitplan_id : string,
  hotspots : object
}
export type deleteHotspot = {
  project_id : string,
  unitplan_id : string,
  hotspot_id : string
}

export type editHotspots = {
  project_id : string,
  unitplan_id : string,
  hotspot_id:string,
  hotspots : object
}
