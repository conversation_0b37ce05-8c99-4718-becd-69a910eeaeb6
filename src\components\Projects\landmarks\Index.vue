<script setup>
import { ref } from 'vue';
import noDataFound from '../../../assets/noDataFound.png';
import { useRoute, useRouter } from 'vue-router';
import <PERSON> from 'papa<PERSON><PERSON>';
import Button from '../../common/Button.vue';
import { getListofLandmark, saveRoutes, fetchNearbyLandmarks, moveLandmarkToTrash } from '../../../api/projects/landmarks/index';
import { ProjectStore } from '../../../store/project';
import EditDeleteButton from '../../UIElements/EditDeleteButton.vue';
import { GetProject } from '../../../api/projects/settings';
import LoaderComp from '../../common/LoaderComp.vue';
import Modal from '../../common/Modal/Modal.vue';
import DeleteModalContent from '../../common/ModalContent/DeleteModalContent.vue';
import TrashedData from '../trash/Index.vue';

const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.project_id);
const projectStore = ProjectStore();
const landmarks = ref(null);
const selectedLandmark = ref();
const disableGetRoutes = ref(true);
const loader = ref(true);
const projectLat = ref(null);
const projectLong = ref(null);
const openDeleteModal = ref(false);
const deleteLoader = ref(false);
const trashedDataRef = ref(null);
const showTrash = ref(false);

const keysToCheck = ['driving', 'transit', 'walking'];

function checkKeys (obj, keys) {
  return keys.every((key) => Object.prototype.hasOwnProperty.call(obj, key) && obj[key]!== null);
}

function validateData (data, keys) {
  return Object.values(data).every((obj) => checkKeys(obj, keys));
}

const handleGetLandmarks = () => {
  getListofLandmark(projectId.value).then((landmark_list) => {
    landmarks.value = landmark_list;
    disableGetRoutes.value = validateData(landmarks.value, keysToCheck);
    projectStore.SyncMultipleLandmarks(landmark_list);
    loader.value=false;
  });
};

handleGetLandmarks();

function handleFetchNearbyLandmark (){
  if (!projectLat.value && !projectLong.value){
    GetProject(route.params.project_id).then((res) => {
      projectLat.value = res.projectSettings.general.lat;
      projectLong.value = res.projectSettings.general.long;
      const data = {
        project_id: route.params.project_id,
        lat: res.projectSettings.general.lat,
        lng: res.projectSettings.general.long,
      };
      fetchNearbyLandmarks(data).then(() => {
      });
    });
  } else {
    const data = {
      project_id: route.params.project_id,
      lat: projectLat.value,
      lng: projectLong.value,
    };
    fetchNearbyLandmarks(data).then(() => {
    });
  }
  handleGetLandmarks();
}

function objectToCsv (data) {
  const csvData = [];
  const title = ['_id', 'name', 'thumbnail', 'distance', 'category', 'walk_timing', 'transit_timing', 'car_timing', 'lat', 'long', 'description', 'driving', 'transit', 'walking'];
  const metadataHeaders = new Set();
  const entries = [];

  for (const key in data) {
    const entry = data[key];
    const csvEntry = {};
    const { ...cleanedEntry } = entry;

    csvEntry._id = cleanedEntry._id ;
    csvEntry.name = cleanedEntry.name ;
    csvEntry.thumbnail = cleanedEntry.thumbnail;
    csvEntry.distance = cleanedEntry.distance;
    csvEntry.category = cleanedEntry.category;
    csvEntry.walk_timing = cleanedEntry.walk_timing;
    csvEntry.transit_timing = cleanedEntry.transit_timing;
    csvEntry.car_timing = cleanedEntry.car_timing;
    csvEntry.lat = cleanedEntry.lat;
    csvEntry.long = cleanedEntry.long;
    csvEntry.description = cleanedEntry.description;
    csvEntry.driving = cleanedEntry.driving;
    csvEntry.transit = cleanedEntry.transit;
    csvEntry.walking = cleanedEntry.walking;

    if (cleanedEntry.metadata !== undefined) {
      for (const metaKey in cleanedEntry.metadata) {
        const header = `metadata_${metaKey}`;
        if (!metadataHeaders.has(header)) {
          metadataHeaders.add(header);
          title.push(header);
        }
        csvEntry[header] = cleanedEntry.metadata[metaKey] || '';
      }
    }

    entries.push(csvEntry);
  }

  csvData.push(title);

  entries.forEach((entry) => {
    const row = title.map((header) => entry[header]);
    csvData.push(row);
  });

  return csvData;
}

function exportCsv () {
  const csvData = objectToCsv(landmarks.value);
  console.log(csvData);
  const csv = Papa.unparse(csvData);

  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: 'text/csv' });

  // Create a URL for the Blob
  const url = window.URL.createObjectURL(blob);

  // Create a link element and trigger the download
  const a = document.createElement('a');
  a.href = url;
  a.download = 'data.csv';
  document.body.appendChild(a);
  a.click();

  // Clean up
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

document.addEventListener('refreshProjectLandmark', () => {
  handleGetLandmarks();
});

const handleMenuSelected = (landmarkId) => {
  if (selectedLandmark.value===landmarkId){
    selectedLandmark.value = null;
  } else {
    selectedLandmark.value = landmarkId;
  }
};

const handleEdit = () => {
  console.log('Edit->landmark', selectedLandmark.value);
  router.push(`/projects/${projectId.value}/landmarks/${selectedLandmark.value}/edit`);
  selectedLandmark.value = null;
};
const handleMoveToTrash = () => {
  deleteLoader.value = true;
  const obj = {
    landmark_id: [selectedLandmark.value],
    timeStamp: Date.now(),
  };
  moveLandmarkToTrash(obj, projectId.value).then(() => {
    deleteLoader.value = false;
    handleGetLandmarks();
    trashedDataRef.value?.fetchTrashData();
    openDeleteModal.value = false;
    selectedLandmark.value = null;
  }).catch(() => {
    deleteLoader.value = false;
  });
};

const handleGetRoutes = async () => {
  loader.value = true;

  const fetchRoutesForModes = async (projectLat, projectLong, landmark) => {
    if (!landmark?.lat && !landmark?.long){
      return;
    }
    const travelModes = [
      google.maps.TravelMode.DRIVING,
      google.maps.TravelMode.TRANSIT,
      google.maps.TravelMode.WALKING,
    ];

    const directionsService = new google.maps.DirectionsService();

    const getDirections = async (mode) => {
      const directionsRequest = {
        origin: { lat: projectLat, lng: projectLong },
        destination: { lat: landmark.lat, lng: landmark.long },
        travelMode: mode,
        waypoints: [],
        optimizeWaypoints: true,
        provideRouteAlternatives: false,
      };

      return new Promise((resolve, reject) => {
        directionsService.route(directionsRequest, (response, status) => {
          if (status === google.maps.DirectionsStatus.OK) {
            const data = {
              project_id: route.params.project_id,
              landmark_id: landmark._id,
              mode: mode.toLowerCase(),
              json: response,
            };
            saveRoutes(data)
              .then(() => resolve())
              .catch((error) => reject(error));
          } else {
            if (status === google.maps.DirectionsStatus.ZERO_RESULTS && mode !== google.maps.TravelMode.WALKING) {
              resolve(getDirections(google.maps.TravelMode.WALKING));
            } else {
              reject(status);
            }
          }
        });
      });
    };

    const requests = travelModes.map(async (mode) => {
      if (!landmark[mode.toLowerCase()]) {
        return getDirections(mode);
      }
      return Promise.resolve();
    });

    return Promise.all(requests);
  };

  try {
    if (!projectLat.value && !projectLong.value) {
      const project = await GetProject(route.params.project_id);
      projectLat.value = project.projectSettings.general.lat;
      projectLong.value = project.projectSettings.general.long;
    }

    if (!landmarks.value || landmarks.value.length === 0) {
      handleGetLandmarks();
    }

    const landmarksArray = Object.values(landmarks.value);

    for (const landmark of landmarksArray) {
      await fetchRoutesForModes(projectLat.value, projectLong.value, landmark);
    }

  } catch (error) {
    console.error('Error occurred:', error);
  } finally {
    loader.value = false;
  }
};

</script>

<template>
    <div class="">
        <main
            class="h-[92.8vh] w-full bg-bg-1000 dark:bg-bg-default relative">
            <LoaderComp v-if="loader" />
            <div
                class="flex items-center justify-between py-3 px-8">
                <div class="min-w-0 gap-3">
                    <h2
                        class="font-bold -tracking-2 leading-7 text-txt-100 dark:text-txt-1000 sm:truncate sm:text-3xl sm:tracking-tight mb-3">
                        Landmarks
                    </h2>
                    <p class="text-txt-600 dark:text-txt-650">
                        Lorem ipsum dolor sit amet consectetur
                        adipisicing
                        elit.
                    </p>
                </div>
                <div class="flex flex-wrap gap-3">
                  <Button v-if="landmarks" :disabled="Object.keys(landmarks).length > 20" title="Fetch Nearby Landmarks" theme="primary" class="h-10" @click="handleFetchNearbyLandmark()"/>
                  <Button :disabled="disableGetRoutes" title="Get Routes" theme="primary" class="h-10" @click="handleGetRoutes"/>
                  <Button title="Create New Landmark"
                      theme="primary" class="h-10"
                      @click="router.push(`/projects/${projectId}/landmarks/create`)">
                      <template v-slot:svg>
                          <svg xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              class="h-6 w-6 fill-txt-1000 dark:fill-txt-50">
                              <g data-name="Layer 2">
                                  <g data-name="plus">
                                      <rect width="24" height="24"
                                          transform="rotate(180 12 12)"
                                          opacity="0" />
                                      <path
                                          d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                                  </g>
                              </g>
                          </svg>
                      </template>
                  </Button>
                  <Button title="Create Multiple Landmark"
                      theme="primary" class="h-10"
                      @click="router.push(`/projects/${projectId}/landmarks/createmultiple`)">
                      <template v-slot:svg>
                          <svg xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              class="h-6 w-6 fill-txt-1000 dark:fill-txt-50">
                              <g data-name="Layer 2">
                                  <g data-name="plus">
                                      <rect width="24" height="24"
                                          transform="rotate(180 12 12)"
                                          opacity="0" />
                                      <path
                                          d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                                  </g>
                              </g>
                          </svg>
                      </template>
                  </Button>
                  <Button title="Export CSV" theme="primary" class="h-10" @click="exportCsv"/>
                  <Button class="h-10" title="Trashed Landmarks" @click="showTrash = !showTrash" theme="primary"></Button>
                </div>
            </div>
            <!-- <BreadCrumb /> -->
            <!-- list of landmark -->
            <div class="mt-3 text-blue-900 px-8" v-if="!showTrash">
                <div :class="[
                    landmarks && Object.values(landmarks).length !== 0 &&
                    'grid sm:grid-cols-4 gap-x-3 gap-y-3 max-h-[68vh] h-inherit overflow-y-auto grid-container pb-10 px-1',
                    'w-full text-white',
                ]">
                    <div v-if="!landmarks || Object.values(landmarks).length === 0"
                        class="flex w-full m-auto justify-center p-4">
                        <div class="w-full">
                            <img class="w-72 m-auto"
                                :src="noDataFound" alt="" />
                            <p
                                class="text-xs text-center text-neutral-100 mt-2">
                                Oops! No Data Found,
                                Contact admin to add
                                Landmarks
                            </p>
                        </div>
                    </div>

                    <div v-else
                        v-for="landmark, landmarkId in landmarks"
                        :key="landmarkId"
                        class="max-sm:rounded-sm rounded-md cursor-pointer max-w-[420px] border-[1px] border-bg-900 relative">
                        <div>
                            <img v-if="landmark.thumbnail !== ''"
                                :src="landmark.thumbnail"
                                alt="img"
                                class="rounded-t-md w-full"
                                style="height: 200px; object-fit: cover" />
                            <div
                                class="flex justify-between items-center">
                                <p
                                    class="text-txt-default dark:text-txt-1000 p-2">
                                    {{ landmark.name }}
                                </p>
                                <button @click="handleMenuSelected(landmark._id)">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        strokeWidth="{1.5}"
                                        stroke="currentColor"
                                        class="w-6 h-6  stroke-bg-default dark:stroke-bg-1000">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                    </svg>
                                </button>
                                <EditDeleteButton @handleDelete="()=>{openDeleteModal=true}" @handleEdit="handleEdit" v-if="selectedLandmark===landmark._id" class="right-1 bottom-10"/>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <TrashedData
              ref="trashedDataRef"
              v-if="showTrash"
              :type="'landmarks'"
              :entity="'projectLandmark'"
              :action="'restoreLandmark'"
              @refreshDataList="handleGetLandmarks"
            />
        </main>
        <Modal :open="openDeleteModal">
          <DeleteModalContent
            :trash="true"
            :loader="deleteLoader"
            @closeModal="(e) => openDeleteModal = false"
            @handleDelete="handleMoveToTrash"
            :dataName="'Landmark'" />
        </Modal>
    </div>
</template>

<style scoped></style>
