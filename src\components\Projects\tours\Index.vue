<script setup>
import { onUnmounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ProjectStore } from '../../../store/project';
import { DeleteVirtualTour } from '../../../api/projects/tours';
import NotfoundImage from '../../common/NotfoundImage.vue';
import Button from '../../common/Button.vue';
import { tourCardsMenuItems } from '@/enum';
import { PhotoIcon } from '@heroicons/vue/20/solid';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import EditTourModal from './EditTourModal.vue';
import CreateTourModal from './CreateTourModal.vue';

const route = useRoute();
const router = useRouter();
const projectId = ref(route.params.project_id);
const openCreateTourModal = ref(false);
const openEditTourModal = ref(false);
const deleteLoader = ref(false);
const projectStore = ProjectStore();
const parentContainerRef = ref(null);
const tourCardsMenuItemsRef = ref({});
const editTourExData = ref(null);
const listOfTabs = ref(['Tours', 'Archive']);
const activeTabs = ref('Tours');
const activePopMenuOpenId = ref(null);
const openCopyMsg = ref(false);
const copyMsgTimeout = ref(null);

if (projectId.value){
  projectStore.RefreshVirtualTours(projectId.value);
}

if (!projectStore.unitplans){
  projectStore.RefreshUnitplans(projectId.value);
}

/* Methods */
const handleEditTour =  (values) => {
  const editData = {
    id: values._id,
    name: values.name,
    type: values.type,
    description: values.description,
    category: values.category,
    ...( values.unitplan_id && {unitplan_id: values.unitplan_id}),
    ...( values.link && {link: values.link}),
    ...( values.space_id && {space_id: values.space_id}),
    ...( values.model && {model: values.model}),
    ...( values.camera && {camera: values.camera}),
  };
  if (values.type === 'MLE') {
    try {
      editData.labels = values.labels;
    } catch (e) {
      editData.labels = [];
    }
  }
  editTourExData.value = editData;
  openEditTourModal.value = true;
};

// Delete Tour
const handleDeleteTour = () => {
  console.log("handleDeleteTour");
  console.log(projectStore.virtualtours);
  console.time("sdfdsf");
  console.log(route.query.deletetour_id);
  deleteLoader.value = true;
  const obj = {
    project_id: projectId.value,
    tour_id: route.query.deletetour_id,
  };
  console.log(obj);
  DeleteVirtualTour(obj).then(() => {
    delete projectStore.virtualtours[route.query.deletetour_id]; // delete in the store
    deleteLoader.value = false;
    router.push({ path: route.path, query: {} });
  }).catch((err) => {
    deleteLoader.value = false;
    console.log("Error", err);
  });
};

// Copy Tour Id
const handleCopyTourId = (e, id) => {
  console.log(id, "Tour ID", e.currentTarget);
  if (openCopyMsg.value && copyMsgTimeout.value){ // If any copy is in-progress already then close it and make a new.
    clearTimeout(copyMsgTimeout.value);
  }

  e.currentTarget?.blur(); // Remove the Focus

  navigator.clipboard.writeText(id).then((res) => {
    console.log("Copied", res);
    openCopyMsg.value = true;
    copyMsgTimeout.value = setTimeout(() => {
      openCopyMsg.value = false;
      copyMsgTimeout.value = null;
    }, 1000); // 1s
  }).catch((err) => {
    console.log("Error in tour Id Copy", err);
  });

};

// Three dots menu
const handleMenuPopup = (e, id) => {
  const listOfRemovalClass = ['hidden', 'top-[30px]', '-top-[105px]', '-right-[10px]', '-right-[8px]'];

  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container

    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      element.classList.add('-right-[8px]');   // both x and y are exceeded the parent container or only y has exceded the parent container
      element.classList.add(`-top-[105px]`);
    }  else { // General
      element.classList.add('-right-[10px]');
      element.classList.add(`top-[30px]`);
    }
  };

  if (tourCardsMenuItemsRef.value[id]){
    // active item
    activePopMenuOpenId.value = id;
    tourCardsMenuItemsRef.value[id].classList.remove(...listOfRemovalClass); // remove class
    dynamicAbsolutePositions(tourCardsMenuItemsRef.value[id]); // apply absolute positions depending on the current position (i.e is based on parent container)
    tourCardsMenuItemsRef.value[id].classList.add('block');   // add class
  }
};

const handleCloseMenuPopup = (id) => {
  if (tourCardsMenuItemsRef.value ? tourCardsMenuItemsRef.value[id].classList.contains('block') : false){
    activePopMenuOpenId.value = null; // reset
    const listOfRemovalClass = ['block', 'top-[30px]', '-top-[105px]', '-right-[10px]', '-right-[8px]'];
    tourCardsMenuItemsRef.value[id].classList.remove(...listOfRemovalClass); // remove class
    tourCardsMenuItemsRef.value[id].classList.add('hidden');   // add class
  }
};

onUnmounted(() => {
  if (copyMsgTimeout.value) { // Just a clean-up
    clearTimeout(copyMsgTimeout.value);
  }
});

</script>
<template>
  <div class="flex-1 h-full pt-2 pe-4 w-full">
    <div class="w-full h-full overflow-hidden gap-5 p-6 relative flex flex-col justify-start items-start bg-white rounded-t-lg">
      <!-- Header -->
      <div class="flex justify-between w-full shrink-0 select-none">
                                  <div class="flex flex-col justify-start items-start gap-2">
                                            <h3 class="text-gray-900 text-lg font-medium">VR Tours</h3>
                                            <p class="text-gray-500 text-xs font-normal mb-0"> Create and manage projects. </p>
                                  </div>
                                  <div class="flex gap-6 items-center">
                                    <div>
                                        <Button title="Create  Tours" theme="primary" @click="() => openCreateTourModal = true"> <!-- router.push(`/projects/${projectId}/design/tours/create` ) -->
                                       <!--      <template v-slot:svg>
                                                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <g clip-path="url(#clip0_306_21007)">
                                                        <path class="fill-txt-1000 dark:fill-txt-default" d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z"/>
                                                        <path class="fill-txt-1000 dark:fill-txt-default" d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z"/>
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_306_21007">
                                                            <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)"/>
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                            </template> -->
                                        </Button>
                                    </div>
                                  </div>
      </div>

      <!-- Tabs -->
      <div class="w-full border-b border-gray-300 flex justify-start items-start gap-0 shrink-0">
             <h5 v-for="item in listOfTabs" :key="item" :class="['w-32 text-center border-b py-1.5 text-base cursor-pointer font-normal leading-normal select-none transition-all -mb-[1px]', item === activeTabs ? 'text-blue-600 border-blue-600' :'text-gray-500 border-transparent']" @click="() => activeTabs = item">
                    {{ item }}
             </h5>
      </div>

      <div ref="parentContainerRef"  class="text-white w-full flex-1  overflow-y-auto">

        <div :class="[
           projectStore.virtualtours && projectStore.virtualtours.length !== 0 &&
          'grid sm:grid-cols-4 gap-x-[84px] gap-y-6 grid-container',
          'w-full text-white px-3 py-3',
        ]">
          <div
            v-if="!projectStore.virtualtours || projectStore.virtualtours.length === 0"
            class="flex w-full m-auto justify-center p-4">
            <div class="w-fit">
              <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <rect width="300" height="286" fill="url(#pattern0)"/>
                        <NotfoundImage/>
                    </svg>
              <p
                class="text-xs text-center text-black mt-2">
                Oops! No Tour Found.
              </p>
            </div>
          </div>

          <div v-else v-for="item, in projectStore.virtualtours"
            :key="item._id"
            :onmouseleave="() => handleCloseMenuPopup(item._id)"
            :onclick="() => router.push(`/projects/${projectId}/design/tours/${item._id}`)"
            class="shadow-md outline-1 rounded-lg bg-white  outline-gray-200 select-none">
            <div>
              <img v-if="item?.images" :src="Object.values(item?.images)[0].thumbnail" alt="img" class="rounded-t-lg w-full h-40" />
              <div v-else class="  rounded-t-lg flex justify-center bg-gray-100 items-center w-full h-40">
                  <PhotoIcon class="w-32 fill-gray-200"/>
              </div>

              <div class="flex flex-col justify-start items-start gap-3 py-3.5 px-4">
                <div class="flex justify-start items-center">
                       <span class="capitalize text-center text-[#1E429F] bg-[#E1EFFE] text-sm font-medium px-3 py-0.5 rounded-md">
                            {{ item.type ?? item.virtualtour_type }}
                       </span>
                </div>
                <div class="w-full flex justify-between items-center">

                <h5 class="text-gray-900 text-lg font-normal">
                  {{ item.name ?? item.tour_name }}
                </h5>

                <div class="flex justify-start items-center gap-4">
                  <button class="group border-none outline-none cursor-pointer" @click.stop="(e) => handleCopyTourId(e,item._id)">
                  <svg class="w-7 h-7 fill-none transition duration-150" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg"  >
                    <path d="M14 0.5C21.4558 0.5 27.5 6.54416 27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5Z"  class="fill-white group-focus-within:fill-[#1E429F]"/>
                    <path d="M14 0.5C21.4558 0.5 27.5 6.54416 27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5Z" class="stroke-[#E5E7EB] group-hover:stroke-[#F3F4F6] group-hover:stroke-[3] group-focus:stroke-[#A4CAFE] group-focus:stroke-[3]"/>
                    <path d="M10.25 10.9004H10.375V11.0996H9.74902V11.5996L9.75 18.7998V19.2998H15.75V18.7002H15.9971V18.7861C15.9924 18.9592 15.9552 19.0885 15.8906 19.1885L15.8174 19.2793C15.676 19.4197 15.4841 19.4999 15.2715 19.5H10.209C9.81489 19.5 9.5 19.1821 9.5 18.7998V11.5996C9.50023 11.2331 9.81749 10.9004 10.25 10.9004ZM17.791 8.5C18.1851 8.5 18.5 8.81789 18.5 9.2002V16.4004C18.4999 16.5911 18.4203 16.7646 18.293 16.8916L18.291 16.3975L18.25 9.19727C18.2497 9.13364 18.2386 9.07223 18.2178 9.01562C18.2074 8.98749 18.1949 8.96176 18.1816 8.93848C18.175 8.92685 18.1681 8.91556 18.1611 8.90527C18.1577 8.90018 18.1539 8.89539 18.1504 8.89062C18.1487 8.88834 18.1472 8.886 18.1455 8.88379C18.1446 8.88263 18.1435 8.88102 18.1426 8.87988L18.1416 8.87793L18.1406 8.87695L17.9893 8.68262L17.7432 8.68457L15.8721 8.69629L15.375 8.69922V11C15.375 11.3666 15.0577 11.7002 14.625 11.7002H12.25V16.9004H18.2842C18.157 17.0229 17.9844 17.0996 17.792 17.0996H12.709C12.315 17.0996 12.0002 16.7825 12 16.4004V11.5H15.125V8.5H17.791ZM14.2783 9.08789L12.6621 10.6396L12.0244 11.251C12.0661 11.0432 12.1611 10.849 12.3047 10.6816L12.3955 10.585L14.1631 8.8877C14.3555 8.70264 14.597 8.57786 14.8623 8.52637L14.2783 9.08789Z" class="fill-[#6B7280] stroke-[#6B7280] group-focus:fill-white group-focus:stroke-white"/>
                  </svg>
                  </button>
                  <div class="relative mb-0 h-[28px]">
                  <button class="outline-none border-none group cursor-pointer h-[inherit]" @click.stop="(e) => handleMenuPopup(e,item._id)">
                      <svg :class="['w-7 h-[inherit]  stroke-[#E5E7EB] transition-all', activePopMenuOpenId === item._id ? 'fill-[#1E429F]' : 'fill-none' ]" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 0.5C21.4558 0.5 27.5 6.54416 27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5Z" fill="white"/>
                        <path d="M14 0.5C21.4558 0.5 27.5 6.54416 27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5Z" :class="['stroke-[#E5E7EB] group-hover:stroke-[#F3F4F6] group-hover:stroke-[3]', activePopMenuOpenId === item._id ? 'stroke-[3]' : '' ]"/>
                        <path d="M14 18.0996C14.3864 18.0997 14.7001 18.4134 14.7002 18.7998C14.7002 19.1863 14.3865 19.4999 14 19.5C13.6134 19.5 13.2998 19.1864 13.2998 18.7998C13.2999 18.4133 13.6135 18.0996 14 18.0996ZM14 13.2998C14.3865 13.2999 14.7002 13.6135 14.7002 14C14.7002 14.3865 14.3865 14.7001 14 14.7002C13.6134 14.7002 13.2998 14.3866 13.2998 14C13.2998 13.6134 13.6134 13.2998 14 13.2998ZM14 8.5C14.3865 8.50011 14.7002 8.81366 14.7002 9.2002C14.7001 9.58664 14.3864 9.90029 14 9.90039C13.6135 9.90039 13.2999 9.5867 13.2998 9.2002C13.2998 8.8136 13.6134 8.5 14 8.5Z" :class="[ activePopMenuOpenId === item._id ? 'fill-white stroke-white' : 'fill-[#6B7280] stroke-[#6B7280] ']" fill="#6B7280" stroke="#6B7280"/>
                      </svg>

                      </button>

                  <div :ref="el => { if(el) tourCardsMenuItemsRef[item._id] = el}" @click.stop="" @mouseover.stop="() => {tourCardsMenuItemsRef[item._id].classList.remove('hidden'); tourCardsMenuItemsRef[item._id].classList.add('block');}" @mouseleave.stop="() => handleCloseMenuPopup(item._id)" :key="'tourCardMenuItem'+item._id" class="bg-transparent hidden py-3 absolute transition-all w-[121px]">
                     <ul class="bg-white rounded-lg outline-1 outline-offset-[-1px] outline-gray-200  shadow-md w-full flex flex-col justify-start items-start list-none py-0.5">
                          <li v-for="menuItem, menuItemKey in tourCardsMenuItems" :key="menuItemKey" @click.stop="() => { if(menuItemKey === 'Delete') { router.push({ path: route.path, query: { deletetour_id: item._id } }); } else { handleEditTour(item); }} " :class="['w-full text-nowrap px-3 py-2 text-sm font-normal cursor-pointer flex justify-start items-center gap-3', menuItemKey === 'Delete' ? ' text-red-700' :'text-gray-500']">
                              <span v-html="menuItem.svg" class="w-4 h-4"></span>
                              {{ menuItem.label }}
                          </li>
                      </ul>
                    </div>
                </div>
                </div>

              </div>

              </div>
            </div>
          </div>
        </div>
      </div>

      <Modal :open="projectStore.virtualtours && route.query.deletetour_id ? true : false">
        <div class="w-full flex justify-center items-center">
            <DeleteModalContent
              v-if="route.query.deletetour_id"
              :trash="false"
              :loader="deleteLoader"
              @closeModal="(e) => router.push({ path: route.path, query: {} })"
              @handleDelete="handleDeleteTour"
              :dataName="'Tour'"
            />
          </div>
      </Modal>

    </div>

    <p :class="['fixed w-[150px] h-10 bg-white rounded-lg shadow-md outline outline-1 outline-gray-200 -translate-x-[50%] left-[50%] text-center text-black text-base font-normal leading-normal z-20 flex justify-center items-center transition-all ease-in-out' , openCopyMsg ? 'bottom-5' : '-bottom-16' ]">
          Tour ID Copied
    </p>

  </div>

  <CreateTourModal v-if="openCreateTourModal"  @close="() => openCreateTourModal = false"/>
  <EditTourModal v-if="openEditTourModal"  :previousData="editTourExData" @close="(e) => openEditTourModal = false"/>

</template>
