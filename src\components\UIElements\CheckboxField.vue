<script setup>
import { ref } from 'vue';
import Multiselect from 'vue-multiselect';

const selectedUnits = ref('');
const isHighlighted = ref(false);

// Inputs should be this format
const units = ref([
  {
    'code': 1,
    'name': 'Unit 123',
  },
  {
    'code': 2,
    'name': 'Unit 1234',
  },
  {
    'code': 3,
    'name': 'Unit 657',
  },
  {
    'code': 4,
    'name': 'Unit 098',
  },
  {
    'code': 5,
    'name': 'Unit 24',
  },
  {
    'code': 6,
    'name': 'Unit 577',
  },
  {
    'code': 7,
    'name': 'Unit 345',
  },
  {
    'code': 8,
    'name': 'Unit 890',
  },

]);

const customTagClass = () => {
  return 'multiselect__tag', 'multiselect'; // Add your custom class name here
};

const highlightDiv = () => {
  isHighlighted.value = !isHighlighted.value;
};

</script>

<template>
   <div class="flex justify-between items-center gap-[24px] w-full"  @click="highlightDiv()">

            <div class="field  w-full h-fit block rounded transition-all duration-[0.3s] ease-in-out border-[1px] border-bg-700 focus:border-bg-default">
                <multiselect :tag-class="customTagClass" v-model="selectedUnits" tag-placeholder="Interested Units" placeholder="Interested Units"
                label="name" track-by="code" :options="units" :multiple="true" :taggable="true" @tag="addTag"></multiselect>
            </div>
    </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>

<style>

  /* Removed scoped attribute */
  .multiselect__tag {
    border-radius: 16px;
    background: var(--White, #FFF);
    box-shadow: 0px 0px 22.9px 0px rgba(0, 0, 0, 0.11);
    color: #000;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    box-sizing: border-box;
  }

  .multiselect{
    width: auto;
    box-sizing: border-box;
  }
  .multiselect__placeholder {
   padding-left: 12px;
   color: rgb(140 140 140 / var(--tw-text-opacity));
   --tw-text-opacity: 1;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .highlighted {
    border: 1px solid black;
  }
</style>
