export enum projectSVGType {
  LANDMARK = 'landmark',
  PROJECT = 'project',
  RADIUS = 'radius',
  ROUTE = 'route',
  PIN = 'pin',
  IMAGE = 'image',
  BUILDING = 'building',
  LABEL = 'label',
  TOWER = 'tower',
  AMENITIES = 'amenity',
  FLOOR = 'floor',
  UNITS = 'units',
  GSPLAT = 'gsplat',
  AMENITYCATEGORY = 'amenitycategory',
  STATIC = 'static',
  COMMUNITY = 'community'
}
export enum placementSVG {
  TOP = 'top',
  BOTTOM = 'bottom',
  LEFT = 'left',
  RIGHT = 'right',
}
export type Layers = {
  layer_id: string;
  building_id?: string;
  scene_id?: string;
  type?: string;
  g: string;
  x: number;
  y: number;
  landmark?: {
    landmark_id: string;
    route_id?: string;
  };
  project_id?: string;
  image_id?: string;
  amenity_id?: string;
  amenity_category?: string;
  community_id?: string;
  floor_id?: string;
  units?: string;
  title?: string;
  category?: string;
  placement?: string;
  reSize?: boolean;
  zIndex?: number;
  maxZoomLevel?: number;
  name?: string;
  rotation?: string;
  position?: string;
  scale?: {
    x:number,
    y:number
  };
  svg_url?: string;
  width: number;
  height: number;
};

export type projectSVG = {
  _id: string;
  scene_id: string;
  svg_url: string;
  layers: Layers;
  type: projectSVGType;
};
export type transformedSVG = {
  [key: string]: projectSVG;
};
export type updatePayload = {
  [key: string]: string | { x: number; y: number } ; // Allow specific types
};

export type EnvVar = {
    name: string;
    value: string;
};
