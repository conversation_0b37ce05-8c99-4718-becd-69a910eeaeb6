<script setup>
import { <PERSON>u, <PERSON>uButton, MenuItem, MenuItems } from '@headlessui/vue';
import { ChevronDownIcon } from '@heroicons/vue/20/solid';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Spinner from '../common/Spinner.vue';
import { getCookie } from '../../helpers/domhelper';
import ErrorMessage from '../common/ErrorMsg.vue';
import { createSVG } from '../../api/masterScene/svg/index';
import Modal from '../common/Modal/Modal.vue';

const route = useRoute();

const sceneId = ref(route.params.scene_id);
const router = useRouter();

const svgFile = ref();
const svgType = ref();
const errorMessage = ref();
const loader = ref(false);
const typeList = ['landmark', 'project', 'radius', 'image', 'pin', 'route', 'unavailable'];

const handleFileChange = (event) => {
  svgFile.value = event.target.files[0];
};

const HandleAddSvg = (data) => {
  loader.value = true;
  createSVG(data).then(() => {
    router.go(-1);
  }).catch(() => {
  }).finally(() => {
    loader.value = false;
  });
};

const handleFormSubmit = () => {
  if (svgFile.value && svgType.value) {
    const formData = new FormData();

    formData.append('svgFile', svgFile.value);
    formData.append('type', svgType.value);
    formData.append('scene_id', sceneId.value);
    formData.append('organization_id', getCookie('organization'));

    errorMessage.value = null;
    HandleAddSvg(formData);
  } else {
    errorMessage.value = 'Enter all Fields to continue';
  }
};
</script>

<template>
    <Modal :open="true">
        <div
            class="modal-content-primary sm:max-w-lg">
            <div class="p-3 sm:p-6 ">
                <div class="mb-2">
                    <h1
                        class="modal-heading-primary">
                        Add Svg</h1>
                    <p class="modal-subheading-primary">Fill details below to create svg</p>
                </div>
                <form action=""
                    class="flex flex-col justify-center ">
                    <div class="col-span-full mt-3">
                        <label for="street-address"
                            class="label-primary">Upload
                            Svg
                            File</label>
                        <div class="mt-2">
                            <input type="file"
                            name="svgFile"
                                id="svgFile"
                                autocomplete="svgFile"
                                @change="handleFileChange"
                                class="input-primary"
                                placeholder="Enter Project Name" />
                        </div>
                    </div>
                    <Menu as="div"
                        class="mt-3 relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label
                            class="label-primary">
                            Type
                        </label>
                        <div class="w-full">
                            <MenuButton
                                style="border: 1px solid #737373;"
                                class="dropdown-btn">
                                {{ svgType ? svgType
                                    : 'SelectType'
                                }}
                                <ChevronDownIcon
                                    class="-mr-1 h-5 w-5 text-gray-400 ml-1.5"
                                    aria-hidden="true" />
                            </MenuButton>
                        </div>

                        <transition
                            enter-active-class="transition ease-out duration-100"
                            enter-from-class="transform opacity-0 scale-95"
                            enter-to-class="transform opacity-100 scale-100"
                            leave-active-class="transition ease-in duration-75"
                            leave-from-class="transform opacity-100 scale-100"
                            leave-to-class="transform opacity-0 scale-95">
                            <MenuItems
                                class="absolute -bottom-24 w-40 h-44 overflow-auto  right-0 z-50 mt-2 rounded-md bg-neutral-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                <div class="py-1">
                                    <MenuItem
                                        v-for="type, typeId in typeList"
                                        :key="typeId">
                                    <a href="#"
                                        @click="() => svgType = type"
                                        class="text-gray-300 block px-3 py-0.5 text-sm hover:bg-neutral-400 hover:text-blue-200 whitespace-nowrap text-ellipsis overflow-x-hidden">
                                        {{ type }}</a>
                                    </MenuItem>

                                </div>
                            </MenuItems>
                        </transition>
                    </Menu>
                    <ErrorMessage
                        :errorMessage="errorMessage" />
                    <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.go(-1)"
                            ref="cancelButtonRef">Cancel</button>
                        <button type="button"
                            class="proceed-btn-primary"
                            @click="handleFormSubmit">Save
                            <Spinner v-if="loader" />
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </Modal>
</template>

<style scoped>
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}
</style>
