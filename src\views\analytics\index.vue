<script setup>
import SideNavBar from '../../components/common/SideNavBar.vue';
import Analytics from '../../components/Projects/analytics/index.vue';
import Navbar from '../../components/common/Navbar.vue';
import { UserStore } from '../../store/index';
const userStore = UserStore();
</script>

<template>
     <div class="w-full h-full overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar/>
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
            <Analytics v-if="userStore.user_data && userStore.user_role"></Analytics>
        </div>
    </div>
</div>
</template>
