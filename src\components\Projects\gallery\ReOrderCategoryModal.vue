<script setup>
import { Draggable } from 'vue-dndrop';
import DnDrop from '../../common/DnDrop.vue';
import Modal from '../../common/Modal/Modal.vue';
import { ref } from 'vue';
import { applyDrag } from '../../../helpers/dnDrophelper';
import { useRouter, useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import { getCategories, updateGallerySettings } from '../../../api/projects/gallery';
import { GetProject } from '../../../api/projects/settings';

const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.project_id);
const category_id = ref(route.params.category_id);
const originalListOfItems = ref([]);
const listOfItems = ref([]);
const loader = ref(false);

/* Methods */
const frameTheArrayOfItems = () => {
  GetProject(projectId.value).then((projectDetails) => {
    getCategories(projectId.value).then((res) => { // Get list of categories

      const completeGalleryCategory = res.map((elem) => { // Getting details of category (id,name,order) from project setting if available
        const objectToFind = projectDetails.projectSettings?.gallery ?Object.values(projectDetails.projectSettings.gallery).find((item) => item.name === elem):null;
        console.log(objectToFind);
        return objectToFind ? objectToFind : {name: elem};
      });
      console.log(completeGalleryCategory);
      const sortedItems = [];
      if (completeGalleryCategory){
        completeGalleryCategory.sort((a, b) => {
          return a.order - b.order;
        }).forEach((item) => {
          sortedItems.push(item);
        });
      }
      const generateArrayofItems = sortedItems.map((item, index) => {
        return {
          ...(item.id && {'id': item.id}),
          'order': item.order ? item.order : index + 1,
          'name': item.name,
        };
      });
      console.log('**', generateArrayofItems);
      originalListOfItems.value = [...generateArrayofItems];
      listOfItems.value = generateArrayofItems;
    }).catch((err) => {
      console.log('output->err', err);
    });
  });

};

frameTheArrayOfItems(); // Initialize

const handleDrop = (dropResult) => {
  console.log(dropResult);
  listOfItems.value = applyDrag(listOfItems.value, dropResult);
};

const compareTheOriginalListOfItems = () => {
  if (originalListOfItems.value.map((item, index) => item.name === listOfItems.value[index].name).includes(false)){
    const filteredArray = listOfItems.value.map((item, index) => {
      const itemObject = {...item};
      itemObject.order = index + 1;
      return itemObject;
    });
    return filteredArray;
  }
  return false;

};

const handleSubmit = () => {
  loader.value = true;
  if (compareTheOriginalListOfItems()){
    const reqBody = {
      'query': compareTheOriginalListOfItems(),
      'project_id': projectId.value,
    };
    // Api Call
    updateGallerySettings(reqBody).then(() => {
      loader.value = false;
      document.dispatchEvent(new Event('refreshGalleryItemsList'));
      router.push(`/projects/${projectId.value}/gallery`);
    });
  } else {
    loader.value = false;
    router.push(`/projects/${projectId.value}/gallery/${category_id.value}`);
  }
};

</script>

<template>
    <Modal :open="true">
       <div class="modal-content-primary !transform-none">
        <div class="p-3 sm:p-6">
                <div class="mb-3">
                    <h1
                        class="modal-heading-primary">
                        ReOrder Gallery Category</h1>
                    <p class="modal-subheading-primary"> Feel free to drag and rearrage the items </p>
                </div>

                  <div class="py-4 w-full flex justify-center items-center" v-if="listOfItems.length > 0">

                    <DnDrop :animationDuration="Number('200')" @on-drop="handleDrop" class="px-2 max-h-[500px] w-5/12 overflow-y-scroll" >
                                <template #items>
                                    <Draggable v-for="item in listOfItems" class="cursor-pointer py-2 text-center" :key="item.id">
                                            <div class="font-medium text-lg capitalize draggable-item bg-bg-150 text-white rounded-full w-full h-fit  px-2 py-3 hover:bg-bg-50" >
                                                        {{ item.name }}
                                            </div>
                                    </Draggable>
                                </template>
                    </DnDrop>

                  </div>

                  <div v-else class="text-center flex flex-col items-center justify-center my-5 gap-2 ">
                    <Spinner/>
                    <p class="text-black text-lg font-medium mb-0"> Loading ... </p>
                  </div>

                  <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                           <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.push(`/projects/${projectId}/gallery`)"> Cancel</button>
                          <button type="button" :disabled="!compareTheOriginalListOfItems() ? true : false || loader" @click="handleSubmit"
                            class="proceed-btn-primary">Save
                            <Spinner v-if="loader" />
                          </button>
                  </div>

        </div>
      </div>
    </Modal>
</template>

<style scoped>

</style>
