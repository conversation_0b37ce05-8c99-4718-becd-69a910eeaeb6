<script setup>
import { defineProps, defineEmits } from 'vue';

defineProps({
  array: Array,
  active: String,
});

defineEmits(['click']);
</script>

<template>
    <div
        class="flex sm:items-center sm:gap-9 w-full sm:w-auto bg-gray-100 sm:bg-transparent sm:dark:bg-transparent rounded-xl">
        <button v-for="(option, index) in array" :key="index" @click="$emit('click', option)" :class="[
            'flex-1 sm:flex-initial py-2 px-4 sm:px-0 sm:pb-2 transition-colors duration-200 ease-in-out capitalize rounded-lg sm:rounded-none',
            'font-normal text-center',
            'relative',
            active === option
                ? 'bg-white sm:bg-transparent sm:dark:bg-transparent text-[#1c64f2] text-base leading-normal shadow-sm sm:shadow-none sm:border-b-[3px] sm:border-current'
                : 'text-txt-500'
        ]">
            {{ option }}
        </button>
    </div>
</template>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@500&display=swap');

.font-roboto {
    font-family: 'Roboto', sans-serif;
}
</style>
