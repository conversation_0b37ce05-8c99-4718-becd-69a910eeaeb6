import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';
import type { Project, updateProjectInput, uploadSettingsFiles } from '@/types/projects';
const api_url = import.meta.env.VITE_API_URL;

export async function GetProject (projectId: string): Promise<Project> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({url: `${api_url}/project/GetProject/${projectId}`}).then((res) => {
      resolve(res as Project);
    }).catch((error: unknown) => {
      reject(error);
    });
  });

}

export async function UpdateProjectSettings (values: updateProjectInput): Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/project/UpdateProjectSettings`, body: values}).then((res) => {
      resolve(res as object);
    }).catch((error: unknown) => {
      reject(error);
    });
  });

}

export async function uploadSettingFiles (formData: uploadSettingsFiles):Promise<object> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/project/uploadSettingFiles`, body: formData}).then((res) => {
      resolve(res as object);
    }).catch((error: unknown) => {
      reject(error);
    });
  });

}

export async function GetApplicationList (): Promise<Project> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({url: `${api_url}/larkxr/GetApplicationList`}).then((res) => {
      resolve(res as Project);
    }).catch((error: unknown) => {
      reject(error);
    });
  });

}
