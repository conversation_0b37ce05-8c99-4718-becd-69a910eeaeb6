<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
// import SideBar from '../../../components/Projects/SideBar.vue';
import ProjectUnits from '../../../components/Projects/units/UnitsFile.vue';
import { UserStore } from '../../../store/index';
import SideNavBar from '@/components/common/SideNavBar.vue';
const userStore = UserStore();
</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar/>
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
                <ProjectUnits />
                <router-view></router-view>
            </div>
        </div>
    </div></template>
