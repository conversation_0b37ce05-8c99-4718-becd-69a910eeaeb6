<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { editTourSchema } from '../../../validationSchema/tour/index';
import { ProjectStore } from '../../../store/project';
import { tourType, tourCategory } from '@/types/virtualTour';
import Spinner from '@/components/common/Spinner.vue';
import { ConvertExternalTour, UpdateVirtualTour, AddTourLabel, UpdateTourLabel, DeleteTourLabel } from '@/api/projects/tours';
import { extractFirebaseStoragePath, truncateString } from '@/helpers/helpers';
import Button from '@/components/common/Button.vue';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

const route = useRoute();
const projectStore = ProjectStore();
const emit = defineEmits(['close']);
const projectId = ref(route.params.project_id);
const tourId = ref(null);
const initialData = ref(null);
const fetchLoader = ref(false);
const fetchStatus = ref('');
const props = defineProps({
  previousData: {
    type: Object,
    required: true,
  },
});
const categorySelection = ref(null);
const selectionTourType = ref(null);
const cameraFileName = ref(null), modelFileName = ref(null);
const loader = ref(false);

if (props.previousData) {
  const { id, type, model, camera, space_id, name, description, category, unitplan_id } = props.previousData;
  tourId.value = id;
  initialData.value = { name, description, type, category, unitplan_id };

  categorySelection.value = category;
  selectionTourType.value = type;

  if (type === tourType.EXTERNAL) {
    initialData.value.link = props.previousData.link || null;
  } else if (type === tourType.MLE) {
    initialData.value.model = model || null;
    modelFileName.value = model;
    cameraFileName.value = camera;
    initialData.value.camera = camera || null;
  } else if (type === tourType.MATTERPORT) {
    initialData.value.space_id = space_id || null;
  }

  console.log(initialData, "initialData");
}

/* Methods */
const compareValues = (values, prevData) => {
  const finalObj = {};
  console.log(values, prevData);
  const sourceValues = {...values};
  const compareValues = {...prevData};
  Object.keys(sourceValues).forEach((key) => {
    if (sourceValues[key] !== compareValues[key]){
      finalObj[key] = sourceValues[key];
    }
  });
  return finalObj;
};

// Upload
const handleImageUpload = (event, imgType) => {
  const file = event.target.files[0];
  if (imgType === 'model'){
    modelFileName.value = file;
  } else {
    cameraFileName.value = file;
  }
};
// Convert Tour
const convertTour = () => {
  const linkValue = initialData.value.link;
  const description = initialData.value.description;
  const category = initialData.value.category;

  if (!linkValue) {
    fetchStatus.value = 'Please enter a valid URL';
    return;
  }

  fetchLoader.value = true;
  fetchStatus.value = 'Processing tour conversion...';

  ConvertExternalTour(tourId.value, projectId.value, linkValue, description, category )
    .then((result) => {
      fetchStatus.value = 'Tour updated successfully with external data!';
      projectStore.SyncMultipleVirtualTours({ [result._id]: result}); // update the tour in store

      emit('close');
    })
    .catch((error) => {
      fetchStatus.value = `Error: ${error.message || 'Failed to convert tour'}`;
      console.error('Error converting tour:', error);
    })
    .finally(() => {
      fetchLoader.value = false;
    });
};

const processCameraFileAndSyncLabels = async (cameraFile) => {
  // Convert object of objects to array, and keep the key for label_id
  const existingLabelsObj = props.previousData.labels || {};
  const existingLabels = Object.entries(existingLabelsObj).map(([key, label]) => ({
    ...label,
    label_id: key,
  }));
  const existingLabelMap = {};
  existingLabels.forEach((label) => {
    if (label.camera_name) {
      existingLabelMap[label.camera_name] = label;
    }
  });

  const gltfloader = new GLTFLoader();
  const cameraURL = URL.createObjectURL(cameraFile);

  await new Promise((resolve, reject) => {
    gltfloader.load(cameraURL, async (gltf) => {
      const model = gltf.scene;
      const addPromises = [];
      const updateQuery = [];
      const deletePromises = [];
      const newCameraNames = [];
      const cameraNameToLabelId = {};

      // 1. Traverse and collect camera names in order
      model.traverse((child) => {
        if (child.type === "PerspectiveCamera") {
          if (!child.name.toLowerCase().startsWith("cam_")) {
            newCameraNames.push(child.name);
          }
        }
      });

      // 2. For each camera, add or update
      newCameraNames.forEach((name, idx) => {
        const basePayload = {
          name: name.replace(/_/g, " "),
          camera_name: name,
          controls_target: { x: 0, y: 0, z: 0 },
          camera_position: model.getObjectByName(name)?.position || {},
        };
        if (existingLabelMap[name]) {
          // Collect for bulk update (do NOT update order here)
          updateQuery.push({
            label_id: existingLabelMap[name].label_id,
            ...basePayload,
          });
          cameraNameToLabelId[name] = existingLabelMap[name].label_id;
        } else {
          // Create new label with correct order
          const createPayload = {
            ...basePayload,
            tour_id: tourId.value,
            project_id: projectId.value,
            order: idx + 1,
          };
          addPromises.push(
            AddTourLabel(createPayload).then((res) => {
              const labelId = Object.keys(res.labels).filter((key) => res.labels[key].camera_name === name)[0];
              if (labelId) {
                cameraNameToLabelId[name] = labelId;
              }
            }),
          );
        }
      });

      // 3. Handle deletions
      for (const label of existingLabels) {
        if (label.camera_name && !newCameraNames.includes(label.camera_name)) {
          deletePromises.push(DeleteTourLabel({
            label_id: label.label_id,
            project_id: projectId.value,
            tour_id: tourId.value,
          }));
        }
      }

      // 4. Wait for all adds and deletes to finish
      await Promise.all([...addPromises, ...deletePromises]);

      // 5. Bulk update all label fields for existing labels
      if (updateQuery.length > 0) {
        await UpdateTourLabel({
          project_id: projectId.value,
          tour_id: tourId.value,
          query: updateQuery,
        });
      }

      // 6. Fill in label_ids for new labels if not already set (fallback to existingLabelMap)
      newCameraNames.forEach((name) => {
        if (!cameraNameToLabelId[name] && existingLabelMap[name]) {
          cameraNameToLabelId[name] = existingLabelMap[name].label_id;
        }
      });

      // 7. Build the order query
      const orderQuery = newCameraNames.map((name, idx) => ({
        label_id: cameraNameToLabelId[name],
        order: idx + 1,
      }));

      // 8. Bulk update order for all labels
      if (orderQuery.length > 0) {
        await UpdateTourLabel({
          project_id: projectId.value,
          tour_id: tourId.value,
          query: orderQuery,
        });
      }

      URL.revokeObjectURL(cameraURL);
      resolve();
    }, undefined, reject);
  });
};

// Save(Submit)
const handleSubmitForm = async (values) => {
  console.log(values, tourId.value, "handleSubmitForm");
  loader.value = true; // open the loader
  const formData = new FormData();
  const changedValues = compareValues(values, initialData.value);
  if (Object.keys(changedValues).length > 0){
    console.log("If", changedValues);

    Object.keys(changedValues).forEach((key) => {
      formData.append(key, changedValues[key]);
    });
    formData.append('tour_id', tourId.value);
    formData.append('project_id', projectId.value);

    try {
      const res = await UpdateVirtualTour(formData);
      console.log({ [res._id]: res});
      projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // update the tour in store
      // If MLE and camera file is a File, process labels
      if (selectionTourType.value === tourType.MLE && cameraFileName.value instanceof File) {
        await processCameraFileAndSyncLabels(cameraFileName.value);
      }
      loader.value = false; // reset
      emit('close');
    } catch (err) {
      console.log("Error", err);
      loader.value = false;
    }
  } else {
    console.log("Else");
    loader.value = false; // reset
    emit('close');
  }
};

</script>

<template>
    <div class="select-none fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg w-[643px] p-4 flex flex-col justify-start items-start gap-4 overflow-hidden">
        <div class="flex justify-between items-center w-full">
          <h4 class="text-gray-900 text-xl font-bold mb-0 select-none">Edit VR Tours</h4>
          <svg :class="['w-2.5 h-2.5 cursor-pointer', loader ? 'pointer-events-auto' : 'pointer-events-auto']"  viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg"  @click="() => emit('close')">
              <g>
              <path d="M5.90741 5L9.30409 1.60332C9.36538 1.54412 9.41427 1.47331 9.4479 1.39502C9.48153 1.31672 9.49924 1.23252 9.49998 1.14731C9.50072 1.0621 9.48448 0.977595 9.45221 0.898729C9.41995 0.819863 9.3723 0.748212 9.31204 0.687958C9.25179 0.627705 9.18014 0.580054 9.10127 0.547787C9.0224 0.515521 8.9379 0.499284 8.85269 0.500024C8.76748 0.500765 8.68328 0.518468 8.60498 0.5521C8.52669 0.585733 8.45588 0.634621 8.39668 0.695913L5 4.09259L1.60332 0.695913C1.48229 0.579017 1.32019 0.514333 1.15193 0.515796C0.983666 0.517258 0.822712 0.584748 0.70373 0.70373C0.584748 0.822712 0.517258 0.983666 0.515796 1.15193C0.514333 1.32019 0.579017 1.48229 0.695913 1.60332L4.09259 5L0.695913 8.39668C0.634621 8.45588 0.585733 8.52669 0.5521 8.60498C0.518468 8.68328 0.500765 8.76748 0.500024 8.85269C0.499284 8.9379 0.515521 9.0224 0.547787 9.10127C0.580054 9.18014 0.627705 9.25179 0.687958 9.31204C0.748212 9.3723 0.819863 9.41995 0.898729 9.45221C0.977595 9.48448 1.0621 9.50072 1.14731 9.49998C1.23252 9.49924 1.31672 9.48153 1.39502 9.4479C1.47331 9.41427 1.54412 9.36538 1.60332 9.30409L5 5.90741L8.39668 9.30409C8.51771 9.42098 8.67981 9.48567 8.84807 9.4842C9.01633 9.48274 9.17729 9.41525 9.29627 9.29627C9.41525 9.17729 9.48274 9.01633 9.4842 8.84807C9.48567 8.67981 9.42098 8.51771 9.30409 8.39668L5.90741 5Z" fill="#6B7280"/>
              </g>
          </svg>
        </div>

        <Form v-slot="{errorBag}" v-if="initialData" :validation-schema="editTourSchema"
                    @submit="handleSubmitForm" :initial-values="initialData" class="flex flex-col justify-start items-start gap-5 w-full mb-0">
          <div class="flex flex-col justify-start items-start gap-5 w-full max-h-[80vh] overflow-y-auto hide-scroll-bar px-1">
            <div class="w-full flex flex-col justify-start items-start gap-2">
              <label for="name" class="mb-0 text-gray-900 text-sm font-medium">Enter name*</label>
              <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field
                  type="text"
                  name="name"
                  id="name"
                  :class="[ errorBag?.name ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 bg-gray-50 placeholder:text-gray-500 outline-gray-300' ,'!px-4 !py-3 w-full leading-none  text-left placeholder:text-left h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px] border-none shadow-none']"
                  placeholder="Enter name"
                />
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="name"/>
              </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2">
               <label for="description" class="mb-0 text-gray-900 text-sm font-medium">Description*</label>
               <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field
                  type="text"
                  name="description"
                  id="description"
                  :class="[ errorBag?.description ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 placeholder:text-gray-500 bg-gray-50 outline-gray-300' ,'!px-4 !py-3 w-full leading-none  text-left placeholder:text-left  h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px] border-none shadow-none']"
                  placeholder="Enter description"
                />
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="description" />
               </div>
            </div>

          <div class="w-full flex flex-col justify-start items-start gap-2">
                <label for="category" class="mb-0 text-gray-900 text-sm font-medium">Category*</label>
                <div class="w-full flex flex-col justify-start items-start gap-2">
                    <Field name="category" v-model="categorySelection">
                              <div
                                class="flex justify-start items-start gap-3">
                                  <span v-for="item in tourCategory" :key="item" :class="['p-2 capitalize block w-24 h-9 text-center rounded-lg outline outline-1 outline-offset-[-1px] outline-[#1C64F2] !text-sm font-medium cursor-pointer', categorySelection === item ? 'bg-[#1C64F2] text-white' : 'bg-transparent text-[#1C64F2]' ]"  @click="() => categorySelection = item">
                                        {{ item }}
                                  </span>
                              </div>
                    </Field>
                    <ErrorMessage as="category"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="category" />
                </div>
          </div>

          <div class="w-full flex flex-col justify-start items-start gap-2">
              <div class="w-full flex justify-start items-center gap-2">
                <label for="type" class="mb-0 text-gray-900 text-sm font-medium">
                 Tour Type*</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>
              <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field name="type" v-model="selectionTourType">
                            <div
                              class="flex justify-start items-start gap-3">
                                <span v-for="item in tourType" :key="item" :class="['p-2 capitalize block w-24 h-9 text-center rounded-lg outline outline-1 outline-offset-[-1px] outline-[#1C64F2] !text-sm font-medium cursor-pointer', selectionTourType === item ? 'bg-[#1C64F2] text-white' : 'bg-transparent text-[#1C64F2]' ]"  @click="() => selectionTourType = item">
                                      {{ item }}
                                </span>
                            </div>
                </Field>
                <ErrorMessage as="type"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="type" />
              </div>
            </div>

        <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.EXTERNAL">
              <div class="w-full flex justify-start items-center gap-2 ">
                <label for="link" class="mb-0 text-gray-900 text-sm font-medium">
                 Link*</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>
              <div class="w-full flex flex-col justify-start items-start gap-2">
                <div class="flex justify-start items-center w-full gap-2">
                <Field
                  type="url"
                  name="link"
                  id="link"
                  :class="[ errorBag?.link ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 placeholder:text-gray-500 bg-gray-50 outline-gray-300','!px-4 !py-3 flex-1 leading-none text-left placeholder:text-left h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px] border-none shadow-none']"
                  placeholder="Enter link url"
                />
               <button
                  type="button"
                  class="!px-0 py-2 !w-20 shrink-0 !bg-[#1C64F2] border-none outline-none text-white rounded flex justify-center items-center text-sm"
                  @click="convertTour"
                  :disabled="fetchLoader"
                >
                  <span v-if="!fetchLoader">Convert</span>
                  <span v-else class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Converting
                  </span>
                </button>
                 </div>
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="link" />
                                 <p v-if="fetchStatus" :class="fetchStatus.includes('Error') ? 'text-rose-500 text-sm mt-1' : 'text-green-500 text-sm mt-1'">
                {{ fetchStatus }}
              </p>
                </div>
            </div>

          <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.MATTERPORT">
              <div class="w-full flex justify-start items-center gap-2 ">
                <label for="space_id" class="mb-0 text-gray-900 text-sm font-medium">
                Space ID*</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>

               <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field
                  type="text"
                  name="space_id"
                  id="space_id"
                  :class="[errorBag?.space_id ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 placeholder:text-gray-500 bg-gray-50 outline-gray-300' ,'!px-4 !py-3 w-full leading-none text-left placeholder:text-left h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px]  border-none shadow-none']"
                  placeholder="Enter space_id"
                />
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="space_id" />
                </div>
            </div>

           <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.MLE">
              <div class="w-full flex justify-start items-center gap-2 ">
                <label for="model" class="mb-0 text-gray-900 text-sm font-medium">
                  Model*</label>
                    <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g >
                    <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                    </g>
                  </svg>
              </div>
              <div class="w-full flex flex-col justify-start items-start gap-2">

                <Field
                            type="file"
                                    name="model"
                                    id="model"
                                    autocomplete="model"
                                    class="hidden"
                                    @change="(e) => handleImageUpload(e, 'model')"
                                    placeholder="Upload Model glb" />

                <div :class="[ errorBag?.model ? 'bg-red-50 outline-red-500' : 'bg-gray-50 outline-gray-300' ,'w-full !px-4 flex justify-start items-center rounded-lg outline outline-1 outline-offset-[-0.50px] h-8' ]">
                        <div class="flex w-full justify-between items-center">
                          <p class=" text-gray-900 text-xs font-medium "> {{ typeof modelFileName === 'string' ? truncateString(extractFirebaseStoragePath(modelFileName, 'filename'), 20) : modelFileName.name }}</p>
                          <label v-if="typeof modelFileName === 'string'" for="model"  class="w-4 h-4 mb-0 cursor-pointer">
                            <svg class="w-full h-full" fill="none" stroke="#6B7280" viewBox="0 0 24 24">
                                  <path fill="#6B7280" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a2 2 0 002 2h12a2 2 0 002-2v-1M12 12V4m0 0l-4 4m4-4l4 4"/>
                            </svg>
                          </label>
                          <svg v-else class="w-4 h-4 cursor-pointer" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" @click="() => modelFileName = initialData.model">
                                            <g >
                                            <path d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM10.9656 9.8344C11.042 9.90819 11.103 9.99647 11.1449 10.0941C11.1868 10.1917 11.2089 10.2967 11.2098 10.4029C11.2107 10.5091 11.1905 10.6144 11.1503 10.7128C11.11 10.8111 11.0506 10.9004 10.9755 10.9755C10.9004 11.0506 10.8111 11.11 10.7128 11.1503C10.6144 11.1905 10.5091 11.2107 10.4029 11.2098C10.2967 11.2089 10.1917 11.1868 10.0941 11.1449C9.99648 11.103 9.9082 11.042 9.8344 10.9656L8 9.1312L6.1656 10.9656C6.01472 11.1113 5.81264 11.192 5.60288 11.1901C5.39312 11.1883 5.19247 11.1042 5.04415 10.9559C4.89582 10.8075 4.81169 10.6069 4.80986 10.3971C4.80804 10.1874 4.88868 9.98528 5.0344 9.8344L6.8688 8L5.0344 6.1656C4.88868 6.01472 4.80804 5.81263 4.80986 5.60288C4.81169 5.39312 4.89582 5.19247 5.04415 5.04414C5.19247 4.89582 5.39312 4.81168 5.60288 4.80986C5.81264 4.80804 6.01472 4.88867 6.1656 5.0344L8 6.8688L9.8344 5.0344C9.98528 4.88867 10.1874 4.80804 10.3971 4.80986C10.6069 4.81168 10.8075 4.89582 10.9559 5.04414C11.1042 5.19247 11.1883 5.39312 11.1901 5.60288C11.192 5.81263 11.1113 6.01472 10.9656 6.1656L9.1312 8L10.9656 9.8344Z" fill="#6B7280"/>
                                            </g>
                            </svg>
                        </div>
                </div>

                <ErrorMessage
                  as="p"
                  class="text-red-600 text-sm font-normal w-full lowercase"
                  name="model"
                />
                 </div>
            </div>

                <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.MLE">
              <div class="w-full flex justify-start items-center gap-2 ">
                <label for="camera" class="mb-0 text-gray-900 text-sm font-medium">
                  Camera*</label>
                    <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g >
                    <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                    </g>
                  </svg>
              </div>
              <div class="w-full flex flex-col justify-start items-start gap-2">

                <Field
                            type="file"
                                    name="camera"
                                    id="camera"
                                    autocomplete="camera"
                                    class="hidden"
                                    @change="(e) => handleImageUpload(e, 'camera')"
                                    placeholder="Upload Camera gltb" />

                <div :class="[ errorBag?.camera ? 'bg-red-50 outline-red-500' : 'bg-gray-50 outline-gray-300' ,'w-full !px-4 flex justify-start items-center rounded-lg outline outline-1 outline-offset-[-0.50px] h-8' ]">
                        <div class="flex w-full justify-between items-center">
                          <p class=" text-gray-900 text-xs font-medium "> {{ typeof cameraFileName === 'string' ? truncateString(extractFirebaseStoragePath(cameraFileName, 'filename'), 20) : cameraFileName.name }}</p>
                          <label v-if="typeof cameraFileName === 'string'" for="camera"  class="w-4 h-4 mb-0 cursor-pointer">
                            <svg class="w-full h-full" fill="none" stroke="#6B7280" viewBox="0 0 24 24">
                                  <path fill="#6B7280" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a2 2 0 002 2h12a2 2 0 002-2v-1M12 12V4m0 0l-4 4m4-4l4 4"/>
                            </svg>
                          </label>
                          <svg v-else class="w-4 h-4 cursor-pointer" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" @click="() => cameraFileName = initialData.camera">
                                            <g >
                                            <path d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM10.9656 9.8344C11.042 9.90819 11.103 9.99647 11.1449 10.0941C11.1868 10.1917 11.2089 10.2967 11.2098 10.4029C11.2107 10.5091 11.1905 10.6144 11.1503 10.7128C11.11 10.8111 11.0506 10.9004 10.9755 10.9755C10.9004 11.0506 10.8111 11.11 10.7128 11.1503C10.6144 11.1905 10.5091 11.2107 10.4029 11.2098C10.2967 11.2089 10.1917 11.1868 10.0941 11.1449C9.99648 11.103 9.9082 11.042 9.8344 10.9656L8 9.1312L6.1656 10.9656C6.01472 11.1113 5.81264 11.192 5.60288 11.1901C5.39312 11.1883 5.19247 11.1042 5.04415 10.9559C4.89582 10.8075 4.81169 10.6069 4.80986 10.3971C4.80804 10.1874 4.88868 9.98528 5.0344 9.8344L6.8688 8L5.0344 6.1656C4.88868 6.01472 4.80804 5.81263 4.80986 5.60288C4.81169 5.39312 4.89582 5.19247 5.04415 5.04414C5.19247 4.89582 5.39312 4.81168 5.60288 4.80986C5.81264 4.80804 6.01472 4.88867 6.1656 5.0344L8 6.8688L9.8344 5.0344C9.98528 4.88867 10.1874 4.80804 10.3971 4.80986C10.6069 4.81168 10.8075 4.89582 10.9559 5.04414C11.1042 5.19247 11.1883 5.39312 11.1901 5.60288C11.192 5.81263 11.1113 6.01472 10.9656 6.1656L9.1312 8L10.9656 9.8344Z" fill="#6B7280"/>
                                            </g>
                            </svg>
                        </div>
                </div>

                <ErrorMessage
                  as="p"
                  class="text-red-600 text-sm font-normal w-full lowercase"
                  name="camera"
                />
                 </div>
            </div>

                 <div class="w-full flex flex-col justify-start items-start gap-2 mb-2">
               <div class="w-full flex justify-start items-center gap-2 ">
                <label for="unitplan_id" class="mb-0 text-gray-900 text-sm font-medium">
                 Unit Plan</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>

              <!-- select-primary -->
               <div class="w-full flex flex-col justify-start items-start gap-2">
                            <Field
                                as="select" type="text"
                                name="unitplan_id" id="unitplan_id"
                                autocomplete="unitplan_id"
                                class="!px-4 !py-3 w-full leading-none text-gray-900 text-left placeholder:text-left placeholder:text-gray-500 h-[42px] text-sm font-normal bg-gray-50 rounded-lg outline outline-1 outline-offset-[-1px] outline-gray-300 border-none shadow-none"
                                placeholder="Select Unit plan">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option
                                    v-if="!projectStore.unitplans || Object.keys(projectStore.unitplans).length === 0"
                                    value=""
                                    class="text-black">
                                   No data found!
                                </option>
                                <option
                                    v-else
                                    :value="option._id"
                                    v-for="option in projectStore.unitplans"
                                    :key="option._id"
                                    class="text-black text-sm font-normal">
                                    {{ option.name }}
                                </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="unitplan_id" />
              </div>
            </div>

          </div>

          <div class="mb-0 flex justify-end items-center w-full">
           <!-- <button
              type="button"
              class="cancel-btn-primary"
              @click=" () => emit('close')"
            >
              Cancel
            </button> -->
            <Button :disabled="loader" type="submit" title="Save" theme="primary" class="!w-[185px] !h-[41px]">
                  <template v-slot:svg>
                    <Spinner v-if="loader" class="!fill-white" />
                  </template>
            </Button>
          </div>
        </Form>
    </div>
</template>

<style scoped>
</style>
