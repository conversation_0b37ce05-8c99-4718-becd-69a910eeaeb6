import { PostRequestWithHeaders, GetRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

type deleteImage = {
  image_id: string,
  tour_id: string,
}

type replaceImage = {
  project_id:string,
  tour_id:string,
  image_id:string,
  thumbnail:Blob,
  thumbnailName:string,
  url:Blob,
  urlName:string
}
type createImage = {
  project_id:string,
  tour_id:string,
  name:string,
  thumbnail:Blob,
  thumbnailName:string,
  url:Blob,
  urlName:string
}
type updateImage = {
  tour_id:string,
  name:string,
  image_id:string,
}

export async function createImage (data:createImage) {
  if (data) {
    const imageFormData = new FormData();
    imageFormData.append('project_id', data.project_id);
    imageFormData.append('tour_id', data.tour_id);
    imageFormData.append('name', data.name);
    imageFormData.append('thumbnail', data.thumbnail, data.thumbnailName);
    imageFormData.append('url', data.url, data.urlName);

    try {
      const res = await PostRequestWithHeaders({
        url: `${api_url}/customTour/createImage`,
        body: imageFormData,
      });
      return { status: 'success',  data: res };
    } catch (err) {
      console.error(err);
      return { status: 'error',  error: err };
    }
  } else {
    return { status: 0, error: 'No data provided' };
  }
}

export async function updateImage (values:updateImage) {
  if (values) {
    try {
      const res = await PostRequestWithHeaders({
        url: `${api_url}/customTour/updateImage`,
        body: {
          name: values.name,
          tour_id: values.tour_id,
          image_id: values.image_id,
          // Rotation:{
          //     X:values.rotation.x,
          //     Y:values.rotation.y,
          //     Z:values.rotation.z,
          // }
        },
      });
      return { status: 'success', res };
    } catch (err) {
      console.error(err);
      return { status: 'error',  err };
    }
  } else {
    return { status: 0, error: 'No data provided' };
  }
}

export async function replaceImage (data:replaceImage) {
  if (data) {
    const replaceFormData = new FormData();
    replaceFormData.append('project_id', data.project_id);
    replaceFormData.append('tour_id', data.tour_id);
    replaceFormData.append('image_id', data.image_id);
    replaceFormData.append('thumbnail', data.thumbnail, data.thumbnailName);
    replaceFormData.append('url', data.url, data.urlName);

    try {
      const res = await PostRequestWithHeaders({
        url: `${api_url}/customTour/replaceImage`,
        body: replaceFormData,
      });
      return { status: 'success',  res };
    } catch (err) {
      console.error(err);
      return { status: 'error', err };
    }
  } else {
    return { status: 0, error: 'No data provided' };
  }
}

export async function deleteImage (values: deleteImage) {
  if (values) {
    try {
      const res = await PostRequestWithHeaders({
        url: `${api_url}/customTour/deleteImage`,
        body: {
          tour_id: values.tour_id,
          image_id: values.image_id,
        },
      });
      return res;
    } catch (err) {
      return err;
    }
  } else {
    return { status: 0, error: 'No data provided' };
  }
}

export async function getImages (project_id:string, tour_id:string) {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/customTour/getImages?project_id=${project_id}&tour_id=${tour_id}` }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });

}
