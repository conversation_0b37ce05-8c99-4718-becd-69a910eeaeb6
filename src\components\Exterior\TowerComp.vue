<script setup>
import { buildingSchema } from '@/validationSchema/building';
import { onBeforeUnmount, ref, defineProps, watch, onMounted, nextTick } from 'vue';
import { Form, Field} from "vee-validate";
import FilesUploader from './FilesUploader.vue';
import { useRoute } from 'vue-router';
import { ProjectStore } from '@/store/project';
import Modal from '../common/Modal/Modal.vue';
import { processFirebaseFile } from '@/helpers/helpers';
import { createBuilding, bulkUpdateFloors, updateBuilding } from '@/api/projects/buildings/index';
import { getListofAssets } from '@/api/projects/assets';
import FloorSwitcher from './FloorSwitcher.vue';
import imgPreviewModal from '../common/Modal/imgPreviewModal.vue';
const route = useRoute();
const projectStore = ProjectStore();

const { row, index, id} = defineProps({
  row: {type: Object, default: () => ({})},
  index: {type: String, default: ''},
  id: {type: String, default: ''},
});

const emit = defineEmits(['deleteRow', 'openDeleteModal', 'handleImgPreview', 'refreshData']);

const filesData = ref({}), showSelector = ref(false),  selectedFile = ref(null);
const projectId = route.params.project_id, copied = ref(false), showLoader = ref(false);
const uploadControllers = ref({}), selectedRowId = ref(null), uploadStatus = ref({}), currentFileObj =ref(null), initialFileObj=ref(null);
const showUpdatefloors = ref(false), isEdited = ref(false), imgModal = ref({ status: false, url: '' });
const rowRef = ref(null);

function deepCopy (obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map(deepCopy);
  }
  const copy = {};
  for (const key in obj) {
    copy[key] = deepCopy(obj[key]);
  }
  return copy;
}

const initialValues = ref(deepCopy(row));
watch(() => row, (newVal) => {
  initialValues.value = deepCopy(newVal);
}, { deep: true, immediate: true });

const handleUpdateData = () => {
  emit('refreshData');
};

if (route.params.project_id) {
  projectStore.RefreshAssets(route.params.project_id);

  getListofAssets(route.params.project_id)
    .then((res) => {
      filesData.value = res.assets.items;
    })
    .catch((err) => {
      console.log("Error:", err);
    });
}

if (row?.isNew || initialValues.value.isNew === undefined ) {
  processFirebaseFile(initialValues.value.thumbnail).then((res) => {
    initialFileObj.value = res;
  });
}

const Towertypes = [
  { name: 'Tower', value: 'tower' },
  { name: 'Villa', value: 'villa' },
];

// Show selector for specific row
const openSelector = (e, rowId) => {
  showSelector.value = !showSelector.value;
  selectedRowId.value = rowId;
};

const getFileName = (url) => {
  try {
    const decodedUrl = decodeURIComponent(url); // Decode URL encoding
    const pathname = new URL(decodedUrl).pathname; // Extract pathname
    const fullFileName = pathname.split('/').pop().split('?')[0]; // Get filename with timestamp

    // Remove the timestamp (if present) before the file extension
    return fullFileName.replace(/(_\d+)(?=\.)/, '');
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
};

// Simulate file upload with progress
const simulateFileUpload = async (index) => {
  return new Promise((resolve) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      uploadStatus.value[index] = {
        ...uploadStatus.value[index],
        progress,
      };
      if (progress >= 100) {
        clearInterval(interval);
        resolve();
      }
    }, 300);
  });
};
const filePreviewUrl= ref(null);
// Handle file selection
const handleFileSelect = async (event, selectedfile) => {
  isEdited.value = true;
  const file = event?.target?.files?.[0]  || selectedfile;
  if (!file) {
    return;
  }
  showSelector.value = false;
  try {
    // initialValues.value.file = file
    currentFileObj.value = file;
    if (file?.type?.startsWith('image/')) {
      filePreviewUrl.value = URL.createObjectURL(file);
    } else {
      filePreviewUrl.value = selectedFile;
    }
    uploadStatus.value[index] = {
      status: 'uploading',
      progress: 0,
      fileName: file.name || file.file_name,
    };

    await simulateFileUpload(index);
    uploadStatus.value[index] = {
      ...uploadStatus.value[index],
      status: 'completed',
    };
  } catch (error) {
    console.error('File upload error:', error);
    uploadStatus.value[index] = {
      ...uploadStatus.value[index],
      status: 'error',
    };
  }
};

// Remove file function
const removeFile = (index) => {
  isEdited.value= true;
  if (initialValues.value.isNew){
    delete uploadStatus.value[index];
  } else {
    if (uploadStatus.value[index]){
      delete uploadStatus.value[index];
    } else {
      delete initialValues.value.thumbnail;
    }
  }
  selectedFile.value = null;
};

// Clean up on unmount
onBeforeUnmount(() => {
  Object.values(uploadControllers.value).forEach((controller) => {
    controller?.abort();
  });
});

// Tooltip popUp
const templateElementForTooltip = (left, top, msg) => {
  const div = document.createElement("div");
  div.setAttribute('data-name', 'toursImagesTooltip');
  div.classList.add(...['flex', 'absolute', 'text-xs', 'bg-[#111111]', 'text-white', 'text-left', 'z-20', 'px-3', 'py-[3px]', 'rounded-[3px]', 'whitespace-nowrap', '-translate-x-[50%]', 'min-w-[auto]', 'max-w-fit', 'mt-2', 'custom_shadow']);
  div.style.left = `${left}px`;
  div.style.top = `${top}px`;
  div.innerText = msg; // message
  return div;
};
const handleOpenTooltip = (e, msg) => {
  document.body.appendChild(templateElementForTooltip(e.clientX, e.clientY + 12, msg));
};
const handleCloseTooltip = () => {
  const toolTipElement = document.querySelector("[data-name='toursImagesTooltip']");
  toolTipElement.remove(); // remove directly from dom
};

function getObjectDifference (previousObj, newObj) {
  const differences = {};

  // Check for changed or new properties
  for (const key in newObj) {
    // Skip any falsy values in new object
    if (key==='modified'){
      continue;
    }

    if (!newObj[key]) {
      continue;
    }

    // If property is new or value has changed
    if (!(key in previousObj) || previousObj[key] !== newObj[key]) {
      differences[key] = newObj[key];
    }
  }

  return differences;
}

// Load communities on component mount
projectStore.RefreshCommunities(projectId);
projectStore.RefreshBuildings(projectId);

const trackChanges = () => {
  isEdited.value = true;
};

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(initialValues.value._id);
    copied.value = true;
    setTimeout(() => (copied.value = false), 1000);
  } catch (e) {
    console.error('Failed to copy ID:', e);
  }
};

const handleSubmit = async (val) => {
  showLoader.value = true;
  if (!val) {
    console.error("Validation failed");
    return;
  }

  if (initialValues.value.isNew) {
    try {
      const formData = new FormData();
      formData.append('name', val.name);
      formData.append('project_id', projectId);
      formData.append('total_floors', val.total_floors);
      formData.append('community_id', val.community_id || '');
      formData.append('type', val.type || 'tower');
      // formData.append('thumbnail', val.file);

      // Handle file if it exists
      if (val.file instanceof File) {
        // const resizedThumbnail = await resizeImage(val.file, 720, 720);
        formData.append("thumbnail", val.file);
      }
      await createBuilding(formData);
      showLoader.value = false;
      emit('refreshData', id);
      document.dispatchEvent(new Event("refreshBuildings"));
    } catch (error) {
      console.error("Error creating building:", error);
    }
  } else {
    const newObj  = getObjectDifference(row, val);
    const formdata = new FormData();
    if (Object.keys(newObj).length > 0 || currentFileObj.value ){
      newObj.name && formdata.append('name', newObj.name);
      newObj.type && formdata.append('type', newObj.type);
      newObj.community_id && formdata.append('community_id', newObj.community_id);
      currentFileObj.value  && formdata.append('thumbnail',  currentFileObj.value );
      formdata.append('project_id', projectId);
      formdata.append('building_id', id);

      updateBuilding(id, formdata ).then(() => {
        showLoader.value = false;
        isEdited.value = false;
        emit('refreshData');
      }).catch((err) => {
        console.log('err', err);
      });

    }
  }
};

const handleFloorUpdate = (val) => {
  const transformData = (obj, projectId, id) => {
    return {
      query: Object.values(obj),
      project_id: projectId,
      building_id: id,
    };
  };

  // Example usage
  const payload = transformData(val, projectId, id);

  bulkUpdateFloors(payload).then(() => {
    emit('refreshData');
  }).catch((err) => {
    console.log('err', err);
  });

};

const handleAddFloor = () => {
  const currentFloorData = {...initialValues.value.floors};
  const floorValues = Object.values(currentFloorData);

  // Find the highest numeric floor_id, ignoring non-numeric values
  const numericFloorIds = floorValues
    .map((floor) => floor.floor_id)
    .filter((id) => !isNaN(parseInt(id)))
    .map((id) => parseInt(id));

  // Get next available numeric ID
  const nextFloorId = numericFloorIds.length > 0 ? Math.max(...numericFloorIds) + 1 : 1;

  // Find the highest order value
  const highestOrder = Math.max(...floorValues.map((floor) => floor.order), 0);

  // Create new floor object
  const newFloor = {
    floor_id: nextFloorId.toString(), // Convert back to string
    floor_name: `Floor ${nextFloorId}`,
    units: [],
    order: highestOrder + 1,
    isNew: true,
    sno: currentFloorData.length + 1,
  };

  // Add to existing data
  // currentFloorData.push(newFloor);

  currentFloorData[nextFloorId] = newFloor;
  // Update state
  initialValues.value.floors = currentFloorData;
};

onMounted(() => {
  if (row.isNew) {
    nextTick(() => {
      if (rowRef.value) {
        rowRef.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
        const input = rowRef.value.querySelector('input');
        if (input) {
          input.focus();
        }
      }
    });
  }
});
</script>

<template>
  <Form :validation-schema="buildingSchema" @submit="handleSubmit" v-slot="{ errors , meta  }" >
    <div ref="rowRef" class="flex w-full h-[50px] gap-6 relative" :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-100'">

        <div class="py-2 text-black text-sm relative flex items-center  ml-1 px-2 cursor-pointer w-32" @click="copyToClipboard">
            {{ !row.isNew ? '..' + initialValues._id.slice(-4)  : '' }}
            <span
              v-if="copied && !row.isNew"
              class="absolute -top-1 left-1/2 transform -translate-x-1/2 text-xs text-black"
            >
              Copied!
            </span>
        </div>

        <div class="py-3  text-sm relative flex items-center w-32" :class="errors.name ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="w-full " >
                <Field
                  name="name"
                  type="text"
                  v-model="initialValues.name"
                  placeholder="Enter name"
                  class="w-full px-2 py-2 placeholder:text-left   focus:outline-none text-sm leading-[21px]"
                  :class="[errors.name ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400', index % 2 === 0 ? 'bg-white' : '!bg-gray-100']"
                  id="name"
                  @input="trackChanges"
                />
              </div>
        </div>

        <div class="py-3 text-sm relative flex items-center w-32" :class="errors.total_floors ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="!w-[80%] flex">
                <Field
                  name="total_floors"
                  type="text"
                  v-model="initialValues.total_floors"
                  placeholder="Enter Floors"
                  class="w-[80%] p-1 placeholder:text-left focus:outline-none text-sm leading-[21px]"
                  :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.total_floors ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
                  id="name"
                  :disabled="row.isNew?false:true"
                  @input="trackChanges"
                />
              </div>
              <div v-if="!initialValues.isNew" @click="() => { showUpdatefloors = true }"  class="w-[20%] h-6 p-1 rounded-md flex justify-center items-center"
                 @mouseenter="(e) => { handleOpenTooltip(e,'Reorder Floors') }"  @mouseleave="handleCloseTooltip()"
              >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M9.7607 15.2924L8.26595 16.5853V8.99956C8.26595 8.73435 8.14416 8.48 7.92736 8.29247C7.71056 8.10494 7.41652 7.99959 7.10992 7.99959C6.80332 7.99959 6.50928 8.10494 6.29248 8.29247C6.07569 8.48 5.95389 8.73435 5.95389 8.99956V16.5853L4.45914 15.2924C4.24111 15.1102 3.94909 15.0094 3.64599 15.0117C3.34288 15.014 3.05293 15.1191 2.83859 15.3045C2.62426 15.4899 2.50268 15.7407 2.50004 16.0029C2.49741 16.2651 2.61393 16.5177 2.82451 16.7063L6.29261 19.7062C6.39999 19.7993 6.52756 19.8732 6.66801 19.9236C6.80846 19.9741 6.95902 20 7.11108 20C7.26314 20 7.4137 19.9741 7.55415 19.9236C7.69459 19.8732 7.82216 19.7993 7.92955 19.7062L11.3976 16.7063C11.6082 16.5177 11.7247 16.2651 11.7221 16.0029C11.7195 15.7407 11.5979 15.4899 11.3836 15.3045C11.1692 15.1191 10.8793 15.014 10.5762 15.0117C10.2731 15.0094 9.98104 15.1102 9.76301 15.2924H9.7607Z" fill="#9CA3AF"/>
                <path d="M17.1755 3.29273L13.7074 0.292829C13.5997 0.199512 13.4716 0.125738 13.3305 0.0758353C13.0477 -0.0252784 12.7301 -0.0252784 12.4473 0.0758353C12.3062 0.125738 12.1781 0.199512 12.0705 0.292829L8.60236 3.29273C8.39178 3.48133 8.27526 3.73393 8.27789 3.99611C8.28052 4.2583 8.4021 4.50911 8.61644 4.69451C8.83078 4.87991 9.12072 4.98508 9.42383 4.98736C9.72694 4.98963 10.019 4.88884 10.237 4.70669L11.734 3.41373V10.9995C11.734 11.2647 11.8558 11.519 12.0726 11.7066C12.2894 11.8941 12.5835 11.9995 12.8901 11.9995C13.1967 11.9995 13.4907 11.8941 13.7075 11.7066C13.9243 11.519 14.0461 11.2647 14.0461 10.9995V3.41373L15.5409 4.70669C15.7589 4.88884 16.0509 4.98963 16.354 4.98736C16.6571 4.98508 16.9471 4.87991 17.1614 4.69451C17.3757 4.50911 17.4973 4.2583 17.5 3.99611C17.5026 3.73393 17.3861 3.48133 17.1755 3.29273Z" fill="#9CA3AF"/>
              </svg>
              </div>
        </div>

        <div class="py-3 text-sm relative flex items-center !w-[255px] p-2" :class="errors.file ?'!bg-red-50 !text-red-700 placeholder-red-700' : ''">
          <Field name="file" :model-value="initialFileObj || currentFileObj" :class="errors.file ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400'">
                 <div class="flex flex-row items-center gap-3">
                   <div class="w-full">
                     <!-- Uploading status -->
                     <div v-if="uploadStatus[index]?.status === 'uploading'" class="w-full flex flex-row">
                       <div class="w-full flex flex-col">
                         <div class="flex flex-row gap-2  justify-between">
                           <p class="text-sm">
                             {{ uploadStatus[index]?.fileName || 'uploading..' }}
                           </p>
                           <p class="text-sm mt-1">
                             {{ uploadStatus[index]?.progress }}%
                           </p>
                         </div>
                         <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                           <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                             :style="{ width: `${uploadStatus[index]?.progress}%` }"></div>
                         </div>
                       </div>
                       <button class="p-1 hover:bg-gray-100 rounded-full">
                       </button>
                     </div>
                     <!-- Completed upload status -->
                     <div v-else-if="uploadStatus[index]?.status === 'completed' || ( !initialValues.isNew && initialValues.thumbnail)" class="flex justify-between gap-2 items-center">
                        <div v-if="!initialValues.isNew && initialValues.thumbnail">
                          <img :src="initialValues.thumbnail" alt="Preview" class="w-10 h-10 rounded-lg" loading="lazy" />
                        </div>
                        <p class="text-sm max-w-[93px] overflow-hidden text-ellipsis whitespace-nowrap"  @mouseenter="(e) => { handleOpenTooltip(e,uploadStatus[index]?.fileName || getFileName(initialValues.thumbnail)) }"  @mouseleave="handleCloseTooltip()">
                          {{ uploadStatus[index]?.fileName || getFileName(initialValues.thumbnail) }}
                        </p>
                        <div class="flex flex-row gap-3">
                          <div v-if="!initialValues.isNew" @click="() =>{ imgModal.status = true ; imgModal.url = row?.thumbnail} "
                              class="w-6 h-6 bg-gray-100 rounded-md flex justify-center items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/></svg>
                          </div>

                          <div  @click="removeFile(index)"  class="w-6 h-6 bg-gray-100 rounded-md flex justify-center items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                              <g clip-path="url(#clip0_1249_10593)">
                              <path d="M14.5179 3.36842H11.2586V1.68421C11.2586 1.23753 11.0869 0.809144 10.7813 0.493294C10.4757 0.177443 10.0612 0 9.62898 0L6.36972 0C5.93751 0 5.52301 0.177443 5.2174 0.493294C4.91178 0.809144 4.74009 1.23753 4.74009 1.68421V3.36842H1.48083C1.26473 3.36842 1.05748 3.45714 0.904669 3.61507C0.751862 3.77299 0.666016 3.98719 0.666016 4.21053C0.666016 4.43387 0.751862 4.64806 0.904669 4.80598C1.05748 4.96391 1.26473 5.05263 1.48083 5.05263H2.29565V14.3158C2.29565 14.7625 2.46734 15.1909 2.77295 15.5067C3.07857 15.8226 3.49307 16 3.92527 16H12.0734C12.5056 16 12.9201 15.8226 13.2257 15.5067C13.5314 15.1909 13.7031 14.7625 13.7031 14.3158V5.05263H14.5179C14.734 5.05263 14.9412 4.96391 15.094 4.80598C15.2468 4.64806 15.3327 4.43387 15.3327 4.21053C15.3327 3.98719 15.2468 3.77299 15.094 3.61507C14.9412 3.45714 14.734 3.36842 14.5179 3.36842ZM6.36972 1.68421H9.62898V3.36842H6.36972V1.68421ZM7.18453 12.6316C7.18453 12.8549 7.09869 13.0691 6.94588 13.227C6.79307 13.385 6.58582 13.4737 6.36972 13.4737C6.15362 13.4737 5.94637 13.385 5.79356 13.227C5.64075 13.0691 5.5549 12.8549 5.5549 12.6316V6.73684C5.5549 6.5135 5.64075 6.29931 5.79356 6.14138C5.94637 5.98346 6.15362 5.89474 6.36972 5.89474C6.58582 5.89474 6.79307 5.98346 6.94588 6.14138C7.09869 6.29931 7.18453 6.5135 7.18453 6.73684V12.6316ZM10.4438 12.6316C10.4438 12.8549 10.3579 13.0691 10.2051 13.227C10.0523 13.385 9.84508 13.4737 9.62898 13.4737C9.41288 13.4737 9.20562 13.385 9.05282 13.227C8.90001 13.0691 8.81416 12.8549 8.81416 12.6316V6.73684C8.81416 6.5135 8.90001 6.29931 9.05282 6.14138C9.20562 5.98346 9.41288 5.89474 9.62898 5.89474C9.84508 5.89474 10.0523 5.98346 10.2051 6.14138C10.3579 6.29931 10.4438 6.5135 10.4438 6.73684V12.6316Z" fill="#6B7280"/>
                              </g>
                              <defs>
                              <clipPath id="clip0_1249_10593">
                                  <rect width="16" height="16" fill="white"/>
                              </clipPath>
                              </defs>
                          </svg>
                          </div>
                        </div>
                    </div>
                     <!-- Default state -->
                     <div v-else @click="openSelector($event, index)" class="flex flex-row gap-4 p-2 cursor-pointer" :class="[errors.file ? '!bg-red-50 !text-red-700' : ' bg-white text-gray-400', index % 2 === 0 ? 'bg-white' : '!bg-gray-100']">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17 8 12 3 7 8"/><line x1="12" y1="3" x2="12" y2="15"/></svg>
                       <p>upload</p>
                     </div>
                   </div>
                 </div>
          </Field>
        </div>

        <div class="py-3 text-sm relative flex items-center w-32" :class="errors.community_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="community_id" v-model="initialValues.community_id" id="community_id" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.community_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
            <option value="" disabled class="!text-gray-400">Choose</option>
            <option :value="option._id" v-for="(option, optIdx) in projectStore.communities" :key="optIdx" class="text-black">
                {{ option.name }}
            </option>
            </Field>
        </div>

        <div class="py-3 text-sm relative flex items-center w-32" :class="errors.type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
          <Field as="select" type="text" name="type" v-model="initialValues.type" id="type" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100',errors.type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
            @input="trackChanges">
            <option value="" disabled>Choose</option>
            <option value="" disabled v-if="!Towertypes">
                No  Type !
            </option>
            <option v-else :value="option.value" v-for="(option, index) in Towertypes" :key="index" class="text-black">
                {{ option.name }}
            </option>
            </Field>
        </div>

        <!-- <div class="py-3 text-sm relative flex items-center w-32" >
          <p> {{ initialValues.updated_at ? formatTimestamp(initialValues.updated_at) : 'Not Modified' }}</p>
        </div> -->

        <div  class="py-3 text-gray-500 text-sm  flex items-center justify-end w-32 gap-6 h-full absolute right-0">
              <div v-if="row.isNew ? meta.dirty : isEdited"  class="py-3 text-gray-500 text-sm  flex items-center w-32">
                  <button  type="submit"
                      class="py-2 px-3 text-sm font-medium text-white bg-blue-700 rounded-lg flex items-center gap-2">
                      <div v-if="showLoader" class="loader !h-5 !w-5 !border-2"></div>
                      Save
                  </button>
              </div>

              <div class=" text-gray-500 text-sm  flex items-center pr-3" >
                  <button type="button" @click="initialValues.isNew || initialValues.isNew !== undefined ? emit('deleteRow',initialValues._id) : emit('openDeleteModal',initialValues._id ,index)" class="text-red-600">
                      <div class="w-8 h-8 flex justify-center items-center bg-gray-100 rounded-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                      </div>
                  </button>
              </div>
            </div>
    </div>
  </Form>

  <Modal :open="showSelector">
    <FilesUploader :data="filesData"
      @fileUploaded="(selectedFile) => handleFileSelect(e,selectedFile)"
      @closeModal="() => showSelector = false"/>
  </Modal>

  <imgPreviewModal v-if="imgModal.url"  :isOpen="imgModal.status" :imageUrl="imgModal.url"   @close="imgModal.status = false"/>

  <Modal :open="showUpdatefloors" :key="showUpdatefloors">
    <FloorSwitcher :floorData="initialValues.floors" @close="()=>{showUpdatefloors=false}" @finalSortedData="handleFloorUpdate"
      @addfloor="handleAddFloor" :key="initialValues.floors" :buildingId="id" @updateData="handleUpdateData"/>
  </Modal>
</template>

<style scoped>
.loader{
  @apply w-20 h-20 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-8 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}
</style>
