
import { UserRole } from './organization';
import { SessionType } from './session';
export enum ExperienceType {
  PROPVR_360 = 'propvr_360',
  RT_APPLICATION = 'rt_application',
  EMBED_V1 = 'embed_v1',
}

export enum PropertyType {
  TOWER = 'tower',
  VILLA = 'villa',
  BOTH = 'both',
}
export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  CUSTOM = 'custom'
}
export type HologramTag = {
  name: string,
  color: string
}
export type generalSettings = {
  slots?: string[];
  is_enabled?: boolean;
  branding_logo?: string;
  branding_logo_dark?: string;
  lat: number;
  timezone: string
  long: number;
};
export type pixelstreamingSettings = {
  is_enabled?: boolean;
  pixel_streaming_endpoint?: string;
  max_concurrent_sessions?: number;
  session_duration?: number;
};
export enum SceneType {
  PROJECT = 'project',
  MASTER = 'master',
}
export type aleSettings = {
  is_enabled?: boolean;
  initial_scene_type?: SceneType;
  initial_scene_id?: string;
  welcome_video?: string;
  welcome_thumbnail?: string,
  shortened_link?: string;
};

export type embedSettings = {
  is_enabled?: boolean;
};
export type salestoolSettings = {
  is_enabled?: boolean,
  session_duration?: number,
  default_experience?: SessionType,
  tags?:string[]
}
export type theme = {
  theme: Theme,
  primary:string,
  secondary:string,
}
export type hologramSettings = {
    project_logo: string,
    project_type: string,
    project_location: string,
    amount: string
    bedrooms: number,
    thumbnail: string,
    file: string,
    tags?: HologramTag[]
}
export type Project = {
  _id: string;
  name: string;
  description: string;
  property_type: PropertyType;
  experience: ExperienceType;
  is_public: boolean;
  organization_id: string;
  city: string;
  country: string;
  project_thumbnail: string;
  roles?: UserRole;
  data_sync?: {
    number_of_units?: number;
    number_of_amenities?: number;
    number_of_floors?: number;
    number_of_towers?: number;
  };
  projectSettings: {
    general: generalSettings,
    pixelstreaming: pixelstreamingSettings,
    ale: aleSettings,
    embed: embedSettings,
    salestool: salestoolSettings,
    theme:theme,
    hologramSettings?: hologramSettings
  };
};

export type createProjectInput = {
  name: string;
  experience: PropertyType;
  property_type: ExperienceType;
  organization_id: string;
  city: string;
  country: string;
  project_thumbnail: string;
  primary:string,
  secondary:string,
  theme: Theme
};
export type editProjectInput={
  id: string;
  name: string;
  experience: ExperienceType;
  property_type: PropertyType;
  city: string;
  country: string;
  project_thumbnail?: string;
}
export type updateProjectInput = {
  _id: string;
  is_enabled: boolean;
  session_duration: number;
  slots: string[];
  pixel_streaming_endpoint?: string;
  max_concurrent_sessions: number,
  initial_scene_type: SceneType,
  initial_scene_id: string,
  default_experience: SessionType,
  primary:string,
  secondary:string,
  lat:number,
  long:number,
  welcome_video: string,
  welcome_thumbnail: string,
  description: string,
  hideStatus:boolean,
  theme: Theme,
  timezone: string,
  shortened_link?: string;
  tags?:string[],
  project_location: string,
  project_type: string,
  amount: string,
  bedrooms: string[],
  thumbnail: string,
  file: string,
  hologram_project_logo: string
  project_id: string
};

export enum ProjectSettingsType {
  GENERAL = 'general',
  PIXELSTREAMING = 'pixelstreaming',
  ALE = 'ale',
  EMBED = 'embed',
  SALESTOOL = 'salestool',
  THEME = 'theme',
  GALLERY = 'gallery',
  HOLOGRAM = 'hologram'
}
export type bulkUpdateQuery = {
  id:string,
  order?:number,
  name?: string
}
export type bulkUpdateType = {
  query:bulkUpdateQuery[],
  project_id:string
}
export type galleryPayload = {
  name?:string,
  order?:string,
  id?:string
}
export type uploadSettingsFiles = {
  branding_logo?: File,
  branding_logo_dark?: File,
  thumbnail?: File,
  file?: File,
  hologram_project_logo?: File,
  font_url?:File,
  welcome_thumbnail?:File,
  welcome_video?:File,
}
export type MoveProjectToTrash = {
    project_id: string,
    timeStamp: string,
}
