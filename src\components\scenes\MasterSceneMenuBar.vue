<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const selectedMenu = ref('scenes');
const sceneId = ref(route.params.scene_id);

if (route.path.includes('/layers')) {
  selectedMenu.value = 'layers';
} else if (route.path.includes('/icons')) {
  selectedMenu.value = 'icons';
} else {
  selectedMenu.value = 'scenes';
}

watch(
  () => route.path,
  (newPath) => {
    if (route.params.scene_id){
      sceneId.value = route.params.scene_id;
    }
    if (newPath.includes('/layers')) {
      selectedMenu.value = 'layers';
    } else if (newPath.includes('/icons')) {
      selectedMenu.value = 'icons';
    } else {
      selectedMenu.value = 'scenes';
    }
  },
  { immediate: true },
);

</script>

<template>
    <div
    class="h-full w-12 bg-gray-100 dark:bg-bg-default flex flex-col justify-between pt-4 overflow-auto">
    <div
      class="flex flex-col justify-center gap-y-2 h-fit p-1">
      <RouterLink :to="`/masterscenes/${sceneId?sceneId:''}`"
        :class="`${selectedMenu==='scenes'? 'bg-white shadow-md' : ''}`"
        @click="selectedMenu='scenes'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div
          :class="` ${selectedMenu==='scenes' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
          <svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg">
<path d="M1.66807 12.608L1.66814 12.608V12.6V5.4H4.81387C5.32681 5.4 5.82263 5.20457 6.1913 4.85064C6.56067 4.49604 6.77216 4.01065 6.77216 3.5V0.5H11.4195C11.6723 0.505398 11.9087 0.606379 12.0785 0.774997C12.2486 0.943804 12.3382 1.16604 12.3345 1.39198L12.3345 1.39198V1.4V12.6H12.3344L12.3345 12.608C12.3382 12.834 12.2486 13.0562 12.0785 13.225C11.9087 13.3936 11.6723 13.4946 11.4195 13.5H2.58309C2.33031 13.4946 2.0939 13.3936 1.92406 13.225C1.75403 13.0562 1.66445 12.834 1.66807 12.608ZM4.21636 0.976939C4.24769 0.947219 4.28022 0.91874 4.31387 0.89156V3H2.11163C2.1263 2.98451 2.14132 2.96928 2.15667 2.9543L4.21636 0.976939Z"/>
</svg>
        </div>
      </RouterLink>
      <RouterLink :to="route.params.scene_id?`/masterscenes/${sceneId}/layers`:''"
        :class="[`${selectedMenu==='layers'? 'bg-white shadow-md' : ''}`,
        !route.params.scene_id ? 'opacity-50 cursor-not-allowed pointer-events-none' : '']"
        @click="selectedMenu='layers'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div
          :class="` ${selectedMenu==='layers' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
          <svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_18990)">
<path d="M12.4061 6.87478L12.129 7.0128L7.22391 9.45631L7.00096 9.56737L6.77802 9.45631L1.87288 7.0128L1.59583 6.87478V6.56525V6.43517C1.59583 6.38527 1.57524 6.33217 1.52998 6.28906C1.48389 6.24517 1.41639 6.21649 1.34119 6.21649C1.26599 6.21649 1.1985 6.24517 1.15241 6.28906C1.10715 6.33217 1.08656 6.38527 1.08656 6.43517V7.00005V7.00014C1.08655 7.03498 1.09646 7.07085 1.11756 7.10407C1.1388 7.13751 1.17131 7.16793 1.21407 7.1893L12.4061 6.87478ZM12.4061 6.87478V6.56525V6.43517C12.4061 6.38527 12.4267 6.33217 12.4719 6.28906C12.518 6.24517 12.5855 6.21649 12.6607 6.21649C12.7359 6.21649 12.8034 6.24517 12.8495 6.28906L13.1943 5.92699M12.4061 6.87478L13.1943 5.92699M13.1943 5.92699L12.8495 6.28906C12.8948 6.33217 12.9154 6.38527 12.9154 6.43517V7.00005V7.00014M13.1943 5.92699L12.9154 7.00014M12.9154 7.00014C12.9154 7.03498 12.9055 7.07085 12.8844 7.10407C12.8631 7.13751 12.8306 7.16793 12.7878 7.1893L12.9154 7.00014ZM12.4016 10.3352L12.1245 10.4732L7.21938 12.9167L6.99644 13.0278L6.77349 12.9167L1.86836 10.4732L1.5913 10.3352V10.0257V9.89561C1.5913 9.84571 1.57071 9.79261 1.52545 9.7495C1.47936 9.70561 1.41186 9.67693 1.33667 9.67693C1.26147 9.67693 1.19397 9.70561 1.14788 9.7495C1.10262 9.79261 1.08203 9.84571 1.08203 9.89561V10.4612V10.4613C1.08203 10.4961 1.09193 10.532 1.11304 10.5652L0.690991 10.8333L1.11304 10.5652C1.1343 10.5987 1.16686 10.6292 1.20971 10.6505L6.86744 13.4703C6.8677 13.4705 6.86795 13.4706 6.8682 13.4707C6.90674 13.4896 6.95078 13.5 6.99644 13.5C7.0421 13.5 7.08616 13.4896 7.1247 13.4707C7.12494 13.4706 7.12518 13.4705 7.12543 13.4703L12.7832 10.6505L12.4016 10.3352ZM12.4016 10.3352V10.0257M12.4016 10.3352V10.0257M12.4016 10.0257V9.89561C12.4016 9.84571 12.4222 9.79261 12.4674 9.7495C12.5135 9.70561 12.581 9.67693 12.6562 9.67693C12.7314 9.67693 12.7989 9.70561 12.845 9.7495C12.8903 9.79261 12.9108 9.84571 12.9108 9.89561V10.4612V10.4613M12.4016 10.0257L12.9108 10.4613M12.9108 10.4613C12.9108 10.4961 12.9009 10.532 12.8798 10.5652C12.8586 10.5987 12.8261 10.6291 12.7833 10.6505L12.9108 10.4613ZM1.21483 3.72886L1.19666 3.7198C1.16194 3.69929 1.13503 3.67256 1.1166 3.64356C1.09548 3.61033 1.08557 3.57446 1.08557 3.53961C1.08557 3.50477 1.09548 3.46889 1.1166 3.43566C1.13788 3.40219 1.17046 3.37173 1.21332 3.35036C1.21332 3.35036 1.21332 3.35036 1.21333 3.35036L6.87309 0.529548L6.87325 0.529468C6.91124 0.510517 6.95489 0.5 7.00021 0.5C7.04552 0.5 7.08918 0.510517 7.12717 0.529469L7.12733 0.529548L12.7871 3.35036C12.83 3.37173 12.8625 3.40219 12.8838 3.43566C12.9049 3.46889 12.9149 3.50477 12.9149 3.53961C12.9149 3.57446 12.9049 3.61033 12.8838 3.64356L13.2639 3.88518L12.8838 3.64356C12.8625 3.67704 12.83 3.7075 12.7871 3.72886L7.12879 6.54895C7.12861 6.54904 7.12843 6.54913 7.12825 6.54922C7.09005 6.568 7.04631 6.57839 7.00096 6.57839C6.95563 6.57839 6.91191 6.56801 6.87371 6.54923C6.87352 6.54914 6.87333 6.54905 6.87314 6.54895L1.21483 3.72886ZM6.87384 10.0101L1.21423 7.18938H12.7877L7.12808 10.0101L7.12792 10.0102C7.08993 10.0291 7.04628 10.0397 7.00096 10.0397C6.95564 10.0397 6.91199 10.0291 6.874 10.0102L6.87384 10.0101Z"/>
</g>
<defs>
<clipPath id="clip0_723_18990">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>
        </div>
      </RouterLink>
      <RouterLink :to="route.params.scene_id?`/masterscenes/${sceneId}/icons`:null"
        :class="[`${selectedMenu==='icons'? 'bg-white shadow-md' : ''}`,
        !route.params.scene_id ? 'opacity-50 cursor-not-allowed pointer-events-none' : '']"
        @click="selectedMenu='icons'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div
          :class="` ${selectedMenu==='icons' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">

<svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="star"><rect width="24" height="24" transform="rotate(90 12 12)" opacity="0"/><path d="M17.56 21a1 1 0 0 1-.46-.11L12 18.22l-5.1 2.67a1 1 0 0 1-1.45-1.06l1-5.63-4.12-4a1 1 0 0 1-.25-1 1 1 0 0 1 .81-.68l5.7-.83 2.51-5.13a1 1 0 0 1 1.8 0l2.54 5.12 5.7.83a1 1 0 0 1 .81.68 1 1 0 0 1-.25 1l-4.12 4 1 5.63a1 1 0 0 1-.4 1 1 1 0 0 1-.62.18z"/></g></g></svg>
        </div>
      </RouterLink>

      <div class="w-full border border-1 border-gray-500"></div>

      <RouterLink :to="``"
       @click="selectedMenu='help'"
        :class="`${selectedMenu.name==='help' ? 'bg-white shadow-md' : ''}`"
        class="h-10 w-full text-lg flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div
          :class="`${selectedMenu.name==='help' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">

<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="menu-arrow-circle"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 16a1 1 0 1 1 1-1 1 1 0 0 1-1 1zm1-5.16V14a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1 1.5 1.5 0 1 0-1.5-1.5 1 1 0 0 1-2 0 3.5 3.5 0 1 1 4.5 3.34z"/></g></g></svg>

        </div>
      </RouterLink>
    </div>
    </div>

</template>

<style scoped>
</style>
