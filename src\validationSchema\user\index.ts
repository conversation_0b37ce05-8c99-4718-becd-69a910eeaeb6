import * as yup from 'yup';
import { RegXEmailPattern } from '../../helpers/validationSchemaHelpers';

// Sign In
export const signInSchema = yup.object({
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required("Email ID is required"),
  password: yup.string().required(),
});

// Sign Up
export const signUpSchema = yup.object({
  first_name: yup.string().required("First Name is a required"),
  last_name: yup.string().required("Last Name is a required"),
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required("Email ID is a required"),
  password: yup.string().min(8).required("Password is a required"),
  confirmPassword: yup.string().oneOf([yup.ref('password')], 'Passwords must match').required("Confirm Password is a required "),
});
export const addUserInOrganization = yup.object({
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required(),
  role: yup.string().required(),
});
export const editUserRoleInOrganization = yup.object({
  role: yup.string().required(),
});
