<script setup>

defineProps({
  percentage: Number,
  msg: String,
});

</script>

<template>
    <div class="fixed z-[9999999] inset-0 top-0 left-0 bg-black opacity-90 flex flex-col justify-center items-center h-screen w-screen gap-2 select-none">
            <p class='text-white text-sm select-none'> {{ msg }}</p>
            <div class="w-[250px] h-5 bg-transparent block border overflow-hidden">
                    <div :style="{width:`${percentage}%`}" class="bg-white h-5 transition-all text-xs flex justify-center items-center text-black font-semibold">
                        {{ percentage > 4 ? percentage > 10 ? `${percentage}%` : percentage : '' }}
                    </div>
            </div>
    </div>
</template>

<style scoped>

</style>
