<script setup>
import Navbar from '../../components/common/Navbar.vue';
import Deepzoom from '../../components/scenes/Deepzoom/Deepzoom.vue';
import { UserStore } from '../../store/index';
import { Org_Store } from '../../store/organization';
import SideNavBar from '@/components/common/SideNavBar.vue';

const userStore = UserStore();
const organizationStore = Org_Store();

// Inialization
organizationStore.RefreshMasterScenes();

</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar />
        <div v-if="organizationStore.masterScenes !== null"
            class="dynamic-viewbox">
            <!-- <SideBar/> -->
            <SideNavBar />
            <div v-if="userStore.user_data"
                class="dynamic-container">
                <Deepzoom/>
                <router-view ></router-view>
            </div>
        </div>
    </div>
</template>
