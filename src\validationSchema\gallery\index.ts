import * as yup from 'yup';
import { fileValidation, handleTypeFileValidation, imageSizeValidation } from '../../helpers/validationSchemaHelpers';
export const gallerySchema = yup.object({
  name: yup.string().required(),
  type: yup.string().required(),
  category: yup.object().required(),
  link: yup.string().when('type', {
    is: (val:string) => val === 'embed_link',
    then: () => yup.string().required(),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  tour_id: yup.string().when('type', {
    is: (val:string) => val === 'virtual_tour',
    then: () => yup.string().required(),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  file: yup.mixed().when('type', ([type], schema) => handleTypeFileValidation(type, schema)),
  thumbnail: yup.mixed().when('type', {
    is: (val:string) => val !== 'image' && val !== '360_image',
    then: () => yup.mixed().required().test('thumbnail', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
      .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
    otherwise: () => yup.string().nullable(),
  }),
});

export const multipleGallerySchema = yup.object().shape({
  fileItems: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string().required("Name is required"),
        type: yup.string().required("Type is required"),
        category: yup.string().required("Category is required"),
        link: yup.string().when('type', {
          is: (val:string) => val === 'embed_link',
          then: () => yup.string().url("Invalid file URL").required("Embed Link is required"),
          otherwise: () => yup.string().notRequired().nullable(),
        }),
        tour_id: yup.string().when('type', {
          is: (val:string) => val === 'virtual_tour',
          then: () => yup.string().required("Virtual Tour is required"),
          otherwise: () => yup.string().notRequired().nullable(),
        }),
        url: yup.mixed().when('type', {
          is: (val: string) => val !== 'embed_link' && val !== 'virtual_tour',
          then: () => yup.mixed().required('File is required')
            .test('url', 'URL is required', function (value) {
              if (this.parent.isNew && !value) {
                return false;
              }
              return true;
            })
            .test('fileType', 'Not a valid file type', function (value) {
              if (value instanceof File) {
                const validationType = this.parent.type === '360_image' ? 'image' : this.parent.type;
                return fileValidation(value, validationType);
              }
              return true;
            })
            .test('fileSize', 'File size is too large', function (value) {
              if (value instanceof File) {
                return imageSizeValidation(value);
              }
              return true;
            }),
          otherwise: () => yup.mixed().nullable(),
        }),
        thumbnail: yup.mixed().nullable().test('thumbnail-validation', function (value) {
          const { type, isNew } = this.parent;

          // For new items
          if (isNew === true) {
            // Skip validation for image, 360_image, and embed_link
            if (type === 'image' || type === '360_image' || type === 'embed_link') {
              return true;
            }

            // For other types, require thumbnail
            if (!value) {
              return this.createError({
                message: `Please upload a thumbnail`,
              });
            }
          } else {
            // Get all non-URL fields from parent to check if this is truly an existing item
            const { ...meta } = this.parent;
            const hasExistingData = Object.values(meta).some((val) => val);

            // If it's an existing item (has some data) and needs a thumbnail
            if (hasExistingData && type !== 'embed_link') {
              if (!value) {
                return this.createError({
                  message: `Please upload a thumbnail`,
                });
              }
            }
          }

          // If there's a value, validate it regardless of type
          if (value instanceof File) {
            if (!fileValidation(value, 'image')) {
              return this.createError({
                message: 'Thumbnail must be a valid image',
              });
            }
            if (!imageSizeValidation(value)) {
              return this.createError({
                message: 'Thumbnail image size exceeds the maximum limit',
              });
            }
          }

          return true;
        }),
      }),
    )
    .strict(),
});

export const GallerySchema = yup.object({
  name: yup.string().required("Name is required"),
  type: yup.string().required("Type is required"),
  category: yup.string().required("Category is required"),
  link: yup.string().when('type', {
    is: (val:string) => val === 'embed_link',
    then: () => yup.string().url("Invalid file URL").required("Embed Link is required"),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  tour_id: yup.string().when('type', {
    is: (val:string) => val === 'virtual_tour',
    then: () => yup.string().required("Virtual Tour is required"),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  url: yup.mixed().when('type', {
    is: (val: string) => val !== 'embed_link' && val !== 'virtual_tour',
    then: () => yup.mixed().test('url-validation', function (value) {
      const { type, isNew } = this.parent;

      // For new items - require url
      if (isNew === true) {
        if (!value) {
          return this.createError({
            message: 'File is required',
          });
        }
      } else {
        // For existing items - check if there's data
        const { ...meta } = this.parent;
        const hasExistingData = Object.values(meta).some((val) => val);

        // If it's an existing item with data but no url
        if (hasExistingData && !value) {
          return this.createError({
            message: 'File is required',
          });
        }
      }

      // Validate file type if it's a File instance
      if (value instanceof File) {
        const validationType = type === '360_image' ? 'image' : type;
        if (!fileValidation(value, validationType)) {
          return this.createError({
            message: 'Not a valid file type',
          });
        }

        // Validate file size
        if (!imageSizeValidation(value)) {
          return this.createError({
            message: 'File size is too large',
          });
        }
      }

      return true;
    }),
    otherwise: () => yup.mixed().nullable(),
  }),
  thumbnail: yup.mixed().nullable().test('thumbnail-validation', function (value) {
    const { type, isNew } = this.parent;

    // For new items
    if (isNew === true) {
      // Skip validation for image, 360_image, and embed_link
      if (type === 'image' || type === '360_image' || type === 'embed_link') {
        return true;
      }

      // For other types, require thumbnail
      if (!value) {
        return this.createError({
          message: `Please upload a thumbnail`,
        });
      }
    } else {
      // Get all non-URL fields from parent to check if this is truly an existing item
      const { ...meta } = this.parent;
      const hasExistingData = Object.values(meta).some((val) => val);

      // If it's an existing item (has some data) and needs a thumbnail
      if (hasExistingData && type !== 'embed_link') {
        if (!value) {
          return this.createError({
            message: `Please upload a thumbnail`,
          });
        }
      }
    }

    // If there's a value, validate it regardless of type
    if (value instanceof File) {
      if (!fileValidation(value, 'image')) {
        return this.createError({
          message: 'Thumbnail must be a valid image',
        });
      }
      if (!imageSizeValidation(value)) {
        return this.createError({
          message: 'Thumbnail image size exceeds the maximum limit',
        });
      }
    }

    return true;
  }),
});

export const editGallerySchema = yup.object({
  name: yup.string(),
  type: yup.string(),
  category: yup.object(),
  link: yup.string(),
  tour_id: yup.string(),
  file: yup.mixed().notRequired(),
  thumbnail: yup.mixed().when('type', {
    is: (val:string) => val !== 'image' && val !== '360_image',
    then: () => yup.mixed().test('thumbnail', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
      .test('thumbnail', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
    otherwise: () => yup.string().nullable(),
  }),
});
