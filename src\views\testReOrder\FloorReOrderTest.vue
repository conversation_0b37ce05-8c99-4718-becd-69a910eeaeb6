<script setup>
import { formatTimestamp, getSortArrayFromObjects } from '../../helpers/helpers';
// import FloorData from "./floorReOrderData.json";
import NestedReOrder from '../../components/common/NestedReOrder.vue';
import { ref, defineEmits, defineProps } from 'vue';

const props = defineProps({
  floorData: {type: Object, default: () => ({})},
});

const emit = defineEmits(['sortedData', 'addNewFloor', 'deleteFloor', 'renameFloor']);
console.log('props in reorder', props.floorData);

const dumpData = ref(null);

/* ---------------- Methods --------------------- */

/* Setup Data (Mandatory) */

// setupData (Modify based on your needs inside logic)
const setupData = (data) => {
  console.log("setupData", data);
  if (data){
    // Structuring your data's in array formats

    // Object

    // Conversion of Objets to Array values then sort it respectively to your needs.
    const convertedData = [...getSortArrayFromObjects(data, null)];

    convertedData.forEach((item, index) => {
      item.sno = index + 1;
      // item.floor_id = Number(item.floor_id); // Convert string to number
    });

    console.log('convertedData', convertedData);

    // Update the your component reference
    dumpData.value = convertedData;
  }
};

// Initial
setupData({...props.floorData});

const handleDelete = ( item ) => {
  console.log('item', item);
  emit('deleteFloor', item);
};

// const originalData = ref({});
// if (!originalData.value[item.id]) {
//     originalData.value[item.id] = { floor_name: item.floor_name };
// }

const isNameModified = (item) => {
  // Check if the current floor_name differs from original
  return props.floorData[item.floor_id] &&
         item.floor_name !== props.floorData[item.floor_id].floor_name;
};

const handleNameChange = (item) => {
  if (item.isNew){
    emit('addNewFloor', item);
  }
  emit('renameFloor', item);

  // You might want to emit an event to update the parent's original data
  // emit('update:originalData', { ...props.floorData, [item.floor_id]: { ...item } });
};
/* Emits Handler */
// Child Sort
const handleChildSortEmit = (val) => {
  console.log("-----------------------------");
  console.log('Parent~ChildSort', val);
  console.log("-----------------------------");

  const sortedObj = {};

  // Add the dragged item first
  if (val.draggedItem) {
    sortedObj[val.draggedItem.floor_id] = val.draggedItem;
  }

  // Add the rest of the sorted items
  if (val.sortedItems && Array.isArray(val.sortedItems)) {
    val.sortedItems.forEach((item) => {
      sortedObj[item.floor_id] = item;
    });
  }

  console.log('Transformed data:', sortedObj);

  emit('sortedData', sortedObj);

  // Conversion Data

  // Api
  /*  create({}).then(res => {
    projectStore.Sync(res); // Sync
    setupData(res); // Call the setup Data
  })  */
};

// const handleNameChange = (item) =>{
//   console.log('deumpdata',dumpData.value);
//   console.log('item', item);
//   emit('addNewFloor', item)
// }

</script>

<template>
  <div class="!w-full h-full overflow-y-auto" v-if="dumpData">
    <div class="relative !w-full h-fit  flex flex-col justify-center items-center rounded shadow bg-white">
      <div class="sticky top-0 w-full bg-gray-50">
        <div class="justify-center items-center gap-3 w-full flex border-b border-gray-200 hover:bg-gray-50">
            <div class="!w-[50px] p-3 text-sm text-gray-900">
              <p>S.no</p>
            </div>
            <div class="!w-20 flex-1 p-2">
              <p>Floor name</p>
            </div>
            <div class="!w-[200px] p-2 text-sm text-gray-500">
               <p>modified</p>
            </div>
            <div class="!w-16 p-2">
              <p></p>
            </div>
          </div>
      </div>
      <NestedReOrder
        v-model="dumpData"
        groupName="floors"
        :allowChildReparenting="false"
        :allowChildSort="true"
        uniqueKey="id"
        ghostClass="sampe_ghost"
        animationMilliSec="450"
        sortReferenceKey="floor_id"
        tag="div"
        @handleChildSort="(val) => handleChildSortEmit(val)"
        class="block w-full"
      >
        <template #default="{item}">
          <div class="w-full flex justify-center items-center gap-3 bg-white border-b border-gray-200 hover:bg-gray-50">
            <div class="!w-[50px] p-3 text-sm text-gray-900">{{ item.sno }}</div>
            <div class="!w-20 flex-1 p-2">
              <div class="text-sm text-gray-900">
                <div class="flex items-center gap-2">
                  <input
                    v-model="item.floor_name"
                    type="text"
                    class="border rounded px-2 py-1 flex-1"
                    />
                  <button
                    v-if="isNameModified(item) || item.isNew "   @click="handleNameChange(item)"
                    class="bg-blue-500 text-white px-3 py-2 rounded text-xs hover:bg-blue-600"
                  >
                    Save
                  </button>
                </div>
              </div>
            </div>
            <div class="!w-[200px] p-2 text-sm text-gray-500 !whitespace-nowrap">
              <div>
                {{ item.updated_at ? formatTimestamp(item.updated_at) : ''}}
                 <!-- <span class="text-xs">DEC 23</span> -->
              </div>
            </div>
            <div class="!w-16">
              <button @click="handleDelete(item)" class="text-gray-400 p-2 rounded-lg bg-gray-200 hover:text-red-500">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 4h12M5.333 4V2.667a1.333 1.333 0 011.334-1.334h2.666a1.333 1.333 0 011.334 1.334V4m2 0v9.333a1.333 1.333 0 01-1.334 1.334H4.667a1.333 1.333 0 01-1.334-1.334V4h9.334z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </template>
      </NestedReOrder>
    </div>
  </div>
</template>

<style scoped>
.sampe_ghost {
  opacity: 0.1;
}
</style>
