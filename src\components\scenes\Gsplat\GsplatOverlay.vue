<script setup>

import { ref } from 'vue';
import gsplatComp from './gsplatComp.vue';
import { useRoute } from 'vue-router';
// import router from '../../../router';
// import RightSidebar from '../../common/Modal/RightSidebar.vue';
import { ProjectStore } from '../../../store/project';
import { Org_Store } from '../../../store/organization';
// import Hotspot from '../Hotspot.vue';
import { cdn } from '../../../helpers';
import { getCategories as getAmenitiesCategories } from '../../../api/projects/amenties';
import { uiOperations } from '../../../store/uiOperations';
import { isMasterScenePath } from '../../../helpers/helpers.ts';

const route = useRoute();
const projectId = ref(route.params.project_id);
// const sceneId = ref(route.params.scene_id);
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const gsplatCompRef = ref(null), showGsplat= ref(true);
// const gsplatLayersUpdateRef = ref(null);
// const updateHotSpotPositionRef = ref(null);
// const updatePlaneScaleRef = ref(null);
const uiStore = uiOperations();
defineEmits(['updateHotspotPosition', 'updatePlaneScale']);
defineProps({
  gsplatLayersUpdateRef: {
    type: Object,
    default: null,
  },
});

const response = {
  type: 'gsplat',
  cameraPosition: {
    x: 2.107234919667583,
    y: 1.1339903035639423,
    z: -0.7236207464939785,
  },
};
const svgLayers = ref({});
const categoryList = ref();

if (!isMasterScene.value){
  getAmenitiesCategories(projectId.value).then((res) => { // Get list of amenities categories
    console.log('res ->', res);
    categoryList.value = res.map((elem) => {
      return {name: elem.category};
    });
  }).catch((err) => {
    console.log('output->err', err);
    uiStore.handleApiErrorMessage(err.message);
  });
}
/* Methods */

// Api calls
if (!isMasterScene.value){
  projectStore.RefreshLandmarks(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshAmenities(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshCommunities(projectId.value);
}
newOrganizationStore.RefreshProjects();
newOrganizationStore.RefreshMasterScenes();

// Close right sidebar
// const handleCloseRightSideBar = () => {
//   router.push(`${route.path}`);
// };

// const HandleUpdateGsplatLayers = (val) => {
//   gsplatLayersUpdateRef.value = val;
// };

const getSceneSource = (sceneInfo) => {
  if (sceneInfo){
    console.log(sceneInfo[route.params.scene_id].svgData);
    console.log(sceneInfo[route.params.scene_id].sceneData.gsplat_link);
    if (sceneInfo[route.params.scene_id]?.sceneData?.gsplat_link){

      response.source = cdn(sceneInfo[route.params.scene_id].sceneData.gsplat_link);
    }

    if (Object.values(sceneInfo[route.params.scene_id].svgData)[0]?.layers){
      svgLayers.value = Object.values(sceneInfo[route.params.scene_id].svgData)[0].layers;
    }
  }
};

if (isMasterScene.value){
  getSceneSource(newOrganizationStore.masterScenes);
} else {
  getSceneSource(projectStore.scenes);
}

</script>
<template>

 <div class="h-full" >
<!--     <p class="bg-white text-black p-2 absolute z-100">  {{ svgLayers }} </p> -->
  <gsplatComp
    v-if="showGsplat"
    ref="gsplatCompRef"
    :data="response"
    :svgLayers="svgLayers"
    :gsplatLayersUpdate="gsplatLayersUpdateRef"
    @updateHotspotPosition="val => $emit('updateHotspotPosition',{x:val.x, y:val.y, z:val.z})  "
    @updatePlaneScale="val =>  $emit('updatePlaneScale',{width:val.width, height:val.height,})" />

  <!-- Add -->
  <!-- <RightSidebar v-if="route.query.layerId === '00' || route.query.layerId === '000'" :open="route.query.layerId"   @closeModal="handleCloseRightSideBar">

                  <Hotspot
                        @updateGsplatLayers = "HandleUpdateGsplatLayers"
                        :landmarks="isMasterScene ? false :projectStore.landmarks"
                        :loader="loader"
                        :svgData="isMasterScene ? newOrganizationStore.masterScenes[sceneId].svgData :projectStore.scenes[sceneId].svgData"
                        :scenes="isMasterScene ? newOrganizationStore.masterScenes : projectStore.scenes"
                        :projects="newOrganizationStore.projects"
                        :projectId="projectId"
                        :defaultPostion="updateHotSpotPositionRef"
                        :defaultScale="updatePlaneScaleRef"
                        type="add"
                        :isPlane="route.query.layerId === '00' ? false  : true"
                        >

                </Hotspot>

  </RightSidebar> -->

  <!-- Edit -->
  <!-- <RightSidebar v-if="route.query.layerId !== '00' && route.query.layerId !== '000'" :open="route.query.layerId"   @closeModal="handleCloseRightSideBar">

          <Hotspot
                @updateGsplatLayers = "HandleUpdateGsplatLayers"
                :landmarks="isMasterScene ? false : projectStore.landmarks"
                :loader="loader"
                :svgData="isMasterScene ? newOrganizationStore.masterScenes[sceneId].svgData : projectStore.scenes[sceneId].svgData"
                :scenes="isMasterScene ? newOrganizationStore.masterScenes : projectStore.scenes"
                :projects="newOrganizationStore.projects"
                :projectId="projectId"
                :defaultPostion="updateHotSpotPositionRef"
                :defaultScale="updatePlaneScaleRef"
                :categoryList="categoryList"
                type='edit'
                :isPlane="svgLayers[route.query.layerId].type !== 'plane' ? false  : true"
                >

        </Hotspot>

  </RightSidebar> -->

</div>

</template>

<style scoped>
.slide-animation
{
  animation:slide 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}

@keyframes slide
{
  0%
  {
    left :-24em;
  }
  50%
  {
    left:6em;
  }
  100%
  {
    @apply md:left-16 ;

  }
}
</style>
