<script setup>
import { ref, defineEmits, onMounted, computed } from 'vue';

const emit = defineEmits(['topicSelected', 'emitTopic']);

const searchClicked = ref(false);
const showUpArrow = ref(true);
// Const showDownArrow = ref(false)
const list = ref();
const option = ref(null);
const searchInput = ref('');
const topic = ref(['virtual tour', 'sale closure', 'RT tour', '360 tour', 'sle closure', 'RT our', '360 tou']);

const toggleSearch = () => {
  searchClicked.value = !searchClicked.value;
  console.log(searchClicked.value);
  if (searchClicked.value){
    showUpArrow.value = false;
  } else {
    showUpArrow.value=true;
  }
};

function outsideClickHandler (event) {
  const div = list.value;
  if (div && !div.contains(event.target)) {
    searchClicked.value = false;
    showUpArrow.value=true;
  }
}

onMounted(() => {
  document.addEventListener('click', outsideClickHandler);
});

const filteredTopics = computed(() => {
  const input = searchInput.value.trim().toLowerCase();
  if (!input) {
    return topic.value;
  }
  return topic.value.filter((item) => item.toLowerCase().includes(input));

});

const emitTopic = (selectedTopic) => {
  searchInput.value = selectedTopic;
  emit('topicSelected', selectedTopic);
};

</script>

<template>

<div @click="toggleSearch()" ref="list"  class="flex w-[480px] h-[40px] border rounded h-10 px-3 py-0 border-[1px] border-bg-700 focus:border-bg-default">
    <input v-model="searchInput" v-on:change="toggleSearch()" type="text" placeholder="Search" class="flex w-full "
            autocomplete="off" required>

    <div class="flex justify-center items-center">
        <svg v-if="showUpArrow" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.8135 9.09588L12.4502 15.5118C12.3911 15.5715 12.3209 15.6188 12.2437 15.6511C12.1664 15.6834 12.0836 15.7 12 15.7C11.9164 15.7 11.8336 15.6834 11.7563 15.6511C11.6791 15.6188 11.6089 15.5715 11.5498 15.5118L5.18648 9.09588C5.06708 8.97549 5 8.81221 5 8.64195C5 8.4717 5.06708 8.30841 5.18648 8.18802C5.30588 8.06763 5.46783 8 5.63669 8C5.80554 8 5.96749 8.06763 6.08689 8.18802L12 14.1508L17.9131 8.18802C17.9722 8.12841 18.0424 8.08113 18.1197 8.04887C18.1969 8.0166 18.2797 8 18.3633 8C18.4469 8 18.5297 8.0166 18.607 8.04887C18.6842 8.08113 18.7544 8.12841 18.8135 8.18802C18.8726 8.24763 18.9195 8.3184 18.9515 8.39629C18.9835 8.47417 19 8.55765 19 8.64195C19 8.72625 18.9835 8.80973 18.9515 8.88762C18.9195 8.9655 18.8726 9.03627 18.8135 9.09588Z" fill="#5B616E"/>
        </svg>

        <svg  v-if="searchClicked" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.8135 14.9041L12.4502 8.48817C12.3911 8.42852 12.3209 8.38119 12.2437 8.34891C12.1664 8.31662 12.0836 8.3 12 8.3C11.9164 8.3 11.8336 8.31662 11.7563 8.34891C11.6791 8.38119 11.6089 8.42852 11.5498 8.48817L5.18648 14.9041C5.06708 15.0245 5 15.1878 5 15.358C5 15.5283 5.06708 15.6916 5.18648 15.812C5.30588 15.9324 5.46783 16 5.63669 16C5.80554 16 5.96749 15.9324 6.08689 15.812L12 9.84915L17.9131 15.812C17.9722 15.8716 18.0424 15.9189 18.1197 15.9511C18.1969 15.9834 18.2797 16 18.3633 16C18.4469 16 18.5297 15.9834 18.607 15.9511C18.6842 15.9189 18.7544 15.8716 18.8135 15.812C18.8726 15.7524 18.9195 15.6816 18.9515 15.6037C18.9835 15.5258 19 15.4424 19 15.358C19 15.2737 18.9835 15.1903 18.9515 15.1124C18.9195 15.0345 18.8726 14.9637 18.8135 14.9041Z" fill="#5B616E"/>
        </svg>

    </div>

   <!-- dropdown -->
   <div  v-if="searchClicked" style="z-index: 4">

       <div class="w-[480px] h-auto rounded-[16px] bg-bg-950 absolute top-[3rem] -left-[0.25rem] flex flex-col p-[15px] ">

            <div class=" flex flex-col max-h-[195px]" style="scroll-behavior: smooth;scrollbar-width: none;overflow: scroll;">

                <label v-for="(item, index) in filteredTopics"
                    :key="index" :class="[ {'checked': option === item}]"
                    class="w-[544px] h-[48px] flex items-center gap-[1.25rem] cursor-pointer p-2"
                    @click="emitTopic(item)">
                    <input type="radio" :value="item" v-model="option" class="bg-bg-1000" :checked="option === item" />

                    <span class="text-txt-default text-base leading-normal font-normal not-italic whitespace-nowrap flex justify-center items-center">{{ item }}</span>
                </label>

            </div>
            <div class="w-[544px] h-[19px] ">
                    <p class="text-base leading-normal font-normal not-italic text-[#5B616E] ">Topic Not found ?  <span class="text-cyan-400">Add New topic</span></p>
            </div>

       </div>
   </div>
</div>
</template>

<style scoped>

::placeholder{
 color: #5B616E;
font-size: 14px;
font-style: normal;
font-weight: 400;
line-height: normal;
text-align: left;
}

/* styling for radio type input */
input[type='radio']{
    width:20px;
    height: 20px;
    border: 2px solid black;
    display: flex;
    justify-content: center;
    align-items: center;

}

input{
    color:black;
}
</style>
