<script setup>
import { computed } from 'vue';
import { VueDraggable } from 'vue-draggable-plus';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  isRoot: {
    type: Boolean,
    default: true,
  },
  uniqueKey: {
    type: String,
    default: 'id',
  },
  nestedChildKey: {
    type: String,
    default: 'children',
  },
  ghostClass: {
    type: String,
    default: 'ghost',
  },
  animationMilliSec: {
    type: String,
    default: '350',
  },
  childSort: {
    type: Boolean,
    default: true,
  },
  childReparentingSort: {
    type: Boolean,
    default: true,
  },
  referenceData: {
    type: Array,
    default: null,
  },
  groupName: {
    type: String,
    default: 'reOrder',
  },
  allowChildReparenting: {
    type: Boolean,
    default: true,
  },
  allowChildSort: {
    type: Boolean,
    default: true,
  },

});

/* console.log(props); */

const emits = defineEmits(['update:modelValue', 'handleChildSort', 'handleChildReparentingSort']);

/* Methods */
// Computed ~ create's a two way binding from component v-model to props modelValue
const list = computed({
  get: () => props.modelValue,
  set: (value) => {
    emits('update:modelValue', value); // triggered, whenever the vue-draggable is updating the v-model value.
  },
});

// End
const handleEnd = (val) => {
  console.log(val);
  console.log(props.referenceData);
  console.log(props.isRoot);

  const isReparenting = val.item.addBaseParentReference && val.item.removeBaseParentReference;
  console.log(isReparenting);

  /* Child Sort */
  if (props.allowChildSort && !isReparenting){
    console.log("Child Sort");
    // Note: props.referenceData will a base Parent(Object) if it's not a root, If you dragged Item is root then props.referenceData will be not return base parent instead it will return null.
    emits('handleChildSort', {
      baseParentReference: !props.isRoot ? {...props.referenceData} : null,
      draggedItem: {
        ...val.data,
      },
    });
  }

  /* Child Reparenting Sort */
  if (props.allowChildReparenting && isReparenting) {
    console.log("Child Reparenting Sort");

    // Add
    const addBaseParentReference = val.item.addBaseParentReference;

    // Remove
    const removeBaseParentReference = val.item.removeBaseParentReference;

    emits('handleChildReparentingSort', {
      add: {
        baseParentReference: addBaseParentReference,
      },
      remove: {
        baseParentReference: removeBaseParentReference,
      },
      draggedItem: {
        baseStatus: 'add',
        Item: {
          ...val.data,
        },
      },
    });

    // Reset the Reference inside the events
    delete val.item.addBaseParentReference;
    delete val.item.removeBaseParentReference;
  }
};

// Add & Remove
const handleReparentingEvents = (e) => {
  if (props.allowChildReparenting){
    console.log(e, props.referenceData);
    if (e.type !== 'add'){
      console.log("remove", e);
      e.item.removeBaseParentReference = {...props.referenceData};
    } else {
      console.log("add", e);
      e.item.addBaseParentReference = {...props.referenceData};
    }
  }
};

</script>

<template>
    <VueDraggable :group="allowChildReparenting && !isRoot ? groupName : false" v-model="list" :animation="animationMilliSec" :ghostClass="ghostClass" @end="(e) => handleEnd(e)" @remove="(e) => handleReparentingEvents(e)" @add="(e) => handleReparentingEvents(e)" >
        <div v-for="item in list" class="w-full" :key="item[props.uniqueKey]">
            <slot :item="item"> </slot>
            <template v-if="item[nestedChildKey] && Array.isArray(item[nestedChildKey])">
                <SceneReOrder class="pl-5" v-model="item[nestedChildKey]" :groupName="groupName" :allowChildReparenting="allowChildReparenting" :allowChildSort="allowChildSort" :isRoot="false" :key="uniqueKey" :nestedChildKey="nestedChildKey" :ghostClass="ghostClass" :referenceData="item"  @handleChildSort="(val) => emits('handleChildSort',val)" @handleChildReparentingSort="(val) => emits('handleChildReparentingSort',val)">
                    <template #default="{ item }">
                        <slot :item="item"></slot>
                    </template>
                </SceneReOrder>
            </template>
        </div>
    </VueDraggable>
</template>

<style scoped>
.ghost {
  opacity: 0.5;
}
</style>
