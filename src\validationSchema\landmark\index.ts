import * as yup from 'yup';
import { decimalRegex, fileValidation, imageSizeValidation } from '../../helpers/validationSchemaHelpers';

export const landmarkSchema = yup.object({
  landmarkName: yup.string().required(),
  landmarkCategory: yup.string().required(),
  thumbnail: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('thumbnail', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
  icon: yup.mixed().test('is-valid-type', 'Invalid file Format. Only png and svg files are accepted', (value) => fileValidation(value as File, 'icon'))
    .test('icon', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
  description: yup.string(),
  distance: yup.number().optional().typeError('value must be a number'),
  walkTiming: yup.number().optional().typeError('value must be a number'),
  transitTiming: yup.number().optional().typeError('value must be a number'),
  carTiming: yup.number().optional().typeError('value must be a number'),
  latitude: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  longitude: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
});

export const multipleLandmarkSchema = yup.object().shape({
  Landmarks: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string().required(),
        category: yup.string().required(),
        thumbnail: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
          .test('thumbnail', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
        icon: yup.mixed().test('is-valid-type', 'Invalid file Format. Only png and svg files are accepted', (value) => fileValidation(value as File, 'icon'))
          .test('icon', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
        description: yup.string(),
        distance: yup.string().matches(decimalRegex, 'Invalid Number.').required(),
        walk_timing: yup.string().matches(decimalRegex, 'Invalid Number.').required(),
        transit_timing: yup.string().matches(decimalRegex, 'Invalid Number.').required(),
        car_timing: yup.string().matches(decimalRegex, 'Invalid Number.').required(),
        lat: yup.string().nullable().transform((value, original) => (original === '' ? null : value)),
        long: yup.string().nullable().transform((value, original) => (original === '' ? null : value)),
      }),
    )
    .strict(),
});

export const editLandmarkSchema = yup.object({
  name: yup.string(),
  category: yup.string(),
  thumbnail: yup.mixed().test('thumbnail', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('thumbnail', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
  icon: yup.mixed().test('is-valid-type', 'Invalid file Format. Only png and svg files are accepted', (value) => fileValidation(value as File, 'icon'))
    .test('icon', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
  description: yup.string(),
  distance: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  walk_timing: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  transit_timing: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  car_timing: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  lat: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  long: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
});
