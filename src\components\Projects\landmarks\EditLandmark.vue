<script setup>
import Spinner from '../../common/Spinner.vue';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { editLandmarkSchema } from '../../../validationSchema/landmark';
import { categoryItems } from '../../../helpers/constants';
import {
  updateLandmark,
  updateLandmarkFiles,
  saveRoutes,
} from '../../../api/projects/landmarks/index';
import { useRoute, useRouter } from 'vue-router';
import { onMounted, ref, watch } from 'vue';
import Modal from '../../common/Modal/Modal.vue';
import { ProjectStore } from '../../../store/project';
import { checkNumberValue } from '../../../helpers/helpers';
import { GetProject } from '../../../api/projects/settings';

const projectStore = ProjectStore();
const router = useRouter();

const loader = ref(false);
const route = useRoute();
const projectId = ref(route.params.project_id);
const landmarkId = ref(route.params.landmark_id);
const disableGetRoute = ref(false);
const spinner = ref(false);
const projectLat = ref(false);
const projectLong = ref(false);

const initialValue = ref({
  // Name:null,
  // Category:null,
  // Distance:null,
  // Description:null,
  // Walk_timing:null,
  // Transit_timing:null,
  // Car_timing:null,
  // Lat:null,
  // Long:null,
});

const handleInitialValue = () => {
  // Initial values which are comming from api
  if (projectStore.landmarks) {
    projectStore.landmarks[landmarkId.value].name
      ? (initialValue.value.name =
          projectStore.landmarks[landmarkId.value].name)
      : '';
    projectStore.landmarks[landmarkId.value].category
      ? (initialValue.value.category =
          projectStore.landmarks[landmarkId.value].category)
      : '';
    checkNumberValue(projectStore.landmarks[landmarkId.value].distance)
      ? (initialValue.value.distance =
          projectStore.landmarks[landmarkId.value].distance)
      : '';
    projectStore.landmarks[landmarkId.value].description
      ? (initialValue.value.description =
          projectStore.landmarks[landmarkId.value].description)
      : '';
    checkNumberValue(projectStore.landmarks[landmarkId.value].walk_timing)
      ? (initialValue.value.walk_timing =
          projectStore.landmarks[landmarkId.value].walk_timing)
      : '';
    checkNumberValue(projectStore.landmarks[landmarkId.value].transit_timing)
      ? (initialValue.value.transit_timing =
          projectStore.landmarks[landmarkId.value].transit_timing)
      : '';
    checkNumberValue(projectStore.landmarks[landmarkId.value].car_timing)
      ? (initialValue.value.car_timing =
          projectStore.landmarks[landmarkId.value].car_timing)
      : '';
    checkNumberValue(projectStore.landmarks[landmarkId.value].lat)
      ? (initialValue.value.lat = projectStore.landmarks[landmarkId.value].lat)
      : '';
    checkNumberValue(projectStore.landmarks[landmarkId.value].long)
      ? (initialValue.value.long =
          projectStore.landmarks[landmarkId.value].long)
      : '';

    disableGetRoute.value = projectStore.landmarks[landmarkId.value].driving || projectStore.landmarks[landmarkId.value].walking || projectStore.landmarks[landmarkId.value].transit;
  }
};
onMounted(() => {
  projectStore.RefreshLandmarks(projectId.value);
  handleInitialValue();
});
watch(
  () => projectStore.landmarks,
  () => {
    handleInitialValue();
  },
);

const handleSubmitForm = (values) => {
  const funcArr = [];
  const landmarkDetail = {};
  const updateRoutes = () => {
    landmarkDetail.driving = null;
    landmarkDetail.walking = null;
    landmarkDetail.transit = null;
  };
  values.name && values.name !== projectStore.landmarks[landmarkId.value].name
    ? (landmarkDetail.name = values.name)
    : {};
  // Values.description && values.description !== projectStore.landmarks[landmarkId.value].description
  //   ? (landmarkDetail.description = values.description)
  //   : {};
  if (values.description !== projectStore.landmarks[landmarkId.value].description) {
    landmarkDetail.description = values.description === null ? null : values.description;
  }
  values.category &&
  values.category !== projectStore.landmarks[landmarkId.value].category
    ? (landmarkDetail.category = values.category)
    : {};
  checkNumberValue(values.distance) &&
  values.distance !== projectStore.landmarks[landmarkId.value].distance
    ? (landmarkDetail.distance = values.distance)
    : {};
  checkNumberValue(values.walk_timing) &&
  values.walk_timing !== projectStore.landmarks[landmarkId.value].walk_timing
    ? (landmarkDetail.walk_timing = values.walk_timing)
    : {};
  checkNumberValue(values.transit_timing) &&
  values.transit_timing !==
    projectStore.landmarks[landmarkId.value].transit_timing
    ? (landmarkDetail.transit_timing = values.transit_timing)
    : {};
  checkNumberValue( values.car_timing) &&
  values.car_timing !== projectStore.landmarks[landmarkId.value].car_timing
    ? (landmarkDetail.car_timing = values.car_timing)
    : {};
  if (values.lat !== projectStore.landmarks[landmarkId.value].lat){
    (landmarkDetail.lat = values.lat === null ? null : values.lat);
    updateRoutes();
  }
  if (values.long !== projectStore.landmarks[landmarkId.value].long){
    (landmarkDetail.long = values.long === null ? null : values.long);
    updateRoutes();
  }

  if (Object.keys(landmarkDetail).length !== 0) {
    landmarkDetail.project_id = projectId.value;
    landmarkDetail.landmark_id = landmarkId.value;
    funcArr.push(updateLandmark(landmarkDetail)); // Adding api calling function to array
  }
  if (values.thumbnail || values.icon) {
    const formData = new FormData();
    formData.append('project_id', projectId.value);
    formData.append('landmark_id', landmarkId.value);
    values.thumbnail ? formData.append('thumbnail', values.thumbnail) : {};
    values.icon ?formData.append('icon', values.icon):{};
    funcArr.push(updateLandmarkFiles(formData)); // Adding api calling function to array
  }

  new Promise((outerResolve, outerReject) => {
    // Calling appis
    loader.value = true;
    Promise.all(funcArr)
      .then(() => {
        document.dispatchEvent(new Event('refreshProjectLandmark'));
        router.go(-1);
        outerResolve();
      })
      .catch((err) => {
        console.log('Error creating Units', err);
        outerReject();
      })
      .finally(() => {
        loader.value = false;
      });
  });
};

const handleGetRoute = () => {
  spinner.value = true;
  const travelModes = [
    google.maps.TravelMode.DRIVING,
    google.maps.TravelMode.TRANSIT,
    google.maps.TravelMode.WALKING,
  ];
  const directionsService = new google.maps.DirectionsService();

  GetProject(route.params.project_id).then((res) => {
    projectLat.value = res.projectSettings.general.lat;
    projectLong.value = res.projectSettings.general.long;

    travelModes.forEach( (mode) => {
      const modeVal = mode.toLowerCase();
      if (!projectStore.landmarks[landmarkId.value][modeVal]){

        const directionsRequest = {
          origin: { lat: projectLat.value, lng: projectLong.value },
          destination: { lat: projectStore.landmarks[landmarkId.value].lat, lng: projectStore.landmarks[landmarkId.value].long },
          travelMode: mode,
          waypoints: [],
          optimizeWaypoints: true,
          provideRouteAlternatives: false,
        };
        console.log(directionsRequest);
        directionsService.route(directionsRequest, (response, status) => {
          if (status === google.maps.DirectionsStatus.OK) {
            const data = {
              project_id: route.params.project_id,
              landmark_id: landmarkId.value,
              mode: mode.toLowerCase(),
              json: response,
            };
            saveRoutes(data)
              .then(() => {
                spinner.value = false;
                disableGetRoute.value = true;
              })
              .catch((error) => console.log(error));
          } else {
            const directionsRequest = {
              origin: { lat: projectStore.landmarks[landmarkId.value].lat, lng: projectStore.landmarks[landmarkId.value].long },
              destination: { lat: projectStore.landmarks[landmarkId.value].lat, lng: projectStore.landmarks[landmarkId.value].long },
              travelMode: google.maps.TravelMode.WALKING,
              waypoints: [],
              optimizeWaypoints: true,
              provideRouteAlternatives: false,
            };
            directionsService.route(directionsRequest, (response, status) => {
              if (status === google.maps.DirectionsStatus.OK) {
                const data = {
                  project_id: route.params.project_id,
                  landmark_id: landmarkId.value,
                  mode: mode.toLowerCase(),
                  json: response,
                };
                saveRoutes(data)
                  .then(() => {
                    spinner.value = false;
                    disableGetRoute.value = true;
                  })
                  .catch((error) => console.log(error));
              }
            });
          }

        });
      }
    });
  });

};

const handleModlaClose = () => {
  document.dispatchEvent(new Event('refreshProjectLandmark'));
  router.go(-1);
};

</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary">
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Edit Project Landmark</h1>
          <p class="modal-subheading-primary">
            Fill details below to Edit Project landmark.
          </p>
        </div>
        <Form
          @submit="handleSubmitForm"
          :validation-schema="editLandmarkSchema"
          class="flex flex-col justify-center"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label for="name" class="label-primary"> Name</label>
              <div class="mt-2">
                <Field
                  v-model="initialValue.name"
                  type="text"
                  name="name"
                  id="name"
                  class="input-primary"
                  placeholder="Enter Landmark Name"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="name"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="category" class="label-primary"> Category</label>
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  v-model="initialValue.category"
                  name="category"
                  id="category"
                  class="select-primary"
                  placeholder="Category"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="!categoryItems">
                    no category!
                  </option>
                  <option
                    v-else
                    :value="option"
                    v-for="(option, index) in categoryItems"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="category"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="thumbnail" class="label-primary">
                Thumbnail Image</label
              >
                <Field
                  type="file"
                  name="thumbnail"
                  id="thumbnail"
                  autocomplete="thumbnail"
                  class="input-primary"
                  placeholder="Upload Thumbnail"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="thumbnail"
                />
            </div>

            <div class="col-span-auto">
              <label for="icon" class="label-primary">
                icon</label
              >
                <Field
                  type="file"
                  name="icon"
                  id="icon"
                  autocomplete="icon"
                  class="input-primary"
                  placeholder="Upload Thumbnail"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="icon"
                />
            </div>

            <div class="col-span-auto">
                <label for="description" class="label-primary"
                  >Description</label
                >
                <Field
                  name="description"
                  v-model="initialValue.description"
                  class="input-primary"
                  placeholder="Write Description here..."
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="description"
                />
              </div>
            <div class="col-span-auto">
              <label for="distance" class="label-primary"> Distance (km)</label>
              <div class="mt-2">
                <Field
                  type="number"
                  v-model="initialValue.distance"
                  name="distance"
                  id="distance"
                  autocomplete="distance"
                  class="h-11 input-primary"
                  placeholder="Distance"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="distance"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="walk_timing" class="label-primary">
                Walk Timing (min)</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  v-model="initialValue.walk_timing"
                  name="walk_timing"
                  id="walk_timing"
                  autocomplete="walk_timing"
                  class="h-11 input-primary"
                  placeholder="Walk Timing"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="walk_timing"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="transit_timing" class="label-primary">
                Transit Timing (min)</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  v-model="initialValue.transit_timing"
                  name="transit_timing"
                  id="transit_timing"
                  autocomplete="transit_timing"
                  class="h-11 input-primary"
                  placeholder="Transit Timing"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="transit_timing"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="car_timing" class="label-primary"> Car Timing (min)</label>
              <div class="mt-2">
                <Field
                  type="number"
                  v-model="initialValue.car_timing"
                  name="car_timing"
                  id="car_timing"
                  autocomplete="car_timing"
                  class="h-11 input-primary"
                  placeholder="Car Timing"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="car_timing"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="lat" class="label-primary"> lat</label>
              <div class="mt-2">
                <Field
                  type="number"
                  v-model="initialValue.lat"
                  name="lat"
                  id="lat"
                  autocomplete="lat"
                  class="h-11 input-primary"
                  placeholder="lat"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="lat"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="long" class="label-primary"> long</label>
              <div class="mt-2">
                <Field
                  type="number"
                  v-model="initialValue.long"
                  name="long"
                  id="long"
                  autocomplete="long"
                  class="h-11 input-primary"
                  placeholder="long"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="long"
                />
              </div>
            </div>
          </div>
          <div class="col-span-auto mt-2">
            <button @click="handleGetRoute()" type="button" :class="(disableGetRoute || spinner) && 'hover:cursor-not-allowed'" :disabled="disableGetRoute || spinner" class="proceed-btn-primary">Get Route&#160;<Spinner v-if="spinner" /></button>
          </div>
          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="handleModlaClose"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="proceed-btn-primary"
            >
              Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
