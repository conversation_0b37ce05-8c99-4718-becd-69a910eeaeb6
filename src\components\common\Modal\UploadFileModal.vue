<script setup>
import { ref } from 'vue';
const emit = defineEmits(['closeModal', 'fileUploaded']);
defineProps({
  showOptions: {
    type: Boolean,
    default: true,
  },
});
const fileInput = ref(null);
const triggerFileInput = () => {
  fileInput.value.click();
};
const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    console.log("File Selected:", file);
    emit("fileUploaded", file);
  }
};
// Handle Drag & Drop File Upload
const handleDrop = (event) => {
  event.preventDefault();
  const file = event.dataTransfer.files[0];
  if (file) {
    console.log("File Dropped:", file);
    emit("fileUploaded", file);
  }
};
</script>
<template>
    {{ console.log('aa', showOptions)   }}
    <div  class="relative transform overflow-hidden rounded-lg bg-white p-3 sm:bg-opacity-10 backdrop-blur-xl  shadow-xl transition-all lg:w-[25rem] h-fit">
        <!-- <div class="p-3 sm:p-6 "> -->
            <div class="h-[10%] w-full">
                <p class="text-xl font-bold">Upload</p>
                <div class="absolute top-[1.5rem] right-5 cursor-pointer" @click=" () => emit('closeModal')">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="x" clip-path="url(#clip0_1158_6409)">
                            <path id="Vector" d="M7.0885 6.00001L11.1645 1.92399C11.2381 1.85295 11.2967 1.76798 11.3371 1.67403C11.3774 1.58007 11.3987 1.47902 11.3996 1.37677C11.4005 1.27452 11.381 1.17312 11.3423 1.07848C11.3035 0.983841 11.2464 0.897861 11.1741 0.825556C11.1018 0.753252 11.0158 0.696071 10.9211 0.657351C10.8265 0.618631 10.7251 0.599147 10.6228 0.600035C10.5206 0.600924 10.4195 0.622167 10.3256 0.662526C10.2316 0.702885 10.1467 0.761551 10.0756 0.835102L5.99961 4.91112L1.92359 0.835102C1.77835 0.694826 1.58383 0.617206 1.38192 0.618961C1.18001 0.620715 0.986864 0.701704 0.844085 0.844482C0.701307 0.987261 0.620319 1.18041 0.618564 1.38232C0.616809 1.58423 0.694429 1.77875 0.834705 1.92399L4.91072 6.00001L0.834705 10.076C0.761155 10.1471 0.702488 10.232 0.662129 10.326C0.621771 10.4199 0.600527 10.521 0.599638 10.6232C0.59875 10.7255 0.618234 10.8269 0.656954 10.9215C0.695674 11.0162 0.752855 11.1021 0.825159 11.1745C0.897464 11.2468 0.983445 11.3039 1.07808 11.3427C1.17272 11.3814 1.27413 11.4009 1.37638 11.4C1.47863 11.3991 1.57968 11.3778 1.67363 11.3375C1.76758 11.2971 1.85256 11.2385 1.92359 11.1649L5.99961 7.08889L10.0756 11.1649C10.2209 11.3052 10.4154 11.3828 10.6173 11.3811C10.8192 11.3793 11.0124 11.2983 11.1551 11.1555C11.2979 11.0127 11.3789 10.8196 11.3807 10.6177C11.3824 10.4158 11.3048 10.2213 11.1645 10.076L7.0885 6.00001Z" fill="#9CA3AF"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_1158_6409">
                                <rect width="12" height="12" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            </div>
            <!-- Hidden File Input -->
            <div class="flex flex-row gap-2 h-[90%] w-full">
                <div class="h-full" :class="showOptions?'w-[45%]':'w-full'">
                    <input type="file" ref="fileInput" class="hidden" @change="handleFileChange" />
                    <div class="mt-2 border-2 border-dashed border-gray-300 rounded-lg text-center content-center bg-gray-100 cursor-pointer h-[15rem]" @dragover.prevent @drop="handleDrop" @click="triggerFileInput">
                        <div class="flex flex-col items-center space-y-2 text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
                                <path d="M18 12.2952H13V12.808C12.9973 13.3496 12.8536 13.8807 12.584 14.3464H18V18.4488H2V14.3464H7.416C7.14635 13.8807 7.00275 13.3496 7 12.808V12.2952H2C1.46957 12.2952 0.960859 12.5113 0.585786 12.896C0.210714 13.2807 0 13.8024 0 14.3464V18.4488C0 18.9928 0.210714 19.5145 0.585786 19.8992C0.960859 20.2839 1.46957 20.5 2 20.5H18C18.5304 20.5 19.0391 20.2839 19.4142 19.8992C19.7893 19.5145 20 18.9928 20 18.4488V14.3464C20 13.8024 19.7893 13.2807 19.4142 12.896C19.0391 12.5113 18.5304 12.2952 18 12.2952Z" fill="#9CA3AF"/>
                                <path d="M6.707 6.3539L9 4.0022V12.808C9 13.08 9.10536 13.3409 9.29289 13.5332C9.48043 13.7255 9.73478 13.8336 10 13.8336C10.2652 13.8336 10.5196 13.7255 10.7071 13.5332C10.8946 13.3409 11 13.08 11 12.808V4.0022L13.293 6.3539C13.4816 6.54073 13.7342 6.6441 13.9964 6.64176C14.2586 6.63943 14.5094 6.53157 14.6948 6.34141C14.8802 6.15126 14.9854 5.89402 14.9877 5.62511C14.99 5.35621 14.8892 5.09714 14.707 4.90371L10.707 0.801308C10.6141 0.705798 10.5038 0.630021 10.3823 0.578317C10.2608 0.526614 10.1305 0.5 9.999 0.5C9.86747 0.5 9.73722 0.526614 9.61573 0.578317C9.49424 0.630021 9.38389 0.705798 9.291 0.801308L5.291 4.90371C5.10349 5.09629 4.99826 5.35737 4.99844 5.62953C4.99863 5.90169 5.10423 6.16262 5.292 6.35493C5.47977 6.54724 5.73434 6.65517 5.99971 6.65498C6.26507 6.65478 6.51949 6.54648 6.707 6.3539Z" fill="#9CA3AF"/>
                                <path d="M15 17.4232C15.5523 17.4232 16 16.964 16 16.3976C16 15.8312 15.5523 15.372 15 15.372C14.4477 15.372 14 15.8312 14 16.3976C14 16.964 14.4477 17.4232 15 17.4232Z" fill="#9CA3AF"/>
                            </svg>
                            <div class="flex text-sm">
                                <p class="ml-1 font-normal"> <span class="font-semibold">Browse</span>
                                 or drag and drop</p>
                            </div>
                            <p class="text-xs font-normal">PNG, JPG or GIF (1.8m x 1.2m)</p>
                        </div>
                    </div>
                </div>
                <div  v-if="showOptions" class="w-[10%] flex justify-center">
                    <p class="flex !items-center justify-center text-sm font-semibold text-gray-500">or</p>
                </div>
                <div  v-if="showOptions" class="h-full w-[45%]">
                    <input type="file" ref="fileInput" class="hidden" @change="handleFileChange" />
                    <div class="mt-2 border-2 border-dashed border-gray-300 rounded-lg text-center content-center bg-gray-100 cursor-pointer h-[15rem]" @dragover.prevent @drop="handleDrop" @click="triggerFileInput">
                        <div class="flex flex-col items-center space-y-2 text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
                                <path d="M18.5 8.7048H13.5V8.192C13.4973 7.65037 13.3536 7.11931 13.084 6.6536H18.5V2.5512H2.5V6.6536H7.916C7.64635 7.11931 7.50275 7.65037 7.5 8.192V8.7048H2.5C1.96957 8.7048 1.46086 8.48869 1.08579 8.10401C0.710714 7.71934 0.5 7.19761 0.5 6.6536V2.5512C0.5 2.00719 0.710714 1.48546 1.08579 1.10078C1.46086 0.716108 1.96957 0.5 2.5 0.5H18.5C19.0304 0.5 19.5391 0.716108 19.9142 1.10078C20.2893 1.48546 20.5 2.00719 20.5 2.5512V6.6536C20.5 7.19761 20.2893 7.71934 19.9142 8.10401C19.5391 8.48869 19.0304 8.7048 18.5 8.7048Z" fill="#9CA3AF"/>
                                <path d="M7.207 14.6461L9.5 16.9978V8.192C9.5 7.91999 9.60536 7.65913 9.79289 7.46679C9.98043 7.27445 10.2348 7.1664 10.5 7.1664C10.7652 7.1664 11.0196 7.27445 11.2071 7.46679C11.3946 7.65913 11.5 7.91999 11.5 8.192V16.9978L13.793 14.6461C13.9816 14.4593 14.2342 14.3559 14.4964 14.3582C14.7586 14.3606 15.0094 14.4684 15.1948 14.6586C15.3802 14.8487 15.4854 15.106 15.4877 15.3749C15.49 15.6438 15.3892 15.9029 15.207 16.0963L11.207 20.1987C11.1141 20.2942 11.0038 20.37 10.8823 20.4217C10.7608 20.4734 10.6305 20.5 10.499 20.5C10.3675 20.5 10.2372 20.4734 10.1157 20.4217C9.99424 20.37 9.88389 20.2942 9.791 20.1987L5.791 16.0963C5.60349 15.9037 5.49826 15.6426 5.49844 15.3705C5.49863 15.0983 5.60423 14.8374 5.792 14.6451C5.97977 14.4528 6.23434 14.3448 6.49971 14.345C6.76507 14.3452 7.01949 14.4535 7.207 14.6461Z" fill="#9CA3AF"/>
                                <path d="M15.5 3.5768C16.0523 3.5768 16.5 4.03597 16.5 4.6024C16.5 5.16882 16.0523 5.628 15.5 5.628C14.9477 5.628 14.5 5.16882 14.5 4.6024C14.5 4.03597 14.9477 3.5768 15.5 3.5768Z" fill="#9CA3AF"/>
                            </svg>
                            <div class="flex text-sm">
                                <p class="font-semibold">Import from Files </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <!-- </div> -->
</template>
<style>
</style>
