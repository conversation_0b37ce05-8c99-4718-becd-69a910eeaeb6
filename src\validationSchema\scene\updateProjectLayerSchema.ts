import { ref } from 'vue';
import * as yup from 'yup';
import { categoryItems } from '../../helpers/constants';

const svgFormats = ['image/svg+xml'];
const jsonFormats = ['application/json'];
interface File{
lastModified:number,
lastModifiedDate:Date,
name:string,
size:number,
type:string,
webkitRelativePath:string
}
function isValidFileType (fileName:File|Blob, type:string) {
  if (fileName !== undefined){
    if (type !== 'deepzoom'){
      // Gsplat
      if (fileName && svgFormats.includes(fileName.type)) {
        return true;
      }
    } else {
      // Deep zoom
      if (fileName && [...jsonFormats, ...svgFormats].includes(fileName.type)) {
        return true;
      }
    }
    return false;

  }
  return true;
}
const imageValidation = ['image/jpeg', 'image/jpg', 'image/webp'];
const iconFileValidation = ['image/png', 'image/svg', 'image/svg+xml'];
const videoValidation = ['video/mp4'];
const zipValidation = ['application/x-zip-compressed', 'application/zip'];
const pdfValidation = ['application/pdf'];

type ValidationKeys = 'image' | 'pdf' | 'zip' | 'video' | 'icon';
const fileValidation = (fileName : File, validateTo: string) => { // ValidateTo => image, pdf, zip, video, icon, 3d
  console.log("File in Validation", fileName);
  if (fileName === null){
    return true;
  }
  if (fileName !== undefined) {
    if (fileName) {
      const validationMap = {
        image: imageValidation,
        pdf: pdfValidation,
        zip: zipValidation,
        video: videoValidation,
        icon: iconFileValidation,
      };
      return validationMap[validateTo as ValidationKeys]?.includes(fileName.type) || false;
    }
    return false;
  }
  return true; // While field is not required then it will not create any error message
};
const videoSizeValidation = (fileName : File) => {
  console.log("-----------file in validation(videoSizeValidation)", fileName);
  console.error("------------file in validation(videoSizeValidation)", fileName);
  console.debug("-------------file in validation(videoSizeValidation)", fileName);

  if (!fileName){
    return true;
  }
  const MAX_VIDEO_SIZE = 40 * 1024 * 1024; // 40 MB in bytes
  if (fileName !== undefined) {
    console.log(fileName.size);
    return fileName.size <= MAX_VIDEO_SIZE;
  }
  return true;
};

export const validationSchema = yup.object({
  type: yup.string().required(),
  name: yup.string().optional().nullable(),
  // iconType: yup.string().required(),
  landmark_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  route_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  project_id: yup.string().when('type', {
    is: (val:string) => val === 'project',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  scene_id: yup.string().when('type', {
    is: (val:string) => val === 'pin' || val==='scene' || val==='building' || val==='floor' || val==='community',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  image_id: yup.string().when('type', {
    is: (val:string) => val === 'image',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  building_id: yup.string().when('type', {
    is: (val:string) => val === 'building' || val ==='floor',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  community_id: yup.string().when('type', {
    is: (val:string) => val === 'community',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  floor_id: yup.string().when('type', {
    is: (val:string) => val === 'floor',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  units: yup.string().when('type', {
    is: (val:string) => val === 'units',
    then: () => yup.array().required(),
    otherwise: () => yup.string().nullable(),
  }),
  amenity_id: yup.string().when('type', {
    is: (val:string) => val === 'amenity',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  amenity_category: yup.string().when('type', {
    is: (val:string) => val === 'amenity_category',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  title: yup.string().when('type', {
    is: (val:string) => val === 'label',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  category: yup.string().when('type', {
    is: (val:string) => val === 'label',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  video_tag: yup.mixed()
    .test('is-valid-type', 'Not a valid Video File', (value) => fileValidation(value as File, 'video'))
    .test('file', 'Video size is more than 40 mb', (value) => videoSizeValidation(value as File))
    .optional().nullable(),
  outputVideoTag: yup.string().optional(),
  showLabel: yup.boolean().optional(),
  group_name: yup.string().when(['type', 'showLabel'], {
    is: (type: string, showLabel: boolean) =>
      type === 'grouped_units' && showLabel === true,
    then: () => yup.string().required('group name is required'),
    otherwise: () => yup.string().optional(),
  }),
  bedrooms: yup.string().when('type', {
    is: (type: string) => type === 'grouped_units',
    then: () => yup.string().required('bedrooms is required'),
    otherwise: () => yup.string().optional(),
  }),
});

/* Gsplat */
export const gsplatFieldValidatteSchema = (type:string, isPlane:boolean) => {
  console.log(type);
  return yup.object({
    name: yup.string().required(),
    type: yup.string().required(),
    landmark_id: yup.string().when('type', {
      is: (val:string) => val === 'landmark',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    route_id: yup.string().when('type', {
      is: (val:string) => val === 'landmark',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    project_id: yup.string().when('type', {
      is: (val:string) => val === 'project',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    scene_id: yup.string().when('type', {
      is: (val:string) => val === 'pin' || val==='scene' || val==='building' || val==='floor',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    image_id: yup.string().when('type', {
      is: (val:string) => val === 'image',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    building_id: yup.string().when('type', {
      is: (val:string) => val === 'building' || val ==='floor',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    floor_id: yup.string().when('type', {
      is: (val:string) => val === 'floor',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    units: yup.string().when('type', {
      is: (val:string) => val === 'units',
      then: () => yup.array().required(),
      otherwise: () => yup.string().nullable(),
    }),
    amenity_id: yup.string().when('type', {
      is: (val:string) => val === 'amenity',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    amenity_category: yup.string().when('type', {
      is: (val:string) => val === 'amenity_category',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    title: yup.string().when('type', {
      is: (val:string) => val === 'label',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    category: yup.string().when('type', {
      is: (val:string) => val === 'label',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    xposition: yup.number().required(),
    yposition: yup.number().required(),
    zposition: yup.number().required(),
    svgFile: type === 'add' && !isPlane ? yup.mixed().required().test('svgFile', 'Not a valid svg', (value) => isValidFileType(value as File|Blob, 'gsplat')) : yup.mixed().nullable().test('svgFile', 'Not a valid svg', (value) => isValidFileType(value as File|Blob, 'gsplat')),
  });
};

/* Deep zoom */
// Create
export const deepzoomCreateLayersValid = yup.object({
  file: yup.mixed().required().test('svgFile', 'It is not a valid SVG nor json', (value) => isValidFileType(value as File|Blob, 'deepzoom')),
  type: yup.string().required(),
  building_id: yup.string().when('type', {
    is: (val:string) => val === 'floor',
    then: () => yup.string().optional().nullable(),
    otherwise: () => yup.string().nullable(),
  }),
});

export const uploadVideoTagValidation = yup.object({
  video_tag: yup.mixed()
    .test('is-valid-type', 'Not a valid Video File', (value) => fileValidation(value as File, 'video'))
    .test('file', 'Video size is more than 40 mb', (value) => videoSizeValidation(value as File))
  // .optional()
    .nullable(),
  outputVideoTag: yup.string().optional(),
  showLabel: yup.string().optional(),
});

// UpdateSchema
export const deepzoomUpdateLayersValid = yup.object({
  type: yup.string().optional().nullable(),
  landmark_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  route_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  project_id: yup.string().when('type', {
    is: (val:string) => val === 'project',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  scene_id: yup.string().when('type', {
    is: (val:string) => val === 'pin' || val==='scene' || val==='building' || val==='floor',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  image_id: yup.string().when('type', {
    is: (val:string) => val === 'image',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  building_id: yup.string().when('type', {
    is: (val:string) => val === 'building' || val ==='floor',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  floor_id: yup.string().when('type', {
    is: (val:string) => val === 'floor',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  units: yup.string().when('type', {
    is: (val:string) => val === 'units',
    then: () => yup.array().required(),
    otherwise: () => yup.string().nullable(),
  }),
  amenity_id: yup.string().when('type', {
    is: (val:string) => val === 'amenity',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  amenity_category: yup.string().when('type', {
    is: (val:string) => val === 'amenity_category',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  title: yup.string().when('type', {
    is: (val:string) => val === 'label',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  category: yup.string().when('type', {
    is: (val:string) => val === 'label',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  name: yup.string().optional().nullable(),
  x: yup.number().required(),
  y: yup.number().required(),
  minandmax: yup.array().required(),
  zindex: yup.array().required(),
  placement: yup.string().when('reSize', {
    is: (val:boolean) => val === true,
    then: () => yup.string().nullable(),
    otherwise: () => yup.string().required(),
  }),
  reSize: yup.boolean(),
  video_tag: yup.mixed()
    .test('is-valid-type', 'Not a valid Video File', (value) => fileValidation(value as File, 'video'))
    .test('file', 'Video size is more than 40 mb', (value) => videoSizeValidation(value as File))
    .optional().nullable(),
  outputVideoTag: yup.string().optional(),
  showLabel: yup.boolean().optional(),
  group_name: yup.string().when(['type', 'showLabel'], {
    is: (type: string, showLabel: boolean) =>
      type === 'grouped_units' && showLabel === true,
    then: () => yup.string().required('group name is required'),
    otherwise: () => yup.string().optional(),
  }),
  bedrooms: yup.string().when('type', {
    is: (type: string) => type === 'grouped_units',
    then: () => yup.string().required('bedrooms is required'),
    otherwise: () => yup.string().optional(),
  }),

});

const formSchema = {
  'type': {
    'name': 'type',
    'label': 'Type',
    'type': 'dropdown',
    'ref': ref('scene'),
    'as': 'select',
    'options': {
      'landmark': {
        'value': 'landmark',
        'toShow': [{ 'field': 'landmark_id', 'optional': false }, { 'field': 'route_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'scene': {
        'value': 'scene',
        'toShow': [{ 'field': 'scene_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'project': {
        'value': 'project',
        'toShow': [{ 'field': 'project_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'pin': {
        'value': 'pin',
        'toShow': [{ 'field': 'scene_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'grouped_units': {
        'value': 'grouped_units',
        'toShow': [{'field': 'showLabel', 'optional': true}],
      },
      'zoom_target': {
        'value': 'zoom_target',
        'toShow': [],
      },
      'image': {
        'value': 'image',
        'toShow': [{ 'field': 'image_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'route': {
        'value': 'route',
        'toShow': [],
      },
      'building': {
        'value': 'building',
        'toShow': [{ 'field': 'building_id', 'optional': false }, { 'field': 'scene_id', 'optional': false }, {'field': 'video_tag', 'optional': false}, {'field': 'showLabel', 'optional': true}],
      },
      'community': {
        'value': 'community',
        'toShow': [{ 'field': 'community_id', 'optional': false }, { 'field': 'scene_id', 'optional': false }, {'field': 'video_tag', 'optional': false}, {'field': 'showLabel', 'optional': true}],
      },
      'floor': {
        'value': 'floor',
        'toShow': [{ 'field': 'building_id', 'optional': false }, { 'field': 'floor_id', 'optional': false }, { 'field': 'scene_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'amenity': {
        'value': 'amenity',
        'toShow': [{ 'field': 'amenity_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'amenitycategory': {
        'value': 'amenitycategory',
        'toShow': [{ 'field': 'amenity_category', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'units': {
        'value': 'units',
        'toShow': [{ 'field': 'units', 'optional': false }],
      },
      'radius': {
        'value': 'radius',
        'toShow': [],
      },
      'label': {
        'value': 'label',
        'toShow': [{ 'field': 'title', 'optional': false }, { 'field': 'category', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'static': {
        'value': 'static',
        'toShow': [],
      },
      'plane': {
        'value': 'plane',
        'toShow': [],
      },
      'none': {
        'value': 'none',
        'toShow': [],
      },

    },
    'defaultValue': 'landmark',
    'static': true,
  },
  'iconType': {
    'name': 'icontype',
    'label': 'Type',
    'type': 'dropdown',
    'ref': ref('scene'),
    'as': 'select',
    'options': {
      'svg': {
        'value': 'svg',
        'toShow': [{ 'field': 'scene_id', 'optional': false }],
      },
    },
    'defaultValue': 'landmark',
    'static': true,
  },
  'landmark_id': {
    'name': 'landmark_id',
    'label': 'landmark_id',
    'type': 'dropdown',
    'ref': ref(),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'project_id': {
    'name': 'project_id',
    'label': 'project_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'scene_id': {
    'name': 'scene_id',
    'label': 'scene_id',
    'type': 'dropdown',
    'ref': ref(),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'route_id': {
    'name': 'route_id',
    'label': 'route_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'image_id': {
    'name': 'image_id',
    'label': 'image_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'building_id': {
    'name': 'building_id',
    'label': 'building_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [
    ],
    'defaultValue': null,

    'static': false,
  },
  'community_id': {
    'name': 'community_id',
    'label': 'community_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [
    ],
    'defaultValue': null,

    'static': false,
  },
  'floor_id': {
    'name': 'floor_id',
    'label': 'floor_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'units': {
    'name': 'units',
    'label': 'units',
    'type': 'multiselect',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },

  'amenity_id': {
    'name': 'amenity_id',
    'label': 'amenity_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,
    'static': false,
  },
  'amenity_category': {
    'name': 'amenity_category',
    'label': 'amenity_category',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,
    'static': false,
  },

  'title': {
    'name': 'title',
    'label': 'title',
    'type': 'text',
    'ref': ref(null),
    'as': 'input',
    'options': [],
    'defaultValue': null,
    'static': false,
  },
  'labelName': {
    'name': 'name',
    'label': 'name',
    'type': 'text',
    'ref': ref(null),
    'as': 'input',
    'options': [],
    'defaultValue': null,
    'static': false,
  },

  'category': {
    'name': 'category',
    'label': 'category',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': categoryItems,
    'defaultValue': null,
    'static': false,
  },
  'outputVideoTag': {
    'name': 'outputVideoTag',
    'label': 'outputVideoTag',
    'id': 'outputVideoTag',
    'type': 'text',
    'outputRef': ref(''),
  },
  'video_tag': {
    'name': 'video_tag',
    'label': 'video_tag',
    'id': 'video_tag',
    'type': 'file',
    'ref': ref(null),
  },
  'showLabel': {
    'name': 'showLabel',
    'label': 'showLabel',
    'id': 'showLabel',
    'type': 'checkbox',
    'ref': ref(false),
  },
};

export default formSchema;
