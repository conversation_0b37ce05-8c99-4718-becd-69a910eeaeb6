import { masterSceneCoordinateLinkType } from '@/enum';
import * as yup from 'yup';

export const CreateCoordinatesSchema = yup.object({
  coordinateName: yup.string().required(),
  latitude: yup.number().required().typeError('value must be a number'),
  longitude: yup.number().required().typeError('value must be a number'),
  linkType: yup.string().required(),
  project_id: yup.string().when('linkType', {
    is: (val:string) => val === masterSceneCoordinateLinkType.PROJECT,
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  scene_id: yup.string().when('linkType', {
    is: (val:string) => val === masterSceneCoordinateLinkType.PROJECT || val === masterSceneCoordinateLinkType.MASTER,
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  link: yup.string().when('linkType', {
    is: (val:string) => val === masterSceneCoordinateLinkType.EXTERNAL,
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  active: yup.boolean(),
});
