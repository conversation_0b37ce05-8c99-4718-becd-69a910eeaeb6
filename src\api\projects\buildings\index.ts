import type { addNewFloorType, Building, bulkFloorUpdateType, deleteBuilding, deleteFloorType, renameFloorType, updateBuilding } from '@/types/building';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function createBuilding (payload:Building):Promise<Building | void>  {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/building/createBuilding`, body: payload }).then((res) => {
      resolve(res as Building);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function getListOfBuildings (projectId :string) : Promise<Record<string, Building>> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/building/getListOfBuildings/${projectId}` }).then((res) => {
      resolve(res as Record<string, Building>);
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function moveBuildingToTrash (obj:deleteBuilding, project_id:string) :Promise<Building | void> {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/building/moveToTrash/${project_id}`, body: obj }).then((res) => {
      resolve(res as Building);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function updateFloor (building_id :string, floor_id :string, payload:object) : Promise<Building | null> {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/building/updateFloor/${building_id}/${floor_id}`, body: payload }).then((res) => {
      resolve(res as Building);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function updateBuilding (building_id : string, payload: updateBuilding):Promise<Building | null> {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/building/updateBuilding/${building_id}`, body: payload }).then((res) => {
      resolve(res as Building);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function bulkUpdateFloors (payload: bulkFloorUpdateType): Promise<string>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/building/updateBulkFloor`, body: payload }).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function renameFloor (payload: renameFloorType): Promise<string>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/building/renameFloor`, body: payload }).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function deleteFloor (payload: deleteFloorType): Promise<string>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/building/deleteFloor`, body: payload }).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function addNewFloor (payload: addNewFloorType): Promise<string>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/building/addNewFloor`, body: payload }).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });
  });
}
