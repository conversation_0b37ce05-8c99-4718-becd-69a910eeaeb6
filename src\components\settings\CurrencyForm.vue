<script setup>
import { ref } from 'vue';
import { UpdateOrganization } from '../../api/organization/index.ts';
import Button from '../common/Button.vue';
import { useForm, useFieldArray, ErrorMessage, Field } from 'vee-validate';
import { currencySchema } from '@/validationSchema/project/settings';
import { Org_Store } from '../../store/organization.ts';
import Multiselect from 'vue-multiselect';
import { currencyList } from '@/helpers/constants';
import Spinner from '@/components/common/Spinner.vue';

const projectOrgStore = Org_Store();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const baseCurrency = ref(null);
const exchangeRates = ref(null);
const loader = ref(false);

/* Methods */

// Initialize form
const { handleSubmit, values } = useForm({
  validationSchema: currencySchema,
  initialValues: {
    exchangeRatio: exchangeRates.value,
  },
});

// Use field array to manage dynamic fields
const { fields, push, remove, replace } = useFieldArray('exchangeRatio');

const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

// Function to add a new currency
const addCurrency = () => {
  push({ currency: '', rate: '' });
};

const setupDataCallBack = (values) => {
  if (values) {
    const data = values;
    console.log(data, values.baseCurrency);

    // Previous Data
    previousData.value = {
      baseCurrency: (data?.baseCurrency ? data?.baseCurrency : null),
      exchangeRatio: (data?.exchangeRatio ? data.exchangeRatio : null),
    };

    initialData.value = {
      baseCurrency: (data?.baseCurrency ? data?.baseCurrency : null),
      exchangeRatio: (data?.exchangeRatio ? data.exchangeRatio : addCurrency()),
    };

    if (data?.baseCurrency) {
      baseCurrency.value = data.baseCurrency;
    }

    if (data?.exchangeRatio) {
      Object.values(data.exchangeRatio).forEach((item) => {
        push(item);
      });
    }
  }
};

const clearFields = () => {
  replace([]);
};// This clears the entire array

function refreshData (res) {
  if (Object.keys(res).length > 0) {
    clearFields();
    setupDataCallBack(projectOrgStore.organization_data);
  }
}

// Submit function
const submitForm = handleSubmit(async (values) => {
  return new Promise((resolve) => {
    loader.value = true;
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...values }; // form values
    newCompareObj.baseCurrency = newCompareObj.baseCurrency ? newCompareObj.baseCurrency : null;
    newCompareObj.exchangeRatio = newCompareObj.exchangeRatio ? newCompareObj.exchangeRatio : null;

    const formObject = {};

    const parms = frameParms(prevData, newCompareObj);

    if (Object.keys(parms).length > 0) {
      if (parms.baseCurrency) {
        formObject.baseCurrency = parms.baseCurrency;
      }
      if (parms.exchangeRatio) {
        formObject.exchangeRatio = parms.exchangeRatio;
      }
    }

    if (Object.keys(parms).length > 0) {
      UpdateOrganization(formObject).then((result) => {

        if (result) {
          loader.value = false;
          projectOrgStore.organization_data = result; // update to store
          refreshData(result);
          resolve(result);
        }
      });
    } else {
      console.log('got in else api call');
      resolve();
    }
  });
});

// Initialize
if (projectOrgStore.organization_data) {
  setupDataCallBack(projectOrgStore.organization_data);
}

</script>

<template>
    <div class="flex flex-col justify-start items-start my-3">

        <!-- Headers -->
        <div class="flex justify-between items-center w-full mb-4">

            <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> Currency </p>

            <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
                @handle-click="() =>
                    isEdit = !isEdit
                ">
                <template v-slot:svg>
                    <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                            fill="white" />
                    </svg>
                </template>
            </Button>

            <div v-if="isEdit" class="flex justify-start items-center gap-3">
                <Button title="Reset" type="button" theme="secondary" @handle-click="() =>
                    isEdit = !isEdit ">
                </Button>
                <label for="editCurrencySettings"
                    :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer']">
                    Save
                  <Spinner v-if="loader" />
                  </label>

            </div>

        </div>

        <!-- View -->
        <div v-show="!isEdit" class="flex flex-col justify-start items-start gap-4 w-full mt-3 mb-5">

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Base Currency: </label>
                <p class="font-medium text-sm text-txt-default capitalize"> {{
                    previousData?.baseCurrency ?
                    previousData?.baseCurrency : '-' }} </p>
            </div>

            <div class="flex flex-col gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50">Exchange Ratio:</label>
                <div class="grid grid-cols-6 gap-2 w-full" v-if="previousData.exchangeRatio">
                    <div v-for="(ratio, index) in previousData.exchangeRatio" :key="index"
                        class="flex justify-between items-center bg-gray-100 p-2 rounded-lg shadow-sm">
                        <span class="font-semibold text-sm text-txt-50">{{ ratio.currency }}</span>
                        <span class="font-medium text-sm text-txt-default">{{ ratio.rate || '-' }}</span>
                    </div>
                </div>
                <div v-else>
                    -
                </div>
            </div>

        </div>

        <!-- Form -->
        <div v-show="isEdit">
            <form @submit="submitForm" class="flex flex-col gap-4">
                <!-- Base Currency Selection -->
                <div class="flex flex-col max-w-40">
                    <label class="font-semibold text-sm text-black">Base Currency <strong>*</strong></label>
                    <Field name="baseCurrency" :model-value="baseCurrency" v-slot="{ field }">
                        <Multiselect :allow-empty="false" v-bind="field" v-model="baseCurrency" :searchable="false"
                            :close-on-select="true" :show-labels="false" placeholder="Choose" :options="currencyList"
                            maxHeight="250">
                        </Multiselect>
                    </Field>
                    <ErrorMessage name="baseCurrency" class="text-red-600 text-xs" />
                </div>

                <!-- Exchange Ratio Inputs -->
                <label class="font-semibold text-sm text-black">Exchange Ratio <strong>*</strong></label>
                <div v-for="(ratio, index) in fields" :key="ratio.key"
                    class="flex gap-4 items-center justify-center min-w-40">
                    <!-- Currency Input -->
                    <div class="flex flex-col min-w-40">
                        <label class="font-semibold text-sm text-gray-600">Currency</label>
                        <Field :name="`exchangeRatio[${index}].currency`" v-slot="{ field }"
                            class="border p-2 rounded w-32">
                            <Multiselect v-bind="field" v-model="values.exchangeRatio[index].currency"
                                :searchable="false" :close-on-select="true" :show-labels="false" placeholder="Choose"
                                :options="currencyList" :maxHeight=250 :allow-empty="false">
                            </Multiselect>
                        </Field>
                        <ErrorMessage :name="`exchangeRatios[${index}].currency`" class="text-red-600 text-xs" />
                    </div>

                    <!-- Rate Input -->
                    <div class="flex flex-col min-w-36">
                        <label class="font-semibold text-sm text-gray-600">Rate</label>
                        <Field :name="`exchangeRatio[${index}].rate`" class="border p-2 rounded w-32" type="number" />
                        <ErrorMessage :name="`exchangeRatios[${index}].rate`" class="text-red-600 text-xs" />
                    </div>

                    <!-- Remove Button -->
                    <button type="button" @click="remove(index)" class="text-red-500">Remove</button>
                </div>

                <!-- Add Currency Button -->
                <Button type="button" title="Add Currency" theme="primary" @handle-click="addCurrency"
                    class="bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer text-sm">Add
                    Currency</Button>

                <!-- Submit Button -->
                <Button id="editCurrencySettings" class="hidden" title="Submit" type="submit"> </Button>
            </form>
        </div>

    </div>
</template>

<style>

</style>
