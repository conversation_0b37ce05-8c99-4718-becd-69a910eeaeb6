<script setup>
import { ref } from 'vue';
import Spinner from '../../common/Spinner.vue';
import { resizeImage, compressImageQuality } from '../../../helpers/helpers';

const errorMsg = ref(null);
const selectedFile = ref(null);
const compressionFilesProgess = ref(false);
const emit = defineEmits(['closeModal', 'handleReplace']);
defineProps({
  loader: Boolean,
});

/* Methods */

// Error handler
const errorHandler = (files) => {
  const file = files;
  console.log(file);
  console.log(typeof file);
  const formats = ['image/jpeg', 'image/jpg']; // only formats
  if (!formats.includes(file.type)){
    errorMsg.value = 'Please check file format should be JPEG or JPG';
    return true;
  }
  errorMsg.value = null;
  return false;
};

// Handle File Change
const handleFileChange = async (e) => {
  console.log("handleFileChange");
  console.log(e);
  console.log(e.target);
  console.log(e.target.values);
  console.log(e.target.files);
  const file = Object.values(e.target.files)[0];
  errorHandler(file); // error handler

  if (!errorMsg.value){

    const targetMb = 10; // 10MB in megaBytes

    // Compression File
    if (file){
      compressionFilesProgess.value = true;
      if (file.size / (1024 *1024) >= targetMb){
        try {
          const response = await compressImageQuality(file, targetMb, 'image/jpeg');
          if (response){
            console.log(response.size / (1024 * 1024) ); // mb
            const compressedResult = new File([response], file.name, { type: file.type });
            selectedFile.value = compressedResult;
            compressionFilesProgess.value = false; // reset the compressionProgress
          }

        } catch (err) {
          selectedFile.value = null;
          compressionFilesProgess.value = false;// reset the compressionProgress
          errorMsg.value = err;
        }
      } else {
        selectedFile.value = file;
        compressionFilesProgess.value = false; // reset the compressionProgress
      }
    }

  } else {
    selectedFile.value = null;
  }
  e.target.value = ''; // reset the files value
};

const handleSave = async () => {

  const frameObj ={
    thumbnail: await resizeImage(selectedFile.value, 1280, 720),
    url: selectedFile.value,
  };
  console.log(frameObj);
  emit('handleReplace', frameObj);
};

</script>

<template>

    <div class="modal-content-primary">
      <div class="p-3 sm:p-6 flex flex-col justify-start items-start gap-2 w-full">
        <div class="flex flex-col justify-start items-start w-full">

            <h1 class="modal-heading-primary">Replace Image</h1>
            <p class="modal-subheading-primary">
                Upload to replace the image Tour.
            </p>

        </div>

        <span class="text-xs font-semibold text-black">Please Note: <span class="italic font-normal text-gray-500"> Only jpeg/jpg are accepted (Max is 10mb, If the images are above 10mb then it will be compressed)..</span> </span>

        <p v-if="errorMsg" class="text-red-700 font-bold text-xs text-left w-full "> {{ errorMsg }}! </p>
        <!-- Images Uploaded -->
        <div class="w-full flex justify-center items-center h-52 overflow-y-auto bg-transparent">

            <div class="w-fit flex flex-col items-center justify-center mb-0 gap-2">
                <label for="uploadimages" :class="[ 'bg-black p-3 rounded text-white text-center mb-0', compressionFilesProgess ? 'opacity-20' : 'opacity-100' ]">
                   + {{ selectedFile ? 'Re-Upload Image' :  'Upload Image'}}
                </label>

                <input @change="handleFileChange" :disabled="compressionFilesProgess" name="uploadimages" id="uploadimages" type="file" class="hidden" />

                <div class="w-fit">
                    <p v-if="selectedFile && !compressionFilesProgess" class="bg-gray-200 p-2 rounded flex justify-start items-center gap-2 font-normal"> Uploaded File: <span class="min-w-auto max-w-[90px] overflow-hidden inline-block text-ellipsis whitespace-nowrap"> {{ selectedFile.name }} </span></p>
                    <p v-else class="p-2 bg-gray-200  rounded"> {{ compressionFilesProgess ? 'Please be patience, Uploading...(If the upload images are above 10mb, it will compressed)' : 'No Image is Upload !'}} </p>
                </div>

          </div>

        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end items-center w-full gap-2">
          <button
              type="button"
              class="cancel-btn-primary"
              @click=" () => emit('closeModal')"
            >
              Cancel
            </button>
            <button
              type="button"
              class="proceed-btn-primary disabled:bg-gray-600"
              :disabled="!errorMsg && !compressionFilesProgess && selectedFile && !loader ? false : true"
              @click="handleSave"
            >
              Save
               <Spinner v-if="loader" />
            </button>
        </div>

      </div>
    </div>

</template>
