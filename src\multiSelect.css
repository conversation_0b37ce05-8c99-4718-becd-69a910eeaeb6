:root {
  --dropDownListBgColor: #f7f8f8;
  --dropDownListBorderColor: var(--dropDownListBgColor);
  --dropDownListSelectionBgColor: #0f0f0f;
  --dropArrowColor: #5b616e;
}

/* ---- Multiselect ---- */

.multiselect {
  background: transparent;
  font-size: 14px;
  font-weight: 400 !important;
}

/* Field */
.label>.multiselect .multiselect__tags{
  border-bottom-left-radius: 0px !important;
  border-top-left-radius: 0px !important;
  border-color:  #e5e7eb !important;
}
.multiselect .multiselect__tags {
  background: transparent;
  height: 40px;
  border: 1px solid rgb(179, 179, 179);
  border-radius: 8px !important;
}

.multiselect .multiselect__single{
    color: black;
    text-overflow: ellipsis;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
}

.multiselect .multiselect__placeholder,
.multiselect .multiselect__input::placeholder {
  color: rgb(140, 140, 140);
  margin-left: 10px;
  text-align: left;
}

/* DropDown Box */
.multiselect .multiselect__content-wrapper,
.multiselect .multiselect__content-wrapper {
 @apply absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm
}

/* ScrollBar */
.multiselect .multiselect__content-wrapper::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.multiselect .multiselect__content-wrapper::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #d8dbdf;
}

/* List Items */
.multiselect .multiselect__option {
@apply relative select-none py-2 pl-3 pr-9 text-gray-900 cursor-pointer hover:bg-gray-200 self-center
}


/* Hover */
 .multiselect .multiselect__content-wrapper .multiselect__option--selected {
  @apply relative select-none py-2 pl-3 pr-9 text-white bg-black cursor-pointer hover:bg-gray-200 hover:text-black self-center

}

 .multiselect .multiselect__option--highlight  {
  background: white;
}

/* Arrow Modification */
.multiselect .multiselect__select::before {
  border: none;
  border-left: 2px solid var(--dropArrowColor);
  border-bottom: 2px solid var(--dropArrowColor);
  width: 10px;
  height: 10px;
  display: inline-block;
  background: transparent;
  transform: rotate(-45deg);
  top: 2%;
}



/*---- Group Multi-Selection ---- */

.customMultiGroupSelection .multiselect__tags {
   height: auto;
   padding: 5px;
}

/* Fields & Pills */
.customMultiGroupSelection .multiselect__tags .multiselect__input{
  position: static;
  margin:10px;
}

.customMultiGroupSelection .multiselect__select{
  top: 10px;
}

.customMultiGroupSelection .multiselect__tags .multiselect__tags-wrap{
  width: 95%;
}

.customMultiGroupSelection .multiselect__tags-wrap{
  display: flex;
  justify-content:start;
  align-items: start;
  flex-wrap: wrap;
  gap: 12px;
}

.customMultiGroupSelection .multiselect__tags-wrap .multiselect__tag{
  background-color: transparent;
  border-radius: 21px;
  border: 1px solid #E9E9E9;
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  gap: 10px;
  margin: 0;
  padding: 6px 14px;
  color: black;
}

.customMultiGroupSelection .multiselect__tags-wrap .multiselect__tag-icon {
  position: static;
  margin: 0;
  width: fit-content;
  margin-top: 1px;
}

.customMultiGroupSelection .multiselect__tag-icon::after , .customMultiGroupSelection .multiselect__tag-icon:hover .multiselect__tag-icon::after {
    color: #49454F !important;
    font-size: 19px;
}