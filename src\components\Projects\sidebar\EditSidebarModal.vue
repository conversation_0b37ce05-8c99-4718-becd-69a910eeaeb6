<script setup>
import Spinner from '../../common/Spinner.vue';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { editSideBarSchema } from '../../../validationSchema/sidebar';
import { editSidebarOption } from '../../../api/projects/sidebar/index';
import { useRoute, useRouter } from 'vue-router';
import { onMounted, ref, watch } from 'vue';
import Modal from '../../common/Modal/Modal.vue';
import { ProjectStore } from '../../../store/project';
import { Org_Store } from '../../../store/organization';
import {sidebar_icons} from '../../../helpers/icons.ts';
import { sidebarTypeList } from '../../../helpers/constants';
import Multiselect from 'vue-multiselect';

const projectStore = ProjectStore();
const organizationStore = Org_Store();
const router = useRouter();

const loader = ref(false);
const route = useRoute();
const projectId = ref(route.params.project_id);
const optionId = ref(route.params.option_id);
const isOpen = ref(null);
const selected = ref(null);
const selectedName = ref(null);
const nameRef = ref(null);
const isIconRequired = ref(false);
const NameList = ref(Object.values(sidebar_icons).map((elem) => {
  return {id: elem.name, name: elem.name};
}));

projectStore.RefreshScenes(projectId.value);
projectStore.RefreshSidebarOptions(projectId.value);
organizationStore.RefreshMasterScenes();

const initialValues = ref({});
// const icon_id = ref();
// const name = ref();
const nameChanged = ref(false);
const scene_id = ref();
const selectedType = ref();
const link = ref();

const sidebarNames = Object.values(sidebar_icons).map((elem) => elem.name);
const handleInitialvalues = () => {
  if (projectStore.sidebarOptions){
    initialValues.value = projectStore.sidebarOptions[optionId.value];
    const icon = Object.values(sidebar_icons).find((elem) => elem.id === projectStore.sidebarOptions[optionId.value].icon_id);
    selected.value = icon;
    selectedName.value = {id: projectStore.sidebarOptions[optionId.value].name, name: projectStore.sidebarOptions[optionId.value].name};
    scene_id.value= projectStore.sidebarOptions[optionId.value].scene_id;
    selectedType.value = projectStore.sidebarOptions[optionId.value].type;
    link.value = projectStore.sidebarOptions[optionId.value].link;
  }

};
onMounted(() => {
  handleInitialvalues();
});

watch(() => projectStore.sidebarOptions, () => {
  handleInitialvalues();
});

watch(() => selectedType.value, () => {
  if (selectedType.value===initialValues.value.type){
    scene_id.value = initialValues.value.scene_id;
  } else {
    scene_id.value = null;
  }
});

const handleEditSidebarOption = (data) => {
  loader.value = true;
  editSidebarOption(data).then(() => {
    document.dispatchEvent(new Event('refreshSidebarList'));
    router.go(-1);
  }).catch((err) => {
    console.log('output->err create sidebar : ', err);
  }).finally(() => {
    loader.value = false;
  });
};
function getChangedValues (layer, query){
  console.log("oldValue", layer);
  console.log("newValue", query);
  const changedFields = {};
  if (query.units){
    changedFields.units = [];
    if (layer.units){
      let count = 0;
      for (const subkey in layer.units){
        if (subkey in query.units && (layer.units[subkey].name !== query.units[subkey].name)){
          changedFields.units[count] = query.units[subkey];
          count ++;
        }
      }
      let count1 = 0;
      for (const subkey in query.units){
        if (!(subkey in layer.units)){
          changedFields.units[count1] = query.units[subkey];
          count1 ++;
        }
      }
    } else {
      for (const subkey in query.units){
        if (query.units[subkey].name){
          console.log("111", query.units[subkey]);
          changedFields.units[subkey] = query.units[subkey];
          console.log("222", changedFields.units[subkey]);
        }
      }
    }
  } else {
    for (const key in layer){
      if (key in query && layer[key] !== query[key]){
        changedFields[key] = query[key];
      }
    }

    for (const key in query){
      if (!(key in layer)){
        changedFields[key] = query[key];
      }
    }
  }
  if (Object.keys(changedFields).length > 0){
    changedFields.type = query.type;
  }
  return changedFields;
}
const handleSubmitForm = (values) => {
  console.log(values);
  const obj = {};

  initialValues.value.name !== values.name.name ? obj.name=values.name.name : {};

  if (nameChanged.value){
    obj.icon_id = Object.values(sidebar_icons).find((elem) => elem.name === values.name).id;
    console.log("first loop", obj.icon_id);
  } else {
    if (initialValues.value.icon_id !== values.icon_id) {
      obj.icon_id = values.icon_id;
      console.log("second loop", obj.icon_id);
    }
  }
  values.type === 'custom' && initialValues.value.link !== values.link && values.link? obj.link=values.link:{};
  (values.type === 'projectscene' || values.type === 'masterscene') &&  initialValues.value.scene_id !== values.scene_id && values.scene_id? obj.scene_id=values.scene_id:{};

  if (Object.keys(obj).length !== 0 && Object.values(getChangedValues(initialValues.value, values)).length > 0){
    obj.type = values.type;
    obj.project_id=projectId.value;
    obj.id = optionId.value;
    handleEditSidebarOption(obj);
  }

};
function selectOption (val){
  isOpen.value = false;
  const id = Object.keys(sidebar_icons).find((key) => {
    return sidebar_icons[key].name === val.name;
  });
  const obj = {id: id, ...val};
  selected.value = obj;
}
watch(selectedName, (newVal) => {
  if (sidebarNames.includes(newVal)){
    isIconRequired.value = false;
    if (initialValues.value.name !== selectedName.value){
      nameChanged.value = true;
    }
    console.log("not required", newVal);
  } else {
    isIconRequired.value = true;
    nameChanged.value = false;
    console.log("required", newVal);
  }
});

const HandleAddName = (newValue) => {
  const newValObj = {id: newValue, name: newValue};
  selectedName.value = newValObj;
  NameList.value.push(newValObj);
};

</script>

<template>
    <Modal :open="true">
        <div
            class="bg-white border border-gray-200 w-[536px] rounded-lg">
            <div class="">
                <div class="h-9  flex justify-between items-center px-2.5 py-2 border-b border-gray-200 rounded-t-lg">
                    <p
                        class="text-sm font-medium text-gray-900">
                        Edit SideBar</p>
                        <button  class="fill-gray-400" @click="router.go(-1)">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
                </div>
                <Form @submit="handleSubmitForm"
                    :validation-schema="editSideBarSchema"

                    class="flex flex-col justify-center p-2 mb-0">
                    <div
                        class="p-2.5 grid grid-cols-2 gap-4">

                        <div class="col-span-auto">
                            <label for="type"
                                class="text-sm font-medium text-gray-900">
                                Type</label>
                            <div class="">
                                <Field as="select" type="text"
                                    name="type"
                                    id="type"
                                    v-model="selectedType"
                                    class="flex w-full rounded-lg h-10 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                    placeholder="Type">
                                    <option value="" disabled>
                                        Choose </option>
                                    <option value="" disabled
                                        v-if="!sidebarTypeList">
                                        no type yet! </option>
                                    <option v-else
                                        :value="option"
                                        v-for="option, index in  sidebarTypeList"
                                        :key="index"
                                        class="text-black"> {{
                                            option }} </option>
                                </Field>
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="type" />
                            </div>
                        </div>

                        <div class="col-span-auto relative">
                            <label class="text-sm font-medium text-gray-900">Name</label>
            <div ref="nameRef" class="relative">
                <Field  name="name" :model-value="selectedName" v-slot="{ field }">

                                      <Multiselect class="h-8"   v-bind="field" :taggable="true" @tag="(val)=>HandleAddName(val)" :allow-empty="false" v-model="selectedName" :searchable="true" :show-labels="false" :custom-label="(val) => val.name" placeholder="Name"   @select="handleProjectSelection" :options="NameList" track-by="id" maxHeight="250" >
                                      </Multiselect>
                                    </Field>
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="name" />
                            </div>
                        </div>

                        <div class="col-span-auto" v-if="selectedType ==='projectscene' || selectedType === 'masterscene'">
                            <label for="scene_id"
                                class="text-sm font-medium text-gray-900">
                                scene</label>
                            <div class="mt-2">
                                <Field as="select" type="text"
                                v-model="scene_id"

                                    name="scene_id"
                                    id="scene_id"
                                     class="flex w-full rounded-lg h-10 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                    placeholder="scene">
                                    <option value="">
                                        Choose </option>
                                    <option value="" disabled
                                        v-if="selectedType==='masterscene'?!organizationStore.masterScenes : !projectStore.scenes">
                                        no type yet! </option>
                                    <option v-else
                                        :value="option.sceneData._id"
                                        v-for="option, index in  selectedType==='masterscene'?organizationStore.masterScenes : projectStore.scenes "
                                        :key="index"
                                        class="text-black"> {{
                                            option.sceneData.name }} </option>
                                </Field>
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="scene_id" />
                            </div>
                        </div>
                        <div class="col-span-auto" v-if="selectedType === 'custom'">
                <label for="link" class="text-sm font-medium text-gray-900"
                  >link</label
                >
                <Field
                  v-model="link"
                  name="link"
                  class="mt-2 flex w-full rounded-lg h-10 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal"
                  placeholder="Enter Link"
                />
                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="link" />
              </div>
              <!-- <div class="col-span-auto" v-if="selectedType === 'custom'">
                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="link_or_scene" />
              </div> -->
              <div v-if="isIconRequired" class="w-full relative pb-2.5">
  <label class="text-sm font-medium text-gray-900">Icon Type</label>
  <div class="relative">
    <button
      @click="isOpen = !isOpen"
      type="button"
      class="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-left shadow-sm"
    >
      <div v-if="selected" class="flex h-full items-center space-x-2">
        <div v-html="selected.active"></div>
      </div>
      <span v-else class="text-gray-500">Choose an icon</span>
      <svg class="absolute right-2 top-3 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <div
      v-if="isOpen"
      class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-50 overflow-auto"
    >
      <div class="grid grid-cols-3 gap-1 p-2">
        <button
          v-for="option in sidebar_icons"
          :key="option.id"
          @click="selectOption(option)"
          type="button"
          class="flex flex-col items-center justify-center p-3 rounded-md hover:bg-gray-100 focus:bg-gray-100 focus:outline-none transition-colors duration-150"
          :class="{ 'bg-blue-50 border border-blue-200': selected?.name === option.name }"
        >
          <div v-html="option.active" class="w-6 h-6 mb-1"></div>
        </button>
      </div>
    </div>
  </div>

  <div v-if="selected" class="mt-3 hidden">
    <label class="text-sm font-medium text-gray-900">Icon Label</label>
    <Field
      v-model="selected.id"
      name="icon_id"
      type="text"
      class="w-full mt-1 rounded-md border border-gray-300 px-3 py-1 text-sm placeholder:text-gray-400"
    />
  </div>
  <ErrorMessage  name="icon_id" class="absolute text-sm text-rose-500 mt-1" />
</div>
                    </div>
                    <div
                        class="flex justify-center gap-x-3">
                        <!-- <button type="button"
                            class="cancel-btn-primary"
                            @click="router.go(-1)">Cancel</button> -->
                        <button type="submit"
                            :disabled="loader"
                            class="h-10 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">Save
                            <Spinner v-if="loader" />
                        </button>
                    </div>
                </Form>
            </div>
        </div>
    </Modal>
</template>

<style scoped>
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
</style>
