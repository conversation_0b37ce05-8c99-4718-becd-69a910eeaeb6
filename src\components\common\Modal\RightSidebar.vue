<script setup>

import { XMarkIcon } from '@heroicons/vue/20/solid';

const emit = defineEmits(['closeModal']);
defineProps({
  open: Boolean,
});

</script>
<template>
    <div v-if="open"
        class="fixed flex justify-end w-72 h-full right-0 z-100">

        <div
            class="absolute top-30 -left-12 flex w-fit justify-center pt-5 px-2">
            <button type="button"
                class="-mt-9 h-fit w-fit p-1.5 bg-bg-1000 dark:bg-bg-50 hover:bg-gray-200 bg-opacity-90 rounded-full cursor-pointer"
                @click="emit('closeModal')">
                <span class="sr-only">Close sidebar</span>
                <XMarkIcon class="h-6 w-6 text-txt-100"
                    aria-hidden="true" />
            </button>
        </div>
        <div class="h-full w-full bg-bg-1000 dark-bg-bg-50">
            <slot>
            </slot>
        </div>

    </div>
</template>

<style></style>
