options:
 logging: CLOUD_LOGGING_ONLY  # Use Cloud Logging for logs
 
steps:

# Build the container image
- name: gcr.io/cloud-builders/docker
  args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api-frontend/$TAG_NAME:${COMMIT_SHA}', -f , prodDockerfile, '.']
  id: Building the container image


# Push the container image to Container Registry
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api-frontend/$TAG_NAME:${COMMIT_SHA}']
  id: Pushing the image to registry



# Deploy container image to Cloud Run
- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', '${_SERVICE_NAME}', '--image', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api-frontend/$TAG_NAME:${COMMIT_SHA}', '--region', 'us-central1', '--platform', 'managed', "--allow-unauthenticated"]

# Invalidate Cloud CDN cache for dashboard.propvr.tech
- name: 'gcr.io/cloud-builders/gcloud'
  args: [
    'compute', 'url-maps', 'invalidate-cdn-cache',
    'propvr-tech-lb',               # URL map name (load balancer)
    '--host', 'dashboard.propvr.tech',
    '--path', '/*', # Invalidate all paths under
    '--async'
  ]
  id: "Invalidating CDN cache for dashboard.propvr.tech/*"

- name: 'curlimages/curl'
  entrypoint: 'curl'
  args: [
      '-X', 'POST',
      'https://api.cloudflare.com/client/v4/zones/0f9b691d2247ba4b25773a2c4e160779/purge_cache',
      '-H', 'Authorization: Bearer DHwCe9TfnoSpExM76fJzOiVz8DZvZjC_arhC5Qa9',
      '-H', 'Content-Type: application/json',
       '--data',
      '{"files":["https://dashboard.propvr.tech/*"]}'
  ]
