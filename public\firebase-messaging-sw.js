// Give the service worker access to Firebase Messaging.
importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-messaging-compat.js');

// Initialize Firebase with config injected by Vite build process
firebase.initializeApp({
  apiKey: '__FIREBASE_API_KEY__',
  authDomain: '__FIREBASE_AUTH_DOMAIN__',
  databaseURL: '__FIREBASE_DATABASE_URL__',
  projectId: '__FIREBASE_PROJECT_ID__',
  storageBucket: '__FIREBASE_STORAGE_BUCKET__',
  appId: '__FIREBASE_APP_ID__',
  messagingSenderId: '__FIREBASE_MESSAGING_SENDER_ID__'
});

// Retrieve messaging instance
const messaging = firebase.messaging();

async function storeNotification(payload) {
  try {
    const cache = await caches.open('notifications-cache');
    const notificationId = 'notification-' + Date.now();
    const notificationData = {
      id: notificationId,
      payload: payload,
      timestamp: Date.now(),
      read: false
    };
    
    // Create a Response object with our notification data
    const response = new Response(JSON.stringify(notificationData));
    await cache.put(`/notifications/${notificationId}`, response);
    
    // Update the notification index
    const indexResponse = await cache.match('/notifications/index') || new Response('[]');
    const indexData = await indexResponse.json().catch(() => []);
    indexData.push(notificationId);
    await cache.put('/notifications/index', new Response(JSON.stringify(indexData)));
    
    console.log('Notification stored in cache:', notificationId);
  } catch (error) {
    console.error('Error storing notification in cache:', error);
  }
}

// Set up background message handler
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  // Customize notification here
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQeLoOUdJLW3VQXv8o1Nrf_QHtXrVpqi67H6A&s',
  };
  storeNotification(payload);

  self.clients.matchAll().then(clients => {
    console.log(`Found ${clients.length} clients to notify`);
    clients.forEach(client => {
      client.postMessage({
        type: 'NEW_NOTIFICATION',
        payload: payload
      });
    });
  });
});
// Add notification click event listener
self.addEventListener('notificationclick', function(event) {
  console.log('[Service Worker] Notification click received', event);
  
  // Close the notification
  event.notification.close();
  
  // This example opens the main app
  const urlToOpen = new URL('/', self.location.origin).href;
  
  // Check if a window is already open and focus it
  event.waitUntil(
    self.clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    })
    .then(function(clientList) {
      // Try to find an existing client to focus
      const matchingClient = clientList.find(client => 
        client.url === urlToOpen && 'focus' in client
      );
      
      if (matchingClient) {
        return matchingClient.focus();
      }
      
      // If no matching client is found, open a new window
      return self.clients.openWindow(urlToOpen);
    })
  );
});

console.log('Firebase messaging service worker initialized with build-time config');