<script setup>
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { editGallerySchema } from '../../../validationSchema/gallery';
import Multiselect from 'vue-multiselect';

import { uiOperations } from '../../../store/uiOperations';
import Modal from '../../common/Modal/Modal.vue';
import { useRouter } from 'vue-router';
import { ProjectStore } from '../../../store/project';
import { galleryDataSyncUp, getCategories, updateGalleryItem } from '../../../api/projects/gallery';
import { apiMediaTypes } from '../../../enum';
import { resizeImage } from '../../../helpers/helpers';

const uiStore = uiOperations();
const router = useRouter();
const categoryList = ref([]);
const projectStore = ProjectStore();
const route = useRoute();
const projectId = ref(route.params.project_id);
const loader = ref(false);
const galleryItemId = ref(route.params.gallery_item_id);
const catRef = ref();
const type = ref();
const name = ref();
const link = ref();
const tourId = ref();

projectStore.RefreshVirtualTours(projectId.value);

const addTag = (newTag) => { // To add new category if it is not in the
  const tag = {
    name: newTag,
  };
  categoryList.value.push(tag); // Adding to list
  catRef.value = tag; // Selecting same new tag

};

// Calling getcategories api
getCategories(projectId.value).then((res) => {
  categoryList.value = res.map((elem) => {
    return {name: elem};
  });
  console.log('output->categoryList.value', categoryList.value);
}).catch((err) => {
  console.log('output->err', err);
});

// Calling the getGalleryItem api if data is not already present
projectStore.RefreshGalleryItems(projectId.value);

const initialData = {
  type: null,
  category: null,
  name: null,
};

const handleInitialData = () => {
  if (projectStore.galleryItems){
    // For showing in the form
    type.value = projectStore.galleryItems[galleryItemId.value].type;
    name.value = projectStore.galleryItems[galleryItemId.value].name;
    catRef.value = {name: projectStore.galleryItems[galleryItemId.value].category};
    link.value = projectStore.galleryItems[galleryItemId.value].link;
    tourId.value = projectStore.galleryItems[galleryItemId.value].tourId;

    // For change reference
    initialData.type = projectStore.galleryItems[galleryItemId.value].type;
    initialData.category = projectStore.galleryItems[galleryItemId.value].category;
    initialData.name = projectStore.galleryItems[galleryItemId.value].name;
    initialData.link = projectStore.galleryItems[galleryItemId.value].link;
    initialData.tour_id = projectStore.galleryItems[galleryItemId.value].tour_id;
  }
};
onMounted(() => {
  handleInitialData();
});
watch(() => projectStore.galleryItems, () => {
  handleInitialData();
});

const handleUpdateGalleryItem = (formData) => { // Update gallery item
  formData.append('project_id', projectId.value);
  formData.append('item_id', galleryItemId.value);
  loader.value = true;
  updateGalleryItem(formData).then(async () => {
    await galleryDataSyncUp(projectId.value);
    document.dispatchEvent(new Event('refreshGalleryItemsList'));
    router.go(-1);
  }).catch((err) => {
    loader.value = false;
    console.log(err);
    uiStore.handleApiErrorMessage(err.message._message);
  }).finally(() => {
    loader.value = false;
  });
};

const handleSubmit = async (values) => {
  // Create a new FormData object to hold the form data
  const formData = new FormData();
  console.log(initialData, values.link);

  // Append changed fields to FormData
  if (initialData.type !== values.type) {
    formData.append('type', values.type);
  }

  if (initialData.category !== values.category.name) {
    formData.append('category', values.category.name);
  }
  if (values.link && initialData.link !== values.link) {
    formData.append('link', values.link);
  }
  if (values.tour_id && initialData.tour_id !== values.tour_id) {
    formData.append('tour_id', values.tour_id);
  }

  if (initialData.name !== values.name) {
    formData.append('name', values.name);
  }

  if (values.file) {
    formData.append('file', values.file);
  }

  // Check if the file is an image type
  const isImageType = values.type === 'image' || values.type === '360_image';

  // Resize dimensions
  let resizeMaxWidth = 1280;
  let resizeMaxHeight = 720;

  if (values.type=== '360_image'){
    resizeMaxWidth = 1000;
    resizeMaxHeight = 1000;
  }

  // Function to handle thumbnail resizing and error checking
  const processThumbnail = async (thumbnail) => {
    const resizedThumbnail = await resizeImage(thumbnail, resizeMaxWidth, resizeMaxHeight);
    formData.append('thumbnail', resizedThumbnail);
    handleUpdateGalleryItem(formData);
  };

  // Handle image resizing and appending thumbnail
  if (values.file && isImageType) {
    await processThumbnail(values.file);
  } else if (values.thumbnail) {
    await processThumbnail(values.thumbnail);
  } else if (!formData.entries().next().done) { // Check if formData is not empty
    handleUpdateGalleryItem(formData); // Adding item_id & item_id in the handleUpdateGalleryItem function
  }
};

</script>

<template>
    <Modal :open="true">
        <div
            class="modal-content-primary">
            <div class="p-3 sm:p-6 ">
                <div class="mb-2">
                    <h1
                        class="modal-heading-primary">
                        Edit Gallery Item</h1>
                    <p class="modal-subheading-primary">Fill details
                        below
                        to Edit Gallery Item.</p>
                </div>
                <Form :validation-schema="editGallerySchema"
                    @submit="handleSubmit">

                    <div
                        class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">

                        <div class="col-span-auto">
                            <label for="name"
                                class="label-primary">
                                Name</label>
                            <Field as="input" type="text"
                            v-model="name"
                                name="name" autocomplete
                                id="name"
                                class="input-primary"
                                :placeholder="`Enter Name`" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="name" />
                        </div>

                        <div class="col-span-auto">
                            <label for="type"
                                class="label-primary">
                                Type</label>
                            <Field
                            v-model="type"
                                disabled
                                as="select" type="text"
                                name="type" id="type"
                                autocomplete="type"
                                class="select-primary"
                                :placeholder="`Seclect Type`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!apiMediaTypes">
                                    No Type found ! </option>
                                <option v-else
                                    :value="option"
                                    v-for="option, index in  apiMediaTypes"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="type" />
                        </div>

                        <div
                            class="col-span-auto">
                            <label for="category"
                                class="label-primary">Category</label>
                            <div class="mt-2">
                               <Field  name="category" :model-value="catRef" v-slot="{ category }">

                                      <Multiselect
                                      v-bind="category"
                                      v-model="catRef"
                                      tag-placeholder="Add this as new category"
                                      placeholder="Search or add Category"
                                      label="name"
                                      track-by="name"
                                      :multiple="false"
                                      :taggable="true"
                                      @tag="addTag"
                                      :options="categoryList" maxHeight="250" >
                                      </Multiselect>

                                    </Field>
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="category" />
                            </div>
                        </div>

                        <div
                            class="col-span-auto" v-if="type!=='embed_link' && type!=='virtual_tour'">
                            <label for="file"
                                class="label-primary">Upload
                                File</label>
                            <div class="mt-2">
                                <Field type="file"
                                    name="file"
                                    id="file"
                                    autocomplete="highRes"
                                    class="input-primary"
                                    placeholder="Upload High Resulation Image" />
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="file" />
                            </div>
                        </div>

                        <div class="col-span-auto"  v-if="type ==='virtual_tour'">
                            <label for="tour_id"
                                class="label-primary">
                                select virtual Tour</label>
                            <Field
                            v-model="tourId"
                                as="select" type="text"
                                name="tour_id" id="tour_id"
                                autocomplete="tour_id"
                                class="select-primary"
                                :placeholder="`Seclect Tour`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!projectStore.virtualtours">
                                    No Tour found ! </option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.virtualtours"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.tour_name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="tour_id" />
                        </div>

                        <div class="col-span-auto" v-if="type==='embed_link'">
                            <label for="link"
                                class="label-primary">
                                Embed Link</label>
                            <Field as="input" type="url"
                            v-model="link"
                                name="link" autocomplete
                                id="link"
                                class="input-primary"
                                :placeholder="`Enter link`" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="link" />
                        </div>

                        <div
                            class="col-span-auto" v-if="type !== 'image' && type !== '360_image'">
                            <label for="thumbnail"
                                class="label-primary">Upload
                                Thumbnail</label>
                            <div class="mt-2">
                                <Field type="file"
                                    name="thumbnail"
                                    id="thumbnail"
                                    class="input-primary"
                                    placeholder="Upload Low Resulation Image" />
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="thumbnail" />
                            </div>
                        </div>
                    </div>
                    <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.go(-1)"
                            ref="cancelButtonRef">Cancel</button>
                        <button type="submit"
                            :disabled="loader"
                            class="proceed-btn-primary">Save
                            <Spinner v-if="loader" />
                        </button>
                    </div>
                </Form>

            </div>
        </div>
    </Modal>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
</style>
