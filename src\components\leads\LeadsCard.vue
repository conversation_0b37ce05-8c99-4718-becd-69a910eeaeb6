<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
// import CalendarSvg from '../../assets/svgs/CalendaerSvg.vue';
// import TimeSvg from '../../assets/svgs/TimeSvg.vue';
import DropdownButtonComp from '../common/DropdownButtonComp.vue';
import EditIcon from '../../assets/svgs/indexsvgs/EditIcon.vue';
import DeleteIcon from '../../assets/svgs/indexsvgs/DeleteIcon.vue';

const isDropdownOpen = ref(false);
const dropdownWrapper = ref(null);
const selectedIndex = ref(null); // Tracks the open dropdown

const toggleDropdown = (index) => {
  isDropdownOpen.value = !isDropdownOpen.value;
  // Toggle dropdown visibility: If the dropdown is already open, close it; otherwise, open it
  selectedIndex.value = selectedIndex.value === index ? null : index;
};

function handleClickOutside (event) {
  if (
    dropdownWrapper.value &&
        !dropdownWrapper.value.contains(event.target)
  ) {
    isDropdownOpen.value = false;
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});

const connect = (method, phoneNumber, email) => {
  if (method === 'WhatsApp') {

    const formattedPhone = `+${phoneNumber}`;

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    if (isMobile) {

      window.location.href = `https://wa.me/${formattedPhone}`;
    } else {
      // For desktop, use the WhatsApp Web link
      window.open(`https://web.whatsapp.com/send?phone=${formattedPhone}`, '_blank');
    }

  } else if (method === 'Call') {
    const formattedPhone = `tel:${phoneNumber}`;
    window.location.href = formattedPhone;
  } else if (method === 'Email') {
    // Ensure the email is encoded correctly
    const encodedEmail = encodeURIComponent(email);

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    if (isMobile) {
      // For mobile, open the default email app with the email address
      const mailToLink = `mailto:${email}`;
      window.location.href = mailToLink;
    } else {
      // For desktop, open Gmail's compose window in a new tab
      const subject = "Subject"; // You can customize this or make it dynamic
      const body = "Body content here"; // You can customize this or make it dynamic
      const emailLink = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodedEmail}&su=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(emailLink, '_blank');
    }
  }

  dropdownOpen.value = false; // Close the dropdown after selection
};

// Helper function to format phone number (remove non-numeric characters)

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  organizationStore: {
    type: Object,
    required: true,
  },
  isMobile: {
    type: Boolean,
    default: false,
  },
});

function verifyUser (id) {
  if (props.organizationStore.users[id]) {
    return true;
  }
  return false;
}

const emit = defineEmits(['action']);
const showMenu = ref(false);

// const toggleMenu = () => {
//   showMenu.value = !showMenu.value;
// };

const closeMenu = () => {
  showMenu.value = false;
};

// const getInterestedProject = computed(() => {
//   if (!props.item?.interested_in?.[0]?.project_id) {
//     return '-';
//   }
//   const projectId = props.item.interested_in[0].project_id;
//   return props.organizationStore.projects[projectId]?.name || projectId;
// });

const handleAction = (action) => {
  emit('action', { action, item: props.item });
  closeMenu();
};

// const statusClass = computed(() => {
//   const classes = {
//     'new': 'bg-green-100 border-lime-600',
//     'hot': 'bg-orange-100 border-orange-500',
//     'cold': 'bg-cyan-50 border-cyan-500',
//     'warm': 'bg-orange-100 border-amber-400',
//   };
//   return classes[props.item.lead_status] || 'bg-gray-100 border-gray-400';
// });

const menuSvg = `<svg width="3" height="12" viewBox="0 0 3 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.56066 0.71595C3.14645 1.30174 3.14645 2.25149 2.56066 2.83727C1.97488 3.42306 1.02512 3.42306 0.439339 2.83727C-0.146446 2.25149 -0.146446 1.30174 0.439339 0.71595C1.0251 0.130165 1.97485 0.130165 2.56066 0.71595Z" fill="currentColor"/>
    <path d="M2.56066 8.71595C3.14645 9.30174 3.14645 10.2515 2.56066 10.8373C1.97488 11.4231 1.02512 11.4231 0.439339 10.8373C-0.146446 10.2515 -0.146446 9.30174 0.439339 8.71595C1.0251 8.13016 1.97485 8.13016 2.56066 8.71595Z" fill="currentColor"/>
    <path d="M2.56066 16.716C3.14645 17.3017 3.14645 18.2515 2.56066 18.8373C1.97488 19.4231 1.02512 19.4231 0.439339 18.8373C-0.146446 18.2515 -0.146446 17.3017 0.439339 16.716C1.0251 16.1302 1.97485 16.1302 2.56066 16.716Z" fill="currentColor"/>
</svg>`;

const dropdownOptions = [
  {
    title: 'Edit',
    iconComp: EditIcon,
    action: () => handleAction('edit'),
  },
  {
    title: 'Delete',
    iconComp: DeleteIcon,
    action: () => handleAction('delete'),
  },
];

</script>

<template>
    <div
        class="bg-white dark:bg-bg-150 rounded-2xl shadow-[0px 4px 16px 0px #0000001A] border border-gray-300 dark:border-bg-200 shadow-sm sm:!mb-0 mb-4 relative">
        <!-- Mobile View -->

        <!-- Desktop View -->

        <div class="flex flex-row h-full p-4 justify-between">
            <div class="flex flex-col gap-3  ">
                <div class="flex gap-2 flex-col">
                    <h4 class=" text-xs font-normal text-[#5B616E] ">NAME</h4>
                    <p class="justify-start text-gray-900 text-sm font-medium truncate capitalize">
                        {{ item.name }}
                    </p>
                </div>
                <div class="flex  gap-2 flex-col">
                    <h4 class=" text-xs font-normal text-[#5B616E]">EMAIL</h4>
                    <p class="justify-start text-gray-900 text-sm font-medium truncate">
                        {{ item.email }}
                    </p>
                </div>

                <div v-if="item.phone_number" class="flex  gap-2 flex-col">
                    <h4 class=" text-xs font-normal text-[#5B616E] mb-2">PHONE</h4>
                    <p class="justify-start text-gray-900 text-sm font-medium truncate">
                        {{ item.phone_number }}
                    </p>
                </div>

                <p class=" text-xs font-normal text-[#5B616E] mb-2">INTERESTED PROJECTS</p>
                <h3 class="justify-start text-gray-900 text-sm font-medium">
                    {{
                        organizationStore.projects?.[item.interested_in?.[0]?.project_id]?.name
                        || '-'
                    }}
                </h3>

                <h4 class=" text-xs font-normal text-[#5B616E] mb-2">INTERESTED UNIT</h4>
                <div class="flex gap-2 flex-wrap">
                    <span :key="unit" class="justify-start text-gray-900 text-sm font-medium capitalize">
                        {{ organizationStore.projects?.[item.interested_in?.[0]?.unit_id]?.name || '-' }}
                    </span>
                </div>

                <div v-if="verifyUser(item.user_id)" class="flex-1 min-w-0">
                    <h4 class=" text-xs font-normal text-[#5B616E] mb-2">SALES EXECUTIVE</h4>
                    <p class="justify-start text-gray-900 text-sm font-medium truncate capitalize">
                        {{ organizationStore.users[item.user_id]?.first_name ||
                            organizationStore.users[item.user_id]?.email || '-' }}
                    </p>
                </div>
            </div>

            <div class="flex items-start gap-2 ml-2 flex-shrink-0 "> <!-- Added flex-shrink-0 -->

                <div v-if="item.lead_status" :class="[
                    'text-center justify-center px-2.5 py-0.5 rounded-md inline-flex items-center gap-  text-xs font-medium leading-none',
                    {
                        'bg-green-100 text-green-800': item.lead_status === 'new',
                        'bg-orange-100 text-orange-800': item.lead_status === 'hot',
                        'bg-cyan-50 text-cyan-800': item.lead_status === 'new',
                        'bg-amber-100 text-amber-800': item.lead_status === 'warm'
                    }
                ]">
                    {{ item.lead_status }}

                </div>

                <DropdownButtonComp :options="dropdownOptions" :button-svg="menuSvg" button-title="" theme="transparent"
                    :button-style="{
                        size: 'icon',
                        variant: 'icon'
                    }" class="flex-shrink-0 !p-0 translate-x-[10px] -translate-y-[7px]" />

            </div>
            <div class="absolute right-4 bottom-4 m-1.5">
                <div class='relative flex items-end justify-start'>
                    <button @click="toggleDropdown(index)" :class="[
                        'border-2 px-2 py-1 h-8 rounded-lg flex items-center transition-colors bottom-0',
                        selectedIndex === index
                            ? 'bg-blue-700 text-white border-blue-700'
                            : 'bg-white text-blue-700 border-blue-700'
                    ]">
                        <svg width="15" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0)">
                                <path
                                    d="M6.68646 9.86067C7.01188 10.185 7.45264 10.3672 7.91214 10.3672C8.37164 10.3672 8.8124 10.185 9.13782 9.86067L9.75066 9.24793C10.0761 8.92357 10.5168 8.74143 10.9763 8.74143C11.4358 8.74143 11.8766 8.92357 12.202 9.24793L13.4277 10.4734C13.6069 10.6234 13.751 10.8109 13.8499 11.0227C13.9488 11.2344 14 11.4652 14 11.6989C14 11.9326 13.9488 12.1634 13.8499 12.3752C13.751 12.5869 13.6069 12.7744 13.4277 12.9244C10.2182 16.1343 6.52362 14.6095 3.30447 11.3908C0.0853122 8.17212 -1.4319 4.48164 1.7785 1.27173C1.92863 1.0927 2.11617 0.948738 2.32792 0.849961C2.53968 0.751185 2.77051 0.699997 3.00418 0.699997C3.23784 0.699997 3.46867 0.751185 3.68043 0.849961C3.89218 0.948738 4.07972 1.0927 4.22985 1.27173L5.45553 2.49722C5.78015 2.82248 5.96246 3.26321 5.96246 3.72271C5.96246 4.18221 5.78015 4.62294 5.45553 4.9482L4.84269 5.56095C4.51807 5.88621 4.33576 6.32694 4.33576 6.78644C4.33576 7.24594 4.51807 7.68667 4.84269 8.01193L6.68646 9.86067Z"
                                    :class="selectedIndex === index ? 'fill-white' : 'fill-blue-700'" />
                            </g>
                            <defs>
                                <clipPath id="clip0">
                                    <rect width="14" height="14" fill="white" transform="translate(0 0.7)" />
                                </clipPath>
                            </defs>
                        </svg>
                        <span class="ml-1 text-sm">Connect</span>
                    </button>

                    <div v-if="selectedIndex === index" :style="dropdownStyle"
                        class="absolute bg-white border border-gray-300 -mt-20 !mr-32 right-0 p-2 rounded-md z-50 drop-shadow-lg">
                        <button @click="connect('Email', item.phone_number, item.email)"
                            class="w-full text-left py-2 pr-4 pl-2 hover:bg-gray-100 rounded-md gap-2 flex items-center">
                            <svg width="14" height="15" viewBox="0 0 14 15" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7 8.1398L13.4925 2.5804C13.2432 2.36859 12.9271 2.25158 12.6 2.25H1.4C1.07287 2.25158 0.756802 2.36859 0.5075 2.5804L7 8.1398Z"
                                    fill="black" />
                                <path
                                    d="M7.875 9.2339C7.62023 9.42877 7.30884 9.53519 6.9881 9.537C6.6892 9.53761 6.39824 9.44079 6.1593 9.2612L0 3.9888L0 11.35C0 11.7213 0.1475 12.0774 0.41005 12.3399C0.672601 12.6025 1.0287 12.75 1.4 12.75H12.6C12.9713 12.75 13.3274 12.6025 13.5899 12.3399C13.8525 12.0774 14 11.7213 14 11.35V3.9888L7.875 9.2339Z"
                                    fill="black" />
                            </svg>

                            <span>Email</span></button>
                        <button @click="connect('WhatsApp', item.phone_number, index)"
                            class="w-full text-left py-2 pr-4 pl-2 hover:bg-gray-100 rounded-md  gap-2 flex items-center">

                            <svg width="14" height="17" viewBox="0 0 14 17" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M6.9834 0.5C10.8402 0.500012 13.9668 3.62659 13.9668 7.4834C13.9668 11.3402 10.8402 14.4668 6.9834 14.4668C3.12659 14.4668 1.1597e-05 11.3402 0 7.4834C0 3.62659 3.12659 0.5 6.9834 0.5ZM5.27539 4.49023C5.17556 4.49023 5.07681 4.51252 4.98633 4.55469C4.89579 4.59692 4.81517 4.65881 4.75098 4.73535C3.37875 6.10768 4.02708 7.68548 5.40332 9.06152C6.77967 10.4377 8.35921 11.0892 9.73145 9.7168C9.808 9.65271 9.86984 9.57286 9.91211 9.48242C9.95435 9.39197 9.97653 9.29318 9.97656 9.19336C9.97656 9.09352 9.95432 8.99477 9.91211 8.9043C9.86983 8.81377 9.80806 8.73309 9.73145 8.66895L9.20801 8.14551C9.06887 8.00683 8.88005 7.92871 8.68359 7.92871C8.48717 7.92874 8.29829 8.00685 8.15918 8.14551L7.89746 8.40723C7.75844 8.54573 7.57027 8.62391 7.37402 8.62402C7.17759 8.62402 6.98874 8.54587 6.84961 8.40723L6.06152 7.61719C5.92273 7.47813 5.84473 7.28923 5.84473 7.09277C5.84473 6.89631 5.92273 6.70743 6.06152 6.56836L6.32324 6.30664C6.4619 6.16767 6.53995 5.9795 6.54004 5.7832C6.54004 5.58674 6.46203 5.39785 6.32324 5.25879L5.79883 4.73535C5.73468 4.65885 5.65493 4.59692 5.56445 4.55469C5.47398 4.51249 5.37522 4.49027 5.27539 4.49023Z"
                                    fill="black" />
                                <path d="M3.56871 13.4654L4.4724 13.9939L3.44805 14.7105L3.56871 13.4654Z" fill="black"
                                    stroke="black" />
                            </svg>

                            WhatsApp</button>
                        <button @click="connect('Call', item.phone_number, index)"
                            class="w-full text-left py-2 pr-4 pl-2 hover:bg-gray-100 rounded-md  gap-2 flex items-center">

                            <svg width="14" height="15" viewBox="0 0 14 15" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_6461_21963)">
                                    <path
                                        d="M6.68646 9.66067C7.01188 9.98503 7.45264 10.1672 7.91214 10.1672C8.37164 10.1672 8.8124 9.98503 9.13782 9.66067L9.75066 9.04793C10.0761 8.72357 10.5168 8.54144 10.9763 8.54144C11.4358 8.54144 11.8766 8.72357 12.202 9.04793L13.4277 10.2734C13.6069 10.4234 13.751 10.6109 13.8499 10.8227C13.9488 11.0344 14 11.2652 14 11.4989C14 11.7326 13.9488 11.9634 13.8499 12.1752C13.751 12.3869 13.6069 12.5744 13.4277 12.7244C10.2182 15.9343 6.52362 14.4095 3.30447 11.1908C0.0853122 7.97212 -1.4319 4.28164 1.7785 1.07173C1.92863 0.892706 2.11617 0.748741 2.32792 0.649964C2.53968 0.551188 2.77051 0.5 3.00418 0.5C3.23784 0.5 3.46867 0.551188 3.68043 0.649964C3.89218 0.748741 4.07972 0.892706 4.22985 1.07173L5.45553 2.29722C5.78015 2.62248 5.96246 3.06322 5.96246 3.52272C5.96246 3.98221 5.78015 4.42295 5.45553 4.74821L4.84269 5.36095C4.51807 5.68621 4.33576 6.12694 4.33576 6.58644C4.33576 7.04594 4.51807 7.48668 4.84269 7.81193L6.68646 9.66067Z"
                                        fill="black" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_6461_21963">
                                        <rect width="14" height="14" fill="white" transform="translate(0 0.5)" />
                                    </clipPath>
                                </defs>
                            </svg>

                            Call</button>
                    </div>
                </div>
            </div>

            <!-- Project and Status -->

            <!-- Contact Info -->
        </div>

    </div>
</template>

<style scoped>
.text-green-600 {
    color: #059669;
}

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap');

.mt-auto {
    margin-top: auto;
}

:deep(.button-override) {
    padding: 8px !important;
    height: auto !important;
    min-height: 0 !important;
    background: transparent !important;
}

/* :deep(.button-override:hover) {
    background: rgb(243 244 246) !important;
} */

:deep(.button-override svg) {
    width: 3px !important;
    height: 20px !important;
}
</style>
