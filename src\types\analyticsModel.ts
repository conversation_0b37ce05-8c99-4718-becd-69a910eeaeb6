// Src/types/googleAnalytics.ts

export interface ReportParams {
    startDate: string;
    endDate: string;
    orgId: string;
    projectId: string;
  }

export interface EventDetailsParams {
    startDate: string;
    endDate: string;
    orgId: string;
    projectId: string;
    eventName: string;
    customFields: string[];
  }

export interface EventDetailsRow {
    eventName: string;
    eventCount: string;
    [key: string]: string;
  }

export interface ReportRow {
    country: string;
    region: string;
    city: string;
    browser: string;
    operatingSystem: string;
    deviceCategory: string;
    sessionSource: string;
    eventName: string;
    activeUsers: number;
    sessions: number;
    totalUsers: number;
    newUsers: number;
    engagementRate: number;
    userEngagementDuration: number;
    screenPageViews: number;
    eventCount: number;
    newUserPercentage: string;
    pageviewsPerUser: string;
  }
