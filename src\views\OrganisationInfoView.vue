
<script setup>

import { onMounted, ref, watch} from 'vue';
import { OrganizationStore } from '../store/archiveprojects';
import { notAuthorized } from '../enum';
import { useRouter } from 'vue-router';
import { UserStore } from '../store/index';

/* State */
const auth = UserStore();
const organizationStore = OrganizationStore();
const isEditInfo = ref(false);
const editName = ref(''); // Name
const editAddress = ref(null); // Address
const editFoundDate = ref(null); // Found Date
const erroMsg = ref({
  isShow: false,
  message: null,
});
const router = useRouter();
/* Methods & Watch */

watch(organizationStore, () => {
  if (organizationStore.organization_data){
    editName.value = organizationStore.organization_data.name;
    editAddress.value = organizationStore.organization_data.address;
    editFoundDate.value = organizationStore.organization_data.founding_date.split('T')[0];
  }
});

const handleResetForm = () => {
  if (organizationStore.organization_data){
    editName.value = organizationStore.organization_data.name;
    editAddress.value = organizationStore.organization_data.address;
    editFoundDate.value = organizationStore.organization_data.founding_date.split('T')[0];
  }
  erroMsg.value.isShow = false;
  erroMsg.value.message = null;
  isEditInfo.value = false; // Close
};

const validationCheck = (value, fieldName) => {
  console.log(fieldName);
  if (value) {
    if (value.length > 0){
      erroMsg.value.isShow = false;
      erroMsg.value.message = null;
      return true;
    }
  } else {
    erroMsg.value.isShow = true;
    erroMsg.value.message = `Please fill the ${fieldName}`;
    return false;
  }
  return false;
};

const HandleAddUser = () => {
  if (validationCheck(editName.value, 'Name') && validationCheck(editAddress.value, 'Address') && validationCheck(editFoundDate.value, 'FoundDate') ){
    organizationStore.UpdateOrganizationDetails(editName.value, editAddress.value, editFoundDate.value).then(() => {
      isEditInfo.value = false;
      erroMsg.value.isShow = false;
      erroMsg.value.message = null;
    }).catch((error) => {
      if (auth.verifyAuth()) {
        router.push('/login');
      } else {
        erroMsg.value.isShow = true;
        if (error.message){
          erroMsg.value.message = error.message;
        } else {
          if (notAuthorized.toLowerCase() !== error.error.toLowerCase()){
            erroMsg.value.message = error.error;
          }
        }
        isEditInfo.value = true;
      }
    });

  }

};

/* Hooks */
onMounted(() => {
  organizationStore.GetOrganizationDetails();
});

</script>

<template>
  <div class="bg-neutral-900 h-full w-full lg:pl-72">

    <div class="w-full h-full p-3">
      <div class="w-full  text-white flex justify-between items-center">
        <div>
          <p class="text-neutral-500 text-xs sm:text-sm font-normal leading-7">Settings / Organisation Info</p>
          <h1 class="text-sm sm:text-Base font-semibold leading-7">Organisation Info</h1>
        </div>
        <div >
          <button @click="() => isEditInfo = true"
            v-if="!isEditInfo"
            class="hidden sm:flex justify-center items-center w-fit h-11 sm:h-11 sm:rounded-md
            border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent m-0
            px-3 text-xs text-white font-semibold leading-6 ">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
           class="w-4 h-4 mr-2">
              <path
                d="M21.731 2.269a2.625 2.625 0 00-3.712 0l-1.157 1.157 3.712 3.712
                1.157-1.157a2.625 2.625 0 000-3.712zM19.513 8.199l-3.712-3.712-8.4 8.4a5.25
                5.25 0 00-1.32 2.214l-.8 2.685a.75.75 0 00.933.933l2.685-.8a5.25 5.25 0 002.214-1.32l8.4-8.4z" />
              <path
                d="M5.25 5.25a3 3 0 00-3 3v10.5a3 3 0 003 3h10.5a3 3 0 003-3V13.5a.75.75
                0 00-1.5 0v5.25a1.5 1.5 0 01-1.5 1.5H5.25a1.5 1.5 0 01-1.5-1.5V8.25a1.5 1.5
                0 011.5-1.5h5.25a.75.75 0 000-1.5H5.25z" />
            </svg>
            Edit Info
          </button>

          <button  @click="() => isEditInfo = !isEditInfo" class="w-fit h-fit p-2 cursor-pointer block sm:hidden">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 fill-white" viewBox="0 0 24 24">
              <g data-name="Layer 2">
                <g data-name="edit">
                  <rect />
                  <path
                    d="M19.4 7.34L16.66 4.6A2 2 0 0 0 14 4.53l-9 9a2 2 0 0 0-.57 1.21L4 18.91a1
                    1 0 0 0 .29.8A1 1 0 0 0 5 20h.09l4.17-.38a2 2 0 0 0 1.21-.57l9-9a1.92 1.92 0 0
                    0-.07-2.71zM16 10.68L13.32 8l1.95-2L18 8.73z" />
                </g>
              </g>
            </svg>
          </button>
        </div>
        <div class="w-full sm:w-fit text-center absolute sm:static bottom-4  left-0  flex
        justify-evenly sm:justify-center items-center" v-if="isEditInfo">
          <button @click="() => handleResetForm()"
            class="w-5/12 sm:w-fit h-11 sm:h-11 rounded-full sm:rounded border-2 border-solid
            border-[#36f] hover:border-[#4572fc] bg-transparent m-0 px-3 text-xs text-white font-semibold leading-6">Discard</button>
          <button @click="HandleAddUser()" id="submit" type="submit"
            class="sm:ml-3 w-5/12 sm:w-fit h-11 sm:h-11 rounded-full sm:rounded bg-[#36f]
            hover:bg-[#4572fc] border-0 px-3 text-xs text-white font-semibold leading-6">Save
            Changes
          </button>
        </div>
      </div>

      <div class="h-px border-t-[1px] mt-3 sm:py-2 border-neutral-800">
        <div class="">
          <div class="sm:flex sm:justify-center sm:items-center">
            <div class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 sm:mr-8 border-b-[1px] sm:border-0 border-neutral-800" :class="isEditInfo?'border-0':''">
              <label class=" text-xs font-semibold mb-1 ml-1" :class="isEditInfo?'text-[#F5F5F5]':'text-neutral-500 '">
                Name
              </label>
              <input v-if="isEditInfo" v-model="editName" style="border: 1px solid #737373;" type="text" id="Username" name="Username"
                class="w-full h-10 p-2 rounded-md justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs"
                placeholder="Name" />
              <p class="text-white text-sm font-medium leading-normal" v-if="!isEditInfo && organizationStore.organization_data"> {{ organizationStore.organization_data.name }}</p>

            </div>

            <div class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 border-b-[1px] sm:border-0 border-neutral-800" :class="isEditInfo?'border-0':''">
              <label class=" text-xs font-semibold mb-1 ml-1" :class="isEditInfo?'text-[#F5F5F5]':'text-neutral-500 '">
                 Founding Date
              </label>
              <input v-if="isEditInfo" v-model="editFoundDate" style="border: 1px solid #737373;" type="date" id="foundDate" name="foundDate"
                class="w-full h-10 p-2 rounded-md justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs"
                placeholder="Found Date" />
                <p v-if="organizationStore.organization_data
                && !isEditInfo" class="text-white text-sm font-medium leading-normal" >
                {{ organizationStore.organization_data.founding_date.split('T')[0] }}</p>
            </div>
          </div>
          <div>
            <div class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 border-b-[1px] sm:border-0 border-neutral-800" :class="isEditInfo?'border-0':''">
              <label class=" text-xs font-semibold mb-1 ml-1" :class="isEditInfo?'text-[#F5F5F5]':'text-neutral-500 '">
                Address
              </label>
              <input v-if="isEditInfo" v-model="editAddress" style="border: 1px solid #737373;" type="text" id="address" name="address"
                class="w-full h-10 p-2 rounded-md justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs"
                placeholder="Address" />

                <p class="text-white text-sm font-medium leading-normal" v-if="!isEditInfo && organizationStore.organization_data" >{{ organizationStore.organization_data.address }}</p>
            </div>
          </div>

          <p v-if="erroMsg.isShow && isEditInfo" class="text-base text-bold not-italic tracking-normal text-left mt-4 text-[#ff4070]" >
                                                <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i> {{ erroMsg.message }}
          </p>
        </div>
    </div>
  </div>

</div>
</template>

<style scoped></style>
