export enum AssetType {
    HOLOGRAM = 'hologram',
    VIDEO = 'video',
}

export enum AssetFileTypes {
    MASTERPLAN = 'master_plan',
    ARCHITECTURAL_DRAWING = 'architectural_drawing',
    INTERIOR_RENDER = 'interior_render',
    EXTERIOR_RENDER = 'exterior_render',
    FLOOR_PLAN = 'floor_plan'
}

export type AssetsObject = {
  project_name: string,
  project_description: string,
  assets: {
    items: object,
    total:number
  }
}

export type createAssets = {
    name: string,
    media_type: string,
    type:string,
    url: string,
    created_at: string,
    updated_at: string,
}

export type updateSettingsFile = {
  project_id:string,
  type:string,
  thumbnail:File| Blob,
  hologram_project_logo:File| Blob
}
export type updateSettingsFileResponse = {
  branding_logo: string,
  branding_logo_dark: string,
  thumbnail: string,
  file: string,
  hologram_project_logo: string,
}

export type deleteAsset = {
  project_id:string,
  asset_id:string
}

export type assets = {
    _id: string,
    name: string,
    media_type: string,
    type:string,
    url: string,
    created_at: string,
    updated_at: string,
}

export type updateAssets = {
    asset_id: string,
    name?:string,
    type?:string,
    url?: string,
    media_type?: string,
    updated_at?: string,
}
