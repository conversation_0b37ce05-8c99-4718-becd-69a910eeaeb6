<script setup>
import SideNavBar from '../../components/common/SideNavBar.vue';
import Navbar from '../../components/common/Navbar.vue';
import ScheduleView from '../../components/schedules/Index.vue';
import { UserStore } from '../../store/index';
const userStore = UserStore();
</script>

<template>
     <div class="w-full h-full overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col">
        <Navbar/>
    <div class="h-full w-full flex justify-start items-start overflow-hidden flex-col-reverse sm:flex-row bg-[#f3f4f6]">
        <SideNavBar />
        <div v-if="userStore.user_data && userStore.user_role" class="pt-0 pb-[80px] sm:pb-0 bg-transparent h-full overflow-hidden w-full flex flex-col justify-start items-start">
            <ScheduleView/>
                <router-view name="modal"></router-view>
        </div>
    </div>
</div>
</template>
