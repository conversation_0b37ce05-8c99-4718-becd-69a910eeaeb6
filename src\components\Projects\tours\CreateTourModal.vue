<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { AddTourLabel, CreateVirtualTour } from '../../../api/projects/tours';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { tourSchema } from '../../../validationSchema/tour/index';
import { tourType, tourCategory } from  '../../../types/virtualTour';
import { ProjectStore } from '@/store/project';
import Spinner from '@/components/common/Spinner.vue';
import Button from '@/components/common/Button.vue';
import router from '@/router';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

const route = useRoute();
const emits = defineEmits(['close']);
const projectId = ref(route.params.project_id);
const projectStore = ProjectStore();
const loader = ref(false);
const selectionTourType = ref(tourType.CUSTOM);
const categorySelection = ref(tourCategory.INTERIOR);
const modelUploadedFile = ref(null);
const cameraUploadedFile = ref(null);
let gltfloader;
const modelProgressLoader = ref({
  status: false,
  precentage: 0,
});
const cameraProgressLoader = ref({
  status: false,
  precentage: 0,
});

if (!projectStore.unitplans){
  projectStore.RefreshUnitplans(projectId.value);
}

/* Methods */
// Watch
watch(modelUploadedFile, (val) => {
  console.log(val, "Watcher");
  if (val){
    modelProgressLoader.value.status = true;
    modelProgressLoader.value.precentage = 5;
    setTimeout(() => {
      modelProgressLoader.value.precentage = 100;
    }, 700);
  }
});

watch(cameraUploadedFile, (val) => {
  if (val){
    cameraProgressLoader.value.status = true;
    cameraProgressLoader.value.precentage = 5;
    setTimeout(() => {
      cameraProgressLoader.value.precentage = 100;
    }, 700);
  }
});

// File ProgressLoaders
watch([modelProgressLoader, cameraProgressLoader], ([modelLoader, cameraLoader]) => {
  if (modelLoader.status && modelLoader.precentage === 100) { // If open & 100, then close it
    setTimeout(() => {
      modelProgressLoader.value = {
        status: false,
        precentage: 0,
      };
    }, 500);
  }

  if (cameraLoader.status && cameraLoader.precentage === 100) { // If open & 100, then close it
    setTimeout(() => {
      cameraProgressLoader.value = {
        status: false,
        precentage: 0,
      };
    }, 500);
  }
}, {deep: true});

// ConvertToMb
const convertToMb = (val) => (val / (1024 * 1024)).toFixed(1);

// Submit
const handleSubmitForm = (values) => {
  loader.value = true; // open the loader
  console.log("Form submitted", values);
  const formData = new FormData();

  formData.append('project_id', projectId.value);
  formData.append('name', values.name);
  formData.append('description', values.description);
  formData.append('category', values.category);
  formData.append('type', values.type);
  values.unitplan_id && formData.append('unitplan_id', values.unitplan_id);
  values.type === tourType.EXTERNAL && formData.append('link', values.link);
  values.type === tourType.MATTERPORT && formData.append('space_id', values.space_id);
  values.type ===  tourType.MLE  && formData.append('model', values.model);
  values.type === tourType.MLE && formData.append('camera', values.camera);
  CreateVirtualTour(formData)
    .then((res) => {
      if (values.type === tourType.MLE){
        gltfloader = new GLTFLoader();
        const cameraFile = values.camera;
        const cameraURL = URL.createObjectURL(cameraFile);
        gltfloader.load(cameraURL, function (gltf) {
          const model = gltf.scene;
          let order = 0;
          model.traverse((child) => {
            if (child.type === "PerspectiveCamera") {
              if (!child.name.toLowerCase().startsWith("cam_")){
                const createLabelPayload = {
                  name: child.name.replace(/_/g, " "),
                  camera_name: child.name,
                  controls_target: {
                    x: 0, y: 0, z: 0,
                  },
                  camera_position: {...child.position},
                  order: order+1,
                  project_id: projectId.value,
                  tour_id: res._id,
                };
                order++;
                AddTourLabel(createLabelPayload);
              }
            }
          });
          // Clean up the URL when you're done to avoid memory leaks
          URL.revokeObjectURL(cameraURL);
        });

      }
      console.log({ [res._id]: res});
      projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // sync the result to store
      loader.value = false;
      emits('close'); // close the modal
      router.push(`/projects/${projectId.value}/design/tours/${res._id}`); // Go the respective screen.
    }).catch((err) => {
      console.log(err);
    }).finally(() => {
      console.log('finally');
    });
};

</script>

<template>
    <div class="select-none fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg w-[643px] p-4 flex flex-col justify-start items-start gap-4 overflow-hidden">
        <div class="flex justify-between items-center w-full">
          <h4 class="text-gray-900 text-xl font-bold mb-0 select-none">Create VR Tours</h4>
          <svg :class="['w-2.5 h-2.5 cursor-pointer', loader ? 'pointer-events-auto' : 'pointer-events-auto']"  viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg"  @click="() => emits('close')">
              <g>
              <path d="M5.90741 5L9.30409 1.60332C9.36538 1.54412 9.41427 1.47331 9.4479 1.39502C9.48153 1.31672 9.49924 1.23252 9.49998 1.14731C9.50072 1.0621 9.48448 0.977595 9.45221 0.898729C9.41995 0.819863 9.3723 0.748212 9.31204 0.687958C9.25179 0.627705 9.18014 0.580054 9.10127 0.547787C9.0224 0.515521 8.9379 0.499284 8.85269 0.500024C8.76748 0.500765 8.68328 0.518468 8.60498 0.5521C8.52669 0.585733 8.45588 0.634621 8.39668 0.695913L5 4.09259L1.60332 0.695913C1.48229 0.579017 1.32019 0.514333 1.15193 0.515796C0.983666 0.517258 0.822712 0.584748 0.70373 0.70373C0.584748 0.822712 0.517258 0.983666 0.515796 1.15193C0.514333 1.32019 0.579017 1.48229 0.695913 1.60332L4.09259 5L0.695913 8.39668C0.634621 8.45588 0.585733 8.52669 0.5521 8.60498C0.518468 8.68328 0.500765 8.76748 0.500024 8.85269C0.499284 8.9379 0.515521 9.0224 0.547787 9.10127C0.580054 9.18014 0.627705 9.25179 0.687958 9.31204C0.748212 9.3723 0.819863 9.41995 0.898729 9.45221C0.977595 9.48448 1.0621 9.50072 1.14731 9.49998C1.23252 9.49924 1.31672 9.48153 1.39502 9.4479C1.47331 9.41427 1.54412 9.36538 1.60332 9.30409L5 5.90741L8.39668 9.30409C8.51771 9.42098 8.67981 9.48567 8.84807 9.4842C9.01633 9.48274 9.17729 9.41525 9.29627 9.29627C9.41525 9.17729 9.48274 9.01633 9.4842 8.84807C9.48567 8.67981 9.42098 8.51771 9.30409 8.39668L5.90741 5Z" fill="#6B7280"/>
              </g>
          </svg>
        </div>
        <Form v-slot="{errorBag}" :validation-schema="tourSchema" @submit="handleSubmitForm" class="flex flex-col justify-start items-start gap-5 w-full mb-0">
          <div class="flex flex-col justify-start items-start gap-5 w-full max-h-[80vh] overflow-y-auto hide-scroll-bar px-1">
            <div class="w-full flex flex-col justify-start items-start gap-2">
              <label for="name" class="mb-0 text-gray-900 text-sm font-medium">Enter name*</label>
              <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field
                  type="text"
                  name="name"
                  id="name"
                  :class="[ errorBag?.name ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 bg-gray-50 placeholder:text-gray-500 outline-gray-300' ,'!px-4 !py-3 w-full leading-none  text-left placeholder:text-left h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px] border-none shadow-none']"
                  placeholder="Enter name"
                />
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="name"/>
              </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2">
               <label for="description" class="mb-0 text-gray-900 text-sm font-medium ">Description*</label>
               <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field
                  type="text"
                  name="description"
                  id="description"
                  :class="[ errorBag?.description ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 placeholder:text-gray-500 bg-gray-50 outline-gray-300' ,'!px-4 !py-3 w-full leading-none  text-left placeholder:text-left  h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px] border-none shadow-none']"
                  placeholder="Enter description"
                />
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="description" />
               </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2">
                            <label for="category" class="mb-0 text-gray-900 text-sm font-medium">Category*</label>
                            <div class="w-full flex flex-col justify-start items-start gap-2">
                            <Field name="category" v-model="categorySelection">
                              <div
                                class="flex justify-start items-start gap-3">
                                  <span v-for="item in tourCategory" :key="item" :class="['p-2 capitalize block w-24 h-9 text-center rounded-lg outline outline-1 outline-offset-[-1px] cursor-pointer outline-[#1C64F2] !text-sm font-medium', categorySelection === item ? 'bg-[#1C64F2] text-white' : 'bg-transparent text-[#1C64F2]' ]"  @click="() => categorySelection = item">
                                        {{ item }}
                                  </span>
                              </div>
                            </Field>
                            <ErrorMessage as="category"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="category" />
                        </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2">

              <div class="w-full flex justify-start items-center gap-2">
                <label for="type" class="mb-0 text-gray-900 text-sm font-medium">
                 Tour Type*</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>

              <div class="w-full flex flex-col justify-start items-start gap-2">

                <Field name="type" v-model="selectionTourType">
                            <div
                              class="flex justify-start items-start gap-3">
                                <span v-for="item in tourType" :key="item" :class="['p-2 cursor-pointer capitalize block w-24 h-9 text-center rounded-lg outline outline-1 outline-offset-[-1px] outline-[#1C64F2] !text-sm font-medium', selectionTourType === item ? 'bg-[#1C64F2] text-white' : 'bg-transparent text-[#1C64F2]' ]"  @click="() => selectionTourType = item">
                                      {{ item }}
                                </span>
                            </div>

                </Field>

                 <ErrorMessage as="type"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="type" />
              </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.MATTERPORT">
              <div class="w-full flex justify-start items-center gap-2 ">
                <label for="space_id" class="mb-0 text-gray-900 text-sm font-medium">
                Space ID*</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>

               <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field
                  type="text"
                  name="space_id"
                  id="space_id"
                  :class="[errorBag?.space_id ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 placeholder:text-gray-500 bg-gray-50 outline-gray-300' ,'!px-4 !py-3 w-full leading-none text-left placeholder:text-left h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px]  border-none shadow-none']"
                  placeholder="Enter space_id"
                />
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="space_id" />
                </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.EXTERNAL">
              <div class="w-full flex justify-start items-center gap-2 ">
                <label for="link" class="mb-0 text-gray-900 text-sm font-medium">
                 Link*</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>
              <div class="w-full flex flex-col justify-start items-start gap-2">
                <Field
                  type="url"
                  name="link"
                  id="link"
                  :class="[ errorBag?.link ? 'bg-red-50 text-red-700 placeholder:text-red-700 outline-red-500' : 'text-gray-900 placeholder:text-gray-500 bg-gray-50 outline-gray-300','!px-4 !py-3 w-full leading-none text-left placeholder:text-left h-[42px] text-sm font-normal rounded-lg outline outline-1 outline-offset-[-1px] border-none shadow-none']"
                  placeholder="Enter link url"
                />
                <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="link" />
                </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.MLE"> <!-- v-if="selectionTourType === tourType.MLE" -->
              <div class="w-full flex justify-start items-center gap-2 ">
                <label for="model" class="mb-0 text-gray-900 text-sm font-medium">
                  Model*</label>
                    <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g >
                    <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                    </g>
                  </svg>
              </div>
              <div class="w-full flex flex-col justify-start items-start gap-2">

                <label v-if="!modelUploadedFile" for="model" :class="[errorBag?.model ? 'bg-red-50 border-red-500' : 'bg-gray-50 border-gray-200' ,'w-full h-20 !px-4 flex justify-start items-center  rounded-lg border-2 border-dashed cursor-pointer']">
                     <div class="flex flex-col justify-start items-start gap-2">
                        <div class="flex gap-2 justify-start items-center">
                              <svg class="w-5 h-5" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M18 12.2952H13V12.808C12.9973 13.3496 12.8536 13.8807 12.584 14.3464H18V18.4488H2V14.3464H7.416C7.14635 13.8807 7.00275 13.3496 7 12.808V12.2952H2C1.46957 12.2952 0.960859 12.5113 0.585786 12.896C0.210714 13.2807 0 13.8024 0 14.3464V18.4488C0 18.9928 0.210714 19.5145 0.585786 19.8992C0.960859 20.2839 1.46957 20.5 2 20.5H18C18.5304 20.5 19.0391 20.2839 19.4142 19.8992C19.7893 19.5145 20 18.9928 20 18.4488V14.3464C20 13.8024 19.7893 13.2807 19.4142 12.896C19.0391 12.5113 18.5304 12.2952 18 12.2952Z" fill="#9CA3AF"/>
                              <path d="M6.707 6.3539L9 4.0022V12.808C9 13.08 9.10536 13.3409 9.29289 13.5332C9.48043 13.7255 9.73478 13.8336 10 13.8336C10.2652 13.8336 10.5196 13.7255 10.7071 13.5332C10.8946 13.3409 11 13.08 11 12.808V4.0022L13.293 6.3539C13.4816 6.54073 13.7342 6.6441 13.9964 6.64176C14.2586 6.63943 14.5094 6.53157 14.6948 6.34141C14.8802 6.15126 14.9854 5.89402 14.9877 5.62511C14.99 5.35621 14.8892 5.09714 14.707 4.90371L10.707 0.801308C10.6141 0.705798 10.5038 0.630021 10.3823 0.578317C10.2608 0.526614 10.1305 0.5 9.999 0.5C9.86747 0.5 9.73722 0.526614 9.61573 0.578317C9.49424 0.630021 9.38389 0.705798 9.291 0.801308L5.291 4.90371C5.10349 5.09629 4.99826 5.35737 4.99844 5.62953C4.99863 5.90169 5.10423 6.16262 5.292 6.35493C5.47977 6.54724 5.73434 6.65517 5.99971 6.65498C6.26507 6.65478 6.51949 6.54648 6.707 6.3539Z" fill="#9CA3AF"/>
                              <path d="M15 17.4232C15.5523 17.4232 16 16.964 16 16.3976C16 15.8312 15.5523 15.372 15 15.372C14.4477 15.372 14 15.8312 14 16.3976C14 16.964 14.4477 17.4232 15 17.4232Z" fill="#9CA3AF"/>
                              </svg>
                               <p class="text-gray-500 text-sm font-normal">
                                    <span class="font-semibold">
                                      Click to upload
                                    </span> <!-- or drag and drop -->
                               </p>
                        </div>
                        <p class="text-gray-500 text-xs font-normal ">GLB (MAX. 10MB)</p>
                     </div>

                     <Field
                            type="file"
                                    v-model="modelUploadedFile"
                                    name="model"
                                    id="model"
                                    autocomplete="model"
                                    class="hidden"
                                    placeholder="Upload Model glb" />
                </label>

                <div v-else :class="[ errorBag?.model ? 'bg-red-50 outline-red-500' : 'bg-gray-50 outline-gray-300' ,'w-full !px-4 flex justify-start items-center rounded-lg outline outline-1 outline-offset-[-0.50px]',  modelProgressLoader.status ? 'h-20' : 'h-8' ]">  <!-- modelUploadedFile -->
                        <div class="flex w-full justify-between items-center">
                            <div class="flex flex-col justify-start items-start ">
                               <p class=" text-gray-900 text-xs font-medium "> {{ modelUploadedFile.name }}</p>
                               <div v-if="modelProgressLoader.status " class="w-[244px] flex flex-col justify-start items-start gap-0.5">
                                      <div class="w-full flex justify-between items-center">
                                           <p class="text-gray-500 text-xs font-normal">{{ convertToMb(modelUploadedFile.size) }} MB</p>
                                               <p class="text-gray-500 text-xs font-normal">{{ modelProgressLoader.precentage }}%</p>
                                      </div>

                                      <span class="block w-full h-[6px] bg-gray-200 rounded-sm overflow-hidden" >
                                           <span :style="{width:`${modelProgressLoader.precentage}%`}" class="block bg-[#1C64F2] h-[6px] rounded-sm transition overflow-hidden"></span>
                                      </span>
                               </div>
                            </div>
                          <svg class="w-4 h-4 cursor-pointer" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" @click="() => modelUploadedFile = null">
                                            <g >
                                            <path d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM10.9656 9.8344C11.042 9.90819 11.103 9.99647 11.1449 10.0941C11.1868 10.1917 11.2089 10.2967 11.2098 10.4029C11.2107 10.5091 11.1905 10.6144 11.1503 10.7128C11.11 10.8111 11.0506 10.9004 10.9755 10.9755C10.9004 11.0506 10.8111 11.11 10.7128 11.1503C10.6144 11.1905 10.5091 11.2107 10.4029 11.2098C10.2967 11.2089 10.1917 11.1868 10.0941 11.1449C9.99648 11.103 9.9082 11.042 9.8344 10.9656L8 9.1312L6.1656 10.9656C6.01472 11.1113 5.81264 11.192 5.60288 11.1901C5.39312 11.1883 5.19247 11.1042 5.04415 10.9559C4.89582 10.8075 4.81169 10.6069 4.80986 10.3971C4.80804 10.1874 4.88868 9.98528 5.0344 9.8344L6.8688 8L5.0344 6.1656C4.88868 6.01472 4.80804 5.81263 4.80986 5.60288C4.81169 5.39312 4.89582 5.19247 5.04415 5.04414C5.19247 4.89582 5.39312 4.81168 5.60288 4.80986C5.81264 4.80804 6.01472 4.88867 6.1656 5.0344L8 6.8688L9.8344 5.0344C9.98528 4.88867 10.1874 4.80804 10.3971 4.80986C10.6069 4.81168 10.8075 4.89582 10.9559 5.04414C11.1042 5.19247 11.1883 5.39312 11.1901 5.60288C11.192 5.81263 11.1113 6.01472 10.9656 6.1656L9.1312 8L10.9656 9.8344Z" fill="#6B7280"/>
                                            </g>
                            </svg>

                        </div>
                </div>

                <ErrorMessage
                  as="p"
                  class="text-red-600 text-sm font-normal w-full lowercase"
                  name="model"
                />
                 </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2" v-if="selectionTourType === tourType.MLE">

            <div class="w-full flex justify-start items-center gap-2 ">
              <label for="camera" class="mb-0 text-gray-900 text-sm font-medium">
                 Camera*</label>
                   <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2">

                <label v-if="!cameraUploadedFile" for="camera" :class="[ errorBag?.camera ? 'bg-red-50 border-red-500' : 'bg-gray-50 border-gray-200' ,'w-full h-20 !px-4 flex justify-start items-center rounded-lg border-2 border-dashed cursor-pointer']">
                     <div class="flex flex-col justify-start items-start gap-2">

                        <div class="flex gap-2 justify-start items-center">
                              <svg class="w-5 h-5" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M18 12.2952H13V12.808C12.9973 13.3496 12.8536 13.8807 12.584 14.3464H18V18.4488H2V14.3464H7.416C7.14635 13.8807 7.00275 13.3496 7 12.808V12.2952H2C1.46957 12.2952 0.960859 12.5113 0.585786 12.896C0.210714 13.2807 0 13.8024 0 14.3464V18.4488C0 18.9928 0.210714 19.5145 0.585786 19.8992C0.960859 20.2839 1.46957 20.5 2 20.5H18C18.5304 20.5 19.0391 20.2839 19.4142 19.8992C19.7893 19.5145 20 18.9928 20 18.4488V14.3464C20 13.8024 19.7893 13.2807 19.4142 12.896C19.0391 12.5113 18.5304 12.2952 18 12.2952Z" fill="#9CA3AF"/>
                              <path d="M6.707 6.3539L9 4.0022V12.808C9 13.08 9.10536 13.3409 9.29289 13.5332C9.48043 13.7255 9.73478 13.8336 10 13.8336C10.2652 13.8336 10.5196 13.7255 10.7071 13.5332C10.8946 13.3409 11 13.08 11 12.808V4.0022L13.293 6.3539C13.4816 6.54073 13.7342 6.6441 13.9964 6.64176C14.2586 6.63943 14.5094 6.53157 14.6948 6.34141C14.8802 6.15126 14.9854 5.89402 14.9877 5.62511C14.99 5.35621 14.8892 5.09714 14.707 4.90371L10.707 0.801308C10.6141 0.705798 10.5038 0.630021 10.3823 0.578317C10.2608 0.526614 10.1305 0.5 9.999 0.5C9.86747 0.5 9.73722 0.526614 9.61573 0.578317C9.49424 0.630021 9.38389 0.705798 9.291 0.801308L5.291 4.90371C5.10349 5.09629 4.99826 5.35737 4.99844 5.62953C4.99863 5.90169 5.10423 6.16262 5.292 6.35493C5.47977 6.54724 5.73434 6.65517 5.99971 6.65498C6.26507 6.65478 6.51949 6.54648 6.707 6.3539Z" fill="#9CA3AF"/>
                              <path d="M15 17.4232C15.5523 17.4232 16 16.964 16 16.3976C16 15.8312 15.5523 15.372 15 15.372C14.4477 15.372 14 15.8312 14 16.3976C14 16.964 14.4477 17.4232 15 17.4232Z" fill="#9CA3AF"/>
                              </svg>
                               <p class="text-gray-500 text-sm font-normal">
                                    <span class="font-semibold">
                                      Click to upload
                                    </span> <!-- or drag and drop -->
                               </p>
                        </div>
                        <p class="text-gray-500 text-xs font-normal ">GLTF (MAX. 10MB)</p>
                     </div>

                     <Field
                            type="file"
                                    v-model="cameraUploadedFile"
                                    name="camera"
                                    id="camera"
                                    autocomplete="camera"
                                    class="hidden"
                                    placeholder="Upload Camera gltf" />
                  </label>

                  <div v-else :class="[ errorBag?.camera ? 'bg-red-50 outline-red-500' : 'bg-gray-50 outline-gray-300' , 'w-full !px-4 flex justify-start items-center rounded-lg  outline outline-1 outline-offset-[-0.50px]', cameraProgressLoader.status ? 'h-20' : 'h-8' ]">
                        <div class="flex w-full justify-between items-center">
                            <div class="flex flex-col justify-start items-start ">
                               <p class=" text-gray-900 text-xs font-medium "> {{ cameraUploadedFile.name }}</p>
                               <div v-if="cameraProgressLoader.status" class="w-[244px] flex flex-col justify-start items-start gap-0.5">
                                      <div class="w-full flex justify-between items-center">
                                           <p class="text-gray-500 !text-xs font-normal">{{ convertToMb(cameraUploadedFile.size) }} MB</p>
                                               <p class="text-gray-500 text-xs font-normal">{{ cameraProgressLoader.precentage }}%</p>
                                      </div>

                                      <span class="block w-full h-[6px] bg-gray-200 rounded-sm overflow-hidden" >
                                           <span :style="{width:`${cameraProgressLoader.precentage}%`}" class="block bg-[#1C64F2] h-[6px] rounded-sm transition overflow-hidden"></span>
                                      </span>
                               </div>
                            </div>
                          <svg class="w-4 h-4 cursor-pointer" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" @click="() => cameraUploadedFile = null">
                                            <g >
                                            <path d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM10.9656 9.8344C11.042 9.90819 11.103 9.99647 11.1449 10.0941C11.1868 10.1917 11.2089 10.2967 11.2098 10.4029C11.2107 10.5091 11.1905 10.6144 11.1503 10.7128C11.11 10.8111 11.0506 10.9004 10.9755 10.9755C10.9004 11.0506 10.8111 11.11 10.7128 11.1503C10.6144 11.1905 10.5091 11.2107 10.4029 11.2098C10.2967 11.2089 10.1917 11.1868 10.0941 11.1449C9.99648 11.103 9.9082 11.042 9.8344 10.9656L8 9.1312L6.1656 10.9656C6.01472 11.1113 5.81264 11.192 5.60288 11.1901C5.39312 11.1883 5.19247 11.1042 5.04415 10.9559C4.89582 10.8075 4.81169 10.6069 4.80986 10.3971C4.80804 10.1874 4.88868 9.98528 5.0344 9.8344L6.8688 8L5.0344 6.1656C4.88868 6.01472 4.80804 5.81263 4.80986 5.60288C4.81169 5.39312 4.89582 5.19247 5.04415 5.04414C5.19247 4.89582 5.39312 4.81168 5.60288 4.80986C5.81264 4.80804 6.01472 4.88867 6.1656 5.0344L8 6.8688L9.8344 5.0344C9.98528 4.88867 10.1874 4.80804 10.3971 4.80986C10.6069 4.81168 10.8075 4.89582 10.9559 5.04414C11.1042 5.19247 11.1883 5.39312 11.1901 5.60288C11.192 5.81263 11.1113 6.01472 10.9656 6.1656L9.1312 8L10.9656 9.8344Z" fill="#6B7280"/>
                                            </g>
                            </svg>

                        </div>
                  </div>

                <ErrorMessage
                  as="p"
                  class="text-red-600 text-sm font-normal w-full lowercase"
                  name="camera"
                />
            </div>
            </div>

            <div class="w-full flex flex-col justify-start items-start gap-2 mb-2">
               <div class="w-full flex justify-start items-center gap-2 ">
                <label for="unitplan_id" class="mb-0 text-gray-900 text-sm font-medium">
                 Unit Plan</label>
                <svg class="w-[10px] h-[10px] mb-0" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g >
                  <path d="M5 0.5C4.0111 0.5 3.0444 0.793245 2.22215 1.34265C1.3999 1.89206 0.759043 2.67295 0.380605 3.58658C0.00216642 4.50021 -0.0968502 5.50555 0.0960758 6.47545C0.289002 7.44535 0.765206 8.33627 1.46447 9.03553C2.16373 9.73479 3.05465 10.211 4.02455 10.4039C4.99445 10.5969 5.99979 10.4978 6.91342 10.1194C7.82705 9.74096 8.60794 9.10009 9.15735 8.27785C9.70675 7.4556 10 6.4889 10 5.5C9.99854 4.17436 9.47129 2.90344 8.53393 1.96607C7.59656 1.02871 6.32564 0.501456 5 0.5ZM4.75 2.5C4.89834 2.5 5.04334 2.54399 5.16668 2.6264C5.29002 2.70881 5.38615 2.82594 5.44291 2.96299C5.49968 3.10003 5.51453 3.25083 5.48559 3.39632C5.45665 3.5418 5.38522 3.67544 5.28033 3.78033C5.17544 3.88522 5.04181 3.95665 4.89632 3.98559C4.75083 4.01453 4.60003 3.99967 4.46299 3.94291C4.32594 3.88614 4.20881 3.79001 4.1264 3.66668C4.04399 3.54334 4 3.39834 4 3.25C4 3.05109 4.07902 2.86032 4.21967 2.71967C4.36032 2.57902 4.55109 2.5 4.75 2.5ZM6 8H4C3.86739 8 3.74022 7.94732 3.64645 7.85355C3.55268 7.75978 3.5 7.63261 3.5 7.5C3.5 7.36739 3.55268 7.24021 3.64645 7.14644C3.74022 7.05268 3.86739 7 4 7H4.5V5.5H4C3.86739 5.5 3.74022 5.44732 3.64645 5.35355C3.55268 5.25978 3.5 5.13261 3.5 5C3.5 4.86739 3.55268 4.74021 3.64645 4.64645C3.74022 4.55268 3.86739 4.5 4 4.5H5C5.13261 4.5 5.25979 4.55268 5.35355 4.64645C5.44732 4.74021 5.5 4.86739 5.5 5V7H6C6.13261 7 6.25979 7.05268 6.35355 7.14644C6.44732 7.24021 6.5 7.36739 6.5 7.5C6.5 7.63261 6.44732 7.75978 6.35355 7.85355C6.25979 7.94732 6.13261 8 6 8Z" fill="#6B7280"/>
                  </g>
                </svg>
              </div>

              <!-- select-primary -->
               <div class="w-full flex flex-col justify-start items-start gap-2">
                            <Field
                                as="select" type="text"
                                name="unitplan_id" id="unitplan_id"
                                autocomplete="unitplan_id"
                                class="!px-4 !py-3 w-full leading-none text-gray-900 text-left placeholder:text-left placeholder:text-gray-500 h-[42px] text-sm font-normal bg-gray-50 rounded-lg outline outline-1 outline-offset-[-1px] outline-gray-300 border-none shadow-none"
                                placeholder="Select Unit plan">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option
                                    v-if="!projectStore.unitplans || Object.keys(projectStore.unitplans).length === 0"
                                    value=""
                                    class="text-black">
                                   No data found!
                                </option>
                                <option
                                    v-else
                                    :value="option._id"
                                    v-for="option in projectStore.unitplans"
                                    :key="option._id"
                                    class="text-black text-sm font-normal">
                                    {{ option.name }}
                                </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-red-600 text-sm font-normal w-full lowercase"
                                name="unitplan_id" />
              </div>
            </div>

          </div>
          <div class="mb-0 flex justify-end items-center w-full">
           <!--  <button
              type="submit"
              class="proceed-btn-primary"
            >  Save         </button> -->
            <Button :disabled="loader" type="submit" title="Create" theme="primary" class="!w-[185px] !h-[41px]">
                  <template v-slot:svg>
                    <Spinner v-if="loader" class="!fill-white" />
                  </template>
            </Button>
          </div>
        </Form>
    </div>
</template>

<style>
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
</style>
