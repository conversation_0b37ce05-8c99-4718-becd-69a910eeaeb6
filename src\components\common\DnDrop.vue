<script setup>
import { Container } from 'vue-dndrop';

const { orientation, lockAxis, animationDuration } = defineProps({
  orientation: String,
  lockAxis: String,
  animationDuration: Number,
});

defineEmits(['onDrop']);

</script>

<template>

       <Container :should-animate-drop="() => true" :orientation="orientation && orientation" :animationDuration="typeof animationDuration === 'number' ? Number(animationDuration) : 350" :lock-axis="lockAxis ? lockAxis : (orientation ?  (orientation !== 'vertical' ? 'x' : 'y' ) : 'y')" @drop="(dropResult) => $emit('onDrop',dropResult)">
            <slot name="items">
            </slot>

       </Container>

</template>

<style scoped>

</style>
