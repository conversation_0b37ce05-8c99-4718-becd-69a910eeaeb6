<script setup>
import { onMounted, ref } from 'vue';
import { tableViewColumns, tableViewStatusTypes} from '../../enum';

/* State, Props and Emits */
const props = defineProps({
  data: Object,
});
const columnData = ref(null);  // Column data
const rowData = ref(null); // Row data
const toggleHideShowUnits = ref([]); // Toggle

/* Methods */

const addAndremoveToggleUnitsItem = (num) => {
  console.log(num);
  if (toggleHideShowUnits.value.length > 0){
    if (toggleHideShowUnits.value.includes(num)){
      // Remove
      toggleHideShowUnits.value.splice(toggleHideShowUnits.value.indexOf(num), 1);
    } else {
      // Add
      toggleHideShowUnits.value.push(num);
    }
  } else {
    // Add
    toggleHideShowUnits.value.push(num);
  }
};

/* Hooks */

onMounted(() => {
  if (props.data){
    if (Object.keys(props.data).length > 0) {
      const DTOData = Object.keys(props.data)[0];
      columnData.value = Object.getOwnPropertyNames(props.data[DTOData]);
      rowData.value = Object.values(props.data);
    }
  }
});

</script>

<template>
    <div class="w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden overflow-x-auto ">
        <table class="w-full rounded-2xl bg-transparent">
        <thead>
            <tr class="bg-gray-50 dark:bg-bg-150 ">
                <th v-bind:key="item" v-for="item in columnData" class="bg-transparent  px-7 pb-3 pt-7 text-txt-500 dark:text-txt-650 text-lg font-medium capitalize whitespace-nowrap"> {{  item }}</th>
            </tr>
        </thead>
        <tbody>

            <tr :key="id" v-for="item,id in rowData" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                <td :key="col" class="bg-transparent px-7 py-3 align-baseline" v-for="col in columnData">
                        <span class="flex justify-start items-center gap-1">
                            <svg class="w-5 h-5 dark:invert"   viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg" v-show="col.toLowerCase() === tableViewColumns[5]">
                            <path d="M2.57715 8.33691H17.4304" stroke="#5B616E" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.7017 11.5916H13.7094" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10.0039 11.5916H10.0116" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6.29834 11.5916H6.30606" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.7017 14.8303H13.7094" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10.0039 14.8303H10.0116" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6.29834 14.8303H6.30606" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.3696 2.16675V4.90906" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6.6377 2.16675V4.90906" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M13.5319 3.48267H6.4758C4.02856 3.48267 2.5 4.84594 2.5 7.35185V14.8932C2.5 17.4385 4.02856 18.8333 6.4758 18.8333H13.5242C15.9791 18.8333 17.5 17.4622 17.5 14.9563V7.35185C17.5077 4.84594 15.9868 3.48267 13.5319 3.48267Z" stroke="#5B616E" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <svg class="w-5 h-5 dark:invert"   viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg" v-show="col.toLowerCase() === tableViewColumns[4]">
                            <path d="M10 2.375C8.39303 2.375 6.82214 2.85152 5.486 3.74431C4.14985 4.6371 3.10844 5.90605 2.49348 7.3907C1.87852 8.87535 1.71762 10.509 2.03112 12.0851C2.34463 13.6612 3.11846 15.1089 4.25476 16.2452C5.39106 17.3815 6.8388 18.1554 8.4149 18.4689C9.99099 18.7824 11.6247 18.6215 13.1093 18.0065C14.594 17.3916 15.8629 16.3502 16.7557 15.014C17.6485 13.6779 18.125 12.107 18.125 10.5C18.1227 8.34581 17.266 6.28051 15.7427 4.75727C14.2195 3.23403 12.1542 2.37727 10 2.375ZM10 17.375C8.64026 17.375 7.31105 16.9718 6.18046 16.2164C5.04987 15.4609 4.16868 14.3872 3.64833 13.1309C3.12798 11.8747 2.99183 10.4924 3.2571 9.15875C3.52238 7.82513 4.17716 6.60013 5.13864 5.63864C6.10013 4.67716 7.32514 4.02237 8.65876 3.7571C9.99238 3.49183 11.3747 3.62798 12.631 4.14833C13.8872 4.66868 14.9609 5.54987 15.7164 6.68045C16.4718 7.81104 16.875 9.14025 16.875 10.5C16.8729 12.3227 16.1479 14.0702 14.8591 15.3591C13.5702 16.6479 11.8227 17.3729 10 17.375ZM15 10.5C15 10.6658 14.9342 10.8247 14.8169 10.9419C14.6997 11.0592 14.5408 11.125 14.375 11.125H10C9.83424 11.125 9.67527 11.0592 9.55806 10.9419C9.44085 10.8247 9.375 10.6658 9.375 10.5V6.125C9.375 5.95924 9.44085 5.80027 9.55806 5.68306C9.67527 5.56585 9.83424 5.5 10 5.5C10.1658 5.5 10.3247 5.56585 10.4419 5.68306C10.5592 5.80027 10.625 5.95924 10.625 6.125V9.875H14.375C14.5408 9.875 14.6997 9.94085 14.8169 10.0581C14.9342 10.1753 15 10.3342 15 10.5Z" fill="#5B616E"/>
                            </svg>
                            <svg class="w-5 h-5 dark:invert" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"  v-show="col.toLowerCase() === tableViewColumns[0]">
                               <path d="M17.5 3.75H2.5C2.33424 3.75 2.17527 3.81585 2.05806 3.93306C1.94085 4.05027 1.875 4.20924 1.875 4.375V15C1.875 15.3315 2.0067 15.6495 2.24112 15.8839C2.47554 16.1183 2.79348 16.25 3.125 16.25H16.875C17.2065 16.25 17.5245 16.1183 17.7589 15.8839C17.9933 15.6495 18.125 15.3315 18.125 15V4.375C18.125 4.20924 18.0592 4.05027 17.9419 3.93306C17.8247 3.81585 17.6658 3.75 17.5 3.75ZM15.893 5L10 10.4023L4.10703 5H15.893ZM16.875 15H3.125V5.79609L9.57734 11.7109C9.69265 11.8168 9.84348 11.8755 10 11.8755C10.1565 11.8755 10.3074 11.8168 10.4227 11.7109L16.875 5.79609V15Z" fill="#5B616E"/>
                            </svg>
                            <svg class="w-5 h-5 dark:invert" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"  v-show="col.toLowerCase() === tableViewColumns[1]">
                               <path d="M17.3727 12.3797L13.6922 10.7304L13.682 10.7257C13.491 10.644 13.2825 10.6112 13.0756 10.6303C12.8687 10.6494 12.6698 10.7198 12.4969 10.8351C12.4765 10.8486 12.457 10.8632 12.4383 10.8789L10.5367 12.5C9.33204 11.9148 8.08829 10.6804 7.50313 9.49137L9.12657 7.5609C9.14219 7.54137 9.15704 7.52184 9.1711 7.50075C9.28394 7.32833 9.35239 7.13068 9.37039 6.92541C9.38838 6.72014 9.35534 6.5136 9.27423 6.32418V6.31481L7.62032 2.62809C7.51309 2.38064 7.3287 2.17451 7.09468 2.04046C6.86067 1.90642 6.58958 1.85165 6.32188 1.88434C5.26326 2.02364 4.29155 2.54353 3.58824 3.34691C2.88492 4.15029 2.49809 5.18222 2.50001 6.24996C2.50001 12.4531 7.54688 17.5 13.75 17.5C14.8177 17.5019 15.8497 17.1151 16.6531 16.4117C17.4564 15.7084 17.9763 14.7367 18.1156 13.6781C18.1484 13.4105 18.0937 13.1395 17.9598 12.9055C17.8259 12.6715 17.62 12.487 17.3727 12.3797ZM13.75 16.25C11.0987 16.2471 8.55687 15.1926 6.68214 13.3178C4.8074 11.4431 3.7529 8.90124 3.75001 6.24996C3.74707 5.48706 4.02192 4.74918 4.52324 4.17411C5.02456 3.59904 5.71806 3.22611 6.47423 3.12496C6.47392 3.12808 6.47392 3.13122 6.47423 3.13434L8.11485 6.80621L6.50001 8.73903C6.48362 8.75789 6.46873 8.778 6.45548 8.79918C6.33791 8.97959 6.26894 9.1873 6.25525 9.4022C6.24157 9.61709 6.28362 9.83188 6.37735 10.0257C7.08516 11.4734 8.54376 12.9211 10.007 13.6281C10.2023 13.7209 10.4184 13.7615 10.634 13.7459C10.8497 13.7303 11.0576 13.659 11.2375 13.539C11.2576 13.5255 11.2769 13.5109 11.2953 13.4953L13.1945 11.875L16.8664 13.5195C16.8664 13.5195 16.8727 13.5195 16.875 13.5195C16.7751 14.2767 16.4027 14.9716 15.8275 15.4742C15.2524 15.9767 14.5138 16.2525 13.75 16.25Z" fill="#5B616E"/>
                            </svg>
                            <p class=" text-lg font-normal text-txt-50 dark:text-txt-950 capitalize whitespace-nowrap" v-show="col.toLowerCase() !== tableViewColumns[3] && col.toLowerCase() !== tableViewColumns[2]"> {{ item[col]  }}  </p>
                            <p v-show="col.toLowerCase() === tableViewColumns[3]" :class="[ (item[col] === tableViewStatusTypes[0] && 'bg-green-100 border-lime-600'), (item[col] === tableViewStatusTypes[1] && ' bg-orange-100 border-orange-500') , (item[col] === tableViewStatusTypes[2] && ' bg-cyan-50 border-cyan-500'),((item[col] === tableViewStatusTypes[3] && 'bg-orange-100 border-amber-400 ')),'rounded-[60px] text-txt-50  border-[1px] px-4 py-2 capitalize']">
                                {{item[col]}}
                            </p>
                            <div v-show="col.toLowerCase() === tableViewColumns[2]" class=" w-80" >
                                    <span v-if="item[col] === null || item[col] === 'null' || item[col].length === 0">
                                          -
                                    </span>

                                    <span v-else class="flex flex-wrap justify-start items-center flex-row gap-3">

                                        <span  v-if="item[col][0] !== undefined" class="px-4 py-2  bg-gray-50 dark:bg-bg-150 rounded-[21px] border border-gray-200 text-txt-50 dark:text-txt-950">
                                            Unit {{item[col][0]}}
                                        </span>

                                        <span v-if="item[col][1] !== undefined" class=" px-4 py-2  bg-gray-50 dark:bg-bg-150 rounded-[21px] border border-gray-200 text-txt-50 dark:text-txt-950">
                                            Unit {{item[col][1]}}
                                        </span>

                                        <span v-if="item[col].length > 2 && !toggleHideShowUnits.includes(id)" class="cursor-pointer bg-gray-50 dark:bg-bg-150 rounded-[50%] flex justify-center items-center flex-shrink-0 w-[36px] h-[36px] border border-gray-200 select-none text-txt-50 dark:text-txt-950" @click="addAndremoveToggleUnitsItem(id)">
                                              +{{item[col].length - 2}}
                                        </span>

                                        <template v-if="toggleHideShowUnits.includes(id)">
                                          <span :key="unitsId" v-for="units,unitsId in item[col]" v-show="unitsId !== 0 && unitsId !== 1"  class=" px-4 py-2  bg-gray-50 dark:bg-bg-150 rounded-[21px] border border-gray-200 text-txt-50 dark:text-txt-950">
                                                    Unit {{units}}
                                          </span>
                                        </template>

                                        <span v-show="item[col].length > 2 && toggleHideShowUnits.includes(id)" class="underline cursor-pointer text-txt-default dark:text-txt-950"  @click="addAndremoveToggleUnitsItem(id)">
                                                    show less
                                        </span>

                                    </span>
                            </div>

                     </span>
                </td>
            </tr>

         </tbody>
      </table>
    </div>
</template>

<style scoped>

/* table */

</style>
