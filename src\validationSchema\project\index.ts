import * as yup from 'yup';
import { fileValidation, imageSizeValidation } from '../../helpers/validationSchemaHelpers';

export const projectSchema = yup.object({
  projectName: yup.string().required(),
  projectLogo: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
  country: yup.string().required(),
  city: yup.string().required(),
  propertyType: yup.string().required(),
  experience: yup.array().min(1, 'Atleast one').required(),
});
