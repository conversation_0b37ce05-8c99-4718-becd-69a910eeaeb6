import * as yup from 'yup';

// Patterns
export const RegXEmailPattern = new RegExp(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))$/);
export const onlyNumber = new RegExp('^[0-9]+$');
export const urlRegEx = /^((ftp|http|https):\/\/)?(www.)?(?!.*(ftp|http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+((\/)[\w#]+)*(\/\w+\?[a-zA-Z0-9_]+=\w+(&[a-zA-Z0-9_]+=\w+)*)?$/gm;
const imageType = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
const imageValidation = ['image/jpeg', 'image/jpg', 'image/webp'];
const iconFileValidation = ['image/png', 'image/svg', 'image/svg+xml'];
const videoValidation = ['video/mp4'];
const videoValidation360 = ['video/mp4', 'video/webm', 'video/ogg'];
const zipValidation = ['application/x-zip-compressed', 'application/zip'];
const pdfValidation = ['application/pdf'];
const threeDValidation = [
  'model/gltf-binary',    // .glb
  'model/gltf+json',      // .gltf
  'application/json',     // .gltf (sometimes detected as JSON)
  'application/octet-stream',  // .fbx, .obj (these might be detected as octet-stream)
];
const ttfDValidation = [
  'font/ttf',    // .ttf
];
const sceneImageType = ['image/jpeg', 'image/jpg'];
// Image validation
export function isValidFileType (fileName:File|Blob) {
  console.log(fileName);
  if (fileName !== null) {
    if (fileName && imageType.includes(fileName.type)) {
      return true;
    }
    return false;
  }
  return true;

}

// Video validation
export function isValidVideoFileType (fileName:File|Blob) {
  if (fileName !== null) {
    if (fileName && videoValidation.includes(fileName.type)) {
      return true;
    }
    return false;

  }
  return true;
}

export const decimalRegex = /^\d+(\.\d+)?$/;

type allowedMediaType = '360_video'|'video'|'multires'|'image'|'360_image'|'pdf'

type ValidationType = 'image' | 'pdf' | 'zip' | 'video' | 'icon' | 'threeD' | 'ttf'|'sceneImage';

export const fileValidationMedia = (file :File, mediaType:allowedMediaType):boolean => {
  // If the value is not a file object (e.g., URL or undefined), return true and skip validation
  if (!file || !(file instanceof File)) {
    return true;
  }

  // Define the allowed file types based on the mediaType
  const allowedTypes = {
    '360_video': ['video/mp4'],
    'video': ['video/mp4'],
    'multires': ['image/jpeg', 'image/jpg', 'image/webp'],
    'image': ['image/jpeg', 'image/jpg', 'image/webp'],
    '360_image': ['image/jpeg', 'image/jpg', 'image/webp'],
    'pdf': ['application/pdf'],
  };

  // Get the allowed file types for the given mediaType
  const validTypes = allowedTypes[mediaType];

  // If no valid types are defined for the mediaType, return false (invalid)
  if (!validTypes) {
    return false;
  }

  // Check if the file's MIME type is included in the allowed types
  return validTypes.includes(file.type);
};

export const fileValidation = (fileName:File, validateTo:ValidationType) => { // ValidateTo => image, pdf, zip, video, icon, 3d
  console.log("File in Validation", fileName);

  if (fileName !== undefined) {
    if (fileName) {
      const validationMap = {
        image: imageType,
        pdf: pdfValidation,
        zip: zipValidation,
        video: videoValidation,
        icon: iconFileValidation,
        threeD: threeDValidation,
        ttf: ttfDValidation,
        '360_video': videoValidation360,
        sceneImage: sceneImageType,
      };

      if (validateTo === 'threeD') {
        // Check for valid 3D file extensions
        const valid3DExtensions = ['.gltf', '.glb', '.obj', '.fbx', '.stl'];
        const fileExtension = '.' + fileName.name.split('.').pop()?.toLowerCase();
        if (valid3DExtensions.includes(fileExtension)) {
          return true;
        }
      }

      return validationMap[validateTo]?.includes(fileName.type) || false;
    }
    return false;
  }
  return true; // While field is not required then it will not create any error message
};

export const imageValidationDeepzoom = (fileName:File) => {
  console.log(fileName);
  if (fileName !== undefined) {
    if (fileName) {
      const formats = [...imageValidation, 'image/png', 'image/tiff']; // Jpeg,png,tiff,png
      return formats.includes(fileName.type) || false;
    }
    return false;
  }
  return true; // While field is not required then it will not create any error message
};

export const imageSizeValidation = (fileName:File) => {
  const MAX_IMAGE_SIZE = 10.6 * 1024 * 1024; // 10 MB in bytes
  if (fileName !== undefined) {
    console.log(fileName.size);
    return fileName.size <= MAX_IMAGE_SIZE;
  }
  return true;
};

export const handleTypeFileValidation = (type :string, schema:yup.AnySchema) => {
  console.log(type);
  switch (type) {
    case 'image':
    case '360_image':
      return schema
        .required('File is required')
        .test('file', 'Not a valid image type', (value : File) => fileValidation(value as File, 'image'))
        .test('file', 'Image size is more than 2 MB', (value : File) => imageSizeValidation(value as File));
    case 'video':
    case '360_video':
      return schema
        .required('File is required')
        .test('file', 'Not a valid video type', (value : File) => fileValidation(value as File, 'video'));
    case 'pdf':
      return schema
        .required('File is required')
        .test('file', 'Not a valid PDF type', (value : File) => fileValidation(value as File, 'pdf'))
        .test('file', 'PDF size is more than 2 MB', (value : File) => imageSizeValidation(value as File));
    case 'embed_link':
      return schema
        .notRequired();
    case 'virtual_tour':
      return schema
        .notRequired();
    default:
      return schema.required('File is required');
  }
};

export const handleEditTypeFileValidation = (type:string, schema:yup.AnySchema) => {
  console.log(type);
  switch (type) {
    case 'image':
    case '360_image':
      return schema
        .test('file', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
        .test('file', 'Image size is more than 2 MB', (value) => imageSizeValidation(value as File));
    case 'video':
    case '360_video':
      return schema
        .test('file', 'Not a valid video type', (value) => fileValidation(value as File, 'video'));
    case 'pdf':
      return schema
        .test('file', 'Not a valid PDF type', (value) => fileValidation(value as File, 'pdf'))
        .test('file', 'PDF size is more than 2 MB', (value) => imageSizeValidation(value  as File));
    case 'embed_link':
      return schema
        .notRequired();
    case 'virtual_tour':
      return schema
        .notRequired();
    default:
      return schema.required('File is required');
  }
};

// Helper function to check if a value is a URL
export function isUrl (value : File|Blob|string) {
  const urlRegex = /^(https?:\/\/[^\s/$.?#].[^\s]*)$/i;
  if (typeof value === "string") {
    return urlRegex.test(value);
  }
  return false;
}

export function validateAndProcessFile (blob: Blob | null | undefined): boolean {
  // Check if the blob is provided
  if (!blob) {
    return false;
  }
  if ('name' in blob) {
    const { name } = blob as File;
    const fileExtension = name.split('.').pop()?.toLowerCase();
    if (fileExtension === 'ttf') {
      return true; // File is valid
    }
    return false; // Invalid file type
  }
  return false; // Invalid blob
}
