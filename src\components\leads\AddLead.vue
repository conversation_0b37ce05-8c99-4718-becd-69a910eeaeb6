<script setup>

import ColouredRadioBtnPill from '../UIElements/ColouredRadioBtnPill.vue';
import Button from '../common/Button.vue';
import Modal from '../common/Modal/Modal.vue';
import Multiselect from 'vue-multiselect';
import { ref, watch } from 'vue';
import { Org_Store } from '../../store/organization';
import { objectToArray } from '../../helpers/domhelper.ts';
import { getListofUnits } from '../../api/projects/units/index.ts';
import FormField from '../common/FormField.vue';
import { ErrorMessage, Form, Field } from 'vee-validate';
import { addLeadsSchema } from '../../validationSchema/lead';
import { CreateLead } from '../../api/leads/index.ts';
import { useRouter } from 'vue-router';
import { uiOperations } from '../../store/uiOperations';
import { leadProductInterest, leadIndustryTypes} from '../../helpers/constants';

/* Props,Ref & Emits */
const router = useRouter();
const organizationsStore = Org_Store();
const uiStore = uiOperations();
const statusItem = ['new', 'hot', 'cold', 'warm', 'not_interested'];
const projectsList = ref([]);
const unitsList = ref([]);
const projectRef = ref(null);
const unitRef = ref(null);
const statusRef = ref(null);
const productInterest = ref();
const IndustryType = ref();

/* Methods */

const handleProjectSelection = (val) => {
  console.log(val._id);
  unitRef.value = null;

  getListofUnits(val._id).then((res) => {
    if (res && Object.keys(res).length > 0){
      unitsList.value = objectToArray(res);
    } else {
      unitsList.value = [];
    }
  }).catch((err) => {
    console.log(err);
    uiStore.handleApiErrorMessage(err.message);

  });
};

const handleSubmit = (val) => {
  console.log(val);
  CreateLead({
    'name': val.name,
    'email': val.email,
    ...(val["phone number"] && {'phone_number': val["phone number"]}),
    ...(val.lead_product_interest && {'lead_product_interest': val.lead_product_interest}),
    ...(val.lead_industry_type && {'lead_industry_type': val.lead_industry_type}),
    ...(val.project && {'project_id': val.project._id}),
    ...((val.project && val.unit) ? {'type': 'unit'} : {'type': 'project'}),
    ...(val.unit && {'unit_id': val.unit._id}),
    'source': 'dashboard',
    ...( val.status && {'lead_status': val.status } ),
  }).then(() => {
    document.dispatchEvent(new Event('refreshLeads'));
    router.go(-1);
  });

};

/* Api Call */
organizationsStore.RefreshProjects();

const handleProjectList = () => {
  if (organizationsStore.projects && Object.keys(organizationsStore.projects).length > 0){
    projectsList.value = objectToArray(organizationsStore.projects);
  } else {
    projectsList.value = [];
  }
};

handleProjectList();

watch(() => organizationsStore.projects, () => {
  handleProjectList();
});

</script>

<template>
     <Modal :open="true">
                <div class="px-4 py-7 bg-bg-1000 dark:bg-bg-150 rounded-2xl w-[600px] sm:h-fit h-screen relative sm:overflow-hidden overflow-y-scroll">
                  <!-- Header -->
                  <div class="flex items-center mb-2 gap-3 sm:justify-between p-2">
                                <svg @click="$router.go(-1)" class="w-6 h-6 hover:cursor-pointer block sm:hidden" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M5 12.0054C5 12.2216 5.07447 12.4054 5.23404 12.5568L13.7447 20.7838C13.883 20.9243 14.0638 21 14.266 21C14.6809 21 15 20.6865 15 20.2541C15 20.0486 14.9149 19.8649 14.7979 19.7351L6.79787 12.0054L14.7979 4.27568C14.9149 4.13514 15 3.95135 15 3.74595C15 3.32432 14.6809 3 14.266 3C14.0638 3 13.883 3.07568 13.7447 3.21622L5.23404 11.4541C5.07447 11.6054 5 11.7892 5 12.0054Z" fill="black"/>
                                </svg>
                                <h2 class=" text-txt-100 dark:text-txt-950 text-xl font-medium"> Add Lead </h2>
                                <svg @click="$router.go(-1)" class="w-6 h-6 hover:cursor-pointer hidden sm:block" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <g clip-path="url(#clip0_172_13831)">
                                    <path d="M4.92535 18.7132C4.74221 18.53 4.63932 18.2816 4.63932 18.0226C4.63932 17.7636 4.74221 17.5152 4.92535 17.3321L17.6864 4.57103C17.8696 4.38789 18.118 4.285 18.377 4.285C18.636 4.285 18.8843 4.38789 19.0675 4.57103C19.2506 4.75417 19.3535 5.00256 19.3535 5.26156C19.3535 5.52056 19.2506 5.76895 19.0675 5.9521L6.30642 18.7132C6.12328 18.8963 5.87489 18.9992 5.61589 18.9992C5.35689 18.9992 5.10849 18.8963 4.92535 18.7132Z" fill="#0F0F0F"/>
                                    <path d="M17.6858 18.7132L4.9247 5.9521C4.74156 5.76895 4.63867 5.52056 4.63867 5.26156C4.63867 5.00256 4.74156 4.75417 4.9247 4.57103C5.10784 4.38789 5.35623 4.285 5.61523 4.285C5.87423 4.285 6.12263 4.38789 6.30577 4.57103L19.0668 17.3321C19.25 17.5152 19.3529 17.7636 19.3529 18.0226C19.3529 18.2816 19.25 18.53 19.0668 18.7132C18.8837 18.8963 18.6353 18.9992 18.3763 18.9992C18.1173 18.9992 17.8689 18.8963 17.6858 18.7132Z" fill="#0F0F0F"/>
                                  </g>
                                  <defs>
                                    <clipPath id="clip0_172_13831">
                                      <rect width="24" height="24" fill="white"/>
                                    </clipPath>
                                  </defs>
                                </svg>
                        </div>
                  <div class=" h-fit max-h-full px-2">
                        <!-- Body -->

                          <Form class="w-full !mb-0" @submit="handleSubmit" :validation-schema="addLeadsSchema" >

                                 <FormField class="mb-7" label="Lead Name"  name="name" required="true" />

                                 <FormField class="mb-7" label="Enter email" type="email" required="true" name="email"  />

                                 <FormField class="mb-7" label="Phone number" type="number"  name="phone number"  />

                                 <div class="relative mb-7">

                                    <Field  name="project" :model-value="projectRef" v-slot="{ field }">

                                      <Multiselect class="h-8"  v-bind="field"  :allow-empty="false" v-model="projectRef" :searchable="true" :show-labels="false" :custom-label="(val) => val.name" placeholder="Interested project*"   @select="handleProjectSelection" :options="projectsList" track-by="_id" maxHeight="250" >
                                      </Multiselect>

                                    </Field>

                                    <ErrorMessage name="project"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                        </svg>
                                                        <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                                   </ErrorMessage>

                                  </div>
                                 <div class="relative mb-7">

                                    <Field  name="lead_product_interest" :model-value="productInterest" v-slot="{ field }">

                                      <Multiselect class="h-8"  v-bind="field"  :allow-empty="false" v-model="productInterest" :searchable="true" :show-labels="false" :custom-label="(val) => val" placeholder="Lead Product Interest*"  :options="leadProductInterest" track-by="" maxHeight="250" >
                                      </Multiselect>

                                    </Field>

                                    <ErrorMessage name="lead_product_interest"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                        </svg>
                                                        <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                                   </ErrorMessage>

                                  </div>

                                 <div class="relative mb-7">

                                    <Field  name="lead_industry_type" :model-value="IndustryType" v-slot="{ field }">

                                      <Multiselect class="h-8"  v-bind="field"  :allow-empty="false" v-model="IndustryType" :searchable="true" :show-labels="false" :custom-label="(val) => val" placeholder="Lead Industry Type*"  :options="leadIndustryTypes" track-by="" maxHeight="250" >
                                      </Multiselect>

                                    </Field>

                                    <ErrorMessage name="lead_industry_type"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                        </svg>
                                                        <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                                   </ErrorMessage>

                                  </div>

                                   <div class="relative mb-7"  v-if="unitsList.length > 0">

                                        <Field  name="unit" :model-value="unitRef" v-slot="{ field }">

                                            <Multiselect class="h-8"   v-bind="field" :allow-empty="false" v-model="unitRef" :searchable="true" :show-labels="false" :custom-label="(val) => val.name" placeholder="Add Interested Units*" :options="unitsList" track-by="name" maxHeight="250"  >
                                            </Multiselect>

                                        </Field>

                                        <ErrorMessage name="unit"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                                                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                              <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                              </svg>
                                                              <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                                        </ErrorMessage>

                                  </div>

                                <div class="w-full mb-4">
                                    <div class="w-full flex justify-start items-center gap-3 mb-3">

                                               <h5 class="text-txt-100 dark:text-txt-950 text-lg font-medium text-left">
                                                    Status
                                                </h5>
                                                <svg  class="w-6 h-6 fill- dark:fill-bg-1000 " viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                     <path d="M18.4166 3H5.58332C3.60833 3 2 4.35736 2 6.03121V13.1838C2 14.8506 3.60833 16.2079 5.58332 16.2079L8.40767 16.2334C8.5893 16.235 8.7569 16.3162 8.85028 16.4477L10.1583 18.2897C10.475 18.7327 11.0333 19 11.65 19C12.2666 19 12.8167 18.7327 13.1333 18.2897L14.5052 16.417C14.6004 16.287 14.7686 16.2079 14.9496 16.2079H18.4167C20.3957 16.2079 22 14.854 22 13.1838V6.03121C22 4.35736 20.3916 3 18.4166 3ZM11.9471 5.73086C12.4353 5.73086 12.7804 6.06781 12.7804 6.43415C12.7804 6.81539 12.4407 7.12843 11.9512 7.12843C11.4542 7.12844 11.1138 6.79946 11.1138 6.43415C11.1138 6.06525 11.4747 5.73086 11.9471 5.73086ZM12.7821 12.7897C12.7821 13.1781 12.4093 13.493 11.9487 13.493C11.4881 13.493 11.1154 13.1781 11.1154 12.7897V9.18149C11.1154 8.7931 11.4881 8.47819 11.9487 8.47819C12.4093 8.47819 12.7821 8.7931 12.7821 9.18149V12.7897Z" />
                                                </svg>

                                    </div>

                                    <div class="relative">
                                      <Field  name="status" :model-value="statusRef" v-slot="{ field }">
                                         <ColouredRadioBtnPill v-bind="field" :status="statusItem" @selected="(val) => statusRef = val"></ColouredRadioBtnPill>
                                      </Field>

                                        <ErrorMessage name="status"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                            </svg>
                                                            <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                                        </ErrorMessage>
                                    </div>

                                </div>

                                  <!-- Footer -->
                                  <div class="w-full flex flex-col justify-start items-start gap-5">
                                              <Button title="Save" type="submit" class="w-full !h-10 !text-sm !font-medium" theme="primary" />
                                              <Button title="Cancel" type="button" class="w-full h-10" theme="secondary" @handle-click="$router.go(-1)" />
                                  </div>
                      </Form>

                  </div>
                </div>
     </Modal>
</template>

<style>

</style>
