<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
import SceneById from '../../../components/scenes/SceneById.vue';
import { UserStore } from '../../../store/index';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ProjectStore } from '../../../store/project';
import designMenuBar from '@/components/scenes/DesignMenuBar.vue';

const userStore = UserStore();
const route = useRoute();
const projectId = ref(route.params.project_id);
const projectStore = ProjectStore();

watch(() => route.params.project_id, (newVal) => {
  projectId.value = newVal;
  if (projectId.value){
    projectStore.ForceRefreshSettings(projectId.value);
    projectStore.RefreshScenes(projectId.value);
  }
}, {immediate: true});
</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar />
        <div
            class="h-full overflow-hidden w-full">
            <div v-if="userStore.user_data"
                class="pt-0 bg-transparent h-full overflow-y-auto w-full flex-1 bg-gray-100 flex flex-col overflow-hidden">
                   <div class="flex justify-evenly h-full w-full overflow-hidden gap-2 border relative z-10">
                   <designMenuBar/>
                   <SceneById />
                   <router-view></router-view>
                   </div>
            </div>
        </div>
    </div>
</template>
