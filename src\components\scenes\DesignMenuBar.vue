<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const selectedMenu = ref('scenes');
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const unitplanId = ref(route.params.unitplan_id);
const tourId = ref(route.params.tour_id);

if (route.path.includes('/layers')) {
  selectedMenu.value = 'layers';
} else if (route.path.includes('/icons')) {
  selectedMenu.value = 'icons';
} else if (route.path.includes('/tours')){
  selectedMenu.value = 'tours';
} else if (route.path.includes('/galleryCategory')){
  selectedMenu.value = 'gallery';
} else if (route.path.includes('/amenityCategory')){
  selectedMenu.value = 'amenity';
} else {
  selectedMenu.value = 'scenes';
}

watch(
  () => route.path,
  (newPath) => {
    console.log(route.params.scene_id);
    if (route.params.scene_id){
      sceneId.value = route.params.scene_id;
    }
    if (route.params.tour_id){
      tourId.value = route.params.tour_id;
    }
    if (route.params.project_id){
      projectId.value = route.params.project_id;
    }
    if (route.params.unitplan_id){
      unitplanId.value = route.params.unitplan_id;
    }
    if (newPath.includes('/layers')) {
      selectedMenu.value = 'layers';
    } else if (newPath.includes('/icons')) {
      selectedMenu.value = 'icons';
    } else if (newPath.includes('/tours')){
      selectedMenu.value = 'tours';
    } else if (newPath.includes('/unitplan')){
      selectedMenu.value = 'unitplan';
    } else if (route.path.includes('/galleryCategory')){
      selectedMenu.value = 'gallery';
    } else if (route.path.includes('/amenityCategory')){
      selectedMenu.value = 'amenity';
    } else {
      selectedMenu.value = 'scenes';
    }
  },
  { immediate: true },
);

watch(() => selectedMenu.value, (newVal) => {
  selectedMenu.value = newVal;
});

</script>

<template>
    <div
    class="h-full w-12 bg-gray-100 dark:bg-bg-default flex flex-col justify-between pt-4 overflow-auto">
    <div
      class="flex flex-col justify-center gap-y-2 h-fit p-1">
      <RouterLink :to="`/projects/${projectId}/design/scenes/${sceneId?sceneId:''}`"
        :class="`${selectedMenu==='scenes'? 'bg-white shadow-md' : ''}`"
        @click="selectedMenu='scenes'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div
          :class="` ${selectedMenu==='scenes' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
          <svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.66807 12.608L1.66814 12.608V12.6V5.4H4.81387C5.32681 5.4 5.82263 5.20457 6.1913 4.85064C6.56067 4.49604 6.77216 4.01065 6.77216 3.5V0.5H11.4195C11.6723 0.505398 11.9087 0.606379 12.0785 0.774997C12.2486 0.943804 12.3382 1.16604 12.3345 1.39198L12.3345 1.39198V1.4V12.6H12.3344L12.3345 12.608C12.3382 12.834 12.2486 13.0562 12.0785 13.225C11.9087 13.3936 11.6723 13.4946 11.4195 13.5H2.58309C2.33031 13.4946 2.0939 13.3936 1.92406 13.225C1.75403 13.0562 1.66445 12.834 1.66807 12.608ZM4.21636 0.976939C4.24769 0.947219 4.28022 0.91874 4.31387 0.89156V3H2.11163C2.1263 2.98451 2.14132 2.96928 2.15667 2.9543L4.21636 0.976939Z"/>
          </svg>
        </div>
      </RouterLink>

      <RouterLink
        :to="route.params.scene_id ? `/projects/${projectId}/design/scenes/${sceneId?sceneId:''}/layers` : ''"
        :class="[
          `${selectedMenu==='layers'? 'bg-white shadow-md' : ''}`,
          !route.params.scene_id ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''
        ]"
        @click="route.params.scene_id && (selectedMenu='layers')"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">

        <div
          :class="` ${selectedMenu==='layers' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
          <svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_723_18990)">
      <path d="M12.4061 6.87478L12.129 7.0128L7.22391 9.45631L7.00096 9.56737L6.77802 9.45631L1.87288 7.0128L1.59583 6.87478V6.56525V6.43517C1.59583 6.38527 1.57524 6.33217 1.52998 6.28906C1.48389 6.24517 1.41639 6.21649 1.34119 6.21649C1.26599 6.21649 1.1985 6.24517 1.15241 6.28906C1.10715 6.33217 1.08656 6.38527 1.08656 6.43517V7.00005V7.00014C1.08655 7.03498 1.09646 7.07085 1.11756 7.10407C1.1388 7.13751 1.17131 7.16793 1.21407 7.1893L12.4061 6.87478ZM12.4061 6.87478V6.56525V6.43517C12.4061 6.38527 12.4267 6.33217 12.4719 6.28906C12.518 6.24517 12.5855 6.21649 12.6607 6.21649C12.7359 6.21649 12.8034 6.24517 12.8495 6.28906L13.1943 5.92699M12.4061 6.87478L13.1943 5.92699M13.1943 5.92699L12.8495 6.28906C12.8948 6.33217 12.9154 6.38527 12.9154 6.43517V7.00005V7.00014M13.1943 5.92699L12.9154 7.00014M12.9154 7.00014C12.9154 7.03498 12.9055 7.07085 12.8844 7.10407C12.8631 7.13751 12.8306 7.16793 12.7878 7.1893L12.9154 7.00014ZM12.4016 10.3352L12.1245 10.4732L7.21938 12.9167L6.99644 13.0278L6.77349 12.9167L1.86836 10.4732L1.5913 10.3352V10.0257V9.89561C1.5913 9.84571 1.57071 9.79261 1.52545 9.7495C1.47936 9.70561 1.41186 9.67693 1.33667 9.67693C1.26147 9.67693 1.19397 9.70561 1.14788 9.7495C1.10262 9.79261 1.08203 9.84571 1.08203 9.89561V10.4612V10.4613C1.08203 10.4961 1.09193 10.532 1.11304 10.5652L0.690991 10.8333L1.11304 10.5652C1.1343 10.5987 1.16686 10.6292 1.20971 10.6505L6.86744 13.4703C6.8677 13.4705 6.86795 13.4706 6.8682 13.4707C6.90674 13.4896 6.95078 13.5 6.99644 13.5C7.0421 13.5 7.08616 13.4896 7.1247 13.4707C7.12494 13.4706 7.12518 13.4705 7.12543 13.4703L12.7832 10.6505L12.4016 10.3352ZM12.4016 10.3352V10.0257M12.4016 10.3352V10.0257M12.4016 10.0257V9.89561C12.4016 9.84571 12.4222 9.79261 12.4674 9.7495C12.5135 9.70561 12.581 9.67693 12.6562 9.67693C12.7314 9.67693 12.7989 9.70561 12.845 9.7495C12.8903 9.79261 12.9108 9.84571 12.9108 9.89561V10.4612V10.4613M12.4016 10.0257L12.9108 10.4613M12.9108 10.4613C12.9108 10.4961 12.9009 10.532 12.8798 10.5652C12.8586 10.5987 12.8261 10.6291 12.7833 10.6505L12.9108 10.4613ZM1.21483 3.72886L1.19666 3.7198C1.16194 3.69929 1.13503 3.67256 1.1166 3.64356C1.09548 3.61033 1.08557 3.57446 1.08557 3.53961C1.08557 3.50477 1.09548 3.46889 1.1166 3.43566C1.13788 3.40219 1.17046 3.37173 1.21332 3.35036C1.21332 3.35036 1.21332 3.35036 1.21333 3.35036L6.87309 0.529548L6.87325 0.529468C6.91124 0.510517 6.95489 0.5 7.00021 0.5C7.04552 0.5 7.08918 0.510517 7.12717 0.529469L7.12733 0.529548L12.7871 3.35036C12.83 3.37173 12.8625 3.40219 12.8838 3.43566C12.9049 3.46889 12.9149 3.50477 12.9149 3.53961C12.9149 3.57446 12.9049 3.61033 12.8838 3.64356L13.2639 3.88518L12.8838 3.64356C12.8625 3.67704 12.83 3.7075 12.7871 3.72886L7.12879 6.54895C7.12861 6.54904 7.12843 6.54913 7.12825 6.54922C7.09005 6.568 7.04631 6.57839 7.00096 6.57839C6.95563 6.57839 6.91191 6.56801 6.87371 6.54923C6.87352 6.54914 6.87333 6.54905 6.87314 6.54895L1.21483 3.72886ZM6.87384 10.0101L1.21423 7.18938H12.7877L7.12808 10.0101L7.12792 10.0102C7.08993 10.0291 7.04628 10.0397 7.00096 10.0397C6.95564 10.0397 6.91199 10.0291 6.874 10.0102L6.87384 10.0101Z"/>
      </g>
      <defs>
      <clipPath id="clip0_723_18990">
      <rect width="14" height="14" fill="white"/>
      </clipPath>
      </defs>
      </svg>
        </div>
      </RouterLink>

      <RouterLink :to="route.params.scene_id?`/projects/${projectId}/design/scenes/${sceneId?sceneId:''}/icons`:null"
        :class="[`${selectedMenu==='icons'? 'bg-white shadow-md' : ''}`
        ,!route.params.scene_id ? 'opacity-50 cursor-not-allowed pointer-events-none' : '']"
        @click="selectedMenu='icons'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div
          :class="` ${selectedMenu==='icons' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">

<svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="star"><rect width="24" height="24" transform="rotate(90 12 12)" opacity="0"/><path d="M17.56 21a1 1 0 0 1-.46-.11L12 18.22l-5.1 2.67a1 1 0 0 1-1.45-1.06l1-5.63-4.12-4a1 1 0 0 1-.25-1 1 1 0 0 1 .81-.68l5.7-.83 2.51-5.13a1 1 0 0 1 1.8 0l2.54 5.12 5.7.83a1 1 0 0 1 .81.68 1 1 0 0 1-.25 1l-4.12 4 1 5.63a1 1 0 0 1-.4 1 1 1 0 0 1-.62.18z"/></g></g></svg>
        </div>
      </RouterLink>

      <RouterLink :to="`/projects/${projectId}/design/unitplan/${unitplanId?unitplanId:''}`"
        :class="`${selectedMenu==='unitplan'? 'bg-white shadow-md' : ''}`"
        @click="selectedMenu='unitplan'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div :class="` ${selectedMenu==='unitplan' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M6.85122 2.0416L6.85122 4.40033C6.85122 4.81454 7.18701 5.15033 7.60122 5.15033C8.01543 5.15033 8.35122 4.81454 8.35122 4.40033L8.35122 2.0046C8.85703 2 9.40518 2 10.0003 2C13.7716 2 15.6574 2 16.8289 3.17162C17.6155 3.95812 17.874 5.06642 17.959 6.85094L14.798 6.85094C14.3838 6.85094 14.048 7.18673 14.048 7.60094C14.048 8.01516 14.3838 8.35094 14.798 8.35094L17.996 8.35094C18.0006 8.85682 18.0006 9.40507 18.0006 10.0003C18.0006 13.7716 18.0006 15.6574 16.8289 16.8289C16.1758 17.4822 15.3006 17.7712 14.0004 17.899L10.7487 17.9816C10.7591 16.1955 12.2103 14.7508 13.9988 14.7508C14.413 14.7508 14.7488 14.415 14.7488 14.0008C14.7488 13.5866 14.413 13.2508 13.9988 13.2508C11.3756 13.2508 9.24905 15.3771 9.24865 18.0002C8.93547 17.9997 8.63661 17.9986 8.35122 17.996L8.35122 8.35094L11.5986 8.35094C12.0128 8.35094 12.3486 8.01516 12.3486 7.60094C12.3486 7.18673 12.0128 6.85094 11.5986 6.85094L7.60122 6.85094L2.04161 6.85094C2.12659 5.06642 2.38511 3.95812 3.17162 3.17162C3.95816 2.38507 5.06653 2.12656 6.85122 2.0416ZM2.00461 8.35094C2 8.85682 2 9.40507 2 10.0003C2 13.7716 2 15.6574 3.17162 16.8289C3.95816 17.6155 5.06653 17.874 6.85122 17.959L6.85122 8.35094L2.00461 8.35094Z" :fill="selectedMenu==='unitplan'?'#2563EB':'#6B7280'"/>
        </svg>
        </div>

      </RouterLink>
            <RouterLink :to="`/projects/${projectId}/design/tours/${tourId?tourId:''}`"
        :class="`${selectedMenu==='tours'? 'bg-white shadow-md' : ''}`"
        @click="selectedMenu='tours'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div :class="` ${selectedMenu==='tours' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
        <svg class="fill-[inherit]" width="17" height="16" viewBox="0 0 17 16"  xmlns="http://www.w3.org/2000/svg">
        <path d="M4.75 1H12.25C13.6519 1 14.3528 1 14.875 1.30144C15.2171 1.49892 15.5011 1.78296 15.6986 2.125C16 2.64711 16 3.34808 16 4.75C16 6.15192 16 6.85285 15.6986 7.375C15.5011 7.71708 15.2171 8.0011 14.875 8.19858C14.3528 8.5 13.6519 8.5 12.25 8.5H11.9547C11.4396 8.5 11.1819 8.5 10.9409 8.44645C10.6183 8.37483 10.3154 8.23293 10.0539 8.03095C9.8584 7.88005 9.69347 7.6822 9.3637 7.28643C9.1009 6.97105 8.9695 6.8134 8.81868 6.7426C8.61678 6.64788 8.38322 6.64788 8.18132 6.7426C8.0305 6.8134 7.8991 6.97105 7.6363 7.28643C7.30653 7.6822 7.1416 7.88005 6.94617 8.03095C6.68457 8.23293 6.38169 8.37483 6.05907 8.44645C5.81805 8.5 5.56046 8.5 5.04527 8.5H4.75C3.34808 8.5 2.64711 8.5 2.125 8.19858C1.78295 8.0011 1.49892 7.71708 1.30144 7.375C1 6.85285 1 6.15192 1 4.75C1 3.34808 1 2.64711 1.30144 2.125C1.49892 1.78296 1.78295 1.49892 2.125 1.30144C2.64711 1 3.34808 1 4.75 1Z"  stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8.57545 13L7.06023 11.5M8.57545 13L7.06023 14.5M8.57545 13C4.78804 13 1.60667 11.5 1 10M10.8483 12.8283C13.4518 12.4235 15.4497 11.3419 16 10" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        </div>
      </RouterLink>

      <RouterLink :to="`/projects/${projectId}/design/galleryCategory`"
        :class="`${selectedMenu==='gallery'? 'bg-white shadow-md' : ''}`"
        @click="selectedMenu='gallery'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div :class="` ${selectedMenu==='gallery' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"  viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="image"><rect opacity="0"/><path d="M18 3H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3zM6 5h12a1 1 0 0 1 1 1v8.36l-3.2-2.73a2.77 2.77 0 0 0-3.52 0L5 17.7V6a1 1 0 0 1 1-1zm12 14H6.56l7-5.84a.78.78 0 0 1 .93 0L19 17v1a1 1 0 0 1-1 1z"/><circle cx="8" cy="8.5" r="1.5"/></g></g></svg>
        </div>

      </RouterLink>

      <RouterLink :to="`/projects/${projectId}/design/amenityCategory`"
        :class="`${selectedMenu==='amenity'? 'bg-white shadow-md' : ''}`"
        @click="selectedMenu='amenity'"
        class="h-10 w-full flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div :class="` ${selectedMenu==='amenity' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">
          <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M760-600q-57 0-99-34t-56-86H354q-11 42-41.5 72.5T240-606v251q52 14 86 56t34 99q0 66-47 113T200-40q-66 0-113-47T40-200q0-57 34-99t86-56v-251q-52-14-86-56t-34-98q0-66 47-113t113-47q56 0 98 34t56 86h251q14-52 56-86t99-34q66 0 113 47t47 113q0 66-47 113t-113 47ZM200-120q33 0 56.5-24t23.5-56q0-33-23.5-56.5T200-280q-32 0-56 23.5T120-200q0 32 24 56t56 24Zm0-560q33 0 56.5-23.5T280-760q0-33-23.5-56.5T200-840q-32 0-56 23.5T120-760q0 33 24 56.5t56 23.5ZM760-40q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113T760-40Zm0-80q33 0 56.5-24t23.5-56q0-33-23.5-56.5T760-280q-33 0-56.5 23.5T680-200q0 32 23.5 56t56.5 24Zm0-560q33 0 56.5-23.5T840-760q0-33-23.5-56.5T760-840q-33 0-56.5 23.5T680-760q0 33 23.5 56.5T760-680ZM200-200Zm0-560Zm560 560Zm0-560Z"/></svg>
        </div>

      </RouterLink>

      <div class="w-full border border-1 border-gray-500"></div>

      <RouterLink :to="`/projects/${projectId}/design/help`"
       @click="selectedMenu='help'"
        :class="`${selectedMenu.name==='help' ? 'bg-white shadow-md' : ''}`"
        class="h-10 w-full text-lg flex flex-col justify-center items-center cursor-pointer relative z-0 rounded-lg">
        <div
          :class="`${selectedMenu.name==='help' ? 'fill-blue-600 stroke-blue-600' : 'fill-gray-500 stroke-gray-500'}`">

<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="menu-arrow-circle"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 16a1 1 0 1 1 1-1 1 1 0 0 1-1 1zm1-5.16V14a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1 1.5 1.5 0 1 0-1.5-1.5 1 1 0 0 1-2 0 3.5 3.5 0 1 1 4.5 3.34z"/></g></g></svg>

        </div>
      </RouterLink>
    </div>
    </div>

</template>

<style scoped>
</style>
