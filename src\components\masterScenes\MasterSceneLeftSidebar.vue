<script setup>
import { ref, watch } from 'vue';
import router from '@/router';
import { Org_Store } from '../../store/organization';
import Modal from '../common/Modal/Modal.vue';
import UpdateMasterSvgLayers from './UpdateMasterSvgLayers.vue';
import CreateCoordinates from '../common/ModalContent/CreateCoordinates.vue';
import { useRoute } from 'vue-router';
import { getCookie } from '../../helpers/domhelper.ts';
import RightSidebar from '../common/Modal/RightSidebar.vue';

const route = useRoute();
const domain = 'https://propvr-ui-library-k5aul2eu6q-uc.a.run.app/';
const sceneId = ref(route.params.scene_id);
const newOrganizationStore = Org_Store();
const selectedSvg = ref();

const openCreateCoordinates = ref(false);
const loader = ref([]);

newOrganizationStore.RefreshMasterScenes();
newOrganizationStore.RefreshProjects();

const handleLayerSelect = (layer) => {
  router.push({ path: route.path, query: { svgId: route.query.svgId, layerId: layer.layer_id } });
};

const handleCloseUpdateSidebar = () => {
  router.push({ path: route.path, query: { svgId: route.query.svgId } });
};

const handleSelectSvg = (svg) => {
  if (selectedSvg.value?._id === svg._id) {
    selectedSvg.value = null;
  } else {
    selectedSvg.value = svg;
  }
};

watch(() => selectedSvg.value, () => {
  if (!selectedSvg.value) {
    router.push({ path: route.path, query: {} });
  } else {
    router.push({ path: route.path, query: { svgId: selectedSvg.value._id } });
  }
});

// Const HandleCreateCoordinates = (data) => {
//   Loader.value.push('createCoordinates ->', data);
//   OrganizationStore.createCoordinates(data).then(() => {
//     Loader.value.splice(loader.value.indexOf('createCoordinates'), 1);
//     OpenCreateCoordinates.value = false;
//   }).catch(() => {
//     Loader.value.splice(loader.value.indexOf('createCoordinates'), 1);
//   });
// };

</script>

<template >
  <!-- Static sidebar for desktop -->
  <div
    class="h-full w-72 dark:bg-bg-default flex flex-col  bg-bg-1000 border-bg-900 border-r-[1px] overflow-auto relative">
    <!-- Sidebar component, swap this element with another sidebar if you like -->
    <div class="flex grow flex-col gap-y-4 overflow-y-auto px-4 pb-4 mt-4">

      <div>
        <a :href="domain + `${getCookie('organization')}/masterscene/${sceneId}`"
          _target="blank" type="button" @click="{ }"
          class=" w-full mb-2 createButton inline-flex items-center rounded-md bg-gray-500 px-3 py-2 text-sm font-semibold text-white shadow-sm  hover:bg-gray-700">
          Preview
        </a>
        <div class="flex gap-2">
          <button type="button"
            @click="() => { newOrganizationStore.masterScenes?.[sceneId].sceneData.type !== 'earth' ? router.push(`/masterscenes/${sceneId}/createsvg`) : openCreateCoordinates = true }"
            class="w-full createButton inline-flex items-center rounded-md  bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 px-3 py-2 text-sm font-semibold shadow-sm">
            <PlusIcon class="h-4 w-4 mr-2 text-white"
              aria-hidden="true" />
            {{
              newOrganizationStore.masterScenes?.[sceneId]?.sceneData.type
              !== 'earth' ? 'Create New Svg'
              : "Create Coordinates" }}
          </button>
          <button type="button"
          @click="() => { newOrganizationStore.masterScenes?.[sceneId].sceneData.type !== 'earth' ?  router.push(`/masterscenes/${sceneId}/edit`) : router.push(`/masterscenes/earth/${sceneId}/edit`)}"
            class="w-fit createButton inline-flex items-center rounded-md  bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 px-2.5 py-2 text-sm font-semibold shadow-sm fill-white">
            <svg xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24" class="h-5 w-5">
              <g data-name="Layer 2">
                <g data-name="settings-2">
                  <rect width="24" height="24"
                    transform="rotate(180 12 12)"
                    opacity="0" />
                  <path
                    d="M12.94 22h-1.89a1.68 1.68 0 0 1-1.68-1.68v-1.09a.34.34 0 0 0-.22-.29.38.38 0 0 0-.41 0l-.74.8a1.67 1.67 0 0 1-2.37 0L4.26 18.4a1.66 1.66 0 0 1-.5-1.19 1.72 1.72 0 0 1 .5-1.21l.74-.74a.34.34 0 0 0 0-.37c-.06-.15-.16-.26-.3-.26H3.68A1.69 1.69 0 0 1 2 12.94v-1.89a1.68 1.68 0 0 1 1.68-1.68h1.09a.34.34 0 0 0 .29-.22.38.38 0 0 0 0-.41L4.26 8a1.67 1.67 0 0 1 0-2.37L5.6 4.26a1.65 1.65 0 0 1 1.18-.5 1.72 1.72 0 0 1 1.22.5l.74.74a.34.34 0 0 0 .37 0c.15-.06.26-.16.26-.3V3.68A1.69 1.69 0 0 1 11.06 2H13a1.68 1.68 0 0 1 1.68 1.68v1.09a.34.34 0 0 0 .22.29.38.38 0 0 0 .41 0l.69-.8a1.67 1.67 0 0 1 2.37 0l1.37 1.34a1.67 1.67 0 0 1 .5 1.19 1.63 1.63 0 0 1-.5 1.21l-.74.74a.34.34 0 0 0 0 .37c.06.15.16.26.3.26h1.09A1.69 1.69 0 0 1 22 11.06V13a1.68 1.68 0 0 1-1.68 1.68h-1.09a.34.34 0 0 0-.29.22.34.34 0 0 0 0 .37l.77.77a1.67 1.67 0 0 1 0 2.37l-1.31 1.33a1.65 1.65 0 0 1-1.18.5 1.72 1.72 0 0 1-1.19-.5l-.77-.74a.34.34 0 0 0-.37 0c-.15.06-.26.16-.26.3v1.09A1.69 1.69 0 0 1 12.94 22zm-1.57-2h1.26v-.77a2.33 2.33 0 0 1 1.46-2.14 2.36 2.36 0 0 1 2.59.47l.54.54.88-.88-.54-.55a2.34 2.34 0 0 1-.48-2.56 2.33 2.33 0 0 1 2.14-1.45H20v-1.29h-.77a2.33 2.33 0 0 1-2.14-1.46 2.36 2.36 0 0 1 .47-2.59l.54-.54-.88-.88-.55.54a2.39 2.39 0 0 1-4-1.67V4h-1.3v.77a2.33 2.33 0 0 1-1.46 2.14 2.36 2.36 0 0 1-2.59-.47l-.54-.54-.88.88.54.55a2.39 2.39 0 0 1-1.67 4H4v1.26h.77a2.33 2.33 0 0 1 2.14 1.46 2.36 2.36 0 0 1-.47 2.59l-.54.54.88.88.55-.54a2.39 2.39 0 0 1 4 1.67z"
                    data-name="&lt;Group&gt;" />
                  <path
                    d="M12 15.5a3.5 3.5 0 1 1 3.5-3.5 3.5 3.5 0 0 1-3.5 3.5zm0-5a1.5 1.5 0 1 0 1.5 1.5 1.5 1.5 0 0 0-1.5-1.5z" />
                </g>
              </g>
            </svg>
          </button>

        </div>
      </div>

      <nav
        class="flex flex-grow flex-col text-white overflow-scroll overflow-x-hidden hide-scroll-bar">
        <ul role="list" class="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" class="">
              <li>
                <div
                  v-for="svg, svgId  in  newOrganizationStore.masterScenes?.[sceneId]?.svgData"
                  :key="svgId">
                  <div
                    class="group flex justify-between gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold cursor-pointer text-txt-100 dark:text-txt-950 hover:text-txt-1000 hover:bg-bg-100 fill-black hover:fill-white"
                    style="transition: color 0.3s;"
                    @click="handleSelectSvg(svg)">
                    <div class="flex items-center">
                      <component :is="ChartPieIcon"
                        class="h-6 w-6 shrink-0 mr-2"
                        aria-hidden="true" />
                      {{ svg.type }}
                    </div>
                    <div class="w-fit h-full my-auto">
                      <svg
                        v-if="$route.query.svgId != svg._id"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        class="h-5 w-5">
                        <g data-name="Layer 2">
                          <g data-name="plus">
                            <rect width="24" height="24"
                              transform="rotate(180 12 12)"
                              opacity="0" />
                            <path
                              d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                          </g>
                        </g>
                      </svg>

                      <svg v-else
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        class="h-5 w-5">
                        <g data-name="Layer 2">
                          <g data-name="minus">
                            <rect width="24" height="24"
                              transform="rotate(180 12 12)"
                              opacity="0" />
                            <path
                              d="M19 13H5a1 1 0 0 1 0-2h14a1 1 0 0 1 0 2z" />
                          </g>
                        </g>
                      </svg>
                    </div>

                  </div>
                  <div v-if="$route.query.svgId === svg._id"
                    class="pl-10 max-h-48 overflow-auto layers-container">
                    <div
                      v-for="layer, layerId  in  svg.layers"
                      :key="layerId"
                      @click="handleLayerSelect(layer)"
                      class="group flex gap-x-3 p-2 text-xs   leading-6 font-semibold cursor-pointer text-black hover:bg-gray-300 rounded"
                      style="transition: color 0.3s;">
                      {{ layer.layer_id }}
                    </div>
                  </div>
                </div>
              </li>
            </ul>

          </li>

        </ul>
      </nav>
    </div>

    <RightSidebar
      :open="$route.query.svgId && $route.query.layerId && newOrganizationStore.masterScenes?.[sceneId]"
      @closeModal="handleCloseUpdateSidebar">
      <UpdateMasterSvgLayers
        @closeModal="() => router.push({ path: route.path, query: { svgId: route.query.svgId } })"
        @UpdateSvgLayer="HandleUpdateSvgLayer"
        :loader="loader"
        :svgData="newOrganizationStore.masterScenes?.[sceneId].svgData"
        :scenes="newOrganizationStore.masterScenes" />
    </RightSidebar>

    <Modal :open="openCreateCoordinates">
      <CreateCoordinates :sceneId="sceneId"
        :dataName="'Create Coordinates'"
        @handleSubmit="HandleCreateCoordinates"
        @closeModal="() => openCreateCoordinates = !openCreateCoordinates"
        :loader="loader" :loaderValue="'createCoordinates'" />
    </Modal>
    <!-- <Modal :open="openEditSettings">
      <EditSceneSettingModal :dataName="'Master Scene'"
        @handleSubmitForm="handleUpdateSetting"
        @closeModal="() => openEditSettings = !openEditSettings"
        :scenes="newOrganizationStore.masterScenes"
        :SceneId="sceneId" :loader="loader" />
    </Modal> -->
  </div>
</template>

<style scoped>
/* width */

.layers-container::-webkit-scrollbar {
  width: 4px;
}

.overflow-x-hidden::-webkit-scrollbar {
  width: 0px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>
