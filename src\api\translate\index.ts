import type { addRemoveLanguageParams, translateParams, updateTranslationCodeParams, storeTranslationParams } from '@/types/translate';
import { GetRequestWithHeaders, GetRequestWithHeadersAndParms, PostRequestWithHeaders } from '../../helpers/apihelper';
import type { AxiosRequestConfig } from 'axios';
import { getCookie } from '@/helpers/domhelper';
import axios from 'axios';

const api_url = import.meta.env.VITE_API_URL;

export async function GetAllTranslation (){
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({url: `${api_url}/translation/GetAllTranslation`}).then((res) => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function DeleteTranslation (translationId:string){
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({url: `${api_url}/translation/DeleteTranslationById?translationId=${translationId}`}).then((res) => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function Translate (params:translateParams){
  return new Promise((resolve, reject) => {
    GetRequestWithHeadersAndParms({
      url: `${api_url}/translation/translate`, params,
    }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function AddNewLanguage (params:addRemoveLanguageParams){
  return new Promise((resolve, reject) => {
    GetRequestWithHeadersAndParms({
      url: `${api_url}/translation/AddNewLanguage`, params,
    }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function RemoveLanguage (params:addRemoveLanguageParams){
  return new Promise((resolve, reject) => {
    GetRequestWithHeadersAndParms({
      url: `${api_url}/translation/RemoveLanguage`, params,
    }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function UpdateTranslation (payload:updateTranslationCodeParams){
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/translation/UpdateTranslationById`, body: {...payload},
    }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function StoreTranslation (text:string, payload:storeTranslationParams) {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/translation/AddTranslation?sourceLanguageCode=en&text=${text}`, body: payload,
    }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function TranslateProjectData (project_id:string) {
  return new Promise((resolve, reject) => {
    GetRequestWithHeadersAndParms({
      url: `${api_url}/translation/TranslateProjectData?project_id=${project_id}`,
    }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function ExportTranslationApi () {
  const config: AxiosRequestConfig = {
    method: 'get',
    url: `${api_url}/translation/download`,
    responseType: 'blob',  // Add this line
    headers: {
      'accesstoken': String(getCookie('accessToken')) || undefined,
      'organization': String(getCookie('organization')) || undefined,
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
  };

  return axios(config);
}

export async function importTranslations (file:File) {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/translation/translations/import`, body: file,
    }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}
