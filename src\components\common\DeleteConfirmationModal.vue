<script setup>
import Spinner from '../common/Spinner.vue';

const emits = defineEmits(['close', 'handleDelete']);

const handleDelete = () => {
  console.log('Delete action triggered');
  emits('handleDelete');
  // emits('close'); // Close the modal after delete action
};

</script>

<template>
        <div
            class="bg-white border border-gray-200 rounded-lg w-[416px]"
            >
            <div class="">
                <div class="h-9  flex justify-between items-center px-2.5 border-b border-gray-200 rounded-t-lg">
                    <p class="text-sm font-medium text-gray-900"></p>
                    <button  class="fill-gray-400" @click="$emit('close')">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
                    </button>

                </div>

                <div class="p-3 sm:p-6 ">

                  <div>
                    <div class="flex flex-col justify-center items-center pb-0 border-b-[none] gap-3">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g id="exclamation" clip-path="url(#clip0_1158_6412)">
                          <path id="Vector" d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34873 18.9426 4.80688 17.0679 2.93215C15.1931 1.05741 12.6513 0.0029116 10 0ZM10 15C9.80222 15 9.60888 14.9413 9.44443 14.8315C9.27998 14.7216 9.15181 14.5654 9.07612 14.3827C9.00044 14.2 8.98063 13.9989 9.01922 13.8049C9.0578 13.6109 9.15304 13.4327 9.2929 13.2929C9.43275 13.153 9.61093 13.0578 9.80491 13.0192C9.99889 12.9806 10.2 13.0004 10.3827 13.0761C10.5654 13.1518 10.7216 13.28 10.8315 13.4444C10.9414 13.6089 11 13.8022 11 14C11 14.2652 10.8946 14.5196 10.7071 14.7071C10.5196 14.8946 10.2652 15 10 15ZM11 11C11 11.2652 10.8946 11.5196 10.7071 11.7071C10.5196 11.8946 10.2652 12 10 12C9.73479 12 9.48043 11.8946 9.2929 11.7071C9.10536 11.5196 9 11.2652 9 11V6C9 5.73478 9.10536 5.48043 9.2929 5.29289C9.48043 5.10536 9.73479 5 10 5C10.2652 5 10.5196 5.10536 10.7071 5.29289C10.8946 5.48043 11 5.73478 11 6V11Z" fill="#9CA3AF"/>
                          </g>
                          <defs>
                          <clipPath id="clip0_1158_6412">
                          <rect width="20" height="20" fill="white"/>
                          </clipPath>
                          </defs>
                      </svg>

                      <h5
                        class="mt-0 font-[bold] not-italic leading-[1.33] mb-0 tracking-[normal] text-base font-normal text-gray-500 text-center">
                        Are you sure you want to delete this Image? </h5>
                    </div>
                  </div>
                  <div class="block pt-2 pb-1 border-t-[none]">
                    <center class="flex justify-center mt-2">
                      <button @click="handleDelete()" type="button" :disabled="loader"
                        class="w-fit h-9 rounded-xl bg-[#c81e1e] m-0 px-3 text-sm  text-white font-semibold leading-6 inline-flex justify-center items-center gap-2">Yes, I'm sure
                        <Spinner v-if="loader" />
                        </button>
                      <button type="button"
                        class="ml-3 w-fit h-9 rounded-xl sm:rounded bg-white  m-0 px-3 text-sm  border border-gray-800 text-black font-semibold leading-6">No, cancel</button>
                    </center>
                  </div>

                  </div>

            </div>
        </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
</style>
