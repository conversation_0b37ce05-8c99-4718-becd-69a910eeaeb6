<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import SideNavBar from '@/components/common/SideNavBar.vue';
import GoogleAnalytics from '../../../components/GoogleAnalytics/GoogleAnalytics.vue';
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
import { GetSessionGoogleAnalytics, GetEventDetails } from '../../../api/analytics/index';
import { getEventNames, getCustomFields } from '../../../config/eventConfig';
import {getCookie} from '../../../helpers/domhelper';

const route = useRoute();
const projectId = route.params.project_id;

const analyticsData = ref([]);
const eventData = ref({});
const isLoading = ref(true);
const error = ref(null);
const startDate = ref(new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0]);
const endDate = ref(new Date().toISOString().split('T')[0]);

const fetchAllEventData = async () => {
  const eventNames = getEventNames();
  const orgId = 'FcxAht';

  for (const eventName of eventNames) {
    const obj = {
      startDate: startDate.value,
      endDate: endDate.value,
      orgId: orgId,
      projectId: projectId,
      eventName: eventName,
      customFields: getCustomFields(eventName),
    };
    try {
      const eventResponse = await GetEventDetails(
        obj,
      );
      eventData.value[eventName] = eventResponse || [];
      console.log(`Event data fetched for ${eventName}:`, eventData.value[eventName]);
    } catch (err) {
      console.error(`Error fetching event data for ${eventName}:`, err);
      error.value = err.message || `An error occurred while fetching event data for ${eventName}`;
    }
  }
};

const fetchAnalyticsData = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const orgId =  getCookie('organization');

    const sessionResponse = await GetSessionGoogleAnalytics(startDate.value, endDate.value, orgId, projectId);

    if (Array.isArray(sessionResponse)) {
      analyticsData.value = sessionResponse;
      isLoading.value = false;
    } else {
      throw new Error('Invalid data received from the session API');
    }

    await fetchAllEventData();

  } catch (err) {
    console.error('Error fetching Google Analytics data:', err);
    error.value = err.message || 'An error occurred while fetching data';
  } finally {
    isLoading.value = false;
  }
};

const handleApply = () => {
  fetchAnalyticsData();
};

onMounted(() => {
  fetchAnalyticsData();
});
</script>

<template>
  <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col">
    <DatacenterNavBar/>
    <div class="h-screen w-full flex justify-start items-start overflow-hidden bg-[#f3f4f6]">
      <SideNavBar />
      <div class="px-2 pt-2 bg-transparent h-full overflow-y-auto w-full">
        <GoogleAnalytics
          :data="analyticsData"
          :eventData="eventData"
          :isLoading="isLoading"
          :error="error"
          :startDate="startDate"
          :endDate="endDate"
          @update:startDate="startDate = $event"
          @update:endDate="endDate = $event"
          @apply="handleApply"
        />
      </div>
    </div>
  </div>
</template>
