import * as yup from 'yup';
import { fileValidation, fileValidationMedia, imageSizeValidation, isUrl } from '../../helpers/validationSchemaHelpers';
export const amenitiesSchema = yup.object({
  name: yup.string().required(),
  mediaType: yup.string().required(),
  category: yup.object().required(),
  communityId: yup.string().nullable(),
  description: yup.string().nullable(),
  link: yup.string().when('mediaType', {
    is: (val:string) => val === 'embed_link',
    then: () => yup.string().required(),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  tour_id: yup.string().when('mediaType', {
    is: (val:string) => val === 'virtual_tour',
    then: () => yup.string().required(),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  thumbnail: yup.mixed().when('mediaType', {
    is: (val:string) =>  val !== 'image' && val !== '360_image',
    then: () => yup.mixed()
      .test('thumbnail', 'Not a valid image type', (value) => {
        return fileValidation(value as File, 'image');
      })
      .test('thumbnail', 'Image size is more than 2 mb', (value) => {
        if (!isUrl(value as File|Blob|string)) {
          return imageSizeValidation(value  as File);
        }
        return true;
      }),
    otherwise: () => yup.string().nullable(),
  }),
  file: yup.mixed().when('mediaType', {
    is: (val:string) => {
      return val !== 'virtual_tour' && val !== 'embed_link';
    },
    then: (schema) => schema.required().test('file', 'Not a valid image type', (value, context) => {
      const mediaType = context.parent.mediaType;
      return fileValidationMedia(value  as File, mediaType);
    }),
    otherwise: () => yup.string().nullable(),
  }),
});

export const multipleAmenitySchema = yup.object().shape({
  fileItems: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string().required("Name is required"),
        media_type: yup.string().required("Media Type is required"),
        category: yup.string().required("Category is required"),
        community_id: yup.string().nullable(),
        description: yup.string().nullable(),
        embed_link: yup.string().when('media_type', {
          is: (val:string) => val === 'embed_link',
          then: () => yup.string().url("Invalid file URL").required("Embed Link is required"),
          otherwise: () => yup.string().notRequired().nullable(),
        }),
        tour_id: yup.string().when('media_type', {
          is: (val:string) => val === 'virtual_tour',
          then: () => yup.string().required("Virtual Tour is required"),
          otherwise: () => yup.string().notRequired().nullable(),
        }),
        file: yup.mixed().when('media_type', {
          is: (val: string) => val !== 'embed_link' && val !== 'virtual_tour',
          then: () => yup.mixed().required('File is required')
            .test('file', 'File is required', function (value) {
              if (this.parent.isNew && !value) {
                return false;
              }
              return true;
            })
            .test('fileType', 'Not a valid file type', function (value) {
              if (value instanceof File) {
                const validationType = this.parent.media_type === '360_image' ? 'image' : this.parent.media_type;
                return fileValidation(value, validationType);
              }
              return true;
            })
            .test('fileSize', 'File size is too large', function (value) {
              if (value instanceof File) {
                return imageSizeValidation(value);
              }
              return true;
            }),
          otherwise: () => yup.mixed().nullable(),
        }),
        thumbnail: yup.mixed().nullable().test('thumbnail-validation', function (value) {
          const { media_type, isNew } = this.parent;

          // For new items
          if (isNew === true) {
            // Skip validation for image, 360_image, and embed_link
            if (media_type === 'image' || media_type === '360_image') {
              return true;
            }

            // For other types, require thumbnail
            if (!value) {
              return this.createError({
                message: `Please upload a thumbnail`,
              });
            }
          } else {
            // Get all non-URL fields from parent to check if this is truly an existing item
            const { ...meta } = this.parent;
            const hasExistingData = Object.values(meta).some((val) => val);

            // If it's an existing item (has some data) and needs a thumbnail
            if (hasExistingData) {
              if (!value) {
                return this.createError({
                  message: `Please upload a thumbnail`,
                });
              }
            }
          }

          // If there's a value, validate it regardless of type
          if (value instanceof File) {
            if (!fileValidation(value, 'image')) {
              return this.createError({
                message: 'Thumbnail must be a valid image',
              });
            }
            if (!imageSizeValidation(value)) {
              return this.createError({
                message: 'Thumbnail image size exceeds the maximum limit',
              });
            }
          }

          return true;
        }),
      }),
    )
    .strict(),
});

export const AmenitySchema = yup.object({
  name: yup.string().required("Name is required"),
  media_type: yup.string().required("Media Type is required"),
  category: yup.string().required("Category is required"),
  community_id: yup.string().nullable(),
  description: yup.string().nullable(),
  embed_link: yup.string().when('media_type', {
    is: (val:string) => val === 'embed_link',
    then: () => yup.string().url("Invalid file URL").required("Embed Link is required"),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  tour_id: yup.string().when('media_type', {
    is: (val:string) => val === 'virtual_tour',
    then: () => yup.string().required("Virtual Tour is required"),
    otherwise: () => yup.string().notRequired().nullable(),
  }),
  file: yup.mixed().when('media_type', {
    is: (val: string) => val !== 'embed_link' && val !== 'virtual_tour',
    then: () => yup.mixed().test('file-validation', function (value) {
      const { media_type, isNew } = this.parent;

      // For new items - require file
      if (isNew === true) {
        if (!value) {
          return this.createError({
            message: 'File is required',
          });
        }
      } else {
        // For existing items - check if there's data
        const hasExistingData = Object.values(this.parent).some((val) =>
          val !== undefined && val !== null && val !== '',
        );

        // If it's an existing item with data but no file
        if (hasExistingData && !value) {
          return this.createError({
            message: 'File is required',
          });
        }
      }

      // Validate file type if it's a File instance
      if (value instanceof File) {
        const validationType = media_type === '360_image' ? 'image' : media_type;
        if (!fileValidation(value, validationType)) {
          return this.createError({
            message: 'Not a valid file type',
          });
        }

        // Validate file size
        if (!imageSizeValidation(value)) {
          return this.createError({
            message: 'File size is too large',
          });
        }
      }

      return true;
    }),
    otherwise: () => yup.mixed().nullable(),
  }),
  thumbnail: yup.mixed().nullable().test('thumbnail-validation', function (value) {
    const { media_type, isNew } = this.parent;

    // For new items
    if (isNew === true) {
      // Skip validation for image, 360_image, and embed_link
      if (media_type === 'image' || media_type === '360_image') {
        return true;
      }

      // For other types, require thumbnail
      if (!value) {
        return this.createError({
          message: `Please upload a thumbnail`,
        });
      }
    } else {
      // Get all non-URL fields from parent to check if this is truly an existing item
      const { ...meta } = this.parent;
      const hasExistingData = Object.values(meta).some((val) => val);

      // If it's an existing item (has some data) and needs a thumbnail
      if (hasExistingData) {
        if (!value) {
          return this.createError({
            message: `Please upload a thumbnail`,
          });
        }
      }
    }

    // If there's a value, validate it regardless of type
    if (value instanceof File) {
      if (!fileValidation(value, 'image')) {
        return this.createError({
          message: 'Thumbnail must be a valid image',
        });
      }
      if (!imageSizeValidation(value)) {
        return this.createError({
          message: 'Thumbnail image size exceeds the maximum limit',
        });
      }
    }

    return true;
  }),
});

export const editAmenity = yup.object({
  name: yup.string().required(),
  category: yup.object().required(),
  description: yup.string().nullable(),
  communityId: yup.string().nullable(),
  media_type: yup.string(),

  thumbnail: yup.mixed().when('media_type', {
    is: (val:string) =>  val !== '',
    then: () => yup.mixed()
      // Only run the function if the value is NOT a URL
      .test('thumbnail', 'Not a valid image type', (value) => {
        if (!isUrl(value as File|Blob|string)) {
          return fileValidation(value as File, 'image');
        }
        return true;
      })
      .test('thumbnail', 'Image size is more than 2 mb', (value) => {
        if (!isUrl(value as File|Blob|string)) {
          return imageSizeValidation(value  as File);
        }
        return true;
      }),

    otherwise: () => yup.string().nullable(),
  }),

  file: yup.mixed().when('media_type', ([type], schema) =>
    schema
      .test('fileValidation', 'Invalid file', (value) => {
        if (!isUrl(value as File|Blob|string)) {
          console.log("media_type", type);
          return fileValidationMedia(value as File, type); // Use fileValidation for general file validation
        }
        return true;
      }),

  ),
});
