<script setup>
import globeComp from './globeComp.vue';
import { ref, defineEmits, defineExpose } from 'vue';

const props = defineProps({
  scene: {
    type: Object,
    required: true,
  },
});
const globeRef = ref(null);
const emit = defineEmits(['unMount', 'hideLoader', 'showInfoCard', 'globeAnimationEnd']);
const hideImage = ref(true);
const checkAssetloaded = ref({
  image: false,
  texture: false,
});
const gData = ref();

gData.value = props.scene?.sceneData?.coordinates;

function disableImage () {
  if (checkAssetloaded.value.texture && checkAssetloaded.value.image) {
    hideImage.value = false;
    globeRef.value.startInitialAnimation();
  }
}

function emitClickData (id) {
  emit('unMount', id);
  // Router.push({ name: 'masterScene', params: { sceneId: id } })
}

function imageloaded () {
  emit('hideLoader');
  checkAssetloaded.value.image = true;
  disableImage();
}

function startAnimation () {
  globeRef.value.startAnimate();
}

function animationEnd () {

  emit('showInfoCard', 'Image');
}

function globeAnimationEnd () {
  emit('globeAnimationEnd');
}

function textureLoaded () {
  checkAssetloaded.value.texture = true;
  disableImage();
}

defineExpose({
  startAnimation,
});

</script>
<template>
 <div class="relative">

        <div style="height:100%;width:100%;overflow: hidden;position: relative;">
            <div v-if="hideImage" class="absolute top-0 left-0 z-[5] h-full w-full overflow-hidden">
                <img @load="imageloaded" src="/assets/Scene1/initialBg.jpg" class="h-full w-full object-cover" />

            </div>
            <div v-if="hideImage" class="absolute top-1/2 -translate-y-1/2 left-8 z-[5]">
                <div class="w-60 md:w-72 lg:w-96 ">
                    <!-- <img src="/assets/Icons/KAFDLogo.svg" alt="KAFD" class="w-auto min-w-[50%] h-full"/> -->
                    <div class="w-full mt-8 bg-primary overflow-hidden h-1 relative">
                        <div class="bg-button-active absolute top-0 left-0 w-1/2 h-full progressBar">

                        </div>
                    </div>
                </div>
            </div>
            <globeComp ref="globeRef" :Data="gData" :positions="props.scene?.sceneData?.earth_position?.x_axis && props.scene?.sceneData?.earth_position?.y_axis && props.scene?.sceneData?.earth_position?.z_axis ? props.scene?.sceneData?.earth_position : false " @emitClickData="emitClickData"
                @initialAnimationEnd="animationEnd" @selectedAnimationEnd="globeAnimationEnd"
                @allTextureLoaded="textureLoaded" class="h-full w-full overflow-hidden" />
        </div>
    </div>
</template>

<style scoped>  .progressBar {
      animation-duration: 1.5s;
      animation-iteration-count: infinite;
      animation-name: progress-bar;
  }

  @keyframes progress-bar {
      from {
          left: -50%;
      }

      to {
          left: 100%;
      }
  }
</style>
