<script setup>
import { ref, onMounted } from 'vue';
import { getAuth, sendPasswordResetEmail, confirmPasswordReset } from "firebase/auth";
import { useRoute, useRouter } from 'vue-router';

const auth = getAuth();
const email = ref("");
const message = ref({ isShow: false, text: "", isErr: false});
const newPassword = ref("");
const confirmPassword = ref("");
const actionCode = ref("");
const isResetMode = ref(false);
const router = useRouter();
const route = useRoute();

// Extract reset code from URL
onMounted(() => {
  actionCode.value = route.query.oobCode || "";
  if (actionCode.value) {
    isResetMode.value = true; // Switch to new password form
  }
});

// Handle sending reset email
const handleForgotPassword = async () => {
  if (!email.value) {
    message.value = { isShow: true, text: "Please enter your email address.", isErr: true};
    return;
  }

  try {
    await sendPasswordResetEmail(auth, email.value);
    message.value = { isShow: true, text: "Password reset email sent! Check your inbox (and spam folder).", isErr: false };
  } catch (error) {
    message.value = { isShow: true, text: error.message, isErr: true };
  }
};

// Handle new password submission
const handleResetPassword = async () => {
  if (newPassword.value !== confirmPassword.value) {
    message.value = { isShow: true, text: "Passwords do not match.", isErr: true };
    return;
  }

  if (!actionCode.value) {
    message.value = { isShow: true, text: "Invalid reset link.", isErr: true };
    return;
  }

  try {
    await confirmPasswordReset(auth, actionCode.value, newPassword.value);
    message.value = { isShow: true, text: "Password reset successful. Redirecting to login...", isErr: false};
    setTimeout(() => {
      router.push('/login'); // Redirect to login
    }, 3000);
  } catch (error) {
    message.value = { isShow: true, text: error.message, isErr: true };
  }
};
</script>
<template>

    <!-- ThemeSwitcher -->
    <!-- <ThemeSwitcher position="fixed"/> -->

    <div class=" h-screen w-full relative bg-bg-1000 dark:bg-bg-default overflow-y-auto flex md:flex-row flex-col ">

        <div class="h-auto md:w-[60vw] w-full relative flex justify-center items-center  ">
            <div
                class=" w-fit h-[238.27px] md:top-auto top-5 md:left-auto left-5 flex-col justify-center items-center absolute md:m-[72px]">
                <div class="justify-start items-center gap-[10.37px] inline-flex top-5">
                    <div class="w-[135.62px] h-[35px] relative">
                        <img class=" left-0 top-0 absolute" src="../../assets/logo/logo.png" />
                    </div>
                </div>
                <div class="self-stretch h-[126px] flex-col justify-start items-start gap-4 md:flex hidden">
                    <div class="self-stretch h-[62px] text-white text-2xl font-extrabold font-[Inter] leading-[30px] ">
                        <span>Discover the Ultimate Platform for Property<br></span>
                        <span>Experience Creation & Management.</span>
                    </div>
                    <div class="self-stretch text-gray-100 text-base font-normal font-['Inter'] leading-normal">Millions
                        of developers and Agents manages their leads & properties with us - the home to the world’s best
                        developers and Agents professionals.</div>
                    <div class="self-stretch justify-start items-center gap-6 inline-flex">
                        <div class="flex-col justify-start items-start gap-1 inline-flex">
                            <div class="text-white text-sm font-normal font-['Inter'] leading-[21px]">Rated Best By Top
                                Developers</div>
                        </div>
                        <div class="w-[29.27px] h-[0px] rotate-90 border border-gray-200"></div>
                        <div
                            class="w-[287px] h-[27px] pl-[3.87px] pr-[1.92px] pt-[3.87px] pb-[3.78px] justify-center items-center flex overflow-hidden">
                            <div class="justify-start items-start gap-[20.90px] inline-flex">
                                <div class="justify-start items-center gap-[20.90px] flex">
                                    <div class="w-full">
                                        <img src="../../assets/logo/kafd.png" />
                                    </div>
                                    <div class="w-full">
                                        <img src="../../assets/logo/godrej.png" />
                                    </div>
                                    <div class="w-full">
                                        <img src="../../assets/logo/damacLogo.png " />
                                    </div>
                                    <div class="w-full">
                                        <img src="../../assets/logo/rustomjee.png" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <img class="object-cover h-full w-full" src="../../assets/loginFront.jpg" />

        </div>

        <!-- reset password  -->

        <div class="h-screen flex justify-center items-center w-full bg-white">
        <div class="md:w-fit w-[-webkit-fill-available] h-fit md:min-w-[576px] min-w-auto p-6 absolute sm:flex sm:bottom-auto bottom-0 bg-white rounded-lg shadow-md border border-gray-200 flex-col justify-center items-center gap-5 inline-flex">
            <h2 class="text-xl font-bold text-gray-900 mb-1 text-left fonts-sans">
                {{ isResetMode ? "Create New Password" : "Forgot the Password?" }}
            </h2>
            <h2 class="text-base  text-gray-500 text-left mb-4 fonts-sans">
                {{ isResetMode ? "Your new password must be different from previous used passwords." : "We’ll email you instructions to reset your password." }}
            </h2>

            <div v-if="!isResetMode">
                <div class="self-stretch justify-start text-gray-900 text-sm font-medium font-['Inter'] leading-tight mb-2">Email</div>
                <input v-model="email" type="email" placeholder="Enter your email"
                    class="w-full p-2 border rounded mb-4 placeholder:text-left placeholder:font-sans" />
                <button @click="handleForgotPassword"
                    class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
                    Send Reset Email
                </button>
            </div>

            <div v-else>
                <div class="self-stretch justify-start text-gray-900 text-base font-medium font-['Inter'] leading-tight mb-2">New Passsword</div>
                <input v-model="newPassword" type="password" placeholder="New Password"
                    class="w-full p-2 border rounded mb-4 placeholder:text-left placeholder:font-sans" />
                <div class="self-stretch justify-start text-gray-900 text-sm font-medium font-['Inter'] leading-tight mb-2">Confirm Password</div>
                <input v-model="confirmPassword" type="password" placeholder="Confirm Password"
                    class="w-full p-2 border rounded mb-4 placeholder:text-left placeholder:font-sans" />
                <button @click="handleResetPassword"
                    class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
                    Reset Password
                </button>
            </div>
            <div class="min-h-6 flex justify-center items-center mt-3">
                <p v-if="message.isShow" class=" text-center font-sans" :class="[message.isErr ? 'text-red-500' : 'text-green-500' ]">{{ message.text }}</p>
            </div>

        </div>
    </div>
    </div>

</template>
