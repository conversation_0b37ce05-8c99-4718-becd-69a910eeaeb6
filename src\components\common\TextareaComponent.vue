<template>
    <label class="w-full h-[100px]">
      <SfTextarea
       v-model="textareaValue"
       :disabled="disableTextArea === true? true : false"
       ref="textareaRef"
       class="w-full !h-[100px] block"
       />
    </label>

    <div class="flex justify-end">
      <p
        v-if="characterLimit && !readonly"
        :class="[ 'typography-error-xs',
          disabled ? 'text-disabled-500' : isAboveLimit ? 'text-negative-700 font-medium' : ' text-xs text-neutral-500',
        ]"
      >
        {{ charsCount }} / {{ characterLimit }}
      </p>
    </div>

</template>
<script setup>
import { ref, computed, defineProps, watch, defineEmits, nextTick } from 'vue';
import { SfTextarea } from '@storefront-ui/vue';

const props = defineProps({
  disableTextArea: {
    type: Boolean,
    default: false,
  },
  previousData: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['aboutText']);
const characterLimit = ref(250);
const textareaValue = ref(props.previousData);
const disabled = false;
const readonly = false;
const textareaRef = ref();
const isUpdatingFromParent = ref(false);

const isAboveLimit = computed(() => textareaValue.value.length > characterLimit.value);
const charsCount = computed(() => textareaValue.value.length);

// Watch for changes in the previousData prop and update textareaValue
watch(() => props.previousData, (newValue) => {
  if (newValue !== undefined && newValue !== null && newValue !== textareaValue.value) {
    isUpdatingFromParent.value = true;
    textareaValue.value = newValue;
    nextTick(() => {
      isUpdatingFromParent.value = false;
    });
  }
}, { immediate: true });

watch(textareaValue, (newValue) => {
  if (!isUpdatingFromParent.value) {
    const finalTextData = newValue;
    emit('aboutText', finalTextData);
  }
});

watch(charsCount, (newCount) => {
  charsCount.value = newCount;
});

</script>
