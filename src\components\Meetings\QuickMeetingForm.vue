<script setup>
import Modal from '../common/Modal/Modal.vue';

import { computed, onMounted, ref } from 'vue';
import { searchIcon } from '@/helpers/icons';
import { UserStore } from '@/store';
import { GetLeads } from '@/api/leads';
import LoaderComp from '../common/LoaderComp.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { QuickMeetingsValidation } from '@/validationSchema/session';

const userStore = UserStore();
const openLeadsInfoModal = ref(false);
const searchText = ref('');
const leads = ref({});
const uniqueLeads = ref([]);
const emits = defineEmits(['closeModal', 'schedule', 'startSession']);
defineProps({
  loader: Boolean,
});
const leadName = ref('');
const phoneNumber = ref('');
const emailId = ref('');
const leadData = ref({});

function schedule (val) {
  // Submit logic
  console.log("form values", val);
  if (val.leadName && val.emailId){
    const values = {
      name: leadName.value,
      phone_number: phoneNumber.value,
      email: emailId.value,
    };
    console.log("values", values);
    emits('startSession', values);
  }

}

function skip () {
  // Skip logic
  emits('startSession', null);
  console.log('Skipped');
}

onMounted(() => {
  userStore.callbackFunctionMonitorChanges();
  GetLeads(`?user_id[]=${userStore.user_data._id}`).then((leads_data) => {
    leads.value = leads_data;
    console.log("leads", leads.value);
    console.log("leads", Object.values(leads.value).length);
    uniqueLeads.value = Array.from(
      new Map(
        Object.values(leads.value).map((obj) => [`${obj.email}-${obj.name}`, obj]),
      ).values(),
    );
    console.log("unique leads", uniqueLeads);
  });
});
function handleLeadDataToSession (val){
  leadData.value = {
    name: val.name,
    phone_number: '',
    email: val.email,
  };
  console.log("leadData", leadData.value);
  // emits('startSession', values);
}
function toggleSelection (val){
  val.isSelected = !val.isSelected;
  handleLeadDataToSession(val);
}

const filteredLeads = computed(() => {
  return uniqueLeads.value.filter((item) => {
    if (!searchText.value){
      return true;
    }
    return item.name.toLowerCase().includes(searchText.value.toLowerCase());
  });
});
</script>
<template>
  <Modal :open="true" :preventOverflow="true">
    <div :class="userStore.isMobile?'h-full':'relative top-[5%] md:w-[35%]'" class="w-full flex items-center justify-center bg-opacity-30 ">
      <LoaderComp v-if="loader"/>
      <div v-if="!openLeadsInfoModal" :class="userStore.isMobile?'!h-full w-full z-[10]':'px-3 py-3 rounded-lg'"   class="bg-white  w-full ">
        <div v-if="userStore.isMobile" class="w-full flex justify-start items-center h-[10%] px-3 overflow-hidden border-b-2">
          <div>
            <span class="w-8 bg-gray-100 p-2.5 rounded-md flex justify-center" @click="emits('closeModal')">
                <svg width="6" height="14" viewBox="0 0 6 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path id="Back" d="M0 7.0039C0 7.16006 0.0446808 7.29279 0.140426 7.4021L5.24681 13.3438C5.32979 13.4453 5.4383 13.5 5.55958 13.5C5.80851 13.5 6 13.2736 6 12.9613C6 12.8129 5.94894 12.6802 5.87872 12.5865L1.07872 7.0039L3.47872 4.21261L5.87872 1.42132C5.94894 1.31982 6 1.18709 6 1.03874C6 0.734234 5.80851 0.5 5.55958 0.5C5.4383 0.5 5.32979 0.554655 5.24681 0.656156L0.140426 6.60571C0.0446808 6.71501 0 6.84775 0 7.0039Z" fill="black"/>
                </svg>
            </span>
          </div>
        </div>
        <div :class="userStore.isMobile?'h-[90%] relative py-3 px-3 overflow-hidden bg-gray-50':''">
        <!-- Header -->
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-gray-900 text-lg md:text-xl font-bold">Enter lead details to send email invite</h2>
          <button v-show="!userStore.isMobile" @click="emits('closeModal')" class="text-xl">✕</button>
        </div>

        <!-- Form Fields -->
      <Form :validation-schema="QuickMeetingsValidation"  @submit="schedule" :class="userStore.isMobile ? '':'m-0'">
        <div class="space-y-5">

          <!-- Lead Name -->
          <div class="relative flex items-center justify-between mb-2">
            <div class="w-full">
              <label class="text-gray-900 text-sm font-medium mb-1 block">Lead Name</label>
              <Field type="text" v-model="leadName" name="leadName" class="input-primary w-[90%] !bg-gray-50" placeholder="Enter Lead Name" />
              <ErrorMessage as="p" class="absolute bottom-[-20px] text-sm text-rose-500 mt-1 capitalize" name="leadName" />
                <!-- <input
                type="text"
                v-model="leadName"
                placeholder="Enter Lead Name"
                class="w-[90%] border rounded-md px-3 py-2 pr-10 bg-gray-50 placeholder:text-left text-left"
                /> -->
            </div>
            <span class="self-end w-10 h-10 flex items-center justify-center bg-gray-100 rounded-lg cursor-pointer" @click="()=>{openLeadsInfoModal=true}">
              <!-- User Icon SVG -->
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 7.57895C10.2941 7.57895 12.1538 5.88234 12.1538 3.78947C12.1538 1.69661 10.2941 0 8 0C5.70589 0 3.84615 1.69661 3.84615 3.78947C3.84615 5.88234 5.70589 7.57895 8 7.57895Z" fill="#111928"/>
                <path d="M9.38462 8.42105H6.61538C5.39176 8.42239 4.21867 8.86643 3.35343 9.65576C2.4882 10.4451 2.00147 11.5153 2 12.6316V15.1579C2 15.3812 2.09725 15.5954 2.27036 15.7534C2.44347 15.9113 2.67826 16 2.92308 16H13.0769C13.3217 16 13.5565 15.9113 13.7296 15.7534C13.9027 15.5954 14 15.3812 14 15.1579V12.6316C13.9985 11.5153 13.5118 10.4451 12.6466 9.65576C11.7813 8.86643 10.6082 8.42239 9.38462 8.42105Z" fill="#111928"/>
              </svg>
            </span>
          </div>

          <!-- Phone Number -->
          <div>
            <label class="text-gray-900 text-sm font-medium mb-1 block">Phone Number</label>
            <Field type="text" v-model="phoneNumber" name="phoneNumber" class="input-primary w-full !bg-gray-50" placeholder="Enter Phone Number" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="phoneNumber" />
            <!-- <input
              type="text"
              v-model="phoneNumber"
              placeholder="Enter Phone Number"
              class="w-full border rounded-md px-3 py-2 bg-gray-50 placeholder:text-left text-left"
            /> -->
          </div>

          <!-- Email IDs -->
          <div class="relative ">
            <label class="text-gray-900 text-sm font-medium mb-1 block">Email ID</label>
            <Field type="email" v-model="emailId" name="emailId" class="input-primary w-full !bg-gray-50" placeholder="Enter Email ID" />
            <ErrorMessage as="p" class="absolute bottom-[-20px] text-sm text-rose-500 mt-1 capitalize" name="emailId" />
            <!-- <div class="mb-2">
              <input
                type="email"
                v-model="emailId"
                placeholder="Enter Email ID"
                class="w-full border rounded-md px-3 py-2 bg-gray-50 placeholder:text-left text-left"
              />
            </div> -->
          </div>
        </div>

        <!-- Buttons -->
        <div :class="userStore.isMobile?'absolute bottom-4 left-0 right-0 w-[90%] mx-auto':''" class="mt-6 flex flex-col gap-6 ">
          <button
            type="submit"
            class="bg-blue-600 text-white py-2 rounded-md font-medium hover:bg-blue-700 flex justify-center items-center gap-2"
          >
            <!-- Plane Icon -->
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
              <path d="M13.8554 13.5158L8.1517 0.915994C8.09572 0.792185 8.00436 0.686982 7.88869 0.613142C7.77303 0.539303 7.63802 0.5 7.50006 0.5C7.36209 0.5 7.22708 0.539303 7.11142 0.613142C6.99575 0.686982 6.90439 0.792185 6.84841 0.915994L1.14475 13.5158C1.0905 13.6357 1.07166 13.7682 1.09037 13.8981C1.10909 14.028 1.16461 14.1501 1.25061 14.2505C1.33661 14.3509 1.44962 14.4256 1.57675 14.466C1.70389 14.5064 1.84003 14.5109 1.96964 14.479L6.7871 13.2967V6.80011C6.7871 6.61446 6.86221 6.43641 6.99592 6.30514C7.12962 6.17387 7.31097 6.10012 7.50006 6.10012C7.68914 6.10012 7.87049 6.17387 8.00419 6.30514C8.1379 6.43641 8.21301 6.61446 8.21301 6.80011V13.2967L13.0312 14.479C13.1607 14.5107 13.2968 14.5061 13.4238 14.4656C13.5508 14.4251 13.6637 14.3505 13.7496 14.2501C13.8355 14.1497 13.891 14.0276 13.9097 13.8978C13.9284 13.768 13.9095 13.6356 13.8554 13.5158Z" fill="white"/>
            </svg>
            Send Invite & Join
            <!-- <Spinner v-if="loader"/> -->
          </button>

          <button @click="(e)=>{e.preventDefault();skip()}" class="text-blue-600 text-sm font-medium hover:underline">
            Skip
          </button>
        </div>
      </Form>
      </div>
      </div>
      <div v-if="openLeadsInfoModal" :class="userStore.isMobile?'h-full !w-full z-[10]':'h-[80vh] px-1 py-3 rounded-lg'" class=" bg-white  w-full ">
        <div v-if="userStore.isMobile" class="w-full flex justify-start items-center h-[9%] px-3 overflow-hidden border-b-2">
          <div>
            <span class="w-8 bg-gray-100 p-2.5 rounded-md flex justify-center" @click="()=>{openLeadsInfoModal = false;searchText = ''}">
                <svg width="6" height="14" viewBox="0 0 6 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path id="Back" d="M0 7.0039C0 7.16006 0.0446808 7.29279 0.140426 7.4021L5.24681 13.3438C5.32979 13.4453 5.4383 13.5 5.55958 13.5C5.80851 13.5 6 13.2736 6 12.9613C6 12.8129 5.94894 12.6802 5.87872 12.5865L1.07872 7.0039L3.47872 4.21261L5.87872 1.42132C5.94894 1.31982 6 1.18709 6 1.03874C6 0.734234 5.80851 0.5 5.55958 0.5C5.4383 0.5 5.32979 0.554655 5.24681 0.656156L0.140426 6.60571C0.0446808 6.71501 0 6.84775 0 7.0039Z" fill="black"/>
                </svg>
            </span>
          </div>

        </div>
        <div :class="userStore.isMobile ?'h-[91%] py-3 px-1 flex flex-col gap-3 relative bg-gray-50':'h-full py-1 flex flex-col gap-4 relative '">
          <div :class="userStore.isMobile?'flex flex-col gap-3 px-2.5':'px-3 flex flex-col gap-3'">
            <div  class="flex justify-between items-center">
              <h2 class="text-gray-900 text-lg md:text-xl font-bold">Select from Existing Lead</h2>
              <button v-if="!userStore.isMobile"  @click="()=>{openLeadsInfoModal=false;searchText=''}" class="text-xl">✕</button>
            </div>
            <div :class="userStore.isMobile?'w-full bg-gray-50':'w-[75%] bg-gray-100'"  class="flex   h-[37px] px-3 items-center border  rounded-lg">
                  <span v-html="searchIcon" class="w-[8%]"></span>
                  <input type="search"
                          v-model="searchText"
                          class="w-[86%] bg-transparent text-gray-500 text-sm font-normal placeholder:text-left"
                         :placeholder="'Search Lead'"/>
            </div>
          </div>
        <div :class="userStore.isMobile?'h-[62vh] w-full px-1':'max-h-[62%] min-h-[62%] h-[62%] '" class=" ">
          <p :class="userStore.isMobile ?'pl-2.5':'pl-3'" class=" text-gray-900 text-sm font-medium ">Leads</p>
          <div :class="userStore.isMobile ?'px-1':''" class="max-h-[95%] h-[95%] scroll overflow-y-auto flex flex-col cursor-pointer" >
            <!-- <LoaderComp v-if="!uniqueLeads" class="z-[9999]"/> -->
           <span
            v-if="uniqueLeads.length === 0"
            :class="[userStore.isMobile ? 'pl-2' : 'px-3', leads.isSelected && '!bg-blue-50']"
            class=" py-2 h-full relative rounded-md  flex justify-center items-center cursor-pointer">No! Leads found for this organization
          </span>
            <span v-for="(leads,leadsId) in filteredLeads" :key="leadsId"
            :class="[userStore.isMobile ? 'pl-2' : 'px-3 hover:bg-gray-100', leads.isSelected && '!bg-blue-50']"
            class="border-b-2 py-2  relative rounded-md  flex justify-between cursor-pointer"
            @click="toggleSelection(leads)">
              <span :class="userStore.isMobile ? 'w-[90%]':''">
                <p :class="userStore.isMobile?'text-sm':'text-base'" class="text-gray-900  font-semibold">{{ leads.email }}</p>
                <p class="text-gray-500 text-xs font-normal capitalize">{{ leads.name }}</p>
              </span>
              <span class="" :class="userStore.isMobile ? 'w-[10%] flex justify-center items-center':'p-2'">
                <input type="checkbox" @change='handleLeadDataToSession(leads)' v-model="leads.isSelected" class="cursor-pointer">
              </span>
            </span>
          </div>
        </div>
        <div :class="userStore.isMobile?'w-[92%] mx-auto':'w-full mx-auto  px-3'" class="flex flex-col gap-2 ">
          <button class="w-full p-2.5 !bg-blue-600 active:!bg-[#1E429F] rounded-md text-white text-sm font-medium flex justify-center relative" @click="()=>{if(Object.values(leadData).length > 0){emits('startSession',leadData)}}">Next</button>
          <button v-if="!userStore.isMobile" class="w-full p-2.5 border !outline-gray-200 !bg-white active:!bg-gray-100 rounded-md text-black text-sm font-medium" @click="()=>{openLeadsInfoModal=false;searchText=''}">Back</button>
          </div>
        </div>

      </div>
    </div>
  </Modal>
</template>

<style>
.scroller::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
.scroll::-webkit-scrollbar {
    width: 4px !important;
    height: 10px !important;
}
@media (max-width:425px) {
  .scroll::-webkit-scrollbar {
    width: 3px !important;
    border-radius: 10px !important;
    height: 10px !important;
}
  .scroller::-webkit-scrollbar {
    display: none !important;
    width: 0px !important;
    height: 10px !important;
}
}
</style>
