<script setup>
import { ref, computed, defineEmits, onMounted} from 'vue';
import ParticipantImageTag from './ParticipantImageTag.vue';

const emit = defineEmits(['emitparticipant']);

const showParticipantList = ref(false);
const participantData = ref('');
const selectedParticipant = ref([]);
const list = ref();

const participantList = () => {
  showParticipantList.value=!showParticipantList.value;
};

const data = ref([
  {
    'name': 'Devon Miles',
    'url': 'data:image/jpeg;base64,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',
  },
  {
    'name': 'Peter Darson',
    'url': 'data:image/jpeg;base64,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',

  },
  {
    'name': 'Williams',
    'url': 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAoHCBUWFRgWFhYYGBYYFRgYGBUYGBgYGBgYGBgZGhgYGBgcIS4lHB4rIRgYJjgmKy8xNTU1GiQ7QDszPy40NTEBDAwMEA8QHBISHzQkJCs0NDQ0NDQ0NDQ0NDQ0MTQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0MTE0Mf/AABEIARMAtwMBIgACEQEDEQH/xAAbAAACAgMBAAAAAAAAAAAAAAAEBQMGAAECB//EAD8QAAEDAwIDBAcFBwQCAwAAAAEAAhEDBCESMQVBUWFxgZEGExQiobHRMkJSwfAzcoKSouHxFSNTYkPSY5PC/8QAGQEAAwEBAQAAAAAAAAAAAAAAAAECAwQF/8QAIREAAwEBAAICAwEBAAAAAAAAAAERAiESMQNREyJBMmH/2gAMAwEAAhEDEQA/AHVmzVlauCGlRsIYMFR1HYkrz20YnFS+ys9vMbIP1rZWnvJ2WmccpSR2eIPndQVbsk5RVGzxJS2/cGuhCnoB7ZVBG6lfWEpJYVCUTVkJJf8AAg7t6rTsjW0gRKrdpXyndvcGIV52vTBMgurczhY0gBE1Hyl9dpnCjXXUJhDXAqcVGgJe0wo6jpSrYDujcNPNTl7OxVmnVg7qd9XoVsnwY0uWdEKHkYKjtrrkTlHOphyz1x1CAa1z03ChFYu3XdxZ8womM0qbQNm0aVttADkpPWiFKx4mE0kgOrekOWFilDBGFizelRUrVtc6jk7Ii6dqEBKLOmSSUa65DNynrmuDMp0I3UxbOyU3HFM42TGwr6gremkHSS4uyxqr1e6L3+Ksd9RBCrZoAP7EZX2ND2xdAU9WqClfrYGFlqS4pqiGVKoBlMba6lKX0oCYWTxEBJLoDJlUc1lUyFE6lKxxjCTbQEDXZypHU2kLT2goSpI6qs8Qzs2snCl9jdCita2UzFURCEoIVUaRa9PqAEIek0HvU5bGyucGR1wlVy48kxrzugw8EkQskukiipXeOSZcOcX7rTraSj7W30laPLnCiQsLVimrPELFgSVO1pnSkvEnkuIT51TSzwVdu6gLie1bZStKQOxhKdWR0hAWwCJceSrTQyW94lGEo9skoivaEpe+gWppJgMGVZTHh0pHSf2p3w6oo2ohMdtoyFulRgrplSAonXIBwpWlBJjFtQgIdziSt0aoKma4c1KAjYSNx4rdRkhY+q2N1yy4GytAKbh5Y5TM4l1WuJEOSRzHSrnSoWajfIylXJ5qsUCQjadyQo02SywPqghCmgSZCioPLkfQdBylnjEaZTI3RDHgLpxadihnuAWutcg6SVIIWKFjp5rFh4MRU7mvLVXLt+U3qv8AdSeuJK1zmFpBdjVEIxlQEpXbsPJMbWieiGo6EGLCISq/7Ez0xug61KSjPQQnARlrcEKZ1oeiLtrEKtSdAlo3TjhEUwScqS3pBp5cxO+6LaWNaScwJwP1KjOL6Gs0nsGdQSNJg7Z5BT3JYScxO3QAY8Sq+L17p5AyQJADW8p7ShmVhPvVh3NO3iDPxW2fjzCvD7HlUNGzvh9FwyDs4Y5JHfNxqDy1vIvdUDSeXvklqT1b97D70Hoctn914+0jXxr+B4ouFenKgFBKLL0gBhr5E7Ex8xgp6x4Kya1n2S08nLKZRFCl1CIt4KMFALOtshumqDIC7e9ROdAQjrr3k2hDNpjdRVnE7Ln1w0oRlb3t4Cp6UKJaTi05WKR9RsLFHkyaUyqOSBenF1RO8JU+nJWycNEgm1YITWmBpSZji1SC7cofRvMGVZ4jtQ1I5KhbVJ3XQKWXBQke+DK7ZXKgcwuXVGifBW5/RBD7omBnt6+CY2IBEvjOzRnHaqxUupfH3QZI/FHXsTDhtd1R+OsR+S0TiN85GnFKDT91jwNhBBgY3BHYljbam06gxzDzgy3xCutPh7dADhPegrnhwAOnboc/HfzlHkX4oqz6rWj3XY7MfDbzHikF7VLZA06Sdo9w9mn7p+HcrNxDhwjLDj7zeSqV7buYTmW/LvCpapOsQFe4DLcDm08u/r3p/wAA4jI0HcZHcqxUluRt+sIrhVSKjDP3gJ7CjSqM2eiWNTKeUqmFWLZ58UwZVdC5dezJobPYHJXc0YOF2y6hD3d2jyvADKLsJddnTldULpdXFVrgnldjBcYEbw7LFlSmOWFpX4IZq5qckHoCIfauiUvqVSDCWqys+yZ7QomUpK16xbY+EvReuhTmABDipJhY+rKHAQicpjS3I2U15UDWGBmIS6nUhFV63+0YGfr17El3SFnP7FaBPvnsx5x9VdfQe0kB5HaFTa45Zk9nyXottbCjbtDsQwahtyyCtmdGUWN9xSbgvZPTUEHXLDsV5vf8XoOcfcJGffEzjdOOEXfuiHOLTgAzI7M7JMpLspYq9Np3Vb43YMIOWjvICO4hXdEAx2qrVLuhqOuXmczJSXSnwQ31DQY5dQhLZ0O7irFxWxpup66WBuRyP91WWmHBa5dRz6zGeo8FpBzA4jkJTKrTACX8Bf7jCNiwfJG3Mri0n5Mw0ugbxla9n1KanRJUwbpKOiAjaRsgrkuaU6fUEJPfVN1WdNMdB6lyeixR0iXDZYtvIdLcLUFvgqXxSlpeVd7WrqaEn4pwgvfKvS5wtcKsFIE3/wBFKk/0qBss/FjbErlym/sBnZEs4X2ISoZYiYwnkjq1QMaAQckDpnkSm9PhwHmtXPDA9pDhuP0U1lplJqlTaZqsJwC9p/qXrHsTKjIe0OBGQVQf9He5mvQ8aBGvSQwu5Q6IPaOqvVpde40zgtB8wi96dEU/Uq/FfRlhdimzSCdIyInfA7gieH8G0AkjJdqO+/irA67bBUVDiNJg1VHDU50NZgfPc9ifAV+hPxnh8vIGJbhVxnAQHzoIdET4aZ74V14hxCk9zfeDScCYEnoBzWrau0jMSknPQNc6irt4GGUy3OeS86uaWl5b0cR5Ehey3bwvJeLN/wB955F7iD1zmPFPD6zP5VxF59F6RFsx0ncz3TCsr6OJS/0Tp66AxjW4+cOAH8ysnqBClqsw0hO2nBWVmCEdVpoZ7VnrPTOCeuxLbu2wU7qDKGrMkYSXsEV6g8tW0Y/gz5kTCxa+DGHejF7raMq406AcF5daa7aqWu+zK9A4XxQOaMrdFMYOswoKtqEWLoFcuqAoaAWG0Ero0gEU94UD3JQCMMCnpMEjHMfNCuei7coGNvaMS4AwBjkCQchKuIWgGWOAkAlhGkAkT7h7eiLuKJiB2D+lKuN0qr6T20yBUABbPVpBI78EDthYs6suOiv2ncuMBv2geUbyorm6tngOL2Z6GfklfD78PqOaZnAe1wIcHgQ4OBzOAmlXh7HZaAOsAfIhL0acb6BOfRJ/aCeRyPidkxp3TDAY4HSAMEFBt4UOY/paFuo1lNuABCB6SvDu8uHEhjGl73fZaPm48h2ojhfozSpsJeNdTZxdkCcnSDyylnBOIONamGtLjUl73A5axu2Oglu3Uq5XLck9gTXEZ6fQqwsWU2BrG6W7x3qWphEvEAdw+SDrOSjORsDrlBOej3iVA+ghogVVlHb05KdtsARlQMsIchZvQHFtZgtGOSxMLRnujuWLpQFZ9IOBNeZjKqdawrUD7slvReuXFuCkvELIQcI1kaZ59T48W4dIKPo8Z1c1HxjhIkwEFZcOIKjpRYqFcuRDphD2tLYASeQG6sFrwCo8S+GDty7+X6pgIoTjhVg9+whoElxGPDqU9tOD0aeY1u/E/PkNkYK0hwH4cIRUK5xG3qMBLHB/PaCCPmkY4hn3vtcwVYrq6AVa4xXY7Ozh94b+PVY651HRjvGT1KdKqPeaNXJ4gOHcUuqvNJ2l/g7k4fkexQ2LKjj7uR1RfFqB9UZyYU2lTxZA6+Z+IeBk+SnpcGZVGuuToIwwEjHV5GfAf4S+jXDwRqPMk/EpxxL1jGkjLQJMcgOxOwH3iDaTLeiNNJjWCA2WjMDYT2Im2pVKn2QdHN5GB071R28Re5+TA6f3Xo3oxfTufdDcztGN1Wf2fTPf6rgXUwO4QgaxTo06dTYlp7NvJB3PCHx7hDvgfIqmjn1liQvypab0LeUnMMOBae0QobeoZWWnDNj5pEId9UBy5Y+QgbgGZlL8kGWGndNhbVZ9oLeaxbfkAvlV4Sq7dKLJJW220roYFfrWOrkhHcOMgNGSYHeVbvZkM6kGuDuihqIa9wm4VwtlFs/aeRl/Tsb0CJr1SomVZUjhKzbpukkL61w4Lq0upcAeePPZS1GBRUaA1t6ah81KfSn6EXGbJ41AHYlU68pPBOqV6Xfhr5Y5wDp908xO0dVVm2rzWZTe3d0nmC0ZJB5iAUbzGV8e6grhdoWU2A4OkE97sx4TCX+kT9NNx/W6tF42FUfSs/7cdXBQ+AusD9FXghzeh+aecWLfVOlzW4OSQAMLzKm2r6xwAdnDS1skt6AnbtzzTd/DLlzB+0IbmMT1j7eVazmdYta15cQPbglw6r0D0bbppPO+A3xP+VRfRi1qmsKT3vDSNb2FuAGkkO1ZjPu8o18+fptFrPVOa2AGaTp2Ik4kJrM6mS9+XGgm2JTKlUclts6EyplJMbN3lNlRml4kfEdoPJUe7oGlULDkbtPUHZXS5fAVV437zmnmJHyU7Mt5TVMpVMLio6VHSaYUVVc79mf8OarZ2WLumcLFukoIu9F4lFteEqY8hcuuiF1+Q4ODUCEuRLTG8YSt/EoUbOLDqk2gVCKFwjadaUmbUBcS04n5pnbkLD04dCdVOL55AJG4QPDOJh72j/sPmmtenIVWoWrmXZP3C0uH7+xHxnzSj8kWp4uj2s/3oMajs0D7vVdWDMknIbsYzndDvh5Ja73mkZ85HdumLBDO/Pnt8IWunwxyugV69VL0lpue0NY0uMyQ0E46471ZrpyUOrOa8kNLvdgwQCM9vcsZWbJzpX+BWj21JexwEQC5pA32z4q6MYI2St1bUJxuCADOO1NqJwnIxPV6C1rVoOsCDESN43+YGFMakU3nnpaSYiYI3AU7ghXOLg8OxILT8sK16aM37TBeG3hqVCBs0Ce8qz03QFVvRK0c1mp/2nOJPnA+ACszjAUZURpqN8ILuthIardTpRl/UOyHoFHtme/UO20cIe6oI/UFBcmQh4RixW1ulYo69SFpTEItDrsAJdccRbsmV3atISKnwA1azWydEy/u6eKv8lfiOjC14dUrN1NIa07SJkdVt/opVP8A5AP4f7q521ANaGgQAIUy28UEKRYei1Wk4vNTWNJlumJ5jnumtq1WJJH09LnN6H4bhRrKXUa4+iR2yVXNMB4cRt+abNaoLmmCFFNCCjQ2jOYJO+neT1PJS3dRbtWaWydzt3IG+qp60JZgHcP3SW4oueSW8jk6i0RG2N0xrvwubRmpvUyTpJgZMZ8lOVdFacyA024EYEbYgeSd2rpCWPohh0jSJMlrWnn8kZaPgR4J6UFl0OCHurYlweCdtMct91KHoiiUkwaJbOhpaApq+ylpqO4GEwArKyFSrB+yGkn5D5/BFX3AwBLMEcuRRXAqf23dob5ZPzCbkLTOVDLfWeeV3lpg4I5KF9WQrfxngzaoluHjY/kVRLmm9jyx4II5fmOxQ01rvoyI64WKK4fyWKQHVtePO+Srnwi10Mkxqdk/RVk2gCKZcvAgPMeCM6ynR8LbKwqs0a7zu9y1WuSPvu81ovlQ6WaAlvEWw9p6iPEf5Sdtw/8AG7zW31DiST3lNvyyVlxjZmy4Ik9g3+i5pPkKCrXDRE96zNjq7rwEiuasqa5uZS+vUwo0ykiC5emVtb+6GxAj3u2RkJTSGp7W9T8OasVJroxAHbyV/EvbJ+V+kKXSyoWaXFh23dGJxPLcQOxdMGkkZ6ienJOHsIESll63Zw5YKveeEYfSVrlPTfCWteiRVwsUzVob0a6kqvkJK25hEuuxp3TooOeFXTGsy4AlziRPh+SLdxOkN3t8wqfVcYUV5QL2QNyFqnyfSOZ66y4HjVD/AJGfzBIPSW8tnsLg9peB7paRM9O5VtnBKhjOF0/0fd+IqPyNr0T5UK4Nw4PbrcJJWJpw4eraGzyWKlIKhbqUhRst3TlE0XkHKIfVClfG/ZYFUtXAYS15dqyrA2qCEO+k0lU/j+iQOkFI9ilLAOa2+FectIoBq3LmDsS6rfEp/bUGvD2HmJB6Ec1W7yxcx5acR+pWXyZa6jo+PS0ozn1sqKq7K7bACFfWkrJmsGfCKBJL+mB+f5eaeMbz+aHsaGljW8wJPeclGtaunGYocu9XVNPOEDUoggjqjyFBk8lbRKYhIIkHcYXdKURxKnpdPUfEfoJcy4grl0o4dWX5Zp3XeQVJZ0XvM/dCjaw1HBrRkn/J7k5o3DGAMEYx3nmVWM11kfJqKIi9mLsKe2pZ09FgugMrn2jRLzsujxRyjOnRA5Lb6YKQH0mYTAM9yxvEajzIaYWekpBtIdOtR1WJLUvao5FaUeKJiNu4qCYRTLrCVtptmVLrA5rcsYe1KGteHqpeEVWB5c4F2kSAIOepk/qUzuuONa5oY2JBkubtkBsaT3ogCGjcPc9oAJE7gEpreUKmkaWOJ7GlFHi7+weCz/Uan4vg36JgR2FvUDwSxwGkySIUvFLIPb0cNnQT4GOSj9uqH7x+CjfcvM++fNDVXRJx1FL4hrY4t0PJBiWscQe4wu+CcOqPqNL2PawHU4uaQMcs9VanPcYJJ75K09x6nbqs18Waa/m1IG+pO8Lr1Z5wEvE9en5rbmrUyoxNLEFw81wabebwhGsAXDyIKQUlv7Jj2FoeAeRPI8jCrdT0eeDm5YO5jvqn7CJ3+K5e1vUKXlP2ilvS4mc8G4e2iCXVC9x3dpjHQdiLbb2oOWOJ33I//SHYR1H6K254z54VJQluusMLrfb1JI7SfqpafqnyxtAOhslpcIjx70uHXJ/XYh6cmqftCGHPvNgktjPg5MYVX9H2esD20m026QNDdMagTJgY5jyRwtWtGyhtK4YHlzjgDLiT16qtcZ9LRqLKfvHmRsPFZbiJYy4xdsYMRMrFRrq7e4y5yxZVigzdxQQhavETyKeDgtNvIKC+4dT0YwVrTQ49GL4vquaf+MnycwfmrIKWt4OvQBzwRPSP1sqf6KUHMuXTt6t4+LT+StFzUFMAwXS6cR05q8vgmujgW1PnV8h/Zb9TR/5HeR/9UlbxIfgd5hdOv/8Aodp3/sq8kKDf1dD8b/I/RY5lD8T/AIpIeI4nR2b/ANl26+d+D4peQQa+roDk8+f1WaaP4XeZ/wDZJ23z/wAIAmNypRcPzhvx+qPIINB6r8B/X8S2PVfg84SVt1U6N8j9Vt11UicduEeQQdl9IZ9WP6fotNuKZGKI8Y+iRvun8iJ/dWGtUjDoPcPojyCDz2lvKm34fRZ7X/0b+vBIadWofvHnmByPcpCXjdzt/wBbI8gg69sdya1b9ud0Z5H6pK5zo+07zQ72PJJ1u2jc/VHkELGb18bN8v7oSvcEkzyHIRvv37JMaTvxvJ/ed8lDwe2LLgy5zppn7RJ+8MwcI8hwOv7U1WhuogT70HfoMeKAPBqbBgBO3O5BBXVTkMlZ7SbJhUuJWkmBHgtKTi/FBTMEZWLKBGHUOJ69ioK9UlR+jts37R5rr0heKcEbTlGXwcJ/R58V2jqHj+kn8lYeK1Gta1zsAOzgnkeQVV9HawdXYe13xaVb7n7IJwNX5FbZ/wAjfsWuv6ODqP8AI/n/AAroX9PlrOPwP+iZU4IkfL6qVrU4Am9tZyZVMf8AxP8AzGVweLsn9lX/APqcn+ns5qMgdAiAKG3QIkUquerAPmVI27d/wv8AHT9UcXEjA/UrulSOZnxRBiz2p/Kg/wDmYF0yvWP/AIY/jb9E00FdNA6pwVE3rq/KgP5x9F0X3B2YwbbvP5BNdlqZRAotZ67ozzd9FIDV56PIpiGLNCIFAg2oc+55Fds1c2sPgUXC00JwKQS/ozu0/wB1lN8vA0MB0n3mtg4IxPTKIhQM/aD913zCAA+M3JZoj7xI8o+q6tme4XHeEN6QgzTPQvJ8mrhnEPcgdFlpzTEihekhLq7vgsXfEHTXJPasUUqjbhbyGjPJb45lhnOFixSvRP8ASL0W/bU/3ld6mx/fb8wsWLfHobCaOw7lKN1ixWB01QuWLECRlPbxCkbv4BYsSGcOUB+ixYmBDaPJLhy6Ixm/gsWIAl5LAsWIQGlsLSxMDb9kLR/afwH5raxAAPpD9gePyVco1DoOeS2sWG/9ElVuDLz3laWLFAH/2Q==',
  },
  {
    'name': 'John Das',
    'url': 'data:image/jpeg;base64,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',
  },
  {
    'name': 'Paul Walker',
    'url': 'data:image/jpeg;base64,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',
  },
]);

const filteredNames = computed(() => {
  const input = participantData.value.trim().toLowerCase();

  if (!input) {
    return data.value;
  }

  return data.value.filter((item) => item.name.toLowerCase().includes(input));
});

const emitParticipant = (item) => {
  if (!selectedParticipant.value.some((p) => p.name === item.name)) {
    selectedParticipant.value.push(item);
  }
  participantData.value = '';
  showParticipantList.value = false;
  emit('emitparticipant', item); // Emit the selected participant
};

const handleDelete = (name) => {
  const index = selectedParticipant.value.findIndex((participant) => participant.name === name);
  if (index !== -1) {
    selectedParticipant.value.splice(index, 1);
  }
};

function outsideClickHandler (event) {
  const div = list.value;
  if (div && !div.contains(event.target)) {
    showParticipantList.value = false;
  }
}
onMounted(() => {
  document.addEventListener('click', outsideClickHandler);
});

</script>

<template>
    <div class="w-full h-[40px] relative ">
        <div ref="list" class="field relative " @click="participantList()">
            <input type="text" placeholder=""
                    v-model="participantData"
                    class=" block w-full rounded h-10 transition-all duration-[0.3s] ease-in-out px-3 py-0 border-[1px] border-bg-700 focus:border-bg-default"
                    autocomplete="off" required
                    v-on:change="participantList()"/>
                    <label for="email"
                    class="bg-white absolute cursor-text z-10 text-sm font-normal text-bg-550 transition-all duration-[0.3s] ease-in-out px-2.5 py-0 left-2.5 top-2" >Enter Participant email Id</label>
        </div>

        <div v-if="showParticipantList" class="w-full bg-bg-950 flex flex-col p-[16px] rounded-2xl z-8 absolute top-[3rem]">
                <div v-for="(item, index) in filteredNames" :key="index"
                     class="flex mb-2 "
                     @click="emitParticipant(item)">
                    <div class="flex gap-[7px] justify-center items-center ">
                        <div class="w-[32px] h-[32px] rounded-full ">
                            <img class=" object-fill rounded-full w-[100%] h-[100%]" :src="item.url"/>
                        </div>
                        <div class=" flex justify-center items-center">
                            <p class="text-txt-100 text-sm font-medium non-italic">{{ item.name }} </p>
                        </div>
                    </div>
                </div>
        </div>

        <div v-if="selectedParticipant.length > 0"  class="w-full min-h-[94px] max-h-[200px] overflow-y-scroll no-scrollbar border border-bg-900 rounded-lg p-[16px] mt-[20px] flex flex-col gap-[10px]">
               <div class="text-xs font-normal text-txt-400">Added participant</div>
               <div v-for="(participant, index) in selectedParticipant" :key="index">
                    <ParticipantImageTag :data="participant" @delete="handleDelete(participant.name)" />
               </div>
        </div>
    </div>
</template>

<style scoped>

.field input:focus+label,
.field select:focus+label,
.field input:valid+label,
.field select:valid+label {
  @apply text-xs -top-2;
}

.field input:focus+label,
.field select:focus+label {
  @apply text-bg-50;
}
.no-scrollbar::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
}

.no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
}
</style>
