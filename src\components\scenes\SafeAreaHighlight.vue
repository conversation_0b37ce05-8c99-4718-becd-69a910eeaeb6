<script setup>

// Props
defineProps({
  safePadding: {
    type: Object,
    default: () => ({
      top: "10px",
      bottom: "10px",
      left: "10px",
      right: "10px",
    }),
  },
  show: {
    type: <PERSON>olean,
  },
});
</script>
<template>
    <div class="w-full h-full box-border z-50">
      <!-- Highlight areas -->
      <div v-if="show"
        class="absolute top-0 left-0 right-0 bg-[#2AA3154D] pointer-events-none z-50"
        :style="{ height: safePadding.top ,left:safePadding.left, right:safePadding.right}"
      ></div>
      <div v-if="show"
        class="absolute bottom-0 left-0 right-0 bg-[#2AA3154D] pointer-events-none z-50"
        :class="`right-[${safePadding.right}] left-[${safePadding.left}]`"
        :style="{ height: safePadding.bottom , left:safePadding.left, right:safePadding.right}"
      ></div>
      <div v-if="show"
        class="absolute top-0 bottom-0 left-0 bg-[#2AA3154D] pointer-events-none z-50"

        :style="{ width: safePadding.left }"
      ></div>
      <div v-if="show"
        class="absolute top-0 bottom-0 right-0 bg-[#2AA3154D] pointer-events-none z-50"
        :style="{ width: safePadding.right }"
      ></div>
      <div class="relative z-10 w-full h-full object-contain flex items-center justify-center">
        <slot />
      </div>
    </div>
  </template>

  <style>
  /* No additional styles needed as Tailwind is used */
  </style>
