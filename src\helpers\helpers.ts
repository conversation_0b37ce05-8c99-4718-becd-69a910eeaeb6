// Import { createApp, h } from 'vue';
import { SVG } from '@svgdotjs/svg.js';
import { PostRequest } from './apihelper';
import { getStorage, ref as referenece, getDownloadURL, uploadBytesResumable } from 'firebase/storage';
import { createApp, h, type Component, type ComponentOptions, type DefineComponent } from 'vue';
import { compressImageQualityFormats, sceneTypeRoutes } from '@/enum';

const storage = getStorage();

// accepts all types of Vue components
type VueComponent = Component | DefineComponent<any, any, any> | ComponentOptions

export function htmlGenerator (MyComponent: VueComponent, props: object){
  const container = document.createElement('div');

  // Vue app instance
  const app = createApp({
    render: () => h(MyComponent, { ...props }),
  });
  app.mount(container);
  return container.innerHTML;
}

export function removeNullProperties (obj :object) {
  return Object.fromEntries(
    Object.entries(obj).filter(([, value]) => value !== null),
  );
}

export function objectToQueryString (obj: Record<string, any>): string {
  const queryString = Object.keys(obj)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
    .join("&");
  return `?${queryString}`;
}

export function checkNumberValue (value: number): boolean | number {
  return value === 0 || value;
}

export const layer_icons = {
  amenity: {
    Gym: {
      icon: `<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_b_3671_6973)">
      <rect width="46" height="46" rx="12" fill="white" fill-opacity="0.74"/>
      <path d="M18.7344 24.7321C18.7344 24.7321 19.6398 23.51 20.2344 23.2316C22.0292 22.3913 22.3943 22.0261 23.2344 20.2307C23.5127 19.636 24.7344 18.7302 24.7344 18.7302M21.2344 27.2329C21.2344 27.2329 22.4561 26.3271 22.7344 25.7324C23.5745 23.9371 23.9396 23.5719 25.7344 22.7315C26.3289 22.4531 27.2344 21.231 27.2344 21.231" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M25.4313 13.8926C25.9382 13.384 26.7613 13.3826 27.2698 13.8897L32.0816 18.6878C32.5901 19.1948 32.5914 20.0182 32.0845 20.5268L30.5559 22.0608C30.049 22.5694 29.2259 22.5707 28.7174 22.0637L23.9056 17.2656C23.3972 16.7586 23.3959 15.9352 23.9027 15.4265L25.4313 13.8926Z" stroke="black" stroke-width="1.5"/>
      <path d="M15.4138 23.9026C15.9207 23.394 16.7438 23.3927 17.2522 23.8997L22.0641 28.6978C22.5725 29.2049 22.5738 30.0282 22.067 30.5369L20.5384 32.0708C20.0315 32.5795 19.2084 32.5808 18.6999 32.0737L13.8881 27.2756C13.3796 26.7686 13.3783 25.9452 13.8852 25.4366L15.4138 23.9026Z" stroke="black" stroke-width="1.5"/>
      <path d="M28.9377 14.4525C30.8201 11.9854 34.14 14.8401 31.5431 17.0287M14.3781 28.9773C11.9985 30.9687 14.9978 34.1586 17.0674 31.4656" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      <defs>
      <filter id="filter0_b_3671_6973" x="-4" y="-4" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3671_6973"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_3671_6973" result="shape"/>
      </filter>
      </defs>
      </svg>`,
    },
    Lounge: {
      icon: `<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_b_3848_1152)">
      <rect width="46" height="46" rx="12" fill="white" fill-opacity="0.74"/>
      <path d="M14.3956 18.5704C14.9223 18.919 15.0266 19.4255 15.1896 20.003C15.2158 20.0932 15.2421 20.1834 15.2685 20.2736C15.3214 20.4549 15.3738 20.6364 15.4256 20.818C15.4936 21.056 15.5631 21.2935 15.6332 21.5308C15.7063 21.7788 15.7793 22.0268 15.8511 22.2752C15.8718 22.3465 15.8718 22.3465 15.8929 22.4193C15.922 22.5313 15.922 22.5313 15.922 22.6251C15.9666 22.6247 16.0113 22.6242 16.0573 22.6238C16.48 22.62 16.9027 22.6172 17.3254 22.6154C17.5427 22.6144 17.76 22.6131 17.9772 22.6109C18.1872 22.6089 18.3972 22.6078 18.6073 22.6073C18.687 22.6069 18.7668 22.6063 18.8466 22.6052C19.417 22.5983 19.951 22.6167 20.3852 23.0402C20.6482 23.3562 20.7625 23.6817 20.7999 24.0883C20.8036 24.1248 20.8073 24.1613 20.8111 24.199C20.8226 24.3149 20.8334 24.4309 20.8439 24.5469C20.849 24.6029 20.849 24.6029 20.8542 24.6599C20.8745 24.885 20.8933 25.1102 20.911 25.3356C20.9201 25.4499 20.9296 25.5642 20.9391 25.6785C21.0469 27.01 21.0469 27.01 20.797 27.3126C20.5387 27.5439 20.289 27.5284 19.9532 27.5235C19.9032 27.5242 19.8532 27.525 19.8016 27.5257C19.5101 27.5243 19.3295 27.51 19.1095 27.3126C18.9429 27.1044 18.9052 26.9727 18.8282 26.7032C17.8692 26.7032 16.9101 26.7032 15.922 26.7032C15.8988 26.8888 15.8988 26.8888 15.8751 27.0782C15.7648 27.2871 15.6635 27.3955 15.4532 27.5001C15.2519 27.5285 15.0499 27.5218 14.8468 27.5206C14.7647 27.522 14.7647 27.522 14.681 27.5234C14.314 27.5232 14.1266 27.4799 13.8582 27.2208C13.7235 27.013 13.7059 26.8073 13.6729 26.5647C13.6653 26.5131 13.6577 26.4616 13.6499 26.4085C13.6337 26.2978 13.6178 26.1872 13.6022 26.0765C13.5787 25.9095 13.5542 25.7427 13.5295 25.5759C13.477 25.2217 13.4261 24.8672 13.3754 24.5127C13.3661 24.4483 13.3569 24.3838 13.3477 24.3194C13.3093 24.0515 13.271 23.7835 13.2328 23.5155C13.1788 23.1363 13.1245 22.7572 13.0697 22.3782C13.0308 22.1085 12.9922 21.8387 12.9539 21.5689C12.9311 21.409 12.9083 21.2491 12.885 21.0893C12.8592 20.9114 12.834 20.7335 12.809 20.5556C12.8014 20.5041 12.7938 20.4526 12.7859 20.3996C12.6418 19.3587 12.6418 19.3587 12.8563 19.0038C13.2335 18.5341 13.8116 18.3116 14.3956 18.5704ZM13.6075 19.3673C13.4066 19.6763 13.5715 20.1906 13.6217 20.5346C13.6314 20.6025 13.6411 20.6704 13.6508 20.7384C13.677 20.9219 13.7035 21.1054 13.7301 21.2889C13.7579 21.481 13.7854 21.6731 13.8129 21.8652C13.865 22.2287 13.9173 22.592 13.9698 22.9554C14.0295 23.3693 14.0889 23.7832 14.1483 24.1971C14.2705 25.0481 14.393 25.8991 14.5157 26.7501C14.7168 26.7501 14.9179 26.7501 15.1251 26.7501C15.1357 26.6776 15.1464 26.605 15.1573 26.5303C15.2025 26.304 15.2653 26.1901 15.4532 26.0469C15.6243 25.9368 15.7745 25.9408 15.9752 25.9396C16.0086 25.9393 16.0419 25.9389 16.0762 25.9386C16.1863 25.9377 16.2963 25.9375 16.4063 25.9374C16.483 25.9371 16.5597 25.9368 16.6364 25.9365C16.7973 25.9359 16.9581 25.9358 17.1189 25.9358C17.3244 25.9358 17.5299 25.9346 17.7354 25.9329C17.8939 25.9319 18.0524 25.9317 18.2109 25.9318C18.2866 25.9316 18.3624 25.9312 18.4381 25.9305C18.5444 25.9296 18.6505 25.9299 18.7568 25.9304C18.8473 25.9302 18.8473 25.9302 18.9396 25.93C19.1804 25.9629 19.3666 26.0494 19.5196 26.2462C19.5949 26.4116 19.6579 26.5788 19.7189 26.7501C19.8735 26.7501 20.0282 26.7501 20.1876 26.7501C20.1667 26.1499 20.1292 25.5508 20.0851 24.952C20.0792 24.8717 20.0736 24.7913 20.0682 24.7109C20.0603 24.5962 20.0517 24.4816 20.043 24.367C20.0408 24.3326 20.0386 24.2982 20.0363 24.2627C20.0161 24.0153 19.9597 23.8105 19.7875 23.6261C19.5457 23.4412 19.41 23.4147 19.1111 23.4131C19.0725 23.4127 19.0339 23.4123 18.9942 23.4119C18.9103 23.4111 18.8263 23.4104 18.7424 23.4097C18.6096 23.4088 18.4769 23.4075 18.3441 23.4061C18.0621 23.4033 17.7801 23.4009 17.4982 23.3985C17.1718 23.3957 16.8454 23.3928 16.519 23.3895C16.3882 23.3883 16.2573 23.3873 16.1264 23.3863C16.0462 23.3855 15.9659 23.3848 15.8857 23.3839C15.8492 23.3838 15.8128 23.3836 15.7753 23.3834C15.6149 23.3815 15.4659 23.3785 15.3126 23.3282C15.305 23.2999 15.2975 23.2717 15.2897 23.2425C15.1302 22.652 14.9571 22.0652 14.7851 21.4781C14.7279 21.2829 14.671 21.0876 14.6146 20.8922C14.5597 20.7018 14.5041 20.5116 14.4482 20.3214C14.427 20.2493 14.4061 20.1771 14.3854 20.1049C14.3565 20.0041 14.3269 19.9036 14.297 19.8031C14.2804 19.7459 14.2637 19.6888 14.2466 19.6299C14.1846 19.477 14.1291 19.3984 14.0001 19.2969C13.8313 19.2688 13.7527 19.2756 13.6075 19.3673Z" fill="black"/>
      <path d="M32.6767 18.5701C32.9537 18.7375 33.1634 18.991 33.2656 19.2967C33.3051 19.7537 33.243 20.2074 33.1775 20.659C33.168 20.727 33.1585 20.7949 33.149 20.8628C33.1236 21.0451 33.0976 21.2273 33.0714 21.4095C33.044 21.6011 33.017 21.7928 32.99 21.9845C32.9446 22.3058 32.8988 22.6271 32.8527 22.9484C32.7996 23.3192 32.7472 23.6901 32.6951 24.061C32.6501 24.3807 32.6049 24.7003 32.5593 25.0198C32.5321 25.2102 32.5051 25.4006 32.4784 25.591C32.4533 25.7698 32.4277 25.9485 32.4019 26.1271C32.3925 26.1925 32.3832 26.2579 32.3741 26.3233C32.2702 27.0715 32.2702 27.0715 32.0686 27.3002C31.7607 27.5286 31.5457 27.5114 31.1679 27.5144C31.1126 27.5158 31.0572 27.5172 31.0002 27.5186C30.4849 27.5223 30.4849 27.5223 30.2656 27.3123C30.1971 27.2218 30.1971 27.2218 30.1543 27.1306C30.1394 27.1008 30.1245 27.0709 30.1092 27.0402C30.0697 26.9095 30.0731 26.7918 30.0781 26.656C30.0469 26.6575 30.0158 26.6589 29.9836 26.6603C29.0302 26.7024 28.0791 26.7084 27.125 26.7029C27.1195 26.7315 27.114 26.7601 27.1084 26.7895C27.1007 26.8272 27.093 26.8649 27.085 26.9038C27.0776 26.9411 27.0702 26.9784 27.0626 27.0168C27.0054 27.2135 26.8861 27.3503 26.7113 27.4594C26.5035 27.5417 26.2732 27.5171 26.0527 27.5174C26.0022 27.5185 25.9518 27.5197 25.8998 27.5208C25.6056 27.5219 25.4249 27.5124 25.2031 27.3123C24.7998 26.8241 25.0418 25.8708 25.088 25.2828C25.1006 25.1206 25.1123 24.9584 25.1241 24.7962C25.1321 24.6918 25.1402 24.5874 25.1483 24.4829C25.1517 24.4351 25.1551 24.3873 25.1586 24.3381C25.2015 23.8136 25.3231 23.3383 25.7118 22.9628C26.1802 22.584 26.692 22.6056 27.2628 22.6111C27.3442 22.6113 27.4256 22.6115 27.507 22.6117C27.7199 22.6122 27.9328 22.6136 28.1457 22.6151C28.3635 22.6166 28.5812 22.6172 28.799 22.6179C29.2254 22.6194 29.6517 22.6218 30.0781 22.6248C30.0895 22.5781 30.1008 22.5315 30.1126 22.4834C30.2161 22.0622 30.3283 21.6446 30.4516 21.2286C30.4771 21.1424 30.4771 21.1424 30.5031 21.0545C30.5386 20.9345 30.5742 20.8146 30.6099 20.6947C30.6554 20.5419 30.7003 20.389 30.7451 20.2361C30.7885 20.0883 30.8325 19.9407 30.8764 19.7931C30.8923 19.7387 30.9082 19.6843 30.9245 19.6282C31.1277 18.9518 31.1277 18.9518 31.4111 18.7195C31.4371 18.6975 31.4632 18.6754 31.49 18.6527C31.8189 18.4318 32.3153 18.4099 32.6767 18.5701ZM31.8125 19.4842C31.7655 19.6033 31.7274 19.7155 31.693 19.8381C31.6773 19.8912 31.6773 19.8912 31.6612 19.9453C31.6269 20.062 31.5934 20.1788 31.56 20.2957C31.5362 20.377 31.5124 20.4582 31.4886 20.5395C31.426 20.7532 31.3642 20.9671 31.3025 21.181C31.2397 21.3986 31.1761 21.616 31.1125 21.8334C31.0199 22.151 30.9275 22.4686 30.8357 22.7865C30.826 22.8197 30.8164 22.8529 30.8065 22.8871C30.7643 23.0335 30.7228 23.1797 30.6875 23.3279C30.5008 23.3894 30.3115 23.3816 30.1174 23.3814C30.0597 23.3817 30.0597 23.3817 30.0009 23.3819C29.9176 23.3823 29.8342 23.3825 29.7508 23.3827C29.6188 23.383 29.4868 23.3838 29.3548 23.3846C28.9795 23.3871 28.6041 23.3893 28.2288 23.3901C27.9991 23.3905 27.7694 23.3919 27.5398 23.3939C27.4523 23.3945 27.3649 23.3947 27.2775 23.3946C27.1551 23.3945 27.0327 23.3955 26.9103 23.3968C26.8743 23.3965 26.8384 23.3962 26.8014 23.3959C26.4999 23.4011 26.3346 23.5087 26.1264 23.7173C26.0274 23.8744 26.0114 23.9916 25.993 24.1761C25.9896 24.2085 25.9862 24.2409 25.9827 24.2744C25.9719 24.3807 25.9623 24.4871 25.9531 24.5935C25.9481 24.6481 25.9481 24.6481 25.943 24.7038C25.9223 24.9326 25.9035 25.1615 25.8859 25.3906C25.8768 25.5088 25.8674 25.627 25.8579 25.7453C25.8519 25.8218 25.8459 25.8983 25.8399 25.9749C25.8371 26.0096 25.8344 26.0442 25.8315 26.08C25.8146 26.3039 25.8101 26.5252 25.8125 26.7498C25.9826 26.7498 26.1528 26.7498 26.3281 26.7498C26.3465 26.6773 26.3648 26.6048 26.3838 26.5301C26.4587 26.28 26.5616 26.138 26.7834 25.9948C26.9332 25.9363 27.0644 25.9402 27.2251 25.9393C27.2584 25.939 27.2918 25.9387 27.3261 25.9383C27.4361 25.9375 27.5461 25.9373 27.6562 25.9372C27.7329 25.9369 27.8096 25.9366 27.8863 25.9362C28.0471 25.9357 28.2079 25.9355 28.3687 25.9355C28.5743 25.9356 28.7798 25.9343 28.9853 25.9327C29.1438 25.9316 29.3023 25.9314 29.4608 25.9315C29.5365 25.9314 29.6122 25.931 29.688 25.9302C29.7942 25.9293 29.9004 25.9296 30.0066 25.9301C30.0971 25.9299 30.0971 25.9299 30.1895 25.9297C30.4022 25.9588 30.5106 26.0199 30.6875 26.1404C30.818 26.3363 30.8517 26.5174 30.875 26.7498C31.0761 26.7498 31.2772 26.7498 31.4843 26.7498C31.6221 25.9099 31.7468 25.0686 31.8645 24.2257C31.8732 24.1635 31.8819 24.1013 31.8906 24.0392C31.936 23.7151 31.9813 23.3909 32.0264 23.0668C32.0683 22.7665 32.1104 22.4663 32.1527 22.1661C32.1893 21.9059 32.2257 21.6456 32.262 21.3853C32.2835 21.2309 32.3051 21.0765 32.3269 20.9221C32.351 20.7508 32.3748 20.5795 32.3985 20.4081C32.4057 20.3578 32.4129 20.3075 32.4202 20.2557C32.5064 19.7952 32.5064 19.7952 32.3925 19.367C32.1492 19.2133 31.984 19.2718 31.8125 19.4842Z" fill="black"/>
      <path d="M20.2968 20.3624C20.3463 20.362 20.3958 20.3616 20.4468 20.3612C20.5006 20.3613 20.5544 20.3613 20.6098 20.3613C20.6668 20.361 20.7239 20.3607 20.7826 20.3604C20.9388 20.3596 21.0949 20.3593 21.251 20.3593C21.3487 20.3592 21.4464 20.359 21.544 20.3588C21.8852 20.3579 22.2263 20.3575 22.5675 20.3576C22.8848 20.3576 23.2022 20.3567 23.5195 20.3552C23.7925 20.3539 24.0655 20.3534 24.3385 20.3535C24.5013 20.3535 24.6641 20.3533 24.8269 20.3523C25.0087 20.3512 25.1904 20.3516 25.3722 20.3522C25.4257 20.3517 25.4792 20.3511 25.5343 20.3506C25.899 20.3535 26.1514 20.4075 26.4333 20.6546C26.4586 20.6851 26.4838 20.7156 26.5098 20.747C26.5358 20.7774 26.5618 20.8078 26.5886 20.8391C26.7985 21.1439 26.7817 21.4705 26.7501 21.8281C26.6713 22.1402 26.4471 22.3524 26.1876 22.5312C25.9614 22.6523 25.7391 22.6369 25.4894 22.6341C25.45 22.634 25.4106 22.6339 25.37 22.6338C25.2236 22.6333 25.0772 22.632 24.9307 22.6308C24.4391 22.6279 24.4391 22.6279 23.9376 22.625C23.9376 23.615 23.9376 24.605 23.9376 25.625C24.0613 25.625 24.1851 25.625 24.3126 25.625C24.4282 25.6975 24.4282 25.6975 24.5001 25.8125C24.5243 25.9685 24.5243 25.9685 24.525 26.1523C24.5252 26.2012 24.5252 26.2012 24.5255 26.251C24.5256 26.3198 24.5253 26.3885 24.5245 26.4572C24.5235 26.5621 24.5245 26.667 24.5257 26.7719C24.5256 26.8388 24.5253 26.9057 24.525 26.9726C24.5248 27.0333 24.5246 27.0939 24.5243 27.1565C24.4955 27.3421 24.4578 27.3854 24.3126 27.5C24.1916 27.5238 24.1916 27.5238 24.0533 27.5242C24.0014 27.5248 23.9494 27.5254 23.8959 27.526C23.8121 27.5254 23.8121 27.5254 23.7266 27.5249C23.6403 27.5251 23.6403 27.5251 23.5522 27.5254C23.4306 27.5255 23.3089 27.5252 23.1872 27.5244C23.0008 27.5234 22.8144 27.5244 22.628 27.5256C22.5098 27.5255 22.3917 27.5252 22.2735 27.5249C22.2176 27.5252 22.1618 27.5256 22.1042 27.526C22.0263 27.5251 22.0263 27.5251 21.9469 27.5242C21.8784 27.524 21.8784 27.524 21.8085 27.5238C21.6524 27.493 21.5965 27.4369 21.5001 27.3125C21.4758 27.1565 21.4758 27.1565 21.4752 26.9726C21.475 26.94 21.4748 26.9074 21.4746 26.8739C21.4745 26.8052 21.4748 26.7364 21.4756 26.6677C21.4766 26.5628 21.4756 26.458 21.4744 26.353C21.4746 26.2861 21.4748 26.2192 21.4752 26.1523C21.4754 26.0916 21.4756 26.031 21.4758 25.9685C21.5001 25.8125 21.5001 25.8125 21.5719 25.6975C21.6876 25.625 21.6876 25.625 22.0626 25.625C22.0626 24.635 22.0626 23.645 22.0626 22.625C21.4161 22.6324 21.4161 22.6324 20.7697 22.6418C20.6879 22.6424 20.6062 22.6429 20.5245 22.6433C20.4621 22.6447 20.4621 22.6447 20.3984 22.6462C20.0654 22.6464 19.8192 22.5665 19.5668 22.3454C19.5416 22.3148 19.5163 22.2843 19.4903 22.2529C19.4643 22.2225 19.4383 22.1921 19.4116 22.1608C19.2016 21.856 19.2185 21.5294 19.2501 21.1718C19.3289 20.8597 19.5531 20.6475 19.8126 20.4687C19.9803 20.3903 20.1122 20.3631 20.2968 20.3624ZM20.0938 21.2656C20.0272 21.3987 20.0322 21.4931 20.0469 21.6406C20.1516 21.7741 20.2134 21.821 20.3751 21.875C20.4464 21.8794 20.5179 21.8813 20.5894 21.8815C20.6553 21.8819 20.6553 21.8819 20.7226 21.8824C20.7708 21.8824 20.8189 21.8825 20.8685 21.8825C20.9193 21.8827 20.9701 21.883 21.0224 21.8832C21.1907 21.8839 21.359 21.8842 21.5274 21.8845C21.6442 21.8847 21.761 21.885 21.8779 21.8853C22.0916 21.8857 22.3054 21.886 22.5192 21.8862C22.8023 21.8863 23.0854 21.8869 23.3685 21.8881C23.6723 21.8894 23.9761 21.89 24.2799 21.8901C24.3957 21.8903 24.5115 21.8906 24.6274 21.8913C24.7894 21.8921 24.9514 21.892 25.1134 21.8917C25.1613 21.8921 25.2092 21.8925 25.2585 21.893C25.5088 21.8915 25.6916 21.8789 25.9063 21.7343C25.9729 21.6012 25.9679 21.5068 25.9532 21.3593C25.8486 21.2258 25.7868 21.1789 25.6251 21.125C25.5537 21.1205 25.4822 21.1186 25.4107 21.1184C25.3668 21.1181 25.3228 21.1178 25.2775 21.1176C25.2294 21.1175 25.1812 21.1175 25.1316 21.1174C25.0809 21.1172 25.0301 21.1169 24.9778 21.1167C24.8094 21.116 24.6411 21.1157 24.4728 21.1154C24.356 21.1152 24.2391 21.1149 24.1223 21.1146C23.9085 21.1142 23.6947 21.1139 23.4809 21.1138C23.1978 21.1136 22.9147 21.113 22.6316 21.1118C22.3278 21.1105 22.024 21.1099 21.7202 21.1098C21.6044 21.1096 21.4886 21.1093 21.3728 21.1087C21.2108 21.1079 21.0487 21.1079 20.8867 21.1082C20.8388 21.1078 20.791 21.1074 20.7417 21.1069C20.4913 21.1084 20.3086 21.121 20.0938 21.2656ZM22.8126 22.625C22.8126 23.615 22.8126 24.605 22.8126 25.625C22.9363 25.625 23.0601 25.625 23.1876 25.625C23.1876 24.635 23.1876 23.645 23.1876 22.625C23.0638 22.625 22.9401 22.625 22.8126 22.625ZM22.2501 26.375C22.2501 26.4987 22.2501 26.6225 22.2501 26.75C22.7451 26.75 23.2401 26.75 23.7501 26.75C23.7501 26.6262 23.7501 26.5025 23.7501 26.375C23.2551 26.375 22.7601 26.375 22.2501 26.375Z" fill="black"/>
      </g>
      <defs>
      <filter id="filter0_b_3848_1152" x="-4" y="-4" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3848_1152"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_3848_1152" result="shape"/>
      </filter>
      </defs>
      </svg>`,
    },
    Lobby: {
      icon: `<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_b_3848_1142)">
      <rect width="46" height="46" rx="12" fill="white" fill-opacity="0.74"/>
      <g clip-path="url(#clip0_3848_1142)">
      <path d="M20.994 14.6928C21.5008 15.1453 21.9057 15.7919 21.9529 16.4858C21.9926 17.4166 21.7904 18.1395 21.1964 18.8746C21.2301 18.8871 21.2637 18.8996 21.2984 18.9125C21.7287 19.094 22.0852 19.3938 22.4151 19.7184C22.4687 19.771 22.4687 19.771 22.5234 19.8247C23.4739 20.8181 23.6808 22.0444 23.6808 23.3746C24.7172 23.3746 25.7536 23.3746 26.8214 23.3746C26.8214 23.2199 26.8214 23.0652 26.8214 22.9059C26.9028 22.464 27.1661 22.1295 27.5146 21.8556C27.7363 21.7169 28.0562 21.5934 28.3214 21.5934C28.3059 21.5624 28.2905 21.5315 28.2745 21.4996C28.2609 21.3456 28.2615 21.1912 28.2599 21.0367C28.2585 20.994 28.2572 20.9513 28.2558 20.9073C28.2528 20.5941 28.2528 20.5941 28.3575 20.4402C28.4667 20.3717 28.522 20.3492 28.6495 20.3512C28.6959 20.3504 28.6959 20.3504 28.7433 20.3497C28.8907 20.3889 28.938 20.4349 29.0245 20.5621C29.0487 20.7209 29.0443 20.8765 29.0392 21.0367C29.0387 21.0798 29.0383 21.1229 29.0378 21.1673C29.0325 21.4837 29.0325 21.4837 28.9776 21.5934C29.006 21.5969 29.0344 21.6004 29.0637 21.604C29.5241 21.6762 29.8797 21.8798 30.1744 22.2427C30.4229 22.6062 30.4794 22.9448 30.4776 23.3746C30.5194 23.3735 30.5194 23.3735 30.5619 23.3724C31.2749 23.356 31.9422 23.3562 32.587 23.7027C32.6244 23.7224 32.6618 23.7421 32.7004 23.7624C33.4508 24.1836 33.9677 24.8507 34.2078 25.6748C34.278 25.9387 34.2945 26.1829 34.2927 26.4549C34.293 26.5022 34.2932 26.5494 34.2935 26.5981C34.2941 26.7526 34.2937 26.9071 34.2932 27.0616C34.2933 27.17 34.2934 27.2784 34.2936 27.3868C34.2938 27.6132 34.2935 27.8397 34.2929 28.0662C34.2921 28.3554 34.2925 28.6446 34.2934 28.9338C34.2939 29.1574 34.2937 29.381 34.2933 29.6046C34.2932 29.7112 34.2934 29.8177 34.2937 29.9243C34.294 30.0737 34.2935 30.2231 34.2927 30.3724C34.293 30.4158 34.2933 30.4592 34.2936 30.5038C34.286 31.295 33.9162 32.0253 33.381 32.5943C32.8269 33.1256 32.0651 33.4791 31.2894 33.4652C31.1808 33.4527 31.1808 33.4527 31.0577 33.3854C30.9645 33.2117 30.939 33.0348 30.9933 32.8434C31.1654 32.6628 31.3164 32.6266 31.5587 32.5914C32.148 32.49 32.6378 32.2161 33.0074 31.7401C33.4911 31.028 33.4852 30.2867 33.4855 29.4588C33.4857 29.376 33.4858 29.2931 33.486 29.2102C33.4863 29.037 33.4864 28.8638 33.4863 28.6906C33.4863 28.4704 33.487 28.2502 33.4878 28.03C33.4883 27.8589 33.4884 27.6878 33.4884 27.5167C33.4884 27.4357 33.4886 27.3546 33.489 27.2735C33.5027 26.342 33.5027 26.342 33.2169 25.4693C33.2005 25.438 33.184 25.4066 33.1671 25.3742C32.8679 24.8289 32.389 24.4763 31.7997 24.2907C31.5681 24.2336 31.3465 24.2125 31.1083 24.2122C31.047 24.212 30.9858 24.2118 30.9226 24.2117C30.8549 24.2117 30.7872 24.2117 30.7196 24.2118C30.6474 24.2117 30.5752 24.2115 30.5031 24.2114C30.3049 24.211 30.1068 24.2109 29.9086 24.2109C29.6948 24.2108 29.4811 24.2104 29.2673 24.2101C28.7999 24.2095 28.3325 24.2092 27.8651 24.209C27.5732 24.2088 27.2812 24.2086 26.9893 24.2084C26.1808 24.2079 25.3723 24.2074 24.5638 24.2072C24.4862 24.2072 24.4862 24.2072 24.407 24.2072C24.3293 24.2072 24.3293 24.2072 24.2499 24.2072C24.1448 24.2071 24.0397 24.2071 23.9347 24.2071C23.8825 24.2071 23.8304 24.2071 23.7767 24.2071C22.9323 24.2069 22.0879 24.2061 21.2435 24.205C20.3763 24.2039 19.5091 24.2033 18.6419 24.2032C18.1551 24.2032 17.6683 24.2029 17.1815 24.2021C16.7669 24.2013 16.3524 24.2011 15.9378 24.2015C15.7264 24.2017 15.515 24.2016 15.3037 24.201C15.1099 24.2004 14.9161 24.2004 14.7223 24.201C14.6524 24.2011 14.5826 24.2009 14.5128 24.2005C13.7237 24.1962 13.0735 24.3505 12.4897 24.9032C12.334 25.068 12.2128 25.2393 12.1026 25.4371C12.0844 25.4692 12.0661 25.5013 12.0473 25.5343C11.7639 26.0941 11.8073 26.7754 11.8089 27.3868C11.8089 27.4694 11.8088 27.5521 11.8087 27.6347C11.8085 27.8067 11.8087 27.9788 11.8092 28.1509C11.8097 28.3696 11.8094 28.5883 11.8088 28.8071C11.8085 28.9773 11.8086 29.1476 11.8089 29.3179C11.8089 29.3985 11.8088 29.479 11.8086 29.5596C11.8067 30.4616 11.8521 31.3204 12.5245 31.9996C12.93 32.3504 13.3977 32.6145 13.9436 32.6151C13.9939 32.6153 14.0443 32.6154 14.0962 32.6156C14.1514 32.6156 14.2067 32.6155 14.2637 32.6155C14.3225 32.6156 14.3812 32.6157 14.4418 32.6159C14.605 32.6162 14.7681 32.6163 14.9313 32.6163C15.1073 32.6164 15.2833 32.6168 15.4593 32.6171C15.7642 32.6176 16.0692 32.618 16.3741 32.6183C16.856 32.6188 17.3379 32.6198 17.8199 32.6208C17.9847 32.6212 18.1495 32.6215 18.3144 32.6219C18.3762 32.622 18.3762 32.622 18.4392 32.6221C18.9081 32.6231 19.3771 32.624 19.846 32.6248C19.9103 32.6249 19.9103 32.6249 19.9759 32.6251C20.6709 32.6263 21.366 32.6272 22.061 32.6279C22.7744 32.6288 23.4877 32.6301 24.201 32.6319C24.6412 32.633 25.0813 32.6337 25.5215 32.6338C25.823 32.6339 26.1245 32.6345 26.426 32.6355C26.6 32.636 26.7741 32.6364 26.9481 32.6361C27.1367 32.6358 27.3252 32.6366 27.5138 32.6375C27.569 32.6372 27.6241 32.637 27.681 32.6367C27.7315 32.6371 27.782 32.6375 27.834 32.638C27.8775 32.638 27.921 32.6381 27.9659 32.6381C28.1437 32.6641 28.2746 32.7369 28.3858 32.8785C28.4299 33.0372 28.4086 33.1538 28.3683 33.3121C28.2279 33.4499 28.1252 33.4703 27.9319 33.4706C27.8811 33.471 27.8304 33.4714 27.7781 33.4718C27.7223 33.4715 27.6666 33.4712 27.6092 33.4709C27.55 33.4712 27.4907 33.4714 27.4297 33.4717C27.2652 33.4724 27.1007 33.4722 26.9362 33.4718C26.7587 33.4716 26.5813 33.4722 26.4039 33.4727C26.0562 33.4735 25.7084 33.4735 25.3607 33.4732C25.0779 33.4731 24.795 33.4731 24.5122 33.4734C24.472 33.4734 24.4317 33.4734 24.3902 33.4735C24.3085 33.4736 24.2267 33.4736 24.145 33.4737C23.3776 33.4743 22.6103 33.4741 21.843 33.4736C21.1418 33.4731 20.4407 33.4737 19.7396 33.4748C19.0193 33.476 18.2991 33.4764 17.5788 33.4761C17.1745 33.4759 16.7703 33.476 16.3661 33.4769C16.0217 33.4775 15.6773 33.4776 15.3329 33.4768C15.1574 33.4764 14.9819 33.4763 14.8063 33.477C14.6155 33.4778 14.4246 33.4771 14.2337 33.4762C14.1791 33.4767 14.1244 33.4771 14.068 33.4776C13.1585 33.4695 12.4457 33.099 11.8214 32.4684C11.7895 32.4375 11.7576 32.4066 11.7247 32.3748C11.0229 31.6464 11.0025 30.7135 11.0042 29.7661C11.004 29.6581 11.0037 29.5501 11.0035 29.4422C11.003 29.2166 11.0031 28.991 11.0036 28.7654C11.0042 28.4778 11.0032 28.1903 11.0018 27.9027C11.001 27.6799 11.001 27.4571 11.0013 27.2343C11.0013 27.1283 11.001 27.0224 11.0004 26.9164C10.9977 26.3465 11.0029 25.8345 11.212 25.2965C11.2244 25.2644 11.2368 25.2323 11.2496 25.1992C11.574 24.4421 12.2423 23.8711 12.9933 23.5621C13.3893 23.4324 13.7669 23.3668 14.1827 23.3717C14.2172 23.3719 14.2517 23.3721 14.2873 23.3723C14.3716 23.3729 14.4559 23.3737 14.5401 23.3746C14.538 23.3401 14.5358 23.3056 14.5336 23.2701C14.5258 23.061 14.5206 22.9024 14.6075 22.7096C14.7607 22.6012 14.8651 22.5932 15.0497 22.6059C15.1495 22.6246 15.1495 22.6246 15.2696 22.6861C15.337 22.8121 15.337 22.8121 15.337 23.3277C17.8275 23.3277 20.318 23.3277 22.8839 23.3277C22.7773 22.0485 22.6155 20.9931 21.6302 20.1199C21.1869 19.7708 20.6916 19.5263 20.16 19.3397C20.0714 19.2965 20.0714 19.2965 19.9776 19.1559C19.9408 18.8293 19.9408 18.8293 20.0714 18.6578C20.146 18.5988 20.2212 18.5402 20.2995 18.4862C20.7371 18.1807 20.9989 17.758 21.1026 17.234C21.1657 16.5852 21.0538 16.0448 20.6462 15.5219C20.2367 15.0735 19.6983 14.8679 19.0995 14.8388C18.574 14.8545 18.0758 15.0534 17.6925 15.4117C17.244 15.8888 17.0951 16.3848 17.1067 17.0249C17.1402 17.4956 17.3425 17.895 17.6485 18.2506C17.7683 18.3569 17.7683 18.3569 17.8796 18.4342C18.1798 18.6515 18.1798 18.6515 18.2755 18.857C18.2914 19.0283 18.2887 19.1052 18.1964 19.2496C18.0177 19.372 17.8304 19.4439 17.628 19.5191C16.6526 19.8981 16.0297 20.5964 15.606 21.5369C15.5003 21.7644 15.5003 21.7644 15.3833 21.8493C15.2285 21.8913 15.1156 21.8689 14.962 21.8277C14.8448 21.7311 14.8448 21.7311 14.7745 21.5934C14.7888 21.3124 14.8562 21.1028 14.9855 20.8551C15.0021 20.8226 15.0188 20.7902 15.036 20.7567C15.4537 19.9677 16.0695 19.365 16.837 18.9215C16.8991 18.9041 16.9615 18.888 17.0245 18.8746C16.9968 18.8371 16.9691 18.7997 16.9405 18.7611C16.6965 18.4248 16.5028 18.0994 16.3683 17.7027C16.3568 17.67 16.3453 17.6372 16.3335 17.6035C16.1221 16.8594 16.2923 16.0474 16.6429 15.3808C17.0208 14.7395 17.6365 14.321 18.337 14.0934C19.2687 13.859 20.2607 14.0685 20.994 14.6928ZM27.8752 22.5962C27.646 22.8571 27.6183 22.9726 27.6183 23.3277C28.2989 23.3277 28.9795 23.3277 29.6808 23.3277C29.6497 22.9201 29.6497 22.9201 29.4464 22.6246C29.0393 22.3108 28.2816 22.2232 27.8752 22.5962Z" fill="black"/>
      <path d="M22.9855 23.0786C23.0997 23.1547 23.0997 23.1547 23.1466 23.2485C23.1736 23.5839 23.1736 23.5839 23.0754 23.7279C22.9158 23.842 22.773 23.8385 22.5841 23.8344C22.5507 23.8352 22.5174 23.8359 22.483 23.8366C22.3215 23.8354 22.2256 23.8229 22.0934 23.7275C21.9967 23.5874 21.9948 23.5032 22.0031 23.3377C22.0316 23.2002 22.0971 23.141 22.2091 23.061C22.4695 22.9742 22.7301 22.98 22.9855 23.0786Z" fill="black"/>
      </g>
      </g>
      <defs>
      <filter id="filter0_b_3848_1142" x="-4" y="-4" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_3848_1142"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_3848_1142" result="shape"/>
      </filter>
      <clipPath id="clip0_3848_1142">
      <rect width="24" height="24" fill="white" transform="translate(11 11)"/>
      </clipPath>
      </defs>
      </svg>`,
    },
  },
};
export async function resizeImage (file: File, maxWidth: number, maxHeight: number):Promise<File>{
  console.log('in reisxeimg', file);

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (event) {
      const img = new Image();
      img.src = event.target?.result as string;
      img.onload = function () {
        const canvas = document.createElement("canvas");
        let width = img.width;
        let height = img.height;
        while (width > maxWidth || height > maxHeight) {
          // Resize image until it is less than 1K
          width /= 2;
          height /= 2;
        }
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.drawImage(img, 0, 0, width, height);
        } else {
          console.log("canvas not created");
        }
        const fileNameParts = file.name.split(".");
        const fileExtension = fileNameParts.pop(); // Remove the last part which is the extension
        const fileNameWithoutExtension = fileNameParts.join("."); // Rejoin the remaining parts

        const newName = fileNameWithoutExtension + "_thumb." + fileExtension;
        canvas.toBlob(function () {
          const webpDataUrl = canvas.toDataURL("image/webp");
          fetch(webpDataUrl)
            .then((res) => res.blob())
            .then((webpBlob) => {
              const resizedWebpFile = new File(
                [webpBlob],
                newName.replace(/\.[^/.]+$/, ".webp"),
                {
                  type: "image/webp",
                  lastModified: Date.now(),
                },
              );
              resolve(resizedWebpFile);
            })
            .catch((err) => {
              reject(err);
            });
        }, "image/webp"); // Converting thumbnail image to webp format
      };
    };
  });
}

export async function getPropertiesOfImage (file:File):Promise<Object> {
  console.log("Inside Properties", file);

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (event) {
      const img = new Image();
      img.src = event.target?.result as string;
      img.onload = function () {
        const imageAttributeObj = {
          width: img.width,
          height: img.height,
        };
        resolve(imageAttributeObj);
      };
      img.onerror = function () {
        reject();
      };
    };
    reader.onerror = function () {
      reject();
    };
  });
}

export async function convertBlobToUrl (fileBlob: File):Promise<Blob> {
  console.log('recieved file', fileBlob);

  return new Promise((resolve, reject) => {
    const img = new Image();

    // Read the Blob data using FileReader
    const reader = new FileReader();
    reader.readAsDataURL(fileBlob);  // Convert Blob to base64

    reader.onload = function (event) {
      img.src = event.target?.result as string;  // Set image source to base64 data

      img.onload = function () {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas dimensions to match the image
        canvas.width = img.width;
        canvas.height = img.height;

        // Draw the image onto the canvas
        if (ctx) {
          ctx.drawImage(img, 0, 0, img.width, img.height);
        } else {
          console.log("Canvas not created");
        }
        // File name adjustment
        const fileNameParts = fileBlob.name.split('.');
        fileNameParts.pop(); // Remove extension
        const fileNameWithoutExtension = fileNameParts.join('.'); // Rejoin remaining parts
        const newName = fileNameWithoutExtension + '_file.webp';  // Change extension to WebP

        // Convert canvas to WebP Blob
        canvas.toBlob(function (webpBlob) {
          if (webpBlob) {
            const resizedWebpFile = new File([webpBlob], newName, {
              type: 'image/webp',
              lastModified: Date.now(),
            });
            resolve(resizedWebpFile);
          }
        }, 'image/webp'); // Convert to webp format

      };

      img.onerror = function (error) {
        reject(error);
      };
    };

    reader.onerror = function (error) {
      reject(error);
    };
  });
}

export async function ShortenUrl (orgUrl: string, title: string, description: string, image: string) {
  const api_url = import.meta.env.VITE_API_URL;
  const url = `${api_url}/tiny/url`;
  const body = {
    org_url: orgUrl,
    title: title,
    description: description,
    image: image,
  };
  interface ShortenedUrlResponse {
    _id: string;
  }
  try {
    const shortenedUrl = await PostRequest({ url, body }) as ShortenedUrlResponse;
    if (shortenedUrl) {
      const id = shortenedUrl._id;
      const res = `${import.meta.env.VITE_TINY_URL}tiny/${id}`;
      return res;
    }
  } catch (error) {
    console.error("Error shortening URL:", error);
    throw error;
  }
}

// Read json file and convert to parse
export async function JsonStringifyFromReadJsonFile (file: File):Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && typeof e.target.result === 'string') {
        const jsonObject = JSON.parse(e.target.result);
        resolve(JSON.stringify(jsonObject, null, 2));
      }
    };
    reader.onerror = reject;
    reader.readAsText(file);
  });
}
interface attrObjType {
  height: number,
  width: number
}
export async function GetCooridnatesFromReadSvgFile (
  file: File,
  src: string,
  attributesObject: attrObjType,
):Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader(); // Built in API
    console.log(reader);
    reader.onload = function (e) {
      if (e.target && typeof e.target.result === 'string') {
        const parser = new DOMParser();
        const doc = parser.parseFromString(e.target.result, "image/svg+xml");
        const svgElement = doc.documentElement;
        const draw = SVG(svgElement);
        const svgChildren = draw.children(); // Array of direct descendants children of svg element
        // Loop through children and create object
        const coordinatesObject : Record<string, any> = {}; // Object of coordinates
        if (svgChildren.length > 0) {
          svgChildren.forEach((item) => {
            console.log(item.node.id);
            const newGroup = SVG(
              `<g><g transform='translate(-${item.bbox().x}, -${
                item.bbox().y
              })'>${item.node.innerHTML}</g></g>`,
            );
            if (item.type === "g" && src === "custom") {
              coordinatesObject[item.node.id] = {
                g: newGroup.node.innerHTML,
                x: item.bbox().x,
                y: item.bbox().y,
                width: item.bbox().width,
                height: item.bbox().height,
              };
            } else {
              coordinatesObject[item.node.id] = {
                g: newGroup.node.innerHTML,
                x: 500.125,
                y: 500.125,
                width: attributesObject.width,
                height: attributesObject.height,
              };
            }
          });
          resolve(JSON.stringify(coordinatesObject, null, 2));
        } else {
          reject("Invalid svg! svg has no children"); // Error handling
        }
      }
      reader.onerror = reject; // Error handling
      reader.readAsText(file);
    };
  });
}

export async function GetHeightWidthFromReadSvgFile (file: File):Promise<{}> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader(); // Built in API
    console.log(reader);
    reader.onload = function (e) {
      if (e.target && typeof e.target.result === 'string'){
        const parser = new DOMParser();
        const doc = parser.parseFromString(e.target.result, "image/svg+xml");
        const svgElement = doc.documentElement;
        const draw = SVG(svgElement);
        const svgChildren = draw.children();
        let attributesObject = {};
        if (svgChildren.length > 0) {
          svgChildren.forEach((item) => {
            console.log(item);
            if (item.type === "g") {
              console.log(item.bbox().width);
              console.log(item.bbox().height);
              attributesObject = {
                width: item.bbox().width,
                height: item.bbox().height,
              };
            }
          });
          resolve(attributesObject);
        } else {
          reject("Invalid svg! svg has no children"); // Error handling
        }
      }
      reader.onerror = reject; // Error handling
      reader.readAsText(file);
    };
  });
}

export async function DownloadFile (url: string, filename: string):Promise<File> {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", url, true);
    xhr.responseType = "blob";

    xhr.onload = function () {
      if (xhr.status === 200) {
        const blob = xhr.response;
        const file = new File([blob], filename, { type: blob.type });
        resolve(file);
      } else {
        reject(new Error(`Failed to download file: ${xhr.statusText}`));
      }
    };

    xhr.onerror = function () {
      reject(new Error("Network error occurred while downloading the file."));
    };

    // Send the request
    xhr.send();
  });
}

export async function getUploadedStorageLink (file: File, storagePath: string):Promise<string> {
  return new Promise((resolve, reject) => {
    const storageRef = referenece(storage, storagePath); // Reference for storage
    // loader.value = true;
    // Upload file on task with monitored progress
    const uploadTask = uploadBytesResumable(storageRef, file);
    // State_changed observer
    uploadTask.on('state_changed',
      () => {
        // Observe state change events such as progress, pause, and resume
        // Get task progress, including the number of bytes uploaded and the total number of bytes to be uploaded
        // const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        // uploadProgess.value = Math.round(progress);
      },
      (error) => {
        // Handle unsuccessful uploads
        console.log(error);
        console.log('Error message: ' + error);
        reject(error);
      },
      () => {
        // Handle successful uploads on complete
        console.log('successfully uploaded');
        getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
          console.log(`File available at ${downloadURL}`);
          resolve(downloadURL);
        });
      },
    );

  });
}

export function convertToArray (data:object, type:string){
  if (!data) {
    return [];
  }

  // Default "All" entry for each type
  const defaultEntry = { id: 'all', label: 'All', value: { name: 'All', label: 'All' } };

  const itemsArray = Object.values(data).map((item) => {
    if (type === 'tag') {
      return {
        id: item.value || 'unknown',
        label: item.value || 'Unknown Tag',
        value: item,
      };
    } else if (type === 'user' || type === 'project') {
      return {
        id: item.user_id || item.email || item.name || 'unknown',
        label: item.first_name || item.name || 'Unnamed User',
        value: item,
      };
    }
    return { id: 'unknown', label: 'Unknown', value: item };
  });
  return [defaultEntry, ...itemsArray];
}

// Check master scene or not via routes path
export function isMasterScenePath (path:string) {
  return path.includes(sceneTypeRoutes.MASTERSCENES);
}

export const processFirebaseFile = async (fileUrl: string): Promise<{ name: string; size: number; type: string; url: string } | null> => {
  const extractFileName = (url: string): string => {
    const segments = url.split('/');
    return segments[segments.length - 1].split('?')[0];
  };

  try {
    const response = await fetch(fileUrl);

    if (!response.ok) {
      throw new Error(`Failed to fetch file: ${response.statusText}`);
    }

    const blob = await response.blob();
    const blobURL = URL.createObjectURL(blob);

    return {
      name: extractFileName(fileUrl),
      size: blob.size,
      type: blob.type,
      url: blobURL,
    };
  } catch (error) {
    console.error("Error processing Firebase URL:", error);
    return null;
  }
};
export function truncateString (str:string, num:number) {
  if (str.length <= num) {
    return str;
  }
  return str.slice(0, num) + '...';
}

type GenericObject = Record<string, any>;

// Function to Deep Check the Two Objects
export const DeepCompareObjects = (sourceObj: GenericObject, compareObj: GenericObject): GenericObject => {
  const modifiedFields: GenericObject = {};

  const deepCompare = (source: GenericObject, compare: GenericObject, path = ""): void => {
    Object.keys(source).forEach((key) => {
      const fullPath = path ? `${path}.${key}` : key; // Track nested keys
      const sourceValue = source[key];
      const compareValue = compare ? compare[key] : undefined;

      if (Array.isArray(sourceValue) && Array.isArray(compareValue)) {
        if (JSON.stringify(sourceValue) !== JSON.stringify(compareValue)) {
          modifiedFields[key] = compareValue;
        }
      } else if (
        typeof sourceValue === "object" &&
        sourceValue !== null &&
        compareValue !== null &&
        typeof compareValue === "object"
      ) {
        deepCompare(sourceValue, compareValue, fullPath);
      } else if (sourceValue !== compareValue) {
        modifiedFields[key] = compareValue;
      }
    });
  };

  deepCompare(sourceObj, compareObj);

  return modifiedFields;
};
// Function to Format TimeStamp as 4:40 PM DEC 23
export function formatTimestamp (timestamp:Date) {
  const now = new Date(timestamp);

  const hours = now.getHours();
  const minutes = now.getMinutes().toString().padStart(2, "0"); // Ensure two-digit minutes
  const month = now.toLocaleString("en-US", { month: "short" });
  const day = now.getDate();

  return `${hours}:${minutes} ${month} ${day}`;
}

export function extractFirebaseStoragePath (url:String, key:String) {
  // console.log(url)
  if (typeof url === 'string') {
    if (url.includes("firebasestorage.googleapis.com")) {
      // Existing logic for Firebase Storage URLs
      const pathMatch = url.match(/\/o\/(.+)\?/);
      if (!pathMatch || !pathMatch[1]) {
        console.warn("Invalid Firebase Storage URL format:", url);
        return null;
      }

      const fullPath = decodeURIComponent(pathMatch[1]);
      const lastSlashIndex = fullPath.lastIndexOf('/');

      return key === 'filename'
        ? lastSlashIndex === -1 ? fullPath : fullPath.substring(lastSlashIndex + 1)
        : lastSlashIndex === -1 ? '' : fullPath.substring(0, lastSlashIndex);
    }

    if (url.includes("storage.googleapis.com")) {
      // Handling non-standard Firebase Storage URLs
      const urlParts = url.split('/');
      return key === 'filename' ? urlParts[urlParts.length - 1] : urlParts.slice(3, -1).join('/');
    }

    throw new Error('Invalid Firebase Storage URL');
  }

  return null; // Add explicit return for non-string inputs
}

// Compress Image Quality (based targeted mb)
export function compressImageQuality (file: File, targetMb: number, targetFormatType: string)  {

  return new Promise((resolve, reject) => {
    if (compressImageQualityFormats.includes(file.type) && compressImageQualityFormats.includes(targetFormatType) && targetMb ){
      let highestQuality = 1.0; //  Search range start with highest quality (1.0 / 1).
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (event) {
        const image = new Image();
        image.src = event.target?.result as string;

        image.onload = function (event) {
          console.log("image Load", event);

          // Create Canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d'); // create context

          if (!ctx) {
            return reject("Failed to get 2D context");
          }

          // Keep the original dimensional of image
          canvas.width = image.width;
          canvas.height = image.height;

          ctx.drawImage(image, 0, 0, image.width, image.height);

          // Create a Recursive Function
          function compress () {
            // in-built quality & format conversion mtd in canvas via blob
            canvas.toBlob((blob)  => {
              if (!blob){
                return reject("Blob creation is failed");
              }

              if (blob.size / (1024 * 1024) <= targetMb ){ // stop when targetMb reached or quality is end-least...
                return resolve(blob);
              }
              highestQuality -= 0.01; // Reduce quality gradually (0.01 for more precise & nearest to the target)
              compress();  // reduce quality & retry till the targetMb.
              return false;

            }, targetFormatType, highestQuality);
          }

          compress();

          return true;
        };
      };

      reader.onerror = (error) => reject(error);
    } else {
      reject( `File type or targetFormatType is not compressible type (i.e it should lies one of these types ${compressImageQualityFormats.join()}, (Or) targetMb is missing or 0 )`);
    }
  });

}

// Get Sort Array Recursively for NestedreOrders from Objects (SortBased)

export function getSortArrayFromObjects  (obj: GenericObject, childKey: string, addDraggableKey: boolean) {
  if (!obj) {
    return [];
  }

  return Object.values(obj).map((item) => {
    const cloneItem = {...item};
    if (addDraggableKey){
      cloneItem.draggable = true;
    }
    if (cloneItem[childKey]) {
      cloneItem[childKey] = [...getSortArrayFromObjects(cloneItem[childKey], childKey, addDraggableKey)];
    }
    return cloneItem;
  }).sort((item1, item2) => item1.order - item2.order);
}
