export enum unitStatus {
  AVAILABLE = 'available',
  ONHOLD = 'onhold',
  RESERVED = 'reserved',
  SOLD = 'sold'
}
export enum priceCurrency {
  USD = 'usd',
  GBP = 'gbp',
  INR = 'inr',
  AED = 'aed',
  EUR = 'eur',
  CAD = 'cad',
  NULL = ''
}
export enum measurementType {
  SQFT = 'sqft',
  SQMT = 'sqmt'
}
export type Units = {
  _id: string;
  unitplan_id: string;
  project_id:string;
  name: string;
  status: unitStatus;
  metadata: object;
  floor_id:string;
  building_id:string;
  price: string;
  community_id:string;
  currency: priceCurrency;
  tour_id: string;
  measurement?:number;
  measurement_type?: measurementType;
  cta_link?:string
};
export type updateUnits = {
  project_id:string;
  unitplan_id?: string;
  name?: string;
  status?: unitStatus;
  metadata?: object;
  floor_id?:string;
  building_id?:string;
  price?: string;
  community_id?:string;
  currency?: priceCurrency;
  tour_id?: string;
  measurement?:number;
  measurement_type?: measurementType;
  cta_link?:string
};

export type createUnits = {
  unitplan_id: string;
  project_id:string;
  name: string;
  status: unitStatus;
  metadata: object;
  floor_id:string;
  building_id:string;
  price: string;
  community_id:string;
  currency: priceCurrency;
  tour_id: string;
  measurement?:number;
  measurement_type?: measurementType;
  cta_link?:string
};

export type unitFilterQuery = {
  project_id?: string,
  min_price? : string,
  max_price? : string,
  unit_id?: string,
  floor?: string,
  building_id?: string,
  is_available?: boolean,
  unitplan_id?: string,
  status?: string,
  sortby?:string,
  orderby?:string,
  bedrooms?:string,
  min_area?:string,
  max_area?:string,
  unitplan_type?: string
  min_floor?: string,
  max_floor?:string
  style_types?:string
}

export type filterQuery = {
  community_id?: string,
  building_id?: string,
  floor_id?:string;
}

export type unitListResponse = {
  'totalCount': number,
  'searchCount': number,
  'data': object,
}

export type unitTrashType = {
  unit_id : string[],
  timetimeStamp: string
}
