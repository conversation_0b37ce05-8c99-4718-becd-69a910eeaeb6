<script setup>

import Spinner from '../common/Spinner.vue';
import { useRoute, useRouter } from 'vue-router';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { onMounted, ref, watch } from 'vue';
import formSchema from '../../validationSchema/scene/updateMasterLayerSchema';
import { validationSchema } from '../../validationSchema/scene/updateMasterLayerSchema';
import { removeUndefinedAndNullInObject } from '../../helpers/domhelper';
import { Org_Store } from '../../store/organization';
import { updateLayers } from '../../api/masterScene/svg/index';

const route = useRoute();
const props = defineProps({
  svgData: Object,
  scenes: Object,
});

const loader = ref(false);
const router = useRouter();
const svgId = ref(route.query.svgId);
const sceneId = ref(route.params.scene_id);
const layerId = ref(route.query.layerId);
const newOrganizationStore = Org_Store();

// Const findTypeRoute = () => {
//   Const routeList = [];
//   Object.values(props.svgData).map((svg) => {

//     Object.values(svg.layers).map((layer) => {
//       If (layer.type === 'route') {
//         RouteList.push({ _id: layer.layer_id, name: layer.layer_id });
//       }
//     });
//   });
//   Return routeList;
// };

const handleChange = (val) => {

  if (val === 'project') {
    if (formSchema.project_id.options.length === 0) {
      formSchema.project_id.options = Object.values(newOrganizationStore.projects).map((project) => ({ '_id': project._id, 'name': project.name }));
    }
  }
  if (val === 'scene' || val === 'pin') {
    if (formSchema.scene_id.options.length === 0) {
      formSchema.scene_id.options = Object.values(props.scenes).map(({ sceneData }) => ({ '_id': sceneData._id, 'name': sceneData.name }));
    }
  }
};

const handleInitialValues = () => {

  // Console.log(props.svgData);
  layerId.value = route.query.layerId;
  // Testing
  const layer = props.svgData[svgId.value].layers[layerId.value];
  formSchema.type.ref.value = layer.type ? layer.type : null;
  formSchema.project_id.ref.value = layer.project_id ? layer.project_id : null;
  formSchema.scene_id.ref.value = layer.scene_id ? layer.scene_id : null;
  formSchema.route_id.ref.value = layer.landmark?.route_id ? layer.landmark.route_id : null;
  formSchema.image_id.ref.value = layer.image_id ? layer.image_id : null;

  // Object.values(formSchema).map((elem)=>{
  //         If(layer.hasOwnProperty(elem.name)){
  //             FormSchema[elem.name].ref.value = layer[elem.name]
  //         }
  // })

  handleChange(formSchema.type.ref.value);

};

onMounted(() => {
  handleInitialValues();
});

watch(() => route.query.svgId, () => {
  svgId.value = route.query.svgId;
});

watch(() => route.query.layerId, () => {
  handleInitialValues();
});

watch(formSchema.type.ref, (val) => {
  console.log(val);
  handleChange(val);
});
const HandleUpdateSvgLayer = (query) => {
  loader.value = true;
  const obj = {
    layer_id: route.query.layerId,
    svg_id: route.query.svgId,
    query: query,
  };

  updateLayers(obj).then(() => {
    router.push({ path: route.path, query: { svgId: route.query.svgId } });
    window.location = `/masterscenes/${sceneId.value}?svgId=${svgId.value}`;
  }).catch((err) => {
    console.log('output->err in Update master Scene layer ', err);
  }).finally(() => {
    loader.value = false;
  });
};
const handelSubmit = (values) => {
  const obj = removeUndefinedAndNullInObject(values);
  console.log(values);
  console.log(obj);
  HandleUpdateSvgLayer(obj);

};

</script>

<template>
  <div
    class="relative transform overflow-hidden rounded-t-2xl rounded-b-none sm:rounded-t-lg sm:rounded-b-lg  text-left  transition-all sm:my-1 w-full h-full sm:max-w-md">
    <div
      class="flex justify-center items-center pt-2 sm:hidden">
      <div
        class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full">
      </div>
    </div>
    <div class="p-3 sm:p-6">
      <Form @submit="handelSubmit"
        :validation-schema="validationSchema">
        <div class="">
          <div class="flex flex-col gap-2 text-white">

            <!-- Type -->
            <div v-if="formSchema.type !== null"
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
              <label :for="formSchema"
                class="label-primary">{{
                  formSchema.type.label }}</label>
              <Field v-model="formSchema.type.ref.value"
                v-if="formSchema.type.as.toLowerCase() === 'select'"
                :as="formSchema.type.as"
                :id="formSchema.type.name"
                :name="formSchema.type.name"
                class="select-primary">
                <option value="" disabled>
                  Choose </option>
                <option value="" disabled
                  v-if="formSchema.type.options === null || formSchema.type.options.length === 0">
                  No Data found ! </option>
                <option v-else :value="option.value"
                  v-for="option, index in  formSchema.type.options"
                  :key="index" class="text-black"> {{
                    option.value }} </option>
              </Field>
              <ErrorMessage :name="formSchema.type.name"
                class="text-sm text-rose-500 mt-1" as="p" />
            </div>

            <!-- Others -->
            <div v-if="formSchema.type.ref.value">
              <div
                v-for="items in formSchema.type.options[formSchema.type.ref.value].toShow"
                :key="items.field" class="text-white">

                <div :key="formSchema[items.field].id"
                  :v-if="formSchema[items.field]"
                  class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                  <label :for="formSchema[items.field].name"
                    class="label-primary">{{
                      formSchema[items.field].label
                    }}</label>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    v-if="formSchema[items.field].as === 'select'"
                    :as="formSchema[items.field].as"
                    :id="formSchema[items.field].name"
                    :name="formSchema[items.field].name"
                    class="select-primary">
                    {{
                      formSchema[items.field].ref.value
                    }}
                    <option value='' class="text-gray-500">
                      Choose </option>
                    <option value="" disabled
                      v-if="formSchema[items.field].options === null || formSchema[items.field].options.length === 0">
                      No Data found !
                    </option>
                    <option v-else :value="option._id"
                      v-for="option, index in formSchema[items.field].options"
                      :key="index" class="text-black">
                      {{
                        option.name }}
                    </option>
                  </Field>
                  {{
                    formSchema[items.field].ref.value
                  }}
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>

              </div>
            </div>

            <div
              class="text-center mt-3 flex justify-end items-center">
              <!-- <button @click="handleCloseModal()"
                            class="w-1/2 sm:w-fit h-11 sm:h-10 rounded-full sm:rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent m-0 px-3 text-xs text-white font-semibold leading-6">Discard</button> -->
              <button id="submit" type="submit"
                class="proceed-btn-primary">Save
                Changes
                <Spinner v-if="loader" />
              </button>
            </div>
          </div>
        </div>
      </Form>
    </div>
  </div>
</template>

<style scoped>
/* width */
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>
