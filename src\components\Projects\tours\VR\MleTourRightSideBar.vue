<script setup>
import * as slider from '@zag-js/slider';
import { normalizeProps, useMachine } from '@zag-js/vue';
import { computed, ref } from 'vue';

const positionXRef= ref(null);
const positionYRef= ref(null);
const positionZRef= ref(null);
const [positionXstate, posXsend] = useMachine(
  slider.machine({
    id: 'positionx',
    name: 'positionx',
    value: [500.125], // Default value
    min: 100,
    max: 1000,
    step: 0.001,
  }),
);
const positionX = ref(positionXstate.value.context.value);
const [positionYstate, posYsend] = useMachine(
  slider.machine({
    id: 'positiony',
    name: 'positiony',
    value: [500.125], // Default value
    min: 100,
    max: 1000,
    step: 0.001,
  }),
);
const positionY = ref(positionYstate.value.context.value);
const [positionZstate, posZsend] = useMachine(
  slider.machine({
    id: 'positionz',
    name: 'positionz',
    value: [500.125], // Default value
    min: 100,
    max: 1000,
    step: 0.001,
  }),
);
const positionZ = ref(positionZstate.value.context.value);
const isAdvanceSettingOpen = ref(false);
const advanceContentRef = ref(null);
const advanceContentheight = ref('0');
/* Methods */

// Computed
const positionXComputed = computed(() => slider.connect(positionXstate.value, posXsend, normalizeProps));
const positionYComputed = computed(() => slider.connect(positionYstate.value, posYsend, normalizeProps));
const positionZComputed = computed(() => slider.connect(positionZstate.value, posZsend, normalizeProps));

// Toggler
const toggleAdvanceSettings = async () => {
  if (!isAdvanceSettingOpen.value){
    advanceContentheight.value = advanceContentRef.value.scrollHeight;
    isAdvanceSettingOpen.value = true;
  } else {
    advanceContentheight.value = '0';
    isAdvanceSettingOpen.value = false;
  }
};

</script>

<template>
    <div class="h-full w-full bg-white rounded-t-lg p-2 flex flex-col justify-start items-start gap-3">
        <div class="border-b border-gray-400 py-1 w-full">
            <h5 class="text-[#1C64F2] text-xs font-medium border-b border-[#1C64F2] leading-none w-fit py-1 -mb-[5px] select-none"> Properties </h5>
        </div>

        <!-- Advance Setting -->
        <div class="flex flex-col justify-start items-start w-full gap-3">
            <div class="flex justify-between items-center w-full">
                         <h5 class="text-gray-900 text-sm font-medium select-none"> Advance Setting</h5>
                        <svg :class="['w-3 h-3 transition-transform', isAdvanceSettingOpen ? 'rotate-180' : 'rotate-0' ]" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg" @click="toggleAdvanceSettings">
                        <path d="M5.01106 7.5C4.77238 7.49995 4.5435 7.4123 4.37476 7.25633L0.77476 3.92981C0.6888 3.8531 0.620236 3.76133 0.573068 3.65987C0.5259 3.55841 0.501072 3.44928 0.500034 3.33886C0.498996 3.22843 0.521767 3.11892 0.56702 3.01672C0.612272 2.91452 0.6791 2.82166 0.763604 2.74358C0.848107 2.6655 0.948594 2.60374 1.0592 2.56193C1.16981 2.52011 1.28832 2.49907 1.40782 2.50003C1.52732 2.50099 1.64542 2.52393 1.75522 2.56752C1.86503 2.6111 1.96434 2.67446 2.04736 2.75389L5.01106 5.49245L7.97476 2.75389C8.1445 2.6024 8.37184 2.51858 8.60782 2.52047C8.8438 2.52237 9.06953 2.60983 9.23639 2.76402C9.40326 2.91821 9.49792 3.12679 9.49997 3.34484C9.50202 3.56289 9.4113 3.77296 9.24736 3.92981L5.64736 7.25633C5.47862 7.4123 5.24973 7.49995 5.01106 7.5Z" fill="#111928"/>
                        </svg>
            </div>
            <div :style="{height: `${advanceContentheight}px`}"  class="w-full overflow-hidden transition-all">
                <div ref="advanceContentRef" class="flex flex-col justify-start items-start gap-3 w-full select-none">
                    <p class="text-gray-900 text-sm font-medium w-full flex items-center justify-start gap-2"> Position <svg class="w-3 h-3" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g >
                                <path d="M5 0C4.0111 0 3.0444 0.293245 2.22215 0.842652C1.3999 1.39206 0.759043 2.17295 0.380605 3.08658C0.00216642 4.00021 -0.0968502 5.00555 0.0960758 5.97545C0.289002 6.94535 0.765206 7.83627 1.46447 8.53553C2.16373 9.23479 3.05465 9.711 4.02455 9.90392C4.99445 10.0969 5.99979 9.99783 6.91342 9.6194C7.82705 9.24096 8.60794 8.60009 9.15735 7.77785C9.70675 6.9556 10 5.9889 10 5C9.99854 3.67436 9.47129 2.40344 8.53393 1.46607C7.59656 0.528707 6.32564 0.0014558 5 0ZM4.75 2C4.89834 2 5.04334 2.04399 5.16668 2.1264C5.29002 2.20881 5.38615 2.32594 5.44291 2.46299C5.49968 2.60003 5.51453 2.75083 5.48559 2.89632C5.45665 3.0418 5.38522 3.17544 5.28033 3.28033C5.17544 3.38522 5.04181 3.45665 4.89632 3.48559C4.75083 3.51453 4.60003 3.49967 4.46299 3.44291C4.32594 3.38614 4.20881 3.29001 4.1264 3.16668C4.04399 3.04334 4 2.89834 4 2.75C4 2.55109 4.07902 2.36032 4.21967 2.21967C4.36032 2.07902 4.55109 2 4.75 2ZM6 7.5H4C3.86739 7.5 3.74022 7.44732 3.64645 7.35355C3.55268 7.25978 3.5 7.13261 3.5 7C3.5 6.86739 3.55268 6.74021 3.64645 6.64644C3.74022 6.55268 3.86739 6.5 4 6.5H4.5V5H4C3.86739 5 3.74022 4.94732 3.64645 4.85355C3.55268 4.75978 3.5 4.63261 3.5 4.5C3.5 4.36739 3.55268 4.24021 3.64645 4.14645C3.74022 4.05268 3.86739 4 4 4H5C5.13261 4 5.25979 4.05268 5.35355 4.14645C5.44732 4.24021 5.5 4.36739 5.5 4.5V6.5H6C6.13261 6.5 6.25979 6.55268 6.35355 6.64644C6.44732 6.74021 6.5 6.86739 6.5 7C6.5 7.13261 6.44732 7.25978 6.35355 7.35355C6.25979 7.44732 6.13261 7.5 6 7.5Z" fill="#6B7280"/>
                                </g>
                                </svg>
                    </p>

                    <!-- Drop -->
                    <div class="flex flex-col justify-start items-start gap-3">
                            <div class="flex flex-col justify-start items-start gap-2">
                                <label for="" class="text-gray-500 text-xs font-medium mb-0">X Position*</label>
                                <input type="text" class="text-gray-500 text-xs font-normal px-2 py-2 bg-gray-100 rounded-lg outline-none border-none" v-model="positionX" disabled/>
                                <!-- Slider (X) -->
                                <div ref="positionXRef" v-bind="positionXComputed.getRootProps()">
                                                            <div v-bind="positionXComputed.getControlProps()">
                                                                <div v-bind="positionXComputed.getTrackProps()">
                                                                <div v-bind="positionXComputed.getRangeProps()" />
                                                                </div>
                                                                <div
                                                                v-for="(_, index) in positionXComputed.value"
                                                                :key="index"
                                                                v-bind="positionXComputed.getThumbProps({ index })"
                                                                >
                                                                <input v-bind="positionXComputed.getHiddenInputProps({ index })" />
                                                                </div>
                                                            </div>
                                </div>
                            </div>
                    </div>

                    <div class="flex flex-col justify-start items-start gap-2">
                            <div class="flex flex-col justify-start items-start gap-2">
                                <label for="" class="text-gray-500 text-xs font-medium mb-0">Y Position*</label>
                                <input type="text" class="text-gray-500 text-xs font-normal px-2 py-2 bg-gray-100 rounded-lg outline-none border-none" v-model="positionY" disabled/>
                                <!-- Slider (Y) -->
                                <div ref="positionYRef" v-bind="positionYComputed.getRootProps()">
                                                            <div v-bind="positionYComputed.getControlProps()">
                                                                <div v-bind="positionYComputed.getTrackProps()">
                                                                <div v-bind="positionYComputed.getRangeProps()" />
                                                                </div>
                                                                <div
                                                                v-for="(_, index) in positionYComputed.value"
                                                                :key="index"
                                                                v-bind="positionYComputed.getThumbProps({ index })"
                                                                >
                                                                <input v-bind="positionYComputed.getHiddenInputProps({ index })" />
                                                                </div>
                                                            </div>
                                </div>
                            </div>
                    </div>

                <div class="flex flex-col justify-start items-start gap-2">
                            <div class="flex flex-col justify-start items-start gap-2">
                                <label for="" class="text-gray-500 text-xs font-medium mb-0">Z Position*</label>
                                <input type="text" class="text-gray-500 text-xs font-normal px-2 py-2 bg-gray-100 rounded-lg outline-none border-none" v-model="positionZ" disabled/>
                                <!-- Slider (Z) -->
                                <div ref="positionZRef" v-bind="positionZComputed.getRootProps()">
                                                            <div v-bind="positionZComputed.getControlProps()">
                                                                <div v-bind="positionZComputed.getTrackProps()">
                                                                <div v-bind="positionZComputed.getRangeProps()" />
                                                                </div>
                                                                <div
                                                                v-for="(_, index) in positionZComputed.value"
                                                                :key="index"
                                                                v-bind="positionZComputed.getThumbProps({ index })"
                                                                >
                                                                <input v-bind="positionZComputed.getHiddenInputProps({ index })" />
                                                                </div>
                                                            </div>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>

</style>

<style>
/* Range Slider */
[data-scope="slider"][data-part="root"] {
  width: 100%;
}
[data-scope="slider"][data-part="root"] > div:first-child {
  display: flex;
  justify-content: space-between;
}
[data-scope="slider"][data-part="label"] {
  margin-right: 0.5rem;
}
[data-scope="slider"][data-part="control"] {
  display: flex;
  align-items: center;
  margin-top: 0rem;
  position: relative;
  padding-block: 0.625rem;
}
[data-scope="slider"][data-part="track"] {
  height: 8px;
  border-radius: 9999px;
  flex: 1;
  background: #A4CAFE;
}
[data-scope="slider"][data-part="range"] {
  height: 100%;
  border-radius: inherit;
  background: #1C64F2;
}
[data-scope="slider"][data-part="thumb"] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 9999px;
  background: #1C64F2;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
[data-scope="slider"][data-part="thumb"]:is(:focus, [data-focus]) {
  outline: none;
}
[data-scope="slider"][data-part="thumb"][data-disabled] {
  background: #e2e8f0;
}

</style>
