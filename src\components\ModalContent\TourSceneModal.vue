<script setup>
import { <PERSON>u, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { ChevronDownIcon } from '@heroicons/vue/20/solid';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { AddSceneToTour } from '../../api/projects/tours';
// Import { getCookie } from "../../helpers/domhelper";
import Modal from '../common/Modal/Modal.vue';
const route = useRoute();

const router = useRouter();
const thumbnail = ref();
const svgType = ref();
const titleRef = ref();
const yaw = ref();
const pitch = ref();
const hfov = ref();
const errorMessage = ref();
const tourId = ref(route.params.tour_id);

const projectId = ref(route.params.project_id);
const typeList = ['equirectangular', 'cubemap', 'multires'];
const handleAddToSceneTour = (formData) => {
  AddSceneToTour(formData)
    .then(() => {
      router.go(-1);
    })
    .catch((err) => {
      console.log(err);
    });
};
const handleFileChange = (event) => {
  thumbnail.value = event.target.files[0];
};
const HandleAddSvg = () => {
  if (thumbnail.value && svgType.value) {
    const formData = new FormData();

    formData.append('thumbnail', thumbnail.value);
    formData.append('scene_type', svgType.value);
    formData.append('title', titleRef.value);
    formData.append('yaw', yaw.value);
    formData.append('pitch', pitch.value);
    formData.append('hfov', hfov.value);
    formData.append('tour_id', tourId.value);
    formData.append('project_id', projectId.value);

    errorMessage.value = null;

    handleAddToSceneTour(formData);
  } else {
    errorMessage.value = 'Enter all Fields to continue';
  }
};
</script>

<template>
  <Modal :open="true">
    <div
      class="modal-content-primary"
    >
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Add Tour Scene</h1>
          <p class="modal-subheading-primary">Add details to your Tour Scene</p>
        </div>
        <form action="" class="flex flex-col justify-center">
          <div class="grid grid-cols-2 gap-3">
            <div class="col-span-full mt-3">
              <label
                for="street-address"
                class="label-primary"
                >Upload panorama</label
              >
              <div class="mt-2">
                <input
                  type="file"
                  name="thumbnail"
                  id="thumbnail"
                  autocomplete="thumbnail"
                  @change="handleFileChange"
                  class="input-primary"
                  placeholder="Enter Project Name"
                />
              </div>
            </div>
            <Menu
              as="div"
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
            >
              <label
                class="text-[#F5F5F5] leading-6 text-sm font-semibold mb-2 ml-1 w-full"
              >
                Type
              </label>
              <div class="w-full">
                <MenuButton
                  style="border: 1px solid #737373"
                  class="dropdown-btn"
                >
                  {{ svgType ? svgType : "Select Type" }}
                  <ChevronDownIcon
                    class="-mr-1 h-5 w-5 text-gray-400 ml-1.5"
                    aria-hidden="true"
                  />
                </MenuButton>
              </div>

              <transition
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <MenuItems
                  class="absolute -bottom-20 w-40 h-20 overflow-auto right-0 z-50 mt-2 rounded-md bg-neutral-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                >
                  <div class="">
                    <MenuItem v-for="(type, typeId) in typeList" :key="typeId">
                      <a
                        href="#"
                        @click="() => (svgType = type)"
                        class="text-gray-300 block p-2 py-1.5 text-sm hover:bg-neutral-400 hover:text-blue-200 whitespace-nowrap text-ellipsis overflow-x-hidden"
                      >
                        {{ type }}</a
                      >
                    </MenuItem>
                  </div>
                </MenuItems>
              </transition>
            </Menu>
            <div class="col-span-auto">
              <label
                for="titleRef"
                class="label-primary"
              >
                Title</label
              >
              <div class="mt-2">
                <input
                  type="text"
                  name="titleRef"
                  id="titleRef"
                  v-model="titleRef"
                  class="input-primary"
                  :placeholder="`Enter Title`"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label
                for="yaw"
                class="label-primary"
              >
                Yaw Angle</label
              >
              <div class="mt-2">
                <input
                  type="number"
                  name="yaw"
                  id="yaw"
                  v-model="yaw"
                  class="input-primary"
                  :placeholder="`Enter Yaw Angle`"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label
                for="pitch"
                class="label-primary"
              >
                Pitch Number</label
              >
              <div class="mt-2">
                <input
                  type="number"
                  name="pitch"
                  id="pitch"
                  v-model="pitch"
                  class="input-primary"
                  :placeholder="`Enter Pitch Number`"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label
                for="hfov"
                class="label-primary"
              >
                horizontal field view number</label
              >
              <div class="mt-2">
                <input
                  type="number"
                  name="hfov"
                  id="hfov"
                  v-model="hfov"
                  class="input-primary"
                  :placeholder="`Enter hfov number`"
                />
              </div>
            </div>
          </div>

          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="
                () => {
                  router.go(-1);
                }
              "
              ref="cancelButtonRef"
            >
              Cancel
            </button>
            <button
              type="button"
              class="proceed-btn-primary"
              @click="HandleAddSvg"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>
