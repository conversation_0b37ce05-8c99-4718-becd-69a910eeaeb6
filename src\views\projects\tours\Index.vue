<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
// import SideBar from '../../../components/Projects/SideBar.vue';
import ProjectTours from '../../../components/Projects/tours/Index.vue';
import { UserStore } from '../../../store/index';
import DesignMenuBar from '@/components/scenes/DesignMenuBar.vue';

const userStore = UserStore();
</script>

<template>
   <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar />
        <div
            class="h-full overflow-hidden w-full">
            <div v-if="userStore.user_data"
                class="pt-0 h-full overflow-y-auto w-full flex-1 bg-gray-100 flex flex-col overflow-hidden">
                   <div class="flex justify-evenly h-full w-full overflow-hidden gap-0 border bg-transparent">
                    <DesignMenuBar/>
                    <ProjectTours />
                   </div>
                   <router-view></router-view>
            </div>
        </div>
    </div>
</template>
