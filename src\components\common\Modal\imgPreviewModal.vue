<template>
    <transition name="fade">
        <div v-if="isOpen"
            class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
            @click="emit('close')">
            <div class="relative max-w-4xl w-full" @click.stop>
                <!-- Close button -->
                <button @click="emit('close')" class="absolute -top-10 -right-10 text-white focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <g id="x">
                            <rect width="32" height="32" rx="16" fill="currentcolor" />
                            <path id="Vector"
                                d="M17.8148 16L24.6082 9.20664C24.7308 9.08824 24.8285 8.94662 24.8958 8.79003C24.9631 8.63345 24.9985 8.46503 24.9999 8.29461C25.0014 8.1242 24.969 7.95519 24.9044 7.79746C24.8399 7.63973 24.7446 7.49642 24.6241 7.37592C24.5036 7.25541 24.3603 7.16011 24.2025 7.09557C24.0448 7.03104 23.8758 6.99857 23.7054 7.00005C23.535 7.00153 23.3666 7.03694 23.21 7.1042C23.0534 7.17147 22.9118 7.26924 22.7934 7.39183L16 14.1852L9.20664 7.39183C8.96458 7.15803 8.64037 7.02867 8.30385 7.03159C7.96733 7.03452 7.64542 7.1695 7.40746 7.40746C7.1695 7.64542 7.03452 7.96733 7.03159 8.30385C7.02867 8.64037 7.15803 8.96458 7.39183 9.20664L14.1852 16L7.39183 22.7934C7.26924 22.9118 7.17147 23.0534 7.1042 23.21C7.03694 23.3666 7.00153 23.535 7.00005 23.7054C6.99857 23.8758 7.03104 24.0448 7.09557 24.2025C7.16011 24.3603 7.25541 24.5036 7.37592 24.6241C7.49642 24.7446 7.63973 24.8399 7.79746 24.9044C7.95519 24.969 8.1242 25.0014 8.29461 24.9999C8.46503 24.9985 8.63345 24.9631 8.79003 24.8958C8.94662 24.8285 9.08824 24.7308 9.20664 24.6082L16 17.8148L22.7934 24.6082C23.0354 24.842 23.3596 24.9713 23.6961 24.9684C24.0327 24.9655 24.3546 24.8305 24.5925 24.5925C24.8305 24.3546 24.9655 24.0327 24.9684 23.6961C24.9713 23.3596 24.842 23.0354 24.6082 22.7934L17.8148 16Z"
                                fill="#9CA3AF" />
                        </g>
                    </svg>
                </button>

                <!-- Image container -->
                <div class="bg-white rounded-lg overflow-hidden">
                    <img :src="imageUrl" alt="Preview" class="w-full h-[85vh] object-contain" loading="lazy" />
                </div>
            </div>
        </div>
    </transition>
</template>

<script setup>
defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  imageUrl: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['close']);

</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
