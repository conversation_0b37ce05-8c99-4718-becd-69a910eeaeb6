import { fileValidation, imageSizeValidation } from '@/helpers/validationSchemaHelpers';
import * as yup from 'yup';

export const unitplanSchema = yup.object({
  type: yup
    .object()
    .nullable()
    .optional(),
  name: yup
    .string()
    .required("Unitplan name is required"),
  file: yup.mixed().when('unit_type', {
    is: (val: string) => val !== 'villa',
    then: () =>
      yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => {
        if (typeof value === 'string') {
          return true;
        } // Allow URLs
        return fileValidation(value as File, 'image'); // Validate only Files
      }).test('file', 'Image size is more than 2MB', (value) => {
        if (typeof value === 'string') {
          return true;
        } // Allow URLs
        return imageSizeValidation(value as File);
      }),
    otherwise: () => yup.string().nullable(),
  }),
  measurement: yup
    .number()
    .typeError('value must be a number')
    .transform((value, originalValue) => (originalValue === "" ? null : Number(originalValue)))
    .when('unit_type', {
      is: (val: string) => val !== 'villa_floor',
      then: (schema) => schema.required("Measurement is required"),
      otherwise: (schema) => schema.optional().nullable(),
    }),
  measurement_type: yup
    .string()
    .when('unit_type', {
      is: (val: string) => val !== 'villa_floor',
      then: (schema) => schema.required("Measurement type is required"),
      otherwise: (schema) => schema.optional().nullable(),
    }),
  bedrooms: yup
    .string()
    .when('unit_type', {
      is: (val: string) => val !== 'villa_floor',
      then: (schema) => schema.required("Bedrooms are required"),
      otherwise: (schema) => schema.optional().nullable(),
    }),
  is_residential: yup
    .string(),
  bathrooms: yup
    .number()
    .nullable()
    .transform((value, originalValue) => (originalValue === "" ? null : Number(originalValue)))
    .typeError('Value must be a number')
    .optional(),
  is_furnished: yup
    .string(),
  unit_type: yup
    .string()
    .required("Unit type is required"),
  balcony_measurement: yup
    .number()
    .nullable()
    .transform((value, originalValue) => (originalValue === "" ? null : value))
    .typeError('Value must be a number')
    .optional(),
  balcony_measurement_type: yup
    .string()
    .nullable()
    .optional(),
  style: yup
    .object()
    .nullable()
    .optional(),
  tour_id: yup
    .string()
    .nullable()
    .optional(),
  exterior_type: yup
    .string()
    .transform((value) => (value === "" ? null : value))
    .nullable()
    .optional(),
  scene_id: yup
    .string()
    .when('exterior_type', {
      is: (val: string) => val === 'scene',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
  gallery_id: yup
    .array()
    .when('exterior_type', {
      is: (val: string) => val === 'gallery',
      then: () => yup.array().required(),
      otherwise: () => yup.array().nullable(),
    }),
});
