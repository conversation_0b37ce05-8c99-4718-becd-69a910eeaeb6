<script setup>
import { ProjectStore } from '@/store/project';
import { ref, watch} from 'vue';
import { useRoute } from 'vue-router';
import CustomTourLeftSideBar from './CustomTourLeftSideBar.vue';
import MleTourLeftSideBar from './MleTourLeftSideBar.vue';
import CustomTour from './CustomTour.vue';
import MleTour from './MleTour.vue';
import { Org_Store } from '@/store/organization';
import MleTourRightSideBar from './MleTourRightSideBar.vue';
import SafeAreaHighlight from '@/components/scenes/SafeAreaHighlight.vue';
import CustomTourRightSideBar from './CustomTourRightSideBar.vue';

const route = useRoute();
const newOrganizationStore = Org_Store();
const projectStore =  ProjectStore();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const safeFrame = ref(false);

/* MLE */
const openHotspot = ref(false); // Label (Hotspot Create)
const hotspotType = ref(null); // Type of hotspot
const trackHotspotDetails = ref(null); // Other Details

// API
newOrganizationStore.RefreshProjects();
projectStore.RefreshVirtualTours(projectId.value);

// Watch (Only for Mle)
watch(() => route.query.image_id, (val, old) => {
  if (projectStore.virtualtours[tourId.value].type === 'MLE' && val && val !== old && openHotspot.value){ // If hotspot present & Only
    console.log(trackHotspotDetails.value);
    /*    if(trackHotspotDetails.value.camera_name !== val){ // If differs
              openHotspot.value = false;
              hotspotType.value = null;
              trackHotspotDetails.value = null;
       } */
  }
});

</script>

<template>
  <div class="relative w-full h-full flex justify-start items-start gap-2 pt-2 bg-transparent px-2" v-if="projectStore.virtualtours">
    <!-- Left-SideBar -->
    <div class="h-full w-[200px] shrink-0">
       <CustomTourLeftSideBar v-if="(projectStore.virtualtours[tourId].type ?? projectStore.virtualtours[tourId].virtualtour_type) === 'custom'"/>
       <MleTourLeftSideBar v-else-if="(projectStore.virtualtours[tourId].type ?? projectStore.virtualtours[tourId].virtualtour_type) === 'MLE'" @openHotspot="() => {openHotspot = true;}" @closeHotspot="() => {openHotspot = false;}" />
       <div v-else class="h-full w-full flex flex-col justify-start items-start gap-3 bg-white p-2 relative py-3 rounded-t-lg">

       </div>
    </div>

    <!-- VR -->
    <div class="flex-1 h-full overflow-hidden flex flex-col justify-start items-start bg-white gap-2">
        <div class="w-full flex-1">
            <div class="bg-neutral-900 h-full w-full overflow-hidden">
                  <div class="h-full flex justify-center items-center text-white relative z-0">
                    <SafeAreaHighlight :show="safeFrame" :safePadding="{ top: '70px', bottom: '70px', left: '80px', right: '80px' }">
                              <CustomTour v-if="(projectStore.virtualtours[tourId].type ?? projectStore.virtualtours[tourId].virtualtour_type) === 'custom'"/>
                              <MleTour v-else-if="(projectStore.virtualtours[tourId].type ?? projectStore.virtualtours[tourId].virtualtour_type) === 'MLE'" :openHotspot="openHotspot" />
                              <div v-else class="w-full h-full flex flex-col justify-center items-center text-center text-white bg-black">
                                    Other tour type
                              </div>
                    </SafeAreaHighlight>
                  </div>
              </div>
        </div>
        <!-- Bottom -->
        <div class="h-[90px] shrink-0 w-full flex flex-row justify-end items-center px-3 bg-white">
          <div class="flex justify-between items-center gap-2">
            <div class="flex justify-between items-center gap-2 rounded-b-md cursor-pointer">
                <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                    <input id="safe_area" v-model="safeFrame" class="sr-only peer" name="safe_area" type="checkbox" :value="true" />
                    <label for="safe_area" class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:rounded-full after:h-[19px] after:w-[19px] after:transition-all peer-checked:bg-blue-600 cursor-pointer">
                    </label>
                </div>
                <label for="safe_area" class="text-sm text-gray-900 mb-0">Safe Frame</label>
            </div>
            <svg class="h-4 w-4 fill-gray-500" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_723_16947)">
          <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z"/>
          </g>
          <defs>
          <clipPath id="clip0_723_16947">
          <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
          </clipPath>
          </defs>
            </svg>
          </div>
        </div>
    </div>

    <!-- Right-SideBar -->
    <div class="h-full w-[200px] shrink-0">
        <CustomTourRightSideBar v-if="(projectStore.virtualtours[tourId].type ?? projectStore.virtualtours[tourId].virtualtour_type) === 'custom'" :openHotspotForm="openHotspot" :formType="hotspotType" @closeHotspotsForm = "() => {openHotspot = false; hotspotType = null;}" :otherFields="trackHotspotDetails"/>
        <MleTourRightSideBar v-else-if="(projectStore.virtualtours[tourId].type ?? projectStore.virtualtours[tourId].virtualtour_type) === 'MLE'"/>
        <div v-else class="h-full w-full bg-white rounded-t-lg p-2 flex flex-col justify-start items-start gap-3">

        </div>
    </div>
  </div>
</template>

<style scoped>

</style>
