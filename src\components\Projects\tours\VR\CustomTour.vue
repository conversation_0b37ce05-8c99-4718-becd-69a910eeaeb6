<script setup>
import { ProjectStore } from '@/store/project';
import { onMounted, onUnmounted, ref, watch, nextTick} from 'vue';
import { useRoute } from 'vue-router';
import Multiselect from 'vue-multiselect';
import Button from '../../../common/Button.vue';
import Spinner from '../../../common/Spinner.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { tourHotspotSchema } from '@/validationSchema/tour/index';
import { addLink, deleteLink, updateLink } from '@/api/projects/tours';
import { cdn } from '../../../../helpers/index.ts';

const route = useRoute();
const projectStore =  ProjectStore();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const imageId = ref(route.query.image_id);
const isTextureLoaded = ref(false);
const SceneTag = ref(null);
const defaultHotspotIcon = "https://storagecdn.propvr.tech/assets%2Fhotspot.svg";
const activeHotspotIcon = "https://storagecdn.propvr.tech/assets%2Fhotspotedit.svg";
const hotspotToggler = ref({
  isOpen: false,
  type: null,
});
const initialHotspotFormData = ref({
  text: null,
  x: null,
  y: null,
  z: null,
  destination_img_id: null,
});
const currentPositions = ref({
  x: null,
  y: null,
  z: null,
});
const hotspotFormLoader = ref({
  delete: false,
  save: false,
});
const selectedDestinationImgId = ref(null);
const currentDraggableElRef = ref(null); // Current Draggable (Edit)
const getTheDblClickPostion = ref(null);
/* Methods */

// Configure
const setConfigure = () => {
  if (SceneTag.value){
    console.log("setConfigure", SceneTag.value);
    const camera = document.getElementById('player');
    const aSKy = SceneTag.value.querySelector('a-sky');
    const getRotationDegrees = projectStore.virtualtours[tourId.value].images[imageId.value].rotation.split(' '); // XYZ
    console.log(projectStore.virtualtours[tourId.value]);
    const polarDeg = Number(getRotationDegrees[0]); // X (Vertical) - pitch
    const azimuthDeg = Number(getRotationDegrees[1]); // Y (Horziontal) - yaw
    console.log(getRotationDegrees);
    console.log(polarDeg, azimuthDeg);

    if (aSKy && camera){
      const src = cdn(projectStore.virtualtours[tourId.value].images[imageId.value].url); // get the image url and convert to cdn
      console.log(src, projectStore.virtualtours[tourId.value].images[imageId.value].url);
      const separator = src.includes("?") ? "&" : "?"; // get the separator
      const newSrc = `${src}${separator}t=${new Date().getTime()}`;       // Append a random query parameter to find this src is unique to avoid aframe optimize assets logic
      // set url (360 img)
      aSKy.setAttribute('src', newSrc); // url
      // set orientation
      const lookControls = camera.components["look-controls"];
      console.log(lookControls)
      if (lookControls) {
        lookControls.pitchObject.rotation.x = THREE.Math.degToRad(polarDeg); // Polar angle
        lookControls.yawObject.rotation.y = THREE.Math.degToRad(azimuthDeg); // Azimuth angle
      }

      console.log(lookControls);

    }
  }
};

// Watch for changes in route query
watch(() => route.query.image_id, async (val, oldVal) => {
  console.log("Yes Watched: " + route.query.image_id);
  isTextureLoaded.value = false;
  if (val){
    imageId.value = val;
  } else {
    imageId.value = null;
  }
  // On going-flow only. Not for the initial
  if (val && oldVal){
    setConfigure();
  }
}, {deep: true});

// Compare Values
const compareValuesFromSource = (source, compare) => {
  const sourceValues = {...source}; // form values
  const compareValues = {...compare}; // previous values
  let newComparedValues;

  // Check if any values in source are different from compare values
  for (const key in sourceValues) {
    if (sourceValues[key] !== compareValues[key]) {
      console.log(key);

      // different
      if (!newComparedValues){
        newComparedValues = {};
      }

      if (key === 'x' || key === 'y' || key === 'z'){
        console.log("Yes");
        if (!newComparedValues.position) {
          newComparedValues.position = {};
        }
        newComparedValues.position[key] = sourceValues[key];
      } else {
        newComparedValues[key] = sourceValues[key];
      }
    }
  }

  return newComparedValues;
};

// Reset the Hotspot Toggler & Form values (Close/Reset)
const handleCloseHotspotToggler = () => {
  console.log("handleCloseHotspotToggler");
  // Reset the current position, destination_imageId & initial hotspot Data
  initialHotspotFormData.value = {
    text: null,
    x: null,
    y: null,
    z: null,
    destination_img_id: null,
  };
  currentPositions.value = {
    x: null,
    y: null,
    z: null,
  };
  selectedDestinationImgId.value = null;
  getTheDblClickPostion.value = null;
  // Close the hotspot Toggler
  hotspotToggler.value = {
    isOpen: false,
    type: null,
  };
};

// Add Hotspots
const addHotspotIntoViewArea = (addEl, camera_El) => {
  console.log("addHotspotIntoViewArea", addEl, camera_El);

  const hotspotEl = addEl; // add hotspot a-image
  const cameraEl = camera_El; // camera El of scene

  const cameraWorldPosition = new THREE.Vector3();  // Get the camera's world position
  cameraEl.object3D.getWorldPosition(cameraWorldPosition);
  console.log("Camera world position:", cameraWorldPosition);
  const direction = new THREE.Vector3(); // Get direction - either from double-click or camera's current direction

  const basePoint = getTheDblClickPostion.value; // dbl clicked point
  if (basePoint) { // check
    console.log("Create Case", basePoint);
    direction.copy(basePoint); // Use the direction from double-click
  }
  /*  else { // Fallback
    console.log("Else case");

    // Fallback to your original method (camera's current direction)
    cameraEl.object3D.getWorldDirection(direction);
    direction.negate(); // Invert direction so it moves FORWARD from camera (not backward)
    console.log("Using camera direction (fallback):", direction);
  } */

  const cameraNearClippingPlane = 5.0; // Default distance

  const hotspotPosition = cameraWorldPosition.clone().add(direction.multiplyScalar(cameraNearClippingPlane));  // Calculate hotspot position
  console.log("Final hotspot position:", hotspotPosition);

  // Set position
  hotspotEl.setAttribute('position', {
    x: String(hotspotPosition.x),
    y: String(hotspotPosition.y),
    z: String(hotspotPosition.z),
  });

  return hotspotPosition;
};

// Bound to Drag Element to get seemlessly updated positions
const handleTrackDragging = (e) => {
  if (e){
    const positions = e.target.object3D.getWorldPosition();
    if (positions){
      currentPositions.value.x = String(positions.x);
      currentPositions.value.y = String(positions.y);
      currentPositions.value.z = String(positions.z);
    }
  }
};

// Add Draggable and set a previous for the current position
const addDraaggableAndUpdate = (element) => {
  console.log('Hotspot clicked!');
  const el = element;
  if (!JSON.parse(el.getAttribute('data-isDragging'))){
    const labelEntity = el.querySelector('.hotspot-label');
    const hotspotId = el.getAttribute('data-Id'); // get the hotspot id
    console.log("isDragging, clicked!");
    el.setAttribute('data-isDragging', 'true');
    currentDraggableElRef.value = el;
    labelEntity.setAttribute('visible', false); // make the label invisible
    el.setAttribute('click-drag', true); // make click & drag
    el.setAttribute('src', "#activeHotspotIcon"); // add the active Hotspot
    el.addEventListener("mouseup", handleTrackDragging);// add event-listener
    // Set all the form values
    hotspotToggler.value = {
      isOpen: true,
      type: 'edit',
    };
    initialHotspotFormData.value = {
      text: projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].text,
      x: null,
      y: null,
      z: null,
      destination_img_id: null,
    };
    currentPositions.value = {...projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].position};
    selectedDestinationImgId.value = projectStore.virtualtours[tourId.value].images[projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].destination_img_id];
  }
};

// Remove Draggable and Reset the position's for the current u
const removeDraggableAndUpdate = (isClose) => {
  if (currentDraggableElRef.value){ // If the current is only exists, go futher..
    const el = currentDraggableElRef.value;
    const hotspotId = el.getAttribute('data-Id'); // get the hotspot id
    el.setAttribute('data-isDragging', 'false');  // back to default
    if (isClose){ // reset previous positions
      el.setAttribute('position', projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].position); // Update entity position instantly
    }
    currentDraggableElRef.value = null; // remove el from reference
    el.removeAttribute('click-drag'); // remove click & drag
    el.removeEventListener('mouseup', handleTrackDragging); // remove event-listener
    el.setAttribute('src', "#defaultHotspotIcon"); // add the default Hotspot
    handleCloseHotspotToggler(); // reset the values
  }
};

// Configure (url & orientation)
AFRAME.registerComponent('configure', {
  init: function () {
    console.log("configure", this.el);
    setConfigure(); // Initialize
  },
});

// Hospots Listeners
AFRAME.registerComponent('hotspot-listener', {
  init: function () {
    console.log("hotspot-listener", this.el);

    const el = this.el; // element
    const isNewHotspot = JSON.parse(el.getAttribute('data-isNewHotspot')); // check the new-hotspot or existing-hotspot.

    if (isNewHotspot){
      removeDraggableAndUpdate(true); // clear all the existing draggable el and update (This line is just pre-check of any existing)
      const scene = el.sceneEl;
      const cameraEl = scene.camera.el;
      console.log(cameraEl);

      if (cameraEl && el){
        const addHotspotPosition = addHotspotIntoViewArea(el, cameraEl);
        currentPositions.value = {
          x: addHotspotPosition.x,
          y: addHotspotPosition.y,
          z: addHotspotPosition.z,
        };
      }
    } else {

      const labelEntity = el.querySelector('.hotspot-label'); // label entity
      const hotspotId = el.getAttribute('data-Id'); // get the hotspot id

      // Mouse Enter
      el.addEventListener('mouseenter', () => {
        console.log("mouseenter");
        if (labelEntity && !JSON.parse(el.getAttribute('data-isDragging'))) {
          labelEntity.setAttribute('visible', true);
        }
        document.querySelector('a-scene').style.cursor = 'pointer';
        el.setAttribute('opacity', "0.9" );
      });
      // Mouse Leave
      el.addEventListener('mouseleave', () => {
        console.log("mouseleave");
        if (labelEntity && !JSON.parse(el.getAttribute('data-isDragging'))) {
          labelEntity.setAttribute('visible', false);
        }
        document.querySelector('a-scene').style.cursor = 'default';
        el.setAttribute('opacity', "0.5");
      });
      // Click
      el.addEventListener('click', () => {
        console.log(currentDraggableElRef.value);

        if (currentDraggableElRef.value && currentDraggableElRef.value.getAttribute('data-Id') !== hotspotId){
          removeDraggableAndUpdate(true); // clear the existing draggable el (existing hotspots)
          addDraaggableAndUpdate(this.el); // add the drag to the current Elem
        } else {
          console.log("This item (Already click / new click)...", currentDraggableElRef.value, hotspotId);
          if (hotspotToggler.value.isOpen && hotspotToggler.value.type === 'add' && !currentDraggableElRef.value){
            console.log("Yes there new hotspot");
            removeDraggableAndUpdate(true); // clear the new draggable el (new hotspot)
          }
          addDraaggableAndUpdate(this.el); // add the drag to the current Elem
        }
      });

    }
  },
});

// Submit (Hotspot Form) (Add/Update)
const handleSubmit = (val) => {
  hotspotFormLoader.value.save = true; // loader
  if (hotspotToggler.value.type === 'add' ){
    // Add
    console.log("Yes Watched Handle Submit", val);
    const parms = {
      project_id: projectId.value,
      tour_id: tourId.value,
      image_id: imageId.value,
      position: {
        x: String(val.x),
        y: String(val.y),
        z: String(val.z),
      },
      text: val.text,
      destination_img_id: val.destination_img_id.id,
    };
    console.log(parms);
    // Api call for add the hotspots
    addLink(parms).then((res) => {
      console.log(res, 'Result');
      handleCloseHotspotToggler(); // first, reset
      projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // then, sync the result to store
      hotspotFormLoader.value.save = false; // reset the loader
    }).catch(() => {
      hotspotFormLoader.value.save = true;
    });
  } else {
    // Edit
    if (currentDraggableElRef.value){
      const hotspotId = currentDraggableElRef.value.getAttribute('data-Id');
      console.log(hotspotId);

      const source = {
        ...val,
        destination_img_id: val.destination_img_id.id,
      };
      const prevData = {
        x: projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].position.x,
        y: projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].position.y,
        z: projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].position.z,
        text: projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].text,
        destination_img_id: projectStore.virtualtours[tourId.value].images[imageId.value].links[hotspotId].destination_img_id,
      };
      console.log(source);
      console.log(prevData);

      const comparedValues = compareValuesFromSource(source, prevData);

      console.log(comparedValues);

      if (comparedValues){
        const frameParms = {
          project_id: projectId.value,
          tour_id: tourId.value,
          image_id: imageId.value,
          link_id: hotspotId,
          ...comparedValues,
        };
        console.log(frameParms);
        updateLink(frameParms).then((res) => {
          console.log(res, 'Result');
          removeDraggableAndUpdate(false); // first, remove the draggable & reset the position state's
          projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // then, sync the result to store
          hotspotFormLoader.value.save = false; // reset the loader
        }).catch(() => {
          hotspotFormLoader.value.save = true;
        });
      }
    }
  }
};

// Delete the Hotspots
const handleDeleteHotspot = () => {
  console.log("handleDeleteHotspot");

  hotspotFormLoader.value.delete = true; // loader
  if (currentDraggableElRef.value){
    console.log(currentDraggableElRef.value);

    const frameParms = {
      project_id: projectId.value,
      tour_id: tourId.value,
      image_id: imageId.value,
      link_id: currentDraggableElRef.value.getAttribute('data-Id'),
    };
    console.log(frameParms);
    deleteLink(frameParms).then((res) => {
      console.log("res", res);
      removeDraggableAndUpdate(false); // first, remove the draggable & reset the position state's
      projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // then, sync the result to store
      hotspotFormLoader.value.delete = false;
    }).catch(() => {
      hotspotFormLoader.value.delete = true;
    });
  }
};

const handleDblClickInScene = (e) => {
  console.log("handleDblClickInScene", e);

  const scene = SceneTag.value;
  const camera = scene.camera;

  if (!camera) { // If camera is'nt available, then throw a error.
    console.error("camera not found");
    return;
  }

  const rect = scene.canvas.getBoundingClientRect();  // Get canvas bounds for mouse position calculation
  const mouse = new THREE.Vector2(
    (((e.clientX - rect.left) / rect.width) * 2) - 1,
    -(((e.clientY - rect.top) / rect.height) * 2) + 1,
  );

  console.log("Mouse position:", mouse);
  console.log("Canvas rect:", rect);

  const raycaster = new THREE.Raycaster(); // create raycaster to get the direction from camera to clicked point
  raycaster.setFromCamera(mouse, camera);

  const clickDirection = raycaster.ray.direction.clone(); // store the ray direction (this is the direction from camera to clicked point)
  console.log("Click direction:", clickDirection);

  getTheDblClickPostion.value = clickDirection; // store this direction for use

  // Open the create hotspot
  hotspotToggler.value.isOpen = true;
  hotspotToggler.value.type = 'add';
};

onMounted(() => {
  setTimeout(() => {
    if (SceneTag.value){ // after a-scene created,
    console.log("onMount", SceneTag.value);
    SceneTag.value.addEventListener('dblclick', handleDblClickInScene); 
  }
}, 0);
});

onUnmounted(() => {
  // cleanup the register Components
  delete AFRAME.components.configure;
  delete AFRAME.components['hotspot-listener'];
});

</script>

<template>
      <!-- No Tour Found -->
     <div class="w-full h-full flex flex-col justify-center items-center text-center text-white bg-black" v-if="!projectStore.virtualtours || !(projectStore.virtualtours[tourId]?.images) || !imageId">
              No VR Tour Found !
     </div>

     <!-- Tour Found -->
     <div v-else class="h-full w-full flex justify-center items-center text-white overflow-hidden relative">
            <img v-if="!isTextureLoaded" :src="cdn(projectStore.virtualtours[tourId].images[imageId].thumbnail)" alt="" class="w-full h-full absolute top-0 left-0 z-[2] object-cover">
            <!-- AScene Component-->
            <a-scene
             id="VR_scene"
             ref="SceneTag"
             class="aScene_Tour"
             embedded
             loading-screen="enabled:false"
             vr-mode-ui="enabled:false"
             device-orientation-permission-ui="enabled: false"
             renderer="colorManagement:true;sortObjects:true;maxCanvasWidth:1920;maxCanvasHeight:1920;"
            >

              <!-- Mouse Cursor (cursor & raycaster for interactive el) -->
              <a-entity id="mouseCursor" cursor="rayOrigin: mouse;fuse:false;" raycaster="objects: [data-raycastable];" ></a-entity>

              <a-entity id="rig" configure>
                    <!-- Player (Camera) -->
                  <a-entity id="player" camera="far:10000;near:0.5;fov:100" look-controls position="0 0 1e-5"></a-entity>
                    <!-- A-Sky -->
                    <a-sky @materialtextureloaded="isTextureLoaded = true" ></a-sky>
              </a-entity>

              <!-- Assets (Hotspot Icons) -->
              <a-assets>
                    <img id="defaultHotspotIcon" crossorigin="anonymous" :src="defaultHotspotIcon" alt="defaultHotspotIcon"/>
                    <img id="activeHotspotIcon" crossorigin="anonymous" :src="activeHotspotIcon" alt="activeHotspotIcon">
              </a-assets>

              <!-- Hotspots -->
              <!-- Add -->
              <a-image v-if="hotspotToggler.isOpen && hotspotToggler.type === 'add'" look-at="[camera]" src="#activeHotspotIcon" scale="0.8 0.8" position click-drag @mouseup="handleTrackDragging" :data-isNewHotspot="true" data-raycastable hotspot-listener></a-image>

              <!-- Existing -->
              <a-entity v-if="projectStore.virtualtours[tourId].images[imageId]?.links && Object.keys(projectStore.virtualtours[tourId].images[imageId]?.links).length > 0">
                    <a-image :class="[ hotspotToggler.isOpen && hotspotToggler.type === 'add' ? 'pointer-events-none' : 'pointer-events-auto' ]" v-for="link, id in projectStore.virtualtours[tourId].images[imageId]?.links" :key="id+imageId" look-at="[camera]" src="#defaultHotspotIcon" :position="link.position" opacity="0.5" scale="0.8 0.8" :data-Id="id" :data-isNewHotspot="false" data-raycastable hotspot-listener :data-isDragging="false">
                        <a-entity
                          class="hotspot-label"
                          position="0 0.9 0"
                          visible="false"
                        >
                            <a-plane
                              width="3"
                              height="0.7"
                              material="color: white;"
                              position="0 0 0"
                            />
                            <a-text
                              :value="link.text"
                              align="center"
                              width="6.9"
                              color="black"
                              position="0 0 0"
                              font="kelsonsans"
                              scale="1.9 1.9"
                            />
                          </a-entity>
                    </a-image>
              </a-entity>

            </a-scene>

            <!-- Hotspots (Add) -->
         <!--    <Button v-if="!hotspotToggler.isOpen" title="Add Hotspot" theme="primary" class="h-3 w-fit absolute right-1 top-[3%] z-[3]"
                  @handleClick="() =>{ hotspotToggler.isOpen = true; hotspotToggler.type = 'add';}">
                  <template v-slot:svg>
                      <svg class="w-3 h-3" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_308_25527)">
                          <path
                            d="M6 12C5.8446 12 5.69556 11.9383 5.58568 11.8284C5.47579 11.7185 5.41406 11.5695 5.41406 11.4141V0.585938C5.41406 0.430537 5.47579 0.281502 5.58568 0.171617C5.69556 0.0617325 5.8446 0 6 0C6.1554 0 6.30444 0.0617325 6.41432 0.171617C6.52421 0.281502 6.58594 0.430537 6.58594 0.585938V11.4141C6.58594 11.5695 6.52421 11.7185 6.41432 11.8284C6.30444 11.9383 6.1554 12 6 12Z"
                            fill="white" />
                          <path
                            d="M11.4141 6.58594H0.585938C0.430537 6.58594 0.281502 6.52421 0.171617 6.41432C0.0617325 6.30444 0 6.1554 0 6C0 5.8446 0.0617325 5.69556 0.171617 5.58568C0.281502 5.47579 0.430537 5.41406 0.585938 5.41406H11.4141C11.5695 5.41406 11.7185 5.47579 11.8284 5.58568C11.9383 5.69556 12 5.8446 12 6C12 6.1554 11.9383 6.30444 11.8284 6.41432C11.7185 6.52421 11.5695 6.58594 11.4141 6.58594Z"
                            fill="white" />
                        </g>
                        <defs>
                          <clipPath id="clip0_308_25527">
                            <rect width="12" height="12" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                    </template>
            </Button> -->

            <!-- Form (Add/Edit/Delete) -->
            <div v-if="hotspotToggler.isOpen" class="px-2.5 py-2 h-fit absolute right-1 top-[3%] z-[3] bg-white rounded w-64">
              <div class="flex justify-between items-center mb-3">
                <p class="text-black text-base font-semibold"> {{ hotspotToggler.type === 'add' ? 'Add' : 'Update/Delete' }} Hotspot : </p>
                <button v-if="hotspotToggler.type === 'edit'" type="button" class="rotate-45" :disabled="hotspotFormLoader.delete || hotspotFormLoader.save ? true : false" @click="removeDraggableAndUpdate(true)">
                  <svg class="w-4 h-4" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_308_25527)">
                          <path
                            d="M6 12C5.8446 12 5.69556 11.9383 5.58568 11.8284C5.47579 11.7185 5.41406 11.5695 5.41406 11.4141V0.585938C5.41406 0.430537 5.47579 0.281502 5.58568 0.171617C5.69556 0.0617325 5.8446 0 6 0C6.1554 0 6.30444 0.0617325 6.41432 0.171617C6.52421 0.281502 6.58594 0.430537 6.58594 0.585938V11.4141C6.58594 11.5695 6.52421 11.7185 6.41432 11.8284C6.30444 11.9383 6.1554 12 6 12Z"
                            fill="red" />
                          <path
                            d="M11.4141 6.58594H0.585938C0.430537 6.58594 0.281502 6.52421 0.171617 6.41432C0.0617325 6.30444 0 6.1554 0 6C0 5.8446 0.0617325 5.69556 0.171617 5.58568C0.281502 5.47579 0.430537 5.41406 0.585938 5.41406H11.4141C11.5695 5.41406 11.7185 5.47579 11.8284 5.58568C11.9383 5.69556 12 5.8446 12 6C12 6.1554 11.9383 6.30444 11.8284 6.41432C11.7185 6.52421 11.5695 6.58594 11.4141 6.58594Z"
                            fill="red" />
                        </g>
                        <defs>
                          <clipPath id="clip0_308_25527">
                            <rect width="12" height="12" fill="red" />
                          </clipPath>
                        </defs>
                      </svg>
                </button>
              </div>
              <Form :key="hotspotToggler.type === 'add' ? '00' : currentDraggableElRef.getAttribute('data-Id')" class=" flex flex-col justify-start items-end w-full gap-3 mb-0"
                @submit="handleSubmit" :initial-values="initialHotspotFormData" :validation-schema="tourHotspotSchema">

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="long"> Text </label>
                    <Field type="text" name="text" id="text" class="input-primary w-full text-black" placeholder="Enter Text" />
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="text"  />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="x"> X </label>
                    <Field v-model="currentPositions.x" type="text" name="x" id="x" class="input-primary w-full text-black" disabled/>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="x" />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="y"> Y </label>
                    <Field  v-model="currentPositions.y" type="text" name="y" id="y" class="input-primary w-full text-black" disabled/>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="y" />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-sm text-txt-50" for="z"> Z </label>
                    <Field v-model="currentPositions.z" type="text" name="z" id="z" class="input-primary w-full text-black" disabled/>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="z" />
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                  <label class="font-semibold text-sm text-txt-50" for="destination_img_id"> Destination img id </label>
                  <Field name="destination_img_id" :model-value="selectedDestinationImgId" v-slot="{ field }">
                    <Multiselect :allow-empty="false" v-bind="field" v-model="selectedDestinationImgId" :searchable="false" :custom-label="(val) => val.name"
                    :close-on-select="true" :show-labels="false" placeholder="Choose" :options="Object.values(projectStore.virtualtours[tourId].images)"
                    maxHeight="250">
                    </Multiselect>
                  </Field>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="destination_img_id" />
                    <p v-if="selectedDestinationImgId" class="text-black px-2 py-1"> {{ selectedDestinationImgId.id }} </p>
                </div>

                  <div class="flex justify-end items-center gap-2 w-full h-fit mt-2">
                    <Button v-if="hotspotToggler.type === 'add'" title="Close" type="button" theme="secondary" :disabled="hotspotFormLoader.save" @handleClick="() => handleCloseHotspotToggler()"> </Button>
                    <Button v-else title="Delete" type="button" class="!bg-rose-500" :disabled="hotspotFormLoader.delete || hotspotFormLoader.save ? true : false" @handleClick="() => handleDeleteHotspot()">
                      <template v-if="hotspotFormLoader.delete" v-slot:svg>
                            <Spinner />
                      </template>
                    </Button>
                    <Button title="Submit" type="submit" theme="primary" :disabled="hotspotFormLoader.delete || hotspotFormLoader.save ? true : false">
                        <template v-if="hotspotFormLoader.save" v-slot:svg>
                            <Spinner />
                        </template>
                    </Button>
                  </div>
              </Form>
            </div>

     </div>

</template>

<style>
.aScene_Tour .a-canvas{
  z-index: 1 !important;
}
</style>
