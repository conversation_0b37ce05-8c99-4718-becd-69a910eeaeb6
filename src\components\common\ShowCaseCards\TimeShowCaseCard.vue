<script setup>
import { UserStore } from '../../../../src/store/index.ts';

const store = UserStore();

defineProps({
  title: String,
  hour: Number,
  minute: Number,
  second: Number,
  isMobile: {
    type: Boolean,
    default: false,
  },
});
console.log(store.isMobile);
store.callbackFunctionMonitorChanges();

</script>

<template>
    <div v-if="!store.isMobile" class="p-4 border-2 border-bg-950 dark:border-bg-default bg-bg-1000 dark:bg-bg-default rounded-2xl flex flex-col justify-start items-start gap-3">
            <div class="flex justify-between items-center flex-row w-full">
                    <div class="text-txt-450 dark:text-txt-900 text-lg font-medium">{{ title }}</div>
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="40" height="40" rx="8" fill="#FFF5EB"/>
                        <path d="M20.2857 11.4286C18.2514 11.4286 16.2628 12.0318 14.5713 13.162C12.8798 14.2922 11.5615 15.8986 10.783 17.7781C10.0045 19.6576 9.80077 21.7257 10.1976 23.7209C10.5945 25.7162 11.5741 27.5489 13.0126 28.9874C14.4511 30.4259 16.2838 31.4055 18.2791 31.8024C20.2743 32.1992 22.3424 31.9955 24.2219 31.217C26.1014 30.4385 27.7078 29.1202 28.838 27.4287C29.9682 25.7372 30.5714 23.7486 30.5714 21.7143C30.5683 18.9873 29.4836 16.3729 27.5554 14.4446C25.6271 12.5164 23.0127 11.4317 20.2857 11.4286ZM20.2857 30.2857C18.5904 30.2857 16.9333 29.783 15.5237 28.8412C14.1141 27.8993 13.0155 26.5606 12.3668 24.9944C11.718 23.4282 11.5483 21.7048 11.879 20.0421C12.2097 18.3794 13.0261 16.8521 14.2248 15.6534C15.4235 14.4546 16.9508 13.6383 18.6135 13.3076C20.2762 12.9768 21.9996 13.1466 23.5659 13.7953C25.1321 14.4441 26.4708 15.5427 27.4126 16.9523C28.3544 18.3618 28.8571 20.019 28.8571 21.7143C28.8546 23.9868 27.9507 26.1655 26.3438 27.7724C24.7369 29.3793 22.5582 30.2832 20.2857 30.2857ZM25.1779 16.8221C25.2576 16.9017 25.3208 16.9963 25.3639 17.1003C25.407 17.2044 25.4292 17.3159 25.4292 17.4286C25.4292 17.5412 25.407 17.6527 25.3639 17.7568C25.3208 17.8609 25.2576 17.9554 25.1779 18.035L20.8921 22.3207C20.8125 22.4004 20.718 22.4635 20.6139 22.5066C20.5099 22.5497 20.3983 22.5719 20.2857 22.5719C20.1731 22.5719 20.0616 22.5497 19.9575 22.5066C19.8535 22.4635 19.7589 22.4004 19.6793 22.3207C19.5997 22.2411 19.5365 22.1465 19.4934 22.0425C19.4503 21.9384 19.4281 21.8269 19.4281 21.7143C19.4281 21.6017 19.4503 21.4901 19.4934 21.3861C19.5365 21.282 19.5997 21.1875 19.6793 21.1079L23.965 16.8221C24.0446 16.7424 24.1391 16.6792 24.2432 16.6361C24.3473 16.593 24.4588 16.5708 24.5714 16.5708C24.6841 16.5708 24.7956 16.593 24.8997 16.6361C25.0037 16.6792 25.0983 16.7424 25.1779 16.8221ZM16.8571 8.85714C16.8571 8.62981 16.9475 8.4118 17.1082 8.25105C17.2689 8.09031 17.487 8 17.7143 8H22.8571C23.0845 8 23.3025 8.09031 23.4632 8.25105C23.624 8.4118 23.7143 8.62981 23.7143 8.85714C23.7143 9.08447 23.624 9.30249 23.4632 9.46323C23.3025 9.62398 23.0845 9.71429 22.8571 9.71429H17.7143C17.487 9.71429 17.2689 9.62398 17.1082 9.46323C16.9475 9.30249 16.8571 9.08447 16.8571 8.85714Z" fill="#FF7F23"/>
                    </svg>
            </div>

            <div class="flex justify-center items-center gap-7 w-full">
                    <div class="flex flex-col justify-center items-center gap-1 bg-bg-950 bg-opacity-50 dark:bg-bg-50 rounded-2xl w-[175px] h-[108px]">
                            <div class="text-txt-100 dark:text-txt-1000 text-5xl font-bold">{{ hour }}</div>
                            <div class="  text-center text-txt-100 dark:text-txt-1000 text-sm font-medium">Hours</div>
                     </div>
                     <div class="flex flex-col justify-center items-center gap-1 bg-bg-950 bg-opacity-50 dark:bg-bg-50 rounded-2xl w-[175px] h-[108px]">
                            <div class="text-txt-100 dark:text-txt-1000 text-5xl font-bold">{{ minute }}</div>
                            <div class="  text-center text-txt-100 dark:text-txt-1000 text-sm font-medium">Minutes</div>
                     </div>
                     <div class="flex flex-col justify-center items-center gap-1 bg-bg-950 bg-opacity-50 dark:bg-bg-50 rounded-2xl w-[175px] h-[108px]" >
                            <div class="text-txt-100 dark:text-txt-1000 text-5xl font-bold">{{ second }}</div>
                            <div class="  text-center text-txt-100 dark:text-txt-1000 text-sm font-medium">Seconds</div>
                     </div>

            </div>
    </div>

    <div v-else class="p-2 border-2 border-bg-950 dark:border-bg-default bg-bg-1000  dark:bg-bg-default rounded-md flex flex-col justify-start items-start">
            <div class="flex flex-col justify-between items-start gap-3 w-full">
                   <svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                       <rect width="40" height="40" rx="8" fill="#FFF5EB"/>
                       <path d="M20.2857 11.4286C18.2514 11.4286 16.2628 12.0318 14.5713 13.162C12.8798 14.2922 11.5615 15.8986 10.783 17.7781C10.0045 19.6576 9.80077 21.7257 10.1976 23.7209C10.5945 25.7162 11.5741 27.5489 13.0126 28.9874C14.4511 30.4259 16.2838 31.4055 18.2791 31.8024C20.2743 32.1992 22.3424 31.9955 24.2219 31.217C26.1014 30.4385 27.7078 29.1202 28.838 27.4287C29.9682 25.7372 30.5714 23.7486 30.5714 21.7143C30.5683 18.9873 29.4836 16.3729 27.5554 14.4446C25.6271 12.5164 23.0127 11.4317 20.2857 11.4286ZM20.2857 30.2857C18.5904 30.2857 16.9333 29.783 15.5237 28.8412C14.1141 27.8993 13.0155 26.5606 12.3668 24.9944C11.718 23.4282 11.5483 21.7048 11.879 20.0421C12.2097 18.3794 13.0261 16.8521 14.2248 15.6534C15.4235 14.4546 16.9508 13.6383 18.6135 13.3076C20.2762 12.9768 21.9996 13.1466 23.5659 13.7953C25.1321 14.4441 26.4708 15.5427 27.4126 16.9523C28.3544 18.3618 28.8571 20.019 28.8571 21.7143C28.8546 23.9868 27.9507 26.1655 26.3438 27.7724C24.7369 29.3793 22.5582 30.2832 20.2857 30.2857ZM25.1779 16.8221C25.2576 16.9017 25.3208 16.9963 25.3639 17.1003C25.407 17.2044 25.4292 17.3159 25.4292 17.4286C25.4292 17.5412 25.407 17.6527 25.3639 17.7568C25.3208 17.8609 25.2576 17.9554 25.1779 18.035L20.8921 22.3207C20.8125 22.4004 20.718 22.4635 20.6139 22.5066C20.5099 22.5497 20.3983 22.5719 20.2857 22.5719C20.1731 22.5719 20.0616 22.5497 19.9575 22.5066C19.8535 22.4635 19.7589 22.4004 19.6793 22.3207C19.5997 22.2411 19.5365 22.1465 19.4934 22.0425C19.4503 21.9384 19.4281 21.8269 19.4281 21.7143C19.4281 21.6017 19.4503 21.4901 19.4934 21.3861C19.5365 21.282 19.5997 21.1875 19.6793 21.1079L23.965 16.8221C24.0446 16.7424 24.1391 16.6792 24.2432 16.6361C24.3473 16.593 24.4588 16.5708 24.5714 16.5708C24.6841 16.5708 24.7956 16.593 24.8997 16.6361C25.0037 16.6792 25.0983 16.7424 25.1779 16.8221ZM16.8571 8.85714C16.8571 8.62981 16.9475 8.4118 17.1082 8.25105C17.2689 8.09031 17.487 8 17.7143 8H22.8571C23.0845 8 23.3025 8.09031 23.4632 8.25105C23.624 8.4118 23.7143 8.62981 23.7143 8.85714C23.7143 9.08447 23.624 9.30249 23.4632 9.46323C23.3025 9.62398 23.0845 9.71429 22.8571 9.71429H17.7143C17.487 9.71429 17.2689 9.62398 17.1082 9.46323C16.9475 9.30249 16.8571 9.08447 16.8571 8.85714Z" fill="#FF7F23"/>
                   </svg>

                    <div class="text-txt-450 dark:text-txt-900 text-sm font-medium ">{{ title }}</div>

                    <div class="flex justify-center items-center gap-7 w-full">
                    <div class="flex flex-col justify-center items-center bg-bg-950 bg-opacity-50 dark:bg-bg-50 rounded-md w-[86px] h-[53px]">
                            <div class="text-txt-100 dark:text-txt-1000 text-2xl font-bold">{{ hour }}</div>
                            <div class="  text-center text-txt-100 dark:text-txt-1000 text-xs font-normal">Hours</div>
                     </div>
                     <div class="flex flex-col justify-center items-center bg-bg-950 bg-opacity-50 dark:bg-bg-50 rounded-md w-[86px] h-[53px]">
                            <div class="text-txt-100 dark:text-txt-1000 text-2xl font-bold">{{ minute }}</div>
                            <div class="  text-center text-txt-100 dark:text-txt-1000 text-xs font-normal">Minutes</div>
                     </div>
                     <div class="flex flex-col justify-center items-center bg-bg-950 bg-opacity-50 dark:bg-bg-50 rounded-md w-[86px] h-[53px]" >
                            <div class="text-txt-100 dark:text-txt-1000 text-2xl font-bold">{{ second }}</div>
                            <div class="  text-center text-txt-100 dark:text-txt-1000 text-xs font-normal">Seconds</div>
                     </div>

                   </div>
            </div>

     </div>
</template>

<!-- 12,20-padding , 30 gap cards , 175width, 4 gap -->
