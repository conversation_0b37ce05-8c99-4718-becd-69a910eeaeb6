import * as yup from 'yup';
import { onlyN<PERSON><PERSON>, RegXEmailPattern } from '../../helpers/validationSchemaHelpers';

// ScheduleSessionsSchema
export const scheduleSessionSchema = yup.object({
  name: yup.string().required(),
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required(),
  "phone number": yup.string().matches(onlyNumber, 'Invalid Phone Number').max(10),
  sessionDate: yup.string().required(),
  project: yup.object().required(),
  unit: yup.object().nullable(),
  time: yup.object().required(),
  description: yup.string().nullable(),
  tag: yup.string().nullable(),
});

// ScheduleSessionsSchemafor testing
export const scheduleSessionSchemaTesting = yup.object({
  name: yup.string(),
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email'),
  "phone number": yup.string().matches(onlyNum<PERSON>, 'Invalid Phone Number').max(10),
  sessionDate: yup.string().required(),
  project: yup.object().required(),
  unit: yup.object().nullable(),
  time: yup.object().required(),
  description: yup.string().nullable(),
  tag: yup.string().nullable(),
});

export const createSessionSchema = yup.object({
  name: yup.string().required(),
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required(),
  "phone number": yup.string().matches(onlyNumber, 'Invalid Phone Number').max(10),
  project: yup.object().required(),
  unit: yup.object().nullable(),
  description: yup.string().nullable(),
  tag: yup.string().nullable(),
});

export const createSessionSchemaTesting = yup.object({
  name: yup.string(),
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email'),
  "phone number": yup.string().matches(onlyNumber, 'Invalid Phone Number').max(10),
  project: yup.object().required(),
  unit: yup.object().nullable(),
  description: yup.string().nullable(),
  tag: yup.string().nullable(),
});

export const ScheduleMeetingsValidation = yup.object().shape({
  name: yup.string().required("Name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  slot: yup.string().required("Slot is required"),
  date: yup.string().required("Date is required"),
  startTime: yup.string().required("Start time is required")
    .test({
      name: 'is-greater-than-now',
      message: 'Invalid Start time',
      test: function (value) {
        if (!value) {
          return true;
        }
        const { date } = this.parent;
        if (!date) {
          return true;
        } // Skip validation if no date selected

        try {
          const selectedDateTime = new Date(`${date}T${value}`);
          const currentTime = new Date();

          return selectedDateTime > currentTime;
        } catch (error) {
          return false;
        }
      },
    }),
  endTime: yup.string().required("End time is required")
    .test({
      name: 'is-greater-than-now',
      message: 'Invalid End time',
      test: function (value) {
        if (!value) {
          return true;
        }
        const { date } = this.parent;
        if (!date) {
          return true;
        } // Skip validation if no date selected

        try {
          const selectedDateTime = new Date(`${date}T${value}`);
          const currentTime = new Date();

          return selectedDateTime > currentTime;
        } catch (error) {
          return false;
        }
      },
    })
    .test({
      name: 'is-greater-than-start-time',
      message: 'Invalid End time',
      test: function (value) {
        if (!value) {
          return true;
        }
        const { date, startTime } = this.parent;
        if (!date || !startTime) {
          return true;
        } // Skip validation if date or startTime is not selected

        try {
          const endDateTime = new Date(`${date}T${value}`);
          const startDateTime = new Date(`${date}T${startTime}`);

          return endDateTime > startDateTime;
        } catch (error) {
          return false;
        }
      },
    }),
});
export const QuickMeetingsValidation = yup.object({
  leadName: yup.string().required("Name is required"),
  emailId: yup.string().email("Invalid email").required("Email is required"),
  phoneNumber: yup.string(),
});
