<script setup>
import VueCal from 'vue-cal';
import Modal from '../common/Modal/Modal.vue';
import { computed, nextTick, onMounted, ref, watch, watchEffect } from 'vue';
import CreateMeetingsForm from "./CreateMeetingsForm.vue";
import { UserStore } from '../../store/index';
import MeetingsNavbar from './MeetingsNavbar.vue';
import { CancelSession, CheckAvailableSessionSlots, createSession, getListOfSessionSlots, GetSessions, EndSession } from '@/api/sessions';
import { Org_Store } from '@/store/organization';
import { CreateLead } from '@/api/leads';
import LoaderComp from '../common/LoaderComp.vue';
import { EndSessionSessionListMenuItems, CancelSessionSessionListMenuItems } from '@/enum';
import Multiselect from 'vue-multiselect';
import moment from 'moment-timezone';
import { uiOperations } from '@/store/uiOperations';
import { useRoute, useRouter} from 'vue-router';
import QuickMeetingForm from './QuickMeetingForm.vue';
import { infoIcon } from '@/helpers/icons';
import { onClickOutside } from '@vueuse/core';

const router = useRouter();
const route = useRoute();
const Organizationstore = Org_Store();
const showCalenderModal = ref(false);
const selected_timezone = ref('Asia/Kolkata');
const timezones = moment.tz.names();
const Store = UserStore();
const infoMessage = ref();
const infoMessageTime = ref();
const sessionData = ref([]);
const filterredSessionData = ref([]);
const listSessionData = ref([]);
// const selectedEvent = ref();
const showFormModal = ref(false);
const showQuickMeetingFormModal = ref(false);
const showInfoModal = ref(false);
const showMobileMeetings = ref(false);
const disableButton = ref(false);
const formLoader = ref(false);
const vuecal = ref(null);
const dates = ref([]);
const loader = ref(false);
const selectedDate = ref(new Date().toISOString().split('T')[0]);
const startDate = ref(new Date());
const events = ref([]);
const originalEvents = ref([]);
const menuActive = ref(false);
const activeMenuId = ref(null);
const parentContainerRef = ref(null);
const meetingsCardMenuRef = ref({});
const calenderStartTime = ref('');
const calenderEndTime = ref('');
const uistore = uiOperations();
const checkedTimestamps = ref({});
const sessionSlotsData = ref([]);
const viewType = ref('day');
const activeItemId = ref(null);
const sessionCardDetails = ref({});
const infoIconActive = ref(false);
const session_fields = ref(['Day', 'Time', 'Project', 'Lead Name', 'Email', 'Phone', '']);
const searchText = ref('');
const dropdownMobileButton = ref();

const getSessionSlots = (date) => {
  console.log("api called");
  const dateObject = new Date(`${date}T00:00:00.000Z`);
  return new Promise((resolve, reject) => {
    getListOfSessionSlots({date: dateObject}).then((data) => {
      sessionSlotsData.value  = data;
      // console.log("ppp", sessionSlotsData.value);
      // console.log("no ofloops", sessionSlotsData.value.length);

      for (const event of sessionSlotsData.value){
        // console.log("slots in date", event);
        const formattedStart = new Date(event.schedule_time);
        const formattedEnd = new Date(event.end_time);
        const timestampKey = `${event.schedule_time}-${event.end_time}`;
        if (event.availableSession !== 0){
          if (!checkedTimestamps.value[timestampKey]){
            console.log("available slot", formattedStart, formattedEnd);
            checkedTimestamps.value[timestampKey] = true;
            events.value.push({
              start: `${formattedStart.getFullYear()}-${String(formattedStart.getMonth() + 1).padStart(2, '0')}-${String(formattedStart.getDate()).padStart(2, '0')} ${String(formattedStart.getHours()).padStart(2, '0')}:${String(formattedStart.getMinutes()).padStart(2, '0')}`,
              end: `${formattedEnd.getFullYear()}-${String(formattedEnd.getMonth() + 1).padStart(2, '0')}-${String(formattedEnd.getDate()).padStart(2, '0')} ${String(formattedEnd.getHours()).padStart(2, '0')}:${String(formattedEnd.getMinutes()).padStart(2, '0')}`,
              title: 'Available',
              class: 'available',
              deletable: false,
              resizable: false,
              draggable: false,
            });
          }
        } else {
          if (!checkedTimestamps.value[timestampKey]){
            console.log("not_available slot", formattedStart, formattedEnd);
            checkedTimestamps.value[timestampKey] = true;
            events.value.push({
              start: `${formattedStart.getFullYear()}-${String(formattedStart.getMonth() + 1).padStart(2, '0')}-${String(formattedStart.getDate()).padStart(2, '0')} ${String(formattedStart.getHours()).padStart(2, '0')}:${String(formattedStart.getMinutes()).padStart(2, '0')}`,
              end: `${formattedEnd.getFullYear()}-${String(formattedEnd.getMonth() + 1).padStart(2, '0')}-${String(formattedEnd.getDate()).padStart(2, '0')} ${String(formattedEnd.getHours()).padStart(2, '0')}:${String(formattedEnd.getMinutes()).padStart(2, '0')}`,
              title: 'Slot not available',
              class: 'not_available',
              deletable: false,
              resizable: false,
              draggable: false,
            });
          }
        }
      }
      console.log("inside getSSessionSlots", events.value);
      resolve();
    }).catch((err) => {
      reject();
      console.log("error", err);
    });
  });
};

function fetchSessions (){
  loader.value = true;
  if (route.query.user_id || route.query.project_id){
    GetSessions(
      {type: 'scheduled',
        ...(route.query.user_id && {user_id: route.query.user_id}),
        ...(route.query.project_id && {project_id: route.query.project_id})})
      .then((sessions_data) => {
        sessionData.value = Object.values(sessions_data);
        if (sessionData.value.length > 0) {
          console.log("zzzzzzzzzzzz", sessionData.value);
          filterredSessionData.value = sessionData.value.filter((item) => {
            const date = new Date(item.start);
            const currentDate = new Date(selectedDate.value.replace(/-/g, '/'));
            return date.getFullYear() ===  currentDate.getFullYear() && date.getMonth() === currentDate.getMonth()  && date.getDate() === currentDate.getDate();
          });
          console.log("xxxxxxxxxx", filterredSessionData.value);
          listSessionData.value = sessionData.value.filter((item) => {
            const date = new Date(item.start);
            const currentDate = new Date();
            return date >= currentDate;
          });
          console.log("yyyyyyyyyy", listSessionData.value);
          getSessionSlots(selectedDate.value).then(() => {
            console.log("events", events.value);
            loader.value = false;
          }).catch((err) => {
            uistore.showToast(`Error Fetching Data into Calender:${err}`, 'error');
          });
        } else {
          loader.value = false;
          sessionData.value = [];
          filterredSessionData.value = [];
          listSessionData.value = [];
        }
      }).catch(() => {
        sessionData.value = null;
        loader.value = false;
      });
  } else {
    GetSessions({type: 'scheduled'}).then((sessions_data) => {
      sessionData.value = Object.values(sessions_data);
      if (sessionData.value.length > 0) {
        console.log("zzzzzzzzzzzz", sessionData.value);
        filterredSessionData.value = sessionData.value.filter((item) => {
          const date = new Date(item.start);
          const currentDate = new Date(selectedDate.value.replace(/-/g, '/'));
          return date.getFullYear() ===  currentDate.getFullYear() && date.getMonth() === currentDate.getMonth()  && date.getDate() === currentDate.getDate();
        });
        console.log("xxxxxxxxxx", filterredSessionData.value);
        listSessionData.value = sessionData.value.filter((item) => {
          const date = new Date(item.start);
          const currentDate = new Date();
          return date >= currentDate;
        });
        console.log("yyyyyyyyyy", listSessionData.value);
        getSessionSlots(selectedDate.value).then(() => {
          console.log("events", events.value);
          loader.value = false;
        }).catch((err) => {
          uistore.showToast(`Error Fetching Data into Calender:${err}`, 'error');
        });
      } else {
        loader.value = false;
        sessionData.value = [];
        filterredSessionData.value = [];
        listSessionData.value = [];
      }
    }).catch(() => {
      sessionData.value = null;
      loader.value = false;
    });
  }
}
watch(
  () => route.query,
  () => {
    fetchSessions();
  },
);
const generateDates = () => {
  const dateArray = [];
  for (let i = 0; i < 23; i++) {
    const futureDate = new Date(startDate.value);
    futureDate.setDate(startDate.value.getDate() + i);

    dateArray.push({
      day: futureDate.toLocaleDateString("en-US", { weekday: "short" }),
      date: futureDate.getDate(),
      month: futureDate.toLocaleString("en-US", { month: "short" }),
      fullDate: futureDate.toISOString().split("T")[0], // For selection comparison
    });
  }
  dates.value = dateArray;

};
onMounted(() => {
  loader.value = true;
  Organizationstore.RefreshProjects();
  Organizationstore.RefreshUsers();
  Store.callbackFunctionMonitorChanges();
  fetchSessions();
  generateDates();
  router.replace({
    query: {},
  });
});
const handleStartSession = (val) => {
  formLoader.value = true;
  const sessionObject = {
    source: 'dashboard',
    is_scheduled: false,
    schedule_time: new Date().toISOString(),
  };
  console.log("start", val);
  if (sessionObject) {
    createSession(sessionObject)
      .then((sessionResponse) => {
        if (val !==null && val.name && val.email) {
          const leadObject = {
            ...(val.name && { 'name': val.name }),
            ...(val.email && { 'email': val.email }),
            ...(val.phone_number && { 'phone_number': val.phone_number }),
            session_id: sessionResponse._id,
            source: 'sales_session',
          };
          CreateLead(leadObject)
            .then(() => {
              const meetingsArray = [{ [sessionResponse._id]: { ...sessionResponse } }];
              Organizationstore.SyncMultipleSessions(meetingsArray);
              Organizationstore.createSession = true;
              formLoader.value = false;
              showFormModal.value = false;
              getSessionSlots(selectedDate.value).then(() => {
                originalEvents.value = events.value;
                loader.value = false;
              }).catch((err) => {
                uistore.showToast(`Error Fetching Data into Calender:${err}`, 'error');
              });
              // router.push(`/sessions/startmeeting/${sessionResponse._id}`);
              window.location.href = `/salestool/preview/${sessionResponse._id}`;
            })
            .catch((err) => {
              console.log('Error creating lead: ', err);
              formLoader.value = false;
            });
        } else {
          const meetingsArray = [{ [sessionResponse._id]: { ...sessionResponse } }];
          Organizationstore.SyncMultipleSessions(meetingsArray);
          Organizationstore.createSession = true;
          formLoader.value = false;
          showFormModal.value = false;
          getSessionSlots(selectedDate.value).then(() => {
            originalEvents.value = events.value;
            loader.value = false;
          }).catch((err) => {
            uistore.showToast(`Error Fetching Data into Calender:${err}`, 'error');
          });
          // router.push(`/sessions/startmeeting/${sessionResponse._id}`);
          window.location.href = `/salestool/preview/${sessionResponse._id}`;
        }
      })
      .catch((err) => {
        console.log('Error creating session: ', err);
        formLoader.value = false;
      });
  }
};
function handleScheduleSession (form){
  selected_timezone.value = 'Asia/Kolkata';
  const slot = (form.slot).toString().replace('min', '');
  infoMessage.value = `${new Date(form.date).toLocaleDateString('en-US', { weekday: 'short', day: 'numeric', month: 'short' })} at ${new Date(`${form.date}T${form.startTime}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })} IST for ${slot} minute`;
  infoMessageTime.value = ` ${new Date(`${form.date}T${form.startTime}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })} `;

  const sessionObject = {
    source: 'dashboard',
    is_scheduled: true,
    duration: Number(slot),
    schedule_time: new Date(`${form.date}T${form.startTime}`),
  };

  if (sessionObject){
    // loader.value = true;
    formLoader.value = true;
    createSession(sessionObject).then((sessionResponse) => {
      const leadObject = {
        ...(form.name && { 'name': form.name }),
        ...(form.email && { 'email': form.email }),
        ...(form.phone && { 'phone_number': form.phone }),
        session_id: sessionResponse._id,
        source: 'sales_session',
        status: "new",
      };
      CreateLead(leadObject).then(() => {
        Organizationstore.createSession = true;
        formLoader.value = false;
        showInfoModal.value = true;
        loader.value = false;
        showFormModal.value = false;
        // events.value = [];
        // originalEvents.value = [];
        // checkedTimestamps.value = {};
        // getSessionSlots(selectedDate.value).then(() => {
        //   // originalEvents.value = events.value;

        // }).catch((err) => {
        //   uistore.showToast(`Error Fetching Data into Calender:${err}`, 'error');
        // });
      })
        .catch((err) => {
          console.log('Error creating lead: ', err);
          formLoader.value = false;
          showInfoModal.value = false;
        });
    }).catch((err) => {
      formLoader.value = false;
      showInfoModal.value = false;
      uistore.showToast(`${err}`, 'error');
    });
  }

}

const selectDate = (date) => {
  selectedDate.value = date;
  console.log("selected in selece", selectedDate.value);

};
const prevDate = () => {
  const date = new Date(selectedDate.value);
  date.setDate(date.getDate() - 1);
  const updated = date.toISOString().split("T")[0];
  selectedDate.value = updated;
  startDate.value.setDate(startDate.value.getDate() - 1);
  generateDates();
};
const nextDate = () => {
  const date = new Date(selectedDate.value);
  date.setDate(date.getDate() + 1);
  const updated = date.toISOString().split("T")[0];
  selectedDate.value = updated;
  startDate.value.setDate(startDate.value.getDate() + 1);
  generateDates();
};

// Convert 24-hour time to 12-hour format in Calender Timeline
const formatHour = (hour) => {
  const period = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
  return `${formattedHour} ${period}`;
};

function redirectToSalestool (id) {
  window.open('/salestool/preview/' + id, '_blank');
}

function reloadPage (){
  loader.value = true;
  showInfoModal.value = false;
  sessionData.value = [];
  GetSessions({type: 'scheduled'}).then((sessions_data) => {
    sessionData.value = Object.values(sessions_data);
    if (sessionData.value.length > 0) {
      filterredSessionData.value = sessionData.value.filter((item) => {
        const date = new Date(item.start);
        const currentDate = new Date(selectedDate.value.replace(/-/g, '/'));
        return date.getFullYear() ===  currentDate.getFullYear() && date.getMonth() === currentDate.getMonth()  && date.getDate() === currentDate.getDate(); // March is month 2 (0-indexed)
      });

      if (viewType.value === 'list'){
        selectedDate.value = new Date().toISOString().split('T')[0];
      }
      listSessionData.value = sessionData.value.filter((item) => {
        const date = new Date(item.start);
        const currentDate = new Date();
        return date >= currentDate;
      });
      console.log("gggg", filterredSessionData.value);
      // const calenderData = filterredSessionData.value.filter((item) => {
      //   const date = new Date(item.start);
      //   return item.status !== 'cancelled' && date.getTime() > Date.now();
      // });
      events.value = [];
      originalEvents.value = [];
      checkedTimestamps.value = {};
      console.log("session slots date", selectedDate.value);
      getSessionSlots(selectedDate.value).then(() => {
        originalEvents.value = events.value;
        loader.value = false;
      }).catch((err) => {
        uistore.showToast(`Error Fetching Data into Calender:${err}`, 'error');
      });
      // if (filterredSessionData.value){
      //   fetchAvailableSessionIntoCalender(calenderData).then(() => {
      //     originalEvents.value = events.value;
      //     loader.value = false;
      //   }).catch((error) => {
      //     uistore.showToast(`Error Fetching Data into Calender:${error}`, 'error');
      //   });
      // }
    }
  }).catch(() => {
    sessionData.value = null;
    loader.value = false;
  });
}
async function HandleEndSession (sessionId) {
  try {
    loader.value = true;
    await EndSession({ session_id: sessionId });
    fetchSessions();
  } catch (error) {
    loader.value = false;
    console.log("ended session error: " + error);
  }
}
async function HandleCancelSession (sessionId) {
  try {
    loader.value = true;
    await CancelSession({ session_id: sessionId });
    fetchSessions();
    // loader.value = false;
  } catch (error) {
    loader.value = false;
    console.log("Cancelled session error: " + error);
  }
}
function handleMeetingsMobileMenu (e, meetingId){
  if (menuActive.value){
    if (activeMenuId.value !== null && activeMenuId.value !== meetingId){
      // console.log("second box");

      if (meetingsCardMenuRef.value[activeMenuId.value]){
        // console.log("removing first box");
        const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
        meetingsCardMenuRef.value[activeMenuId.value].classList.remove(...listOfRemovalClass); // remove class
        meetingsCardMenuRef.value[activeMenuId.value].classList.add('hidden');   // add class
      }
      menuActive.value = true;
      activeMenuId.value = meetingId;

    } else {
      // console.log("first box");
      activeMenuId.value = null;
      menuActive.value = false;
    }
  } else {
    // console.log("first box");
    activeMenuId.value = meetingId;
    menuActive.value = true;
  }
  e.preventDefault();

  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container
    console.log("calcParentWidth", calcParentWidth);
    console.log("calcParentHeight", calcParentHeight);
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      console.log("upper");
      console.log("xPosition", xPosition);
      console.log("yPosition", yPosition);
      if (viewType.value === 'list' && Store.isMobile){
        element.classList.add('right-[20px]');
        element.classList.add(`top-[-15px]`);
      } else if (viewType.value === 'day' && Store.isMobile){
        element.classList.add('right-[10px]');
        element.classList.add(`top-[-25px]`);
      } else {
        element.classList.add('right-[1px]');   // both x and y are exceeded the parent container or only y has exceded the parent container
        element.classList.add(`top-[-65px]`);
      }

    } else {
      console.log("lower");
      console.log("xPosition", xPosition);
      console.log("yPosition", yPosition);
      if (viewType.value === 'list' && Store.isMobile){
        element.classList.add(`top-[60px]`);
        element.classList.add('right-[20px]');
      } else {
        element.classList.add(`top-[50px]`);
        element.classList.add('right-[10px]');  // Only x has exceed the parent container
      }
    }
  };
  if (menuActive.value){
    if (meetingsCardMenuRef.value[meetingId]){
      console.log("displayed");
      // active item
      meetingsCardMenuRef.value[meetingId].classList.remove(...listOfRemovalClass); // remove class
      dynamicAbsolutePositions(meetingsCardMenuRef.value[meetingId]); // apply absolute positions depending on the current position (i.e is based on parent container)
      meetingsCardMenuRef.value[meetingId].classList.add('flex');   // add class
    }
  } else {
    if (meetingsCardMenuRef.value ? meetingsCardMenuRef.value[meetingId].classList.contains('flex') : false){
      console.log("hidden");
      const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[50px]', 'top-[220px]', 'right-[10px]', 'right-[40px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
      meetingsCardMenuRef.value[meetingId].classList.remove(...listOfRemovalClass); // remove class
      meetingsCardMenuRef.value[meetingId].classList.add('hidden');   // add class
    }
  }
}
function handleMeetingsMenu (e, meetingId){
  e.preventDefault();

  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      console.log("upper");
      element.classList.add('right-[30px]');
      element.classList.add(`top-[-25px]`); // both x and y are exceeded the parent container or only y has exceded the parent container

    } else {
      console.log("lower");
      element.classList.add(`top-[55px]`);
      element.classList.add('right-[30px]');  // Only x has exceed the parent container
    }
  };
  if (meetingsCardMenuRef.value[meetingId]){
    // active item
    meetingsCardMenuRef.value[meetingId].classList.remove(...listOfRemovalClass); // remove class
    dynamicAbsolutePositions(meetingsCardMenuRef.value[meetingId]); // apply absolute positions depending on the current position (i.e is based on parent container)
    meetingsCardMenuRef.value[meetingId].classList.add('flex');   // add class
  }
}
function handleCloseMenuPopup (meetingId){
  if (meetingsCardMenuRef.value ? meetingsCardMenuRef.value[meetingId].classList.contains('flex') : false){
    const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
    meetingsCardMenuRef.value[meetingId].classList.remove(...listOfRemovalClass); // remove class
    meetingsCardMenuRef.value[meetingId].classList.add('hidden');   // add class
  }
}
watchEffect(() => {
  console.log("watcEffect is triggered");
  if (events.value.length > 0 && originalEvents.value.length === 0) {
    originalEvents.value = [...events.value];
  }
});

watch(selected_timezone, () => {
  if (originalEvents.value.length > 0) {
    events.value = originalEvents.value.map((slot) => ({
      ...slot,
      start: new Date(slot.start)
        .toLocaleString("en-US", {
          timeZone: selected_timezone.value,
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
        .replace(/(\d+)\/(\d+)\/(\d+), (\d+):(\d+)/, "$3-$1-$2 $4:$5"),

      end: new Date(slot.end)
        .toLocaleString("en-US", {
          timeZone: selected_timezone.value,
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
        .replace(/(\d+)\/(\d+)\/(\d+), (\d+):(\d+)/, "$3-$1-$2 $4:$5"),
    }));
  }
  console.log("evetttt", events.value);

});

watch(selectedDate, () => {
  console.log("events", originalEvents.value);
  filterredSessionData.value = sessionData.value.filter((item) => {
    const date = new Date(item.start);
    const currentDate = new Date(selectedDate.value.replace(/-/g, '/'));
    return date.getFullYear() ===  currentDate.getFullYear() && date.getMonth() === currentDate.getMonth()  && date.getDate() === currentDate.getDate(); // March is month 2 (0-indexed)
  });
  console.log("today's session", filterredSessionData.value);

  if (new Date(selectedDate.value).getDate() !== new Date().getDate()){
    console.log("not today");

    // const calenderData = filterredSessionData.value.filter((item) => {
    //   const date = new Date(item.start);
    //   return item.status !== 'cancelled' && date.getTime() > Date.now();
    // });
    // events.value = [];
    // originalEvents.value = [];
    // checkedTimestamps.value = {};
    getSessionSlots(selectedDate.value).then(() => {
      originalEvents.value = events.value;
    }).catch((err) => {
      uistore.showToast(`Error Fetching Data into Calender:${err}`, 'error');
    });
    // if (filterredSessionData.value){
    //   fetchAvailableSessionIntoCalender(calenderData).then(() => {
    //     originalEvents.value = events.value;
    //     loader.value = false;
    //   }).catch((error) => {
    //     uistore.showToast(`Error Fetching Data into Calender:${error}`, 'error');
    //   });
    // }
  }

});
function forceRerenderEvents () {
  const temp = [...events.value];
  events.value = [];
  nextTick(() => {
    events.value = temp;
  });
}
function formatToISTClean (date) {
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone: 'Asia/Kolkata',
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  });

  const parts = formatter.formatToParts(date);

  const get = (type) => parts.find((p) => p.type === type)?.value;

  return `${get('weekday')} ${get('month')} ${get('day')} ${get('year')} ${get('hour')}:${get('minute')}:${get('second')}`;
}

function convertTimeWithParts (year, month, day, hour, minute, second, sourceTimeZone) {
  const isoDateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:${String(second).padStart(2, '0')}`;

  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone: sourceTimeZone,
    year: 'numeric',
    timeZoneName: 'longOffset',
  });

  // Find the timezone offset from the formatted string
  const tzMatch = formatter.format(new Date()).match(/GMT([+-])(\d{1,2}):?(\d{2})?/);
  let tzOffsetHours = 0;
  let tzOffsetMinutes = 0;

  if (tzMatch) {
    tzOffsetHours = parseInt(tzMatch[2], 10);
    tzOffsetMinutes = tzMatch[3] ? parseInt(tzMatch[3], 10) : 0;
    if (tzMatch[1] === '-') {
      tzOffsetHours = -tzOffsetHours;
      tzOffsetMinutes = -tzOffsetMinutes;
    }
  }
  const dateInSourceTZ = new Date(isoDateString);

  // Adjust for timezone difference (source TZ to UTC to IST)
  // IST is UTC+5:30
  const istOffsetHours = 5;
  const istOffsetMinutes = 30;

  // Calculate total minutes offset from source TZ to IST
  const totalMinutesOffset = ((istOffsetHours - tzOffsetHours) * 60) + (istOffsetMinutes - tzOffsetMinutes);

  // Add the offset to convert to IST
  dateInSourceTZ.setMinutes(dateInSourceTZ.getMinutes() + totalMinutesOffset);

  return dateInSourceTZ.toString();
}
function convertToIndianTime (dateString, sourceTimeZone) {

  let parts;

  // For strings without timezone info, we need to parse manually
  try {
    // Parse date like "Thu May 08 2025 12:00:00"
    parts = dateString.split(" ");
    if (parts.length < 4) {
      console.error("Invalid date format:", dateString);
      return "Invalid date format";
    }

    const monthMap = {
      "Jan": 0, "Feb": 1, "Mar": 2, "Apr": 3, "May": 4, "Jun": 5,
      "Jul": 6, "Aug": 7, "Sep": 8, "Oct": 9, "Nov": 10, "Dec": 11,
    };

    const day = parseInt(parts[2], 10);
    const month = monthMap[parts[1]];
    const year = parseInt(parts[3], 10);

    let hour = 0, minute = 0, second = 0;
    if (parts.length > 4 && parts[4].includes(":")) {
      const timeParts = parts[4].split(":");
      hour = parseInt(timeParts[0], 10);
      minute = parseInt(timeParts[1], 10);
      second = timeParts.length > 2 ? parseInt(timeParts[2], 10) : 0;
    }

    // Create date in the source timezone context
    return convertTimeWithParts(year, month, day, hour, minute, second, sourceTimeZone);
  } catch (error) {
    console.error("Error parsing date:", error);
    return "Error parsing date: " + error.message;
  }

}

function handleEventDrag (event){
  if (selected_timezone.value !== 'Asia/Kolkata'){

    const startIST = convertToIndianTime(formatToISTClean(event.start), selected_timezone.value);
    const endIST = convertToIndianTime(formatToISTClean(event.end), selected_timezone.value);
    calenderStartTime.value = startIST;
    calenderEndTime.value = endIST;
  } else {
    calenderStartTime.value = event.start;
    calenderEndTime.value = event.end;
  }
  if (new Date(calenderStartTime.value).getTime() > Date.now()){
    loader.value = true;
    CheckAvailableSessionSlots({start_time: event.start, end_time: event.end}).then((result) => {
      loader.value = false;
      console.log("result", result);
      if (result.availableSessions === 0){
        forceRerenderEvents();
        uistore.showToast('Slot is not available', 'error');
        disableButton.value = true;
      } else {
        disableButton.value = false;
        showFormModal.value = true;
        showCalenderModal.value = false;
      }

    }).catch((err) => {
      loader.value = false;
      forceRerenderEvents();
      uistore.showToast(`Error in Networks`, 'error');
      console.log("err", err);
    });
  } else {
    forceRerenderEvents();
    uistore.showToast('Scheduled time has already passed', 'error');
  }
}
function handleEventClick (event){
  console.log("inside hadle clikc", event);
  if (selected_timezone.value !== 'Asia/Kolkata'){
    const startIST = convertToIndianTime(formatToISTClean(event.start), selected_timezone.value);
    const endIST = convertToIndianTime(formatToISTClean(event.end), selected_timezone.value);
    calenderStartTime.value = startIST;
    calenderEndTime.value = endIST;
  } else {
    calenderStartTime.value = event.start;
    calenderEndTime.value = event.end;
  }

  if (event.title !== 'Available'){
    uistore.showToast('Selected Slot Not Available', 'error');
  } else {
    if (new Date().getTime() < new Date(calenderStartTime.value).getTime()){
      loader.value = false;
      showFormModal.value = true;
      showCalenderModal.value = false;
    } else {
      uistore.showToast('Scheduled time has already passed', 'error');
    }

  }

}
function scrollToCurrentTime (){
  const calendar = document.querySelector('#vuecal .vuecal__bg');
  const hours = new Date().getHours() + (new Date().getMinutes() / 60);
  calendar.scrollTo({ top: hours * Store.isMobile?50:70, behavior: 'smooth' });
}
function InCalenderNextButton (event){
  event.stopPropagation(); // Stops event from reaching the package handler
  event.preventDefault();
  nextDate();
}
function InCalenderPrevButton (event){
  event.stopPropagation(); // Stops event from reaching the package handler
  event.preventDefault();
  prevDate();
}
function handleToggleView (val){
  console.log("1111111", val);
  if (val === 'card'){
    viewType.value = 'day';
  } else {
    selectedDate.value = new Date().toISOString().split('T')[0];
    viewType.value = 'list';
  }
}
const processedData = computed(() => {
  const result = [];
  let currentDay = null;
  let count = 0;
  let startIndex = 0;

  const filteredSessions = listSessionData.value.filter((item) => {
    if (!searchText.value) {
      return true;
    } // if no search, include all
    return item.leads?.[0]?.name?.toLowerCase().includes(searchText.value.toLowerCase());
  });

  filteredSessions.forEach((item) => {
    const dateObj = new Date(item.start);
    const dayName = dateObj.toLocaleDateString('en-US', { weekday: 'long' }); // e.g., "Monday"

    if (dayName !== currentDay) {
      // Finalize previous group
      if (count > 0) {
        result[startIndex].rowspan = count;
      }

      currentDay = dayName;
      count = 1;
      startIndex = result.length;

      result.push({
        ...item,
        day: dayName,
        showDay: true,
        rowspan: 1,
      });
    } else {
      count++;
      result.push({
        ...item,
        day: dayName,
        showDay: false, // don't show duplicate day
      });
    }
  });

  // Final group
  if (count > 0) {
    result[startIndex].rowspan = count;
  }

  return result;
});
function getFormattedDate (dateStr) {
  const date = new Date(dateStr);
  const day = date.getDate();
  const month = date.toLocaleString('en-US', { month: 'short' }).toUpperCase();
  return `${day} ${month}`;
}
function getDateTag (dateStr) {
  const date = new Date(dateStr);
  const now = new Date();

  const oneDay = 24 * 60 * 60 * 1000;

  const cleanDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const cleanNow = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  const diffDays = Math.round((cleanDate - cleanNow) / oneDay);

  // Week boundaries
  const todayDay = now.getDay(); // 0 (Sun) to 6 (Sat)
  const startOfWeek = new Date(cleanNow);
  startOfWeek.setDate(now.getDate() - todayDay);

  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(endOfWeek.getDate() + 6);

  const nextWeekStart = new Date(endOfWeek);
  nextWeekStart.setDate(endOfWeek.getDate() + 1);

  const nextWeekEnd = new Date(nextWeekStart);
  nextWeekEnd.setDate(nextWeekStart.getDate() + 6);

  // Next month boundaries
  const nextMonthStart = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  const nextMonthEnd = new Date(now.getFullYear(), now.getMonth() + 2, 0);

  if (diffDays === 0) {
    return 'Today';
  }
  if (diffDays === 1) {
    return 'Tomorrow';
  }
  if (cleanDate >= startOfWeek && cleanDate <= endOfWeek) {
    return 'This Week';
  }
  if (cleanDate >= nextWeekStart && cleanDate <= nextWeekEnd) {
    return 'Next Week';
  }
  if (cleanDate >= nextMonthStart && cleanDate <= nextMonthEnd) {
    return 'Next Month';
  }

  return '';
}
const groupedSessions = computed(() => {
  const groups = {};

  listSessionData.value.forEach((session) => {
    const date = new Date(session.start).toDateString(); // groups by day
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(session);
  });

  return Object.entries(groups).map(([date, sessions]) => ({
    date,
    sessions,
  }));
});

function handleSessionDetails (e, sessionId){
  if (infoIconActive.value){
    if (activeItemId.value !== null && activeItemId.value !== sessionId){
      console.log("second box");

      if (sessionCardDetails.value[activeItemId.value]){
        console.log("removing first box");
        const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
        sessionCardDetails.value[activeItemId.value].classList.remove(...listOfRemovalClass); // remove class
        sessionCardDetails.value[activeItemId.value].classList.add('hidden');   // add class
      }

      infoIconActive.value = true;
      activeItemId.value = sessionId;

    } else {
      console.log("first box");
      activeItemId.value = null;
      infoIconActive.value = false;
    }
  } else {
    console.log("first box");
    activeItemId.value = sessionId;
    infoIconActive.value = true;
  }

  e.preventDefault();

  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      console.log("upper");
      element.classList.add('right-[1px]');   // both x and y are exceeded the parent container or only y has exceded the parent container
      element.classList.add(`top-[-70px]`);
    } else {
      console.log("lower");
      element.classList.add(`top-[75px]`);
      element.classList.add('right-[0px]');  // Only x has exceed the parent container
    }
  };
  if (infoIconActive.value){
    if (sessionCardDetails.value[sessionId]){
      console.log("displayed");
      // active item
      sessionCardDetails.value[sessionId].classList.remove(...listOfRemovalClass); // remove class
      dynamicAbsolutePositions(sessionCardDetails.value[sessionId]); // apply absolute positions depending on the current position (i.e is based on parent container)
      sessionCardDetails.value[sessionId].classList.add('flex');   // add class
    }

  } else {
    console.log("hidden");
    if (sessionCardDetails.value ? sessionCardDetails.value[sessionId].classList.contains('flex') : false){
      const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
      sessionCardDetails.value[sessionId].classList.remove(...listOfRemovalClass); // remove class
      sessionCardDetails.value[sessionId].classList.add('hidden');   // add class
    }
  }

}
function handleSearchMeetings (value) {
  searchText.value = value.toLowerCase();
}

const searchedSessionsData = computed(() => {
  return filterredSessionData.value.filter((item) => {
    if (!searchText.value) {
      return true;
    } // if no search, include all
    return item.leads?.[0]?.name?.toLowerCase().includes(searchText.value.toLowerCase());
  });
});

onClickOutside(dropdownMobileButton, () => {
  showMobileMeetings.value = false;
});
</script>

<template>
    <div class="h-full w-full select-none relative">
        <LoaderComp v-if="loader" class="z-[9999]"/>
        <MeetingsNavbar @toggleView="handleToggleView" @searchMeetings="handleSearchMeetings"  @openStartSessionModal="()=>{showQuickMeetingFormModal = true}" @openScheduleModal="()=>{showCalenderModal = true}"/>
        <div ref="dropdownMobileButton" v-if="Store.isMobile && !uistore.disableAddSessionIcon">
            <button :class="{'!bg-[#1E429F]':showMobileMeetings}" class="absolute w-[40px] h-[40px] rounded-full bottom-[60px] right-2 flex justify-center items-center bg-blue-600 z-[50]" @click="()=>showMobileMeetings = !showMobileMeetings">
                    <svg width="20" height="20" viewBox="0 0 20 20" class="transition-transform duration-300" :class="{ 'rotate-45 ': showMobileMeetings }" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="plus">
                    <path id="Vector" d="M16.4229 9.19687H10.8028V3.57679C10.8028 3.36386 10.7183 3.15965 10.5677 3.00908C10.4171 2.85851 10.2129 2.77393 9.99998 2.77393C9.78704 2.77393 9.58283 2.85851 9.43226 3.00908C9.2817 3.15965 9.19711 3.36386 9.19711 3.57679V9.19687H3.57704C3.3641 9.19687 3.15989 9.28145 3.00932 9.43202C2.85876 9.58259 2.77417 9.7868 2.77417 9.99973C2.77417 10.2127 2.85876 10.4169 3.00932 10.5674C3.15989 10.718 3.3641 10.8026 3.57704 10.8026H9.19711V16.4227C9.19711 16.6356 9.2817 16.8398 9.43226 16.9904C9.58283 17.141 9.78704 17.2255 9.99998 17.2255C10.2129 17.2255 10.4171 17.141 10.5677 16.9904C10.7183 16.8398 10.8028 16.6356 10.8028 16.4227V10.8026H16.4229C16.6358 10.8026 16.8401 10.718 16.9906 10.5674C17.1412 10.4169 17.2258 10.2127 17.2258 9.99973C17.2258 9.7868 17.1412 9.58259 16.9906 9.43202C16.8401 9.28145 16.6358 9.19687 16.4229 9.19687Z" fill="white"/>
                    </g>
                    </svg>
            </button>
                <div v-if="showMobileMeetings" class="absolute  w-[180px] flex flex-col gap-2 top-[62%] right-3 z-[50]">
                    <button @click="()=>{showQuickMeetingFormModal = true}" class="bg-white  hover:!bg-gray-100 px-3 py-[10px] flex items-center gap-4 shadow-xl  rounded-md " >
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="videocamera">
                        <g id="Vector">
                        <path d="M6.6 1.5H1.2C0.537258 1.5 0 2.07563 0 2.78571V9.21429C0 9.92437 0.537258 10.5 1.2 10.5H6.6C7.26274 10.5 7.79999 9.92437 7.79999 9.21429V2.78571C7.79999 2.07563 7.26274 1.5 6.6 1.5Z" fill="#1C64F2"/>
                        <path d="M11.7 2.33571C11.6091 2.2787 11.5058 2.24833 11.4005 2.24766C11.2952 2.24698 11.1915 2.27602 11.1 2.33186L8.99999 3.61114V8.45379L11.079 9.86807C11.17 9.92979 11.2747 9.96415 11.3824 9.96754C11.49 9.97093 11.5965 9.94323 11.6907 9.88735C11.7848 9.83146 11.8633 9.74946 11.9177 9.64992C11.9721 9.55039 12.0005 9.437 12 9.32164V2.89307C12.0001 2.78012 11.9724 2.66913 11.9198 2.57129C11.8671 2.47344 11.7913 2.39219 11.7 2.33571Z" fill="#1C64F2"/>
                        </g>
                        </g>
                        </svg>
                    <p class="text-sm">Quick Session</p></button>
                    <button class="bg-white hover:!bg-gray-100 px-3 py-[10px] flex items-center gap-4 shadow-xl  rounded-md" @click="()=>showCalenderModal = true">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="calendar-day" clip-path="url(#clip0_3240_4699)">
                        <g id="Vector">
                        <path d="M12 2.4C12 2.08174 11.8736 1.77652 11.6485 1.55147C11.4235 1.32643 11.1183 1.2 10.8 1.2H9.6V0.6C9.6 0.44087 9.53679 0.288258 9.42426 0.175736C9.31174 0.0632141 9.15913 0 9 0C8.84087 0 8.68826 0.0632141 8.57574 0.175736C8.46321 0.288258 8.4 0.44087 8.4 0.6V1.2H6.6V0.6C6.6 0.44087 6.53679 0.288258 6.42426 0.175736C6.31174 0.0632141 6.15913 0 6 0C5.84087 0 5.68826 0.0632141 5.57574 0.175736C5.46321 0.288258 5.4 0.44087 5.4 0.6V1.2H3.6V0.6C3.6 0.44087 3.53679 0.288258 3.42426 0.175736C3.31174 0.0632141 3.15913 0 3 0C2.84087 0 2.68826 0.0632141 2.57574 0.175736C2.46321 0.288258 2.4 0.44087 2.4 0.6V1.2H1.2C0.88174 1.2 0.576515 1.32643 0.351472 1.55147C0.126428 1.77652 0 2.08174 0 2.4V3.6H12V2.4Z" fill="#1C64F2"/>
                        <path d="M0 10.8C0 11.1183 0.126428 11.4235 0.351472 11.6485C0.576515 11.8736 0.88174 12 1.2 12H10.8C11.1183 12 11.4235 11.8736 11.6485 11.6485C11.8736 11.4235 12 11.1183 12 10.8V4.8H0V10.8ZM3 6H9C9.15913 6 9.31174 6.06321 9.42426 6.17574C9.53679 6.28826 9.6 6.44087 9.6 6.6C9.6 6.75913 9.53679 6.91174 9.42426 7.02426C9.31174 7.13679 9.15913 7.2 9 7.2H3C2.84087 7.2 2.68826 7.13679 2.57574 7.02426C2.46321 6.91174 2.4 6.75913 2.4 6.6C2.4 6.44087 2.46321 6.28826 2.57574 6.17574C2.68826 6.06321 2.84087 6 3 6Z" fill="#1C64F2"/>
                        </g>
                        </g>
                        <defs>
                        <clipPath id="clip0_3240_4699">
                        <rect width="12" height="12" fill="white"/>
                        </clipPath>
                        </defs>
                        </svg>
                        <p class="text-sm">Schedule Session</p></button>
                </div>
        </div>
        <div v-if="viewType === 'day'&& !Store.isMobile"  class="" :class="Store.isMobile?'h-[70%]':'h-[80%]'">
        <div class="w-full mx-auto flex items-center  bg-gray-50 py-2 relative select-none rounded-xl">
            <!-- Prev Button -->
            <div
            class="w-[40px] h-[40px] flex justify-center items-center cursor-pointer  left-3 rounded-xl shadow-xl bg-white border border-gray-300 text-lg text-black  backdrop-blur-sm "
            @click="prevDate"
            >
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
        </g>
        </svg>

            </div>

            <!-- Date Slider -->
            <div class="w-full overflow-x-auto whitespace-nowrap no-scrollbar ">
            <div class="flex gap-3">
                <div
                v-for="(item, index) in dates"
                :key="index"
                class="flex min-w-[60px] max-w-[60px] w-[60px] flex-col text-gray-400 items-center p-2 cursor-pointer"
                :class="{ '!text-blue-600 font-normal': selectedDate === item.fullDate }"
                @click="selectDate(item.fullDate)"
                >
                <span>{{ item.day }}</span>
                <span class="font-medium"> {{ item.date }} {{ item.month }}</span>
                </div>
            </div>
            </div>

            <!-- Next Button -->
            <div
            class="w-[40px] h-[40px] flex justify-center items-center cursor-pointer  right-3 bg-white border border-gray-300 rounded-lg text-lg text-black shadow-xl backdrop-blur-sm "
            @click="nextDate"
            >
              <svg width="14" height="14" style="transform: scaleX(-1)"
      viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g id="chevron-right">
              <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
              </g>
              </svg>
            </div>
        </div>
        <div ref="parentContainerRef" class=" overflow-y-scroll" :class="Store.isMobile?'h-[70%]':'h-[86%]'">
          <div v-if="filterredSessionData.length === 0" class="w-full h-full flex justify-center items-center">
            <p class="font-medium">UH-OH, THERE ARE NO SESSIONS BOOKED</p>
          </div>
          <div v-if="searchedSessionsData.length === 0 && filterredSessionData.length !== 0" class="w-full h-full flex justify-center items-center">
            <p class="font-medium">No Session Found</p>
          </div>
          {{ console.log("iiiiii",searchedSessionsData) }}
            <div v-for="item,itemId in searchedSessionsData" :key="itemId"
            :onmouseleave="()=> handleCloseMenuPopup(itemId)"
            class="flex w-full border-b-2 relative" :class="Store.isMobile?'py-2':'py-2'">
                <div class=" flex flex-col  text-gray-500 items-end" :class="Store.isMobile?'w-[23%] p-1':'w-[100px] p-2'">
                    <p class="font-semibold">{{ new Date(item.start).toLocaleTimeString('en-US',
                     {hour: 'numeric', minute: '2-digit', hour12: true}) }}</p>
                    <p class="text-gray-500 text-xs font-normal">{{ new Date(item.start).toLocaleDateString('en-US', {
                        day: '2-digit',
                        month: 'short'
                        }).replace(',', '') }}</p>
                </div>
                <div class="" :class="Store.isMobile?'w-[60%] p-1':'w-[83%] p-2'">
                    <p class="text-base font-semibold"> {{ item.leads && item.leads.length > 0 ? item.leads[0].name : '-' }}</p>
                    <p class="text-gray-500 text-xs font-normal">{{ item.leads && item.leads.length > 0 ? item.leads[0].email : '-' }}</p>
                </div>
                <div v-show="item.status !== 'ended' && item.status !== 'cancelled'" class=" flex   items-center" :class="Store.isMobile?'w-[20%] gap-1 justify-between':'w-[100px] gap-3 p-2 justify-end'">
                    <button class="flex justify-center items-center rounded-md border !border-blue-600 hover:!bg-blue-800 active:!bg-blue-800 group active:[outline:3px_solid_#b3d6fc] active:[outline-offset:-3px]" :class="Store.isMobile?'w-[25px] h-[25px]':'w-[30px] h-[30px]'" @click="()=>redirectToSalestool(item._id)">
                        <svg width="14" height="15" class="group-active:hidden group-hover:hidden" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="videocamera">
                        <g id="Vector">
                        <path d="M7.69999 2.25H1.4C0.626801 2.25 0 2.92157 0 3.75L0 11.25C0 12.0784 0.626801 12.75 1.4 12.75H7.69999C8.47319 12.75 9.09999 12.0784 9.09999 11.25V3.75C9.09999 2.92157 8.47319 2.25 7.69999 2.25Z" fill="#1C64F2"/>
                        <path d="M13.65 3.225C13.5439 3.15849 13.4234 3.12306 13.3006 3.12227C13.1777 3.12148 13.0568 3.15536 12.95 3.2205L10.5 4.713V10.3627L12.9255 12.0128C13.0316 12.0848 13.1539 12.1248 13.2794 12.1288C13.405 12.1327 13.5292 12.1004 13.6391 12.0352C13.749 11.97 13.8405 11.8744 13.904 11.7582C13.9675 11.6421 14.0006 11.5098 14 11.3752V3.87525C14.0001 3.74347 13.9678 3.61399 13.9064 3.49984C13.8449 3.38568 13.7565 3.29089 13.65 3.225Z" fill="#1C64F2"/>
                        </g>
                        </g>
                        </svg>
                        <svg width="14" height="15" class="hidden group-active:inline group-hover:inline" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="videocamera">
                          <g id="Vector">
                            <path d="M7.69999 2.25H1.4C0.626801 2.25 0 2.92157 0 3.75L0 11.25C0 12.0784 0.626801 12.75 1.4 12.75H7.69999C8.47319 12.75 9.09999 12.0784 9.09999 11.25V3.75C9.09999 2.92157 8.47319 2.25 7.69999 2.25Z" fill="#FFFFFF"/>
                            <path d="M13.65 3.225C13.5439 3.15849 13.4234 3.12306 13.3006 3.12227C13.1777 3.12148 13.0568 3.15536 12.95 3.2205L10.5 4.713V10.3627L12.9255 12.0128C13.0316 12.0848 13.1539 12.1248 13.2794 12.1288C13.405 12.1327 13.5292 12.1004 13.6391 12.0352C13.749 11.97 13.8405 11.8744 13.904 11.7582C13.9675 11.6421 14.0006 11.5098 14 11.3752V3.87525C14.0001 3.74347 13.9678 3.61399 13.9064 3.49984C13.8449 3.38568 13.7565 3.29089 13.65 3.225Z" fill="#FFFFFF"/>
                          </g>
                        </g>
                      </svg>
                    </button>
                    <div @click="(e)=>{if(Store.isMobile){handleMeetingsMobileMenu(e,itemId)}else{handleMeetingsMenu(e,itemId)}}" class="flex justify-center items-center border border-gray-500 rounded-full cursor-pointer"
                      :class="Store.isMobile?'w-[25px] h-[25px]':'w-[30px] h-[30px]'">
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="dots-vertical-outline">
                        <path id="Vector" d="M6.70078 1.7C6.70078 2.0866 6.38738 2.4 6.00078 2.4C5.61418 2.4 5.30078 2.0866 5.30078 1.7C5.30078 1.3134 5.61418 1 6.00078 1C6.38738 1 6.70078 1.3134 6.70078 1.7ZM6.70078 6.5C6.70078 6.8866 6.38738 7.2 6.00078 7.2C5.61418 7.2 5.30078 6.8866 5.30078 6.5C5.30078 6.1134 5.61418 5.8 6.00078 5.8C6.38738 5.8 6.70078 6.1134 6.70078 6.5ZM6.70078 11.3C6.70078 11.6866 6.38738 12 6.00078 12C5.61418 12 5.30078 11.6866 5.30078 11.3C5.30078 10.9134 5.61418 10.6 6.00078 10.6C6.38738 10.6 6.70078 10.9134 6.70078 11.3Z" fill="#6B7280" stroke="#6B7280"/>
                        </g>
                        </svg>
                    </div>
                    <ul
                            :ref="el => { if(el) meetingsCardMenuRef[itemId] = el}"
                            @onmouseleave.stop="() => handleCloseMenuPopup(itemId)"
                            :key="'sessionListMenuItem'+ itemId"
                            class="hidden absolute h-fit rounded-lg border-[#d8dbdf] bg-white active:!bg-gray-100 shadow border w-fit flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">
                            <li v-show="item.status === 'upcoming' || item.status === 'scheduled'" v-for="menuItem, menuItemKey in CancelSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'Cancel Session') {e.preventDefault();HandleCancelSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                            <li v-show="item.status === 'on-going' || item.status === 'active' || item.status === 'on-hold'" v-for="menuItem, menuItemKey in EndSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'End Session') {e.preventDefault();HandleEndSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                      </ul>
                </div>
            </div>
        </div>
        </div>
        <div v-if="viewType === 'day'&& Store.isMobile"  class="h-[70%]">
        <div class="w-full mx-auto flex items-center  bg-gray-50 py-2 relative select-none rounded-xl">
            <!-- Prev Button -->
            <div
            class="w-[40px] h-[40px] flex justify-center items-center cursor-pointer  left-3 rounded-xl shadow-xl bg-white border border-gray-300 text-lg text-black  backdrop-blur-sm "
            @click="prevDate"
            >
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
        </g>
        </svg>

            </div>

            <!-- Date Slider -->
            <div class="w-full overflow-x-auto whitespace-nowrap no-scrollbar ">
            <div class="flex gap-3">
                <div
                v-for="(item, index) in dates"
                :key="index"
                class="flex min-w-[60px] max-w-[60px] w-[60px] flex-col text-gray-400 items-center p-2 cursor-pointer"
                :class="{ '!text-blue-600 font-normal': selectedDate === item.fullDate }"
                @click="selectDate(item.fullDate)"
                >
                <span>{{ item.day }}</span>
                <span class="font-medium"> {{ item.date }} {{ item.month }}</span>
                </div>
            </div>
            </div>

            <!-- Next Button -->
            <div
            class="w-[40px] h-[40px] flex justify-center items-center cursor-pointer  right-3 bg-white border border-gray-300 rounded-lg text-lg text-black shadow-xl backdrop-blur-sm "
            @click="nextDate"
            >
              <svg width="14" height="14" style="transform: scaleX(-1)"
      viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g id="chevron-right">
              <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
              </g>
              </svg>
            </div>
        </div>
        <div ref="parentContainerRef" class=" overflow-y-scroll h-[71%]">
          <div v-if="filterredSessionData.length === 0" class="w-full h-full flex justify-center items-center">
            <p class="font-medium">UH-OH, THERE ARE NO SESSIONS BOOKED</p>
          </div>
          <div v-if="searchedSessionsData.length === 0 && filterredSessionData.length !== 0" class="w-full h-full flex justify-center items-center">
            <p class="font-medium">No Session Found</p>
          </div>
            <div v-for="item,itemId in searchedSessionsData" :key="itemId"
            class="flex w-full border-b-2 relative" :class="Store.isMobile?'py-2':'py-2'">
                <div class=" flex flex-col  text-gray-500 items-end justify-around " :class="Store.isMobile?'w-[20%] p-1':'w-[100px] p-2'">
                    <p class="text-gray-500 text-xs font-bold">{{ new Date(item.start).toLocaleTimeString('en-US',
                     {hour: 'numeric', minute: '2-digit', hour12: true}) }}</p>
                    <p class="text-gray-500 text-xs font-normal">{{ new Date(item.start).toLocaleDateString('en-US', {
                        day: '2-digit',
                        month: 'short'
                        }).replace(',', '') }}</p>
                </div>
                <div class="w-[60%] p-1">
                    <p class="text-gray-900 text-base font-semibold"> {{ item.leads && item.leads.length > 0 ? item.leads[0].name : '-' }}</p>
                    <p class="text-gray-500 text-xs font-normal">{{ Organizationstore.users[item.user_id] ? Organizationstore.users[item.user_id].name ?
                    Organizationstore.users[item.user_id].name : Organizationstore.users[item.user_id].email :
                    item.user_id }}</p>
                </div>
                <div v-show="item.status !== 'ended' && item.status !== 'cancelled'" class=" flex w-[20%] gap-1 justify-between  items-center">
                    <button class="flex justify-center items-center rounded-md border !border-blue-600 w-[26px] h-[26px]" @click="()=>redirectToSalestool(item._id)">
                        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="videocamera">
                        <g id="Vector">
                        <path d="M7.69999 2.25H1.4C0.626801 2.25 0 2.92157 0 3.75L0 11.25C0 12.0784 0.626801 12.75 1.4 12.75H7.69999C8.47319 12.75 9.09999 12.0784 9.09999 11.25V3.75C9.09999 2.92157 8.47319 2.25 7.69999 2.25Z" fill="#1C64F2"/>
                        <path d="M13.65 3.225C13.5439 3.15849 13.4234 3.12306 13.3006 3.12227C13.1777 3.12148 13.0568 3.15536 12.95 3.2205L10.5 4.713V10.3627L12.9255 12.0128C13.0316 12.0848 13.1539 12.1248 13.2794 12.1288C13.405 12.1327 13.5292 12.1004 13.6391 12.0352C13.749 11.97 13.8405 11.8744 13.904 11.7582C13.9675 11.6421 14.0006 11.5098 14 11.3752V3.87525C14.0001 3.74347 13.9678 3.61399 13.9064 3.49984C13.8449 3.38568 13.7565 3.29089 13.65 3.225Z" fill="#1C64F2"/>
                        </g>
                        </g>
                        </svg>
                    </button>
                    <div @click="(e)=>{handleMeetingsMobileMenu(e,itemId)}" class="flex justify-center items-center border border-gray-500 rounded-full cursor-pointer w-[25px] h-[25px]"
                      >
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="dots-vertical-outline">
                        <path id="Vector" d="M6.70078 1.7C6.70078 2.0866 6.38738 2.4 6.00078 2.4C5.61418 2.4 5.30078 2.0866 5.30078 1.7C5.30078 1.3134 5.61418 1 6.00078 1C6.38738 1 6.70078 1.3134 6.70078 1.7ZM6.70078 6.5C6.70078 6.8866 6.38738 7.2 6.00078 7.2C5.61418 7.2 5.30078 6.8866 5.30078 6.5C5.30078 6.1134 5.61418 5.8 6.00078 5.8C6.38738 5.8 6.70078 6.1134 6.70078 6.5ZM6.70078 11.3C6.70078 11.6866 6.38738 12 6.00078 12C5.61418 12 5.30078 11.6866 5.30078 11.3C5.30078 10.9134 5.61418 10.6 6.00078 10.6C6.38738 10.6 6.70078 10.9134 6.70078 11.3Z" fill="#6B7280" stroke="#6B7280"/>
                        </g>
                        </svg>
                    </div>
                    <ul
                            :ref="el => { if(el) meetingsCardMenuRef[itemId] = el}"
                            @onmouseleave.stop="() => handleCloseMenuPopup(itemId)"
                            :key="'sessionListMenuItem'+ itemId"
                            class="hidden absolute h-fit rounded-lg border-[#d8dbdf] bg-white active:!bg-gray-100 shadow border w-fit flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">
                            <li v-show="item.status === 'upcoming' || item.status === 'scheduled'" v-for="menuItem, menuItemKey in CancelSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'Cancel Session') {e.preventDefault();HandleCancelSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                            <li v-show="item.status === 'on-going' || item.status === 'active' || item.status === 'on-hold'" v-for="menuItem, menuItemKey in EndSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'End Session') {e.preventDefault();HandleEndSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                      </ul>
                </div>
            </div>
        </div>
        </div>
        <div v-if="viewType === 'list'&& !Store.isMobile" :class="Store.isMobile?'h-[65%]':'h-[80%]'">
          <div class="h-[100%] max-h-[100%] overflow-y-auto overflow-x-auto infinite_scrollbar">
          <table  class="w-full rounded-lg bg-transparent">
            <thead class="bg-gray-100 dark:bg-bg-150">
              <tr>
                <th v-for="(item, index) in session_fields" :key="index"
                  class="px-3 py-4 text-left text-xs uppercase font-semibold text-gray-500">{{ item }}</th>
              </tr>
            </thead>
            <tbody ref="parentContainerRef" class="overflow-y-scroll">
              <tr v-for="item,itemId in processedData" :key="itemId" class="border-b-2 !min-h-[50px]"
              :onmouseleave="()=> handleCloseMenuPopup(itemId)">
                <td v-if="item.showDay" :rowspan="item.rowspan" class="p-0 relative align-middle border !bg-gray-50">
                  <div  class="px-2 py-2 rounded-lg  text-center h-full w-full mx-auto">
                      <p class="text-gray-500 text-sm font-normal">{{ getDateTag(item.start) }}</p>
                      <p class="text-gray-900 text-sm font-medium">{{ getFormattedDate(item.start) }}</p>
                  </div>

                </td>
                <td class="p-3 text-txt-50 whitespace-nowrap">
                  <div class="flex items-center gap-1">
                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="clock" clip-path="url(#clip0_5947_21259)">
                    <path id="Vector" d="M8.7427 8.84281L8.74281 8.8427C8.94903 8.63642 9.06487 8.35668 9.06487 8.065C9.06487 7.77332 8.94903 7.49359 8.74281 7.2873L8.74276 7.28725L7.1 5.6445V3.70001C7.1 3.40827 6.98411 3.12848 6.77782 2.92219C6.57153 2.7159 6.29174 2.60001 6 2.60001C5.70826 2.60001 5.42847 2.7159 5.22218 2.92219C5.0159 3.12848 4.9 3.40827 4.9 3.70001L4.9 6.1L4.90001 6.10257C4.9015 6.39296 5.01701 6.67114 5.22166 6.87717L5.22285 6.87836L7.18725 8.84276L7.1873 8.84281C7.39358 9.04903 7.67332 9.16488 7.965 9.16488C8.25668 9.16488 8.53642 9.04903 8.7427 8.84281ZM2.94437 1.52692C3.84876 0.922627 4.91203 0.600061 5.99973 0.600006C7.45797 0.601679 8.85602 1.18171 9.88716 2.21285C10.9184 3.24405 11.4984 4.64221 11.5 6.10055C11.4999 7.18816 11.1773 8.25132 10.5731 9.15564C9.96874 10.0601 9.10975 10.7651 8.10476 11.1813C7.09977 11.5976 5.9939 11.7065 4.92701 11.4943C3.86011 11.2821 2.8801 10.7583 2.11092 9.98909C1.34173 9.2199 0.817902 8.2399 0.605684 7.173C0.393465 6.10611 0.502383 5.00024 0.918665 3.99525C1.33495 2.99025 2.0399 2.13127 2.94437 1.52692Z" fill="#6B7280" stroke="#6B7280"/>
                    </g>
                    <defs>
                    <clipPath id="clip0_5947_21259">
                    <rect width="12" height="12" fill="white" transform="translate(0 0.100006)"/>
                    </clipPath>
                    </defs>
                    </svg>
                    <p class="text-sm text-gray-500 font-medium">
                    {{ new Date(item.start).toLocaleTimeString('en-GB', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true,
                      timeZone: 'Asia/Kolkata'
                    }).replace(/(am|pm)/, '').trim() }} -
                    {{ new Date(item.end_time).toLocaleTimeString('en-GB', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true,
                      timeZone: 'Asia/Kolkata'
                    }).replace(/(am|pm)/, '').trim() }}

                    </p>
                  </div>
                </td>
                <td class="p-3 whitespace-nowrap">
                    -
                </td>
                <td class="p-3 whitespace-nowrap">
                  <div class="flex items-center">
                    <p>{{ item.leads && item.leads.length > 0 ? item.leads[0].name : '-' }}</p>
                  </div>
                </td>
                <td class="p-3 whitespace-nowrap">
                  <div class="flex items-center">
                    <p>{{ item.leads && item.leads.length > 0 ? item.leads[0].email  : '-' }}</p>
                  </div>
                </td>
                <td class="p-3 whitespace-nowrap">
                  <div class="flex items-center">
                    <p>{{ item.leads && item.leads.length > 0 ? item.leads[0].phone_number ? item.leads[0].phone_number:'-': '-' }}</p>
                  </div>
                </td>
                <td class="p-2 relative">
                   <!-- Actions -->
                    <div v-show="item.status !== 'ended' && item.status !== 'cancelled'" class="w-full flex flex-col items-center justify-around sm:flex-row gap-4">
                      <button
                      class="!w-[80px] group !border text-blue-600  !border-blue-600 hover:!bg-blue-800  hover:!text-white hover:!border-blue-800 active:!bg-blue-800 active:!text-white active:[outline:3px_solid_#b3d6fc] active:[outline-offset:-3px] p-2 flex items-center justify-center gap-2 rounded-md "  @click="()=>redirectToSalestool(item._id)">
                      <svg width="14" height="15" class="group-active:hidden group-hover:hidden" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="videocamera">
                        <g id="Vector">
                        <path d="M7.69999 2.45001H1.4C0.626801 2.45001 0 3.12159 0 3.95001L0 11.45C0 12.2784 0.626801 12.95 1.4 12.95H7.69999C8.47319 12.95 9.09999 12.2784 9.09999 11.45V3.95001C9.09999 3.12159 8.47319 2.45001 7.69999 2.45001Z" fill="#1C64F2"/>
                        <path d="M13.65 3.42501C13.5439 3.3585 13.4234 3.32307 13.3006 3.32228C13.1777 3.32149 13.0568 3.35537 12.95 3.42051L10.5 4.91301V10.5628L12.9255 12.2128C13.0316 12.2848 13.1539 12.3248 13.2794 12.3288C13.405 12.3328 13.5292 12.3004 13.6391 12.2353C13.749 12.1701 13.8405 12.0744 13.904 11.9583C13.9675 11.8421 14.0006 11.7098 14 11.5753V4.07526C14.0001 3.94349 13.9678 3.814 13.9064 3.69985C13.8449 3.58569 13.7565 3.4909 13.65 3.42501Z" fill="#1C64F2"/>
                        </g>
                        </g>
                      </svg>
                      <svg width="14" height="15" class="hidden group-active:inline group-hover:inline" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="videocamera">
                          <g id="Vector">
                            <path d="M7.69999 2.25H1.4C0.626801 2.25 0 2.92157 0 3.75L0 11.25C0 12.0784 0.626801 12.75 1.4 12.75H7.69999C8.47319 12.75 9.09999 12.0784 9.09999 11.25V3.75C9.09999 2.92157 8.47319 2.25 7.69999 2.25Z" fill="#FFFFFF"/>
                            <path d="M13.65 3.225C13.5439 3.15849 13.4234 3.12306 13.3006 3.12227C13.1777 3.12148 13.0568 3.15536 12.95 3.2205L10.5 4.713V10.3627L12.9255 12.0128C13.0316 12.0848 13.1539 12.1248 13.2794 12.1288C13.405 12.1327 13.5292 12.1004 13.6391 12.0352C13.749 11.97 13.8405 11.8744 13.904 11.7582C13.9675 11.6421 14.0006 11.5098 14 11.3752V3.87525C14.0001 3.74347 13.9678 3.61399 13.9064 3.49984C13.8449 3.38568 13.7565 3.29089 13.65 3.225Z" fill="#FFFFFF"/>
                          </g>
                        </g>
                      </svg>
                      <p class="text-sm font-medium">Join</p>
                      </button>
                    <button @click="(e)=>{handleMeetingsMenu(e,itemId)}" class="flex justify-center items-center border border-gray-500 rounded-full cursor-pointer"
                      :class="Store.isMobile?'w-[25px] h-[25px]':'w-[30px] h-[30px]'">
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="dots-vertical-outline">
                        <path id="Vector" d="M6.70078 1.7C6.70078 2.0866 6.38738 2.4 6.00078 2.4C5.61418 2.4 5.30078 2.0866 5.30078 1.7C5.30078 1.3134 5.61418 1 6.00078 1C6.38738 1 6.70078 1.3134 6.70078 1.7ZM6.70078 6.5C6.70078 6.8866 6.38738 7.2 6.00078 7.2C5.61418 7.2 5.30078 6.8866 5.30078 6.5C5.30078 6.1134 5.61418 5.8 6.00078 5.8C6.38738 5.8 6.70078 6.1134 6.70078 6.5ZM6.70078 11.3C6.70078 11.6866 6.38738 12 6.00078 12C5.61418 12 5.30078 11.6866 5.30078 11.3C5.30078 10.9134 5.61418 10.6 6.00078 10.6C6.38738 10.6 6.70078 10.9134 6.70078 11.3Z" fill="#6B7280" stroke="#6B7280"/>
                        </g>
                        </svg>
                    </button>
                    <ul
                            :ref="el => { if(el) meetingsCardMenuRef[itemId] = el}"
                            @onmouseleave.stop="() => handleCloseMenuPopup(itemId)"
                            :key="'sessionListMenuItem'+ itemId"
                            class="hidden absolute h-fit rounded-lg border-[#d8dbdf] active:!bg-gray-100 !bg-white shadow border w-fit flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">
                            <li v-show="item.status === 'on-going' || item.status === 'active' || item.status === 'on-hold'" v-for="menuItem, menuItemKey in EndSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'End Session') {e.preventDefault();HandleEndSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                            <li v-show="item.status === 'upcoming' || item.status === 'scheduled'" v-for="menuItem, menuItemKey in CancelSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'Cancel Session') {e.preventDefault();HandleCancelSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                      </ul>
                    </div>
                </td>
              </tr>
            </tbody>
          </table>
            <div v-if="processedData.length === 0" class="!w-full !h-[80%] flex justify-center items-center">
              <p class="font-medium">UH-OH, THERE ARE NO SESSIONS</p>
            </div>
          </div>
        </div>
        <div  v-if="viewType === 'list' && Store.isMobile" class="h-[65%]">
          <div v-if="groupedSessions.length === 0" class="w-full h-full flex justify-center items-center">
            <p>No Session Found</p>
          </div>
          <div ref="parentContainerRef" class="max-h-[100%] h-[100%] overflow-y-auto infinite_scrollbar">
            <div v-for="group,index in groupedSessions" :key="index" class="mt-2 mb-4"
           v-show="searchText.length > 0 ? group.sessions.some(item => item.leads[0].name.toLowerCase().includes(searchText.toLowerCase())) : true">

              <!-- Date Header -->
              <div class="bg-gray-50 px-4 py-2 rounded-t-lg text-center text-xs font-medium text-gray-700">
                 <p class="text-gray-500 text-sm font-medium">{{ new Date(group.date).getFullYear() === new Date().getFullYear() ?  getDateTag(group.date) : new Date(group.date).getFullYear()}}</p>
                <p class="text-gray-900 text-sm font-normal">{{ new Date(group.date).getDate() }} {{ new Date(group.date).toLocaleString('default', { month: 'short' }) }}</p>

              </div>
              <!-- Sessions -->
              <div v-for="(session) in group.sessions" :key="session._id" class="bg-white relative p-4 border-b border-gray-200 flex items-center justify-between"
               v-show="searchText.length > 0 ? session.leads[0].name?.toLowerCase().includes(searchText):true">
                <div class="flex gap-3">
                <div>
                  <p class="text-sm font-medium text-gray-800 flex gap-1 items-center">
                  <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="clock" clip-path="url(#clip0_5947_21259)">
                    <path id="Vector" d="M8.7427 8.84281L8.74281 8.8427C8.94903 8.63642 9.06487 8.35668 9.06487 8.065C9.06487 7.77332 8.94903 7.49359 8.74281 7.2873L8.74276 7.28725L7.1 5.6445V3.70001C7.1 3.40827 6.98411 3.12848 6.77782 2.92219C6.57153 2.7159 6.29174 2.60001 6 2.60001C5.70826 2.60001 5.42847 2.7159 5.22218 2.92219C5.0159 3.12848 4.9 3.40827 4.9 3.70001L4.9 6.1L4.90001 6.10257C4.9015 6.39296 5.01701 6.67114 5.22166 6.87717L5.22285 6.87836L7.18725 8.84276L7.1873 8.84281C7.39358 9.04903 7.67332 9.16488 7.965 9.16488C8.25668 9.16488 8.53642 9.04903 8.7427 8.84281ZM2.94437 1.52692C3.84876 0.922627 4.91203 0.600061 5.99973 0.600006C7.45797 0.601679 8.85602 1.18171 9.88716 2.21285C10.9184 3.24405 11.4984 4.64221 11.5 6.10055C11.4999 7.18816 11.1773 8.25132 10.5731 9.15564C9.96874 10.0601 9.10975 10.7651 8.10476 11.1813C7.09977 11.5976 5.9939 11.7065 4.92701 11.4943C3.86011 11.2821 2.8801 10.7583 2.11092 9.98909C1.34173 9.2199 0.817902 8.2399 0.605684 7.173C0.393465 6.10611 0.502383 5.00024 0.918665 3.99525C1.33495 2.99025 2.0399 2.13127 2.94437 1.52692Z" fill="#6B7280" stroke="#6B7280"/>
                    </g>
                    <defs>
                    <clipPath id="clip0_5947_21259">
                    <rect width="12" height="12" fill="white" transform="translate(0 0.100006)"/>
                    </clipPath>
                    </defs>
                    </svg>
                    {{ new Date(session.start).toLocaleTimeString('en-GB', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true,
                      timeZone: 'Asia/Kolkata'
                    }).replace(/(am|pm)/, '').trim() }} -
                    {{ new Date(session.end_time).toLocaleTimeString('en-GB', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true,
                      timeZone: 'Asia/Kolkata'
                    }).replace(/(am|pm)/, '').trim() }}
                  </p>
                  <div class="flex gap-8">
                    <p class="text-gray-900 text-sm font-medium capitalize">{{ session.leads?.[0]?.name || '-' }}</p>
                  </div>
                </div>
                <div class="flex justify-end items-end">
                    <span :key="session._id" class="cursor-pointer" v-html="activeItemId === session._id ? infoIcon.inActive : infoIcon.active" @click="(e) =>{handleSessionDetails(e,session._id)}"></span>
                </div>
                <div :ref="el => { if(el) sessionCardDetails[session._id] = el}"
                     :key="'sessionCardDetails'+ session._id"
                 class="hidden flex-col gap-2 absolute bottom-[-80px] bg-white left-2 border w-[220px] h-fit z-20 px-2 py-3 rounded-lg">
                  <div class="flex items-center gap-[10px]">
                           <p class="text-[#111928] text-sm font-medium capitalize">{{ session.leads?.[0]?.name || '-' }}</p>
                  </div>
                  <div class="flex flex-col gap-[10px]">
                    <span class="flex gap-2 items-center">
                      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g id="envelope">
                      <g id="Vector">
                      <path d="M7 7.6398L13.4925 2.0804C13.2432 1.86859 12.9271 1.75158 12.6 1.75H1.4C1.07287 1.75158 0.756802 1.86859 0.5075 2.0804L7 7.6398Z" fill="#6B7280"/>
                      <path d="M7.875 8.7339C7.62023 8.92877 7.30884 9.03519 6.9881 9.037C6.6892 9.03761 6.39824 8.94079 6.1593 8.7612L0 3.4888V10.85C0 11.2213 0.1475 11.5774 0.41005 11.8399C0.672601 12.1025 1.0287 12.25 1.4 12.25H12.6C12.9713 12.25 13.3274 12.1025 13.5899 11.8399C13.8525 11.5774 14 11.2213 14 10.85V3.4888L7.875 8.7339Z" fill="#6B7280"/>
                      </g>
                      </g>
                      </svg>
                      <p>{{ session.leads && session.leads.length > 0 ? session.leads[0].email  : '-' }}</p>
                    </span>
                    <span class="flex gap-2 items-center">
                      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g id="phone" clip-path="url(#clip0_5946_25605)">
                      <path id="Vector" d="M6.68646 9.16067C7.01188 9.48503 7.45264 9.66717 7.91214 9.66717C8.37164 9.66717 8.8124 9.48503 9.13782 9.16067L9.75066 8.54793C10.0761 8.22357 10.5168 8.04144 10.9763 8.04144C11.4358 8.04144 11.8766 8.22357 12.202 8.54793L13.4277 9.77342C13.6069 9.92344 13.751 10.1109 13.8499 10.3227C13.9488 10.5344 14 10.7652 14 10.9989C14 11.2326 13.9488 11.4634 13.8499 11.6752C13.751 11.8869 13.6069 12.0744 13.4277 12.2244C10.2182 15.4343 6.52362 13.9095 3.30447 10.6908C0.0853122 7.47212 -1.4319 3.78164 1.7785 0.571733C1.92863 0.392706 2.11617 0.248741 2.32792 0.149964C2.53968 0.0511883 2.77051 0 3.00418 0C3.23784 0 3.46867 0.0511883 3.68043 0.149964C3.89218 0.248741 4.07972 0.392706 4.22985 0.571733L5.45553 1.79722C5.78015 2.12248 5.96246 2.56322 5.96246 3.02272C5.96246 3.48221 5.78015 3.92295 5.45553 4.24821L4.84269 4.86095C4.51807 5.18621 4.33576 5.62694 4.33576 6.08644C4.33576 6.54594 4.51807 6.98668 4.84269 7.31193L6.68646 9.16067Z" fill="#6B7280"/>
                      </g>
                      <defs>
                      <clipPath id="clip0_5946_25605">
                      <rect width="14" height="14" fill="white"/>
                      </clipPath>
                      </defs>
                      </svg>
                      <p>{{ session.leads && session.leads.length > 0 ? session.leads[0].phone_number ? session.leads[0].phone_number:'-': '-' }}</p>
                    </span>
                  </div>
                </div>
                </div>
                <!-- Actions -->
                <div v-show="session.status !== 'ended' && session.status !== 'cancelled'" class="flex items-center space-x-2 ">
                  <button class="border !border-blue-600 p-2 rounded-md">
                      <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg"  @click="()=>redirectToSalestool(session._id)">
                        <g id="videocamera">
                        <g id="Vector">
                        <path d="M7.69999 2.45001H1.4C0.626801 2.45001 0 3.12159 0 3.95001L0 11.45C0 12.2784 0.626801 12.95 1.4 12.95H7.69999C8.47319 12.95 9.09999 12.2784 9.09999 11.45V3.95001C9.09999 3.12159 8.47319 2.45001 7.69999 2.45001Z" fill="#1C64F2"/>
                        <path d="M13.65 3.42501C13.5439 3.3585 13.4234 3.32307 13.3006 3.32228C13.1777 3.32149 13.0568 3.35537 12.95 3.42051L10.5 4.91301V10.5628L12.9255 12.2128C13.0316 12.2848 13.1539 12.3248 13.2794 12.3288C13.405 12.3328 13.5292 12.3004 13.6391 12.2353C13.749 12.1701 13.8405 12.0744 13.904 11.9583C13.9675 11.8421 14.0006 11.7098 14 11.5753V4.07526C14.0001 3.94349 13.9678 3.814 13.9064 3.69985C13.8449 3.58569 13.7565 3.4909 13.65 3.42501Z" fill="#1C64F2"/>
                        </g>
                        </g>
                      </svg>

                  </button>
                  <div @click="(e)=>handleMeetingsMobileMenu(e,session._id)" class="border border-gray-400 rounded-full p-2 cursor-pointer">
                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="dots-vertical-outline">
                        <path id="Vector" d="M6.70078 1.7C6.70078 2.0866 6.38738 2.4 6.00078 2.4C5.61418 2.4 5.30078 2.0866 5.30078 1.7C5.30078 1.3134 5.61418 1 6.00078 1C6.38738 1 6.70078 1.3134 6.70078 1.7ZM6.70078 6.5C6.70078 6.8866 6.38738 7.2 6.00078 7.2C5.61418 7.2 5.30078 6.8866 5.30078 6.5C5.30078 6.1134 5.61418 5.8 6.00078 5.8C6.38738 5.8 6.70078 6.1134 6.70078 6.5ZM6.70078 11.3C6.70078 11.6866 6.38738 12 6.00078 12C5.61418 12 5.30078 11.6866 5.30078 11.3C5.30078 10.9134 5.61418 10.6 6.00078 10.6C6.38738 10.6 6.70078 10.9134 6.70078 11.3Z" fill="#6B7280" stroke="#6B7280"/>
                        </g>
                    </svg>
                  </div>
                  <ul
                      :ref="el => { if(el) meetingsCardMenuRef[session._id] = el}"

                        :key="'sessionListMenuItem'+ session._id"
                        class="hidden absolute h-fit rounded-lg border-[#d8dbdf] bg-white shadow border w-fit flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">
                            <li v-show="session.status === 'on-going' || session.status === 'active' || session.status === 'on-hold'" v-for="menuItem, menuItemKey in EndSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'End Session') {e.preventDefault();HandleEndSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                            <li v-show="session.status === 'upcoming' || session.status === 'scheduled'" v-for="menuItem, menuItemKey in CancelSessionSessionListMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'Cancel Session') {e.preventDefault();HandleCancelSession(item._id)}} " :class="['text-nowrap text-gray-500 text-sm flex font-medium justify-start items-center gap-2 cursor-pointer w-full px-3 py-2.5']">
                                    <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                    {{ menuItem.label }}
                            </li>
                      </ul>
                </div>
              </div>
              </div>

          </div>
        </div>
        <Modal v-if="!Store.isMobile" :open="showCalenderModal" class="z-[999]">
            <div class="bg-white flex flex-col gap-3 p-4 rounded-lg  h-[80%]  relative" :class="Store.isMobile?'w-full h-full':'w-[50%] relative top-[5%]'">
                <div class="w-full h-[10%] flex justify-between items-center">
                    <div class="h-full flex justify-start">
                        <p class="text-gray-900 text-lg font-extrabold relative top-[-5px]">Select Date & Time</p>
                    </div>
                    <div class="h-full flex gap-7 items-center ">
                      <div class="h-full flex justify-start">
                        <span class="w-[200px] h-[40px] relative !bg-sky-100 rounded-lg">
                                <svg width="18" height="18" viewBox="0 0 18 18" class="absolute left-3 top-[11px]" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g id="globe">
                                <path id="Vector" d="M9 1.5C7.51664 1.5 6.0666 1.93987 4.83323 2.76398C3.59986 3.58809 2.63856 4.75943 2.07091 6.12987C1.50325 7.50032 1.35472 9.00832 1.64411 10.4632C1.9335 11.918 2.64781 13.2544 3.6967 14.3033C4.7456 15.3522 6.08197 16.0665 7.53683 16.3559C8.99168 16.6453 10.4997 16.4968 11.8701 15.9291C13.2406 15.3614 14.4119 14.4001 15.236 13.1668C16.0601 11.9334 16.5 10.4834 16.5 9C16.5 8.01509 16.306 7.03982 15.9291 6.12987C15.5522 5.21993 14.9997 4.39314 14.3033 3.6967C13.6069 3.00026 12.7801 2.44781 11.8701 2.0709C10.9602 1.69399 9.98492 1.5 9 1.5ZM7.71675 14.8447C6.8988 14.6579 6.12921 14.3014 5.45781 13.7982C4.78641 13.295 4.22816 12.6565 3.81922 11.9239C3.41028 11.1912 3.15976 10.3809 3.08389 9.54533C3.00801 8.70975 3.10847 7.86754 3.37875 7.07325L3.56775 7.08825C3.96758 7.27898 4.32148 7.55384 4.60523 7.89402C4.88897 8.23421 5.09587 8.63169 5.21175 9.05925C5.3364 9.55308 5.57587 10.0105 5.91067 10.3943C6.24548 10.7781 6.66617 11.0775 7.1385 11.268C8.0445 11.6858 8.23425 12.8572 7.71675 14.8447ZM10.5593 7.875C10.5592 7.81509 10.5519 7.75541 10.5375 7.69725C10.4018 7.08236 10.1327 6.50478 9.74918 6.00531C9.36571 5.50585 8.87723 5.09669 8.31825 4.80675C7.662 4.3905 7.25475 4.13175 7.14525 3.3135C8.31292 2.92008 9.57349 2.89622 10.7552 3.24517C11.9369 3.59411 12.9823 4.2989 13.749 5.2635C12.654 5.42775 12.0593 7.02975 12.0593 7.87425C11.9792 7.99808 11.8674 8.09819 11.7354 8.16414C11.6035 8.23009 11.4564 8.25948 11.3093 8.24925C11.1622 8.25956 11.0151 8.23029 10.8832 8.16447C10.7513 8.09865 10.6394 7.9987 10.5593 7.875ZM11.8433 14.2297L11.313 12.6405C11.3386 12.2794 11.4939 11.9398 11.7503 11.6842C12.0067 11.4287 12.3468 11.2744 12.708 11.25L14.3123 11.7382C13.7578 12.7976 12.8975 13.6657 11.8433 14.2297Z" fill="#1C64F2"/>
                                </g>
                                </svg>
                                <Multiselect :allow-empty="false"  v-bind="field" v-model="selected_timezone" :searchable="true"
                                :close-on-select="true" :show-labels="false" placeholder="Choose" :options="timezones"
                                maxHeight="150" class="w-full h-full !text-[#2563eb] !text-xs !font-medium relative cursor-pointer">
                                </Multiselect>
                        </span>
                      </div>
                        <div class="h-full cursor-pointer flex justify-start" @click="()=>{showCalenderModal = false;selected_timezone='Asia/Kolkata'}">
                            <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g id="x" clip-path="url(#clip0_707_6588)">
                                <path id="Vector" d="M5.90741 5L9.30409 1.60332C9.36538 1.54412 9.41427 1.47331 9.4479 1.39502C9.48153 1.31672 9.49924 1.23252 9.49998 1.14731C9.50072 1.0621 9.48448 0.977595 9.45221 0.898729C9.41995 0.819863 9.3723 0.748212 9.31204 0.687958C9.25179 0.627705 9.18014 0.580054 9.10127 0.547787C9.0224 0.515521 8.9379 0.499284 8.85269 0.500024C8.76748 0.500765 8.68328 0.518468 8.60498 0.5521C8.52669 0.585733 8.45588 0.634621 8.39668 0.695913L5 4.09259L1.60332 0.695913C1.48229 0.579017 1.32019 0.514333 1.15193 0.515796C0.983666 0.517258 0.822712 0.584748 0.70373 0.70373C0.584748 0.822712 0.517258 0.983666 0.515796 1.15193C0.514333 1.32019 0.579017 1.48229 0.695913 1.60332L4.09259 5L0.695913 8.39668C0.634621 8.45588 0.585733 8.52669 0.5521 8.60498C0.518468 8.68328 0.500765 8.76748 0.500024 8.85269C0.499284 8.9379 0.515521 9.0224 0.547787 9.10127C0.580054 9.18014 0.627705 9.25179 0.687958 9.31204C0.748212 9.3723 0.819863 9.41995 0.898729 9.45221C0.977595 9.48448 1.0621 9.50072 1.14731 9.49998C1.23252 9.49924 1.31672 9.48153 1.39502 9.4479C1.47331 9.41427 1.54412 9.36538 1.60332 9.30409L5 5.90741L8.39668 9.30409C8.51771 9.42098 8.67981 9.48567 8.84807 9.4842C9.01633 9.48274 9.17729 9.41525 9.29627 9.29627C9.41525 9.17729 9.48274 9.01633 9.4842 8.84807C9.48567 8.67981 9.42098 8.51771 9.30409 8.39668L5.90741 5Z" fill="#6B7280"/>
                                </g>
                                <defs>
                                <clipPath id="clip0_707_6588">
                                <rect width="10" height="10" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                        </div>
                    </div>

                </div>
                <div class="h-[78%] w-full">
                <vue-cal
                    id="vuecal"
                    ref="vuecal"
                    :events="events"
                    :disable-views="['years','year','month','week']"
                    hide-view-selector
                    hide-weekends
                    :editable-events="{ title: true, drag: true, resize: true, delete: true, create: true }"
                    :snap-to-time="10"
                    timeCellHeight="70"
                     today-button
                     :selected-date="selectedDate"
                    @event-drag-create="handleEventDrag"
                    :on-event-click="handleEventClick"
                    >
                    <template #arrow-prev>
                      <div class="w-full h-full flex justify-center items-center" @click="(e)=>InCalenderPrevButton(e)">
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <g id="chevron-down">
                              <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
                              </g>
                              </svg>
                      </div>
                    </template>
                    <template #arrow-next>
                      <div class="w-full h-full flex justify-center items-center" @click="(e)=>{InCalenderNextButton(e)}">
                          <svg width="14" height="14" style="transform: scaleX(-1)"
                  viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g id="chevron-right">
                          <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
                          </g>
                          </svg>
                      </div>

                    </template>
                  <template #today-button>
                    <div class="bg-white p-2 !relative !left-[-20px] rounded-md text-gray-400">Today</div>
                  </template>
                <template #time-cell="{ hours, minutes }">
                    <div :class="{ 'vuecal__time-cell-line': true, hours: !minutes }">
                    <span v-if="!minutes" class="!text-xs">{{ formatHour(hours) }}</span>
                    </div>
                </template>
                  <template #event="{ event }">
                    <div class="vuecal__event-title !absolute left-3" v-html="event.title" />
                    <small v-if="event.title==='Available' || event.title===''" class="vuecal__event-time absolute right-3 text-blue-600 !text-xs">
                      <span>{{ event.start.toLocaleTimeString('en-US',
                     {hour: 'numeric', minute: '2-digit', hour12: true}) }}</span> to
                      <span>{{ event.end.toLocaleTimeString('en-US',
                     {hour: 'numeric', minute: '2-digit', hour12: true}) }}</span>
                    </small>
                  </template>
                </vue-cal>
                </div>
                <div class="flex justify-end">
                    <button :class="disableButton?'cursor-not-allowed':''" class="bg-[#1c64f2] w-[50%] text-white p-2 rounded-md" @click="()=>{if(!disableButton){showFormModal = true;showCalenderModal= false}}">Next</button>
                </div>

            </div>
        </Modal>
        <Modal v-if="Store.isMobile" :open="showCalenderModal" :preventOverflow="true" class="z-[999]">
            <div class="bg-white flex flex-col relative" :class="Store.isMobile?'w-screen h-full':'w-[50%] p-4'">
                    <div class="flex w-full gap-5 items-center border-b-2 h-[9%] py-1 px-2">
                      <span class="w-8 flex justify-center bg-gray-100 p-2 rounded-md" @click="()=>{showCalenderModal = false;selected_timezone='Asia/Kolkata'}">
                        <svg width="6" height="14" viewBox="0 0 6 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path id="Back" d="M0 7.0039C0 7.16006 0.0446808 7.29279 0.140426 7.4021L5.24681 13.3438C5.32979 13.4453 5.4383 13.5 5.55958 13.5C5.80851 13.5 6 13.2736 6 12.9613C6 12.8129 5.94894 12.6802 5.87872 12.5865L1.07872 7.0039L3.47872 4.21261L5.87872 1.42132C5.94894 1.31982 6 1.18709 6 1.03874C6 0.734234 5.80851 0.5 5.55958 0.5C5.4383 0.5 5.32979 0.554655 5.24681 0.656156L0.140426 6.60571C0.0446808 6.71501 0 6.84775 0 7.0039Z" fill="black"/>
                        </svg>
                      </span>
                        <p class="text-xl font-bold">Schedule Session</p>
                    </div>
              <div class="bg-gray-50 h-[90%] flex flex-col gap-4">
                <div class="flex w-full justify-between items-center mt-2 px-3">
                          <div>
                              <p class="text-gray-900 text-base font-extrabold">Select Data & Time</p>
                          </div>
                          <div class="flex gap-7 items-center">
                          <div class="w-[180px] h-[40px] relative !bg-sky-100 rounded-lg">
                                  <svg width="18" height="18" viewBox="0 0 18 18" class="absolute left-3 top-[11px]" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <g id="globe">
                                  <path id="Vector" d="M9 1.5C7.51664 1.5 6.0666 1.93987 4.83323 2.76398C3.59986 3.58809 2.63856 4.75943 2.07091 6.12987C1.50325 7.50032 1.35472 9.00832 1.64411 10.4632C1.9335 11.918 2.64781 13.2544 3.6967 14.3033C4.7456 15.3522 6.08197 16.0665 7.53683 16.3559C8.99168 16.6453 10.4997 16.4968 11.8701 15.9291C13.2406 15.3614 14.4119 14.4001 15.236 13.1668C16.0601 11.9334 16.5 10.4834 16.5 9C16.5 8.01509 16.306 7.03982 15.9291 6.12987C15.5522 5.21993 14.9997 4.39314 14.3033 3.6967C13.6069 3.00026 12.7801 2.44781 11.8701 2.0709C10.9602 1.69399 9.98492 1.5 9 1.5ZM7.71675 14.8447C6.8988 14.6579 6.12921 14.3014 5.45781 13.7982C4.78641 13.295 4.22816 12.6565 3.81922 11.9239C3.41028 11.1912 3.15976 10.3809 3.08389 9.54533C3.00801 8.70975 3.10847 7.86754 3.37875 7.07325L3.56775 7.08825C3.96758 7.27898 4.32148 7.55384 4.60523 7.89402C4.88897 8.23421 5.09587 8.63169 5.21175 9.05925C5.3364 9.55308 5.57587 10.0105 5.91067 10.3943C6.24548 10.7781 6.66617 11.0775 7.1385 11.268C8.0445 11.6858 8.23425 12.8572 7.71675 14.8447ZM10.5593 7.875C10.5592 7.81509 10.5519 7.75541 10.5375 7.69725C10.4018 7.08236 10.1327 6.50478 9.74918 6.00531C9.36571 5.50585 8.87723 5.09669 8.31825 4.80675C7.662 4.3905 7.25475 4.13175 7.14525 3.3135C8.31292 2.92008 9.57349 2.89622 10.7552 3.24517C11.9369 3.59411 12.9823 4.2989 13.749 5.2635C12.654 5.42775 12.0593 7.02975 12.0593 7.87425C11.9792 7.99808 11.8674 8.09819 11.7354 8.16414C11.6035 8.23009 11.4564 8.25948 11.3093 8.24925C11.1622 8.25956 11.0151 8.23029 10.8832 8.16447C10.7513 8.09865 10.6394 7.9987 10.5593 7.875ZM11.8433 14.2297L11.313 12.6405C11.3386 12.2794 11.4939 11.9398 11.7503 11.6842C12.0067 11.4287 12.3468 11.2744 12.708 11.25L14.3123 11.7382C13.7578 12.7976 12.8975 13.6657 11.8433 14.2297Z" fill="#1C64F2"/>
                                  </g>
                                  </svg>
                                  <Multiselect :allow-empty="false" v-bind="field" v-model="selected_timezone" :searchable="false"
                                  :close-on-select="true" :show-labels="false" placeholder="Choose" :options="timezones"
                                  maxHeight="150" class="w-full h-full !text-[#2563eb] !text-xs !font-medium relative"
                                  option-class="text-xs font-medium text-[#2563eb]">
                                  </Multiselect>
                          </div>
                          </div>
                </div>
                <div class="h-[78%] w-full px-3">
                  <vue-cal
                      id="vuecal"
                      ref="vuecal"
                      :events="events"
                      :disable-views="['years', 'year','month','week']"
                      hide-view-selector
                      hide-weekends
                      :editable-events="{ title: true, drag: true, resize: true, delete: true, create: true }"
                      :snap-to-time="15"
                      timeCellHeight="50"
                      today-button
                      :selected-date="selectedDate"
                      @event-drag-create="handleEventDrag"
                      :on-event-click="handleEventClick"
                      @ready="scrollToCurrentTime"
                      >
                      <template #arrow-prev>
                        <div class="w-full h-full flex justify-center items-center" @click="(e)=>InCalenderPrevButton(e)">
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g id="chevron-down">
                                <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
                                </g>
                                </svg>
                        </div>
                      </template>
                      <template #arrow-next>
                        <div class="w-full h-full flex justify-center items-center" @click="(e)=>{InCalenderNextButton(e)}">
                            <svg width="14" height="14" style="transform: scaleX(-1)"
                    viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g id="chevron-right">
                            <path id="Vector" d="M3.5 6.98432C3.50007 7.31847 3.62277 7.6389 3.84114 7.87514L8.49826 12.9151C8.60566 13.0355 8.73414 13.1315 8.87618 13.1975C9.01823 13.2635 9.17101 13.2983 9.3256 13.2998C9.48019 13.3012 9.63351 13.2693 9.77659 13.206C9.91968 13.1426 10.0497 13.0491 10.159 12.9308C10.2683 12.8125 10.3548 12.6718 10.4133 12.5169C10.4718 12.3621 10.5013 12.1962 10.5 12.0289C10.4986 11.8616 10.4665 11.6962 10.4055 11.5425C10.3445 11.3888 10.2558 11.2497 10.1446 11.1335L6.31058 6.98432L10.1446 2.83514C10.3566 2.5975 10.474 2.27923 10.4713 1.94886C10.4687 1.61849 10.3462 1.30247 10.1304 1.06885C9.91451 0.835238 9.62249 0.702724 9.31722 0.699853C9.01195 0.696982 8.71785 0.823983 8.49826 1.0535L3.84114 6.0935C3.62277 6.32974 3.50007 6.65018 3.5 6.98432Z" fill="#1F2A37"/>
                            </g>
                            </svg>
                        </div>
                      </template>
                    <template #today-button>
                      <div class="bg-white p-2 !relative !left-[-20px] rounded-md text-gray-400">Today</div>
                    </template>
                  <template #time-cell="{ hours, minutes }">
                      <div :class="{ 'vuecal__time-cell-line': true, hours: !minutes }">
                      <span v-if="!minutes" class="!text-xs">{{ formatHour(hours) }}</span>
                      </div>
                  </template>
                    <template #event="{ event }">
                      <div class="vuecal__event-title !absolute left-3 " v-html="event.title" />
                      <small v-if="event.title==='Available' || event.title===''" class="vuecal__event-time absolute right-3 text-blue-600 !text-xs">
                        <span>{{ event.start.toLocaleTimeString('en-US',
                      {hour: 'numeric', minute: '2-digit', hour12: true}) }}</span> to
                        <span>{{ event.end.toLocaleTimeString('en-US',
                      {hour: 'numeric', minute: '2-digit', hour12: true}) }}</span>
                      </small>
                    </template>
                  </vue-cal>
                </div>
                <div class="flex justify-end px-3">
                      <button class="bg-[#1c64f2] w-full text-white p-2 rounded-md" @click="()=>{showFormModal = true;showCalenderModal= false}">Next</button>
                </div>
              </div>
            </div>

        </Modal>
        <QuickMeetingForm v-if="showQuickMeetingFormModal" :loader="formLoader" @closeModal="{showQuickMeetingFormModal = false;}" @startSession="handleStartSession"/>
        <CreateMeetingsForm v-if="showFormModal" :loader="formLoader" :availableSlots="originalEvents" :calenderStartTime="calenderStartTime" :calenderEndTime="calenderEndTime" @closeModal="{calenderStartTime = '';calenderEndTime='';selected_timezone='Asia/Kolkata';if(Store.isMobile){showFormModal = false;showCalenderModal=true}else{showFormModal = false;showCalenderModal= true }}"  @schedule="handleScheduleSession" class="z-[9999]" />
        <Modal  :open="showInfoModal && !loader">
            <div class=" bg-white rounded-lg flex flex-col gap-3 justify-center items-center p-3" :class="Store.isMobile?'w-[90%]':'w-[25%]'">
                <div class="bg-green-100 w-10 h-10 rounded-full flex justify-center items-center">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="check">
                    <path id="Vector" d="M7.15541 18.3332C6.82899 18.3347 6.51503 18.1814 6.28067 17.906L0.37744 10.9623C0.259778 10.823 0.165894 10.6571 0.101148 10.4738C0.0364024 10.2906 0.00206288 10.0937 9.0224e-05 9.8944C-0.00389373 9.49187 0.124201 9.1039 0.356196 8.81586C0.588191 8.52781 0.905082 8.36327 1.23716 8.35844C1.56923 8.35361 1.88929 8.50888 2.12691 8.7901L7.1604 14.7083L17.8722 2.09785C18.1102 1.81663 18.4305 1.66151 18.7628 1.66663C19.0951 1.67174 19.4122 1.83666 19.6442 2.12511C19.8762 2.41356 20.0041 2.80191 19.9999 3.20473C19.9957 3.60755 19.8596 3.99184 19.6217 4.27306L8.03014 17.906C7.79578 18.1814 7.48182 18.3347 7.15541 18.3332Z" fill="#0E9F6E"/>
                    </g>
                    </svg>
                </div>
                <div class="w-[80%] text-center"><p class="text-base font-medium">Meeting scheduled for {{ infoMessage }}</p></div>
                <div class="w-[90%] flex">
                    <span class="relative " :class="Store.isMobile?'left-[2px]':'left-[6px]'">
                        <svg width="14" height="14" class="mr-1" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="exclamation" clip-path="url(#clip0_3256_7788)">
                        <path id="Vector" d="M7 0C5.61553 0 4.26215 0.410543 3.11101 1.17971C1.95987 1.94888 1.06266 3.04213 0.532846 4.32121C0.00303299 5.6003 -0.13559 7.00776 0.134506 8.36563C0.404603 9.7235 1.07129 10.9708 2.05026 11.9497C3.02922 12.9287 4.2765 13.5954 5.63437 13.8655C6.99224 14.1356 8.3997 13.997 9.67879 13.4672C10.9579 12.9373 12.0511 12.0401 12.8203 10.889C13.5895 9.73784 14 8.38447 14 7C13.998 5.14411 13.2598 3.36482 11.9475 2.0525C10.6352 0.74019 8.85589 0.00203812 7 0ZM7 10.5C6.86156 10.5 6.72622 10.4589 6.6111 10.382C6.49599 10.3051 6.40627 10.1958 6.35329 10.0679C6.3003 9.93997 6.28644 9.79922 6.31345 9.66343C6.34046 9.52765 6.40713 9.40292 6.50503 9.30502C6.60292 9.20713 6.72765 9.14046 6.86344 9.11345C6.99923 9.08644 7.13997 9.1003 7.26788 9.15328C7.39579 9.20626 7.50511 9.29598 7.58203 9.4111C7.65895 9.52621 7.7 9.66155 7.7 9.8C7.7 9.98565 7.62625 10.1637 7.49498 10.295C7.3637 10.4262 7.18565 10.5 7 10.5ZM7.7 7.7C7.7 7.88565 7.62625 8.0637 7.49498 8.19497C7.3637 8.32625 7.18565 8.4 7 8.4C6.81435 8.4 6.6363 8.32625 6.50503 8.19497C6.37375 8.0637 6.3 7.88565 6.3 7.7V4.2C6.3 4.01435 6.37375 3.8363 6.50503 3.70502C6.6363 3.57375 6.81435 3.5 7 3.5C7.18565 3.5 7.3637 3.57375 7.49498 3.70502C7.62625 3.8363 7.7 4.01435 7.7 4.2V7.7Z" fill="#E02424"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_3256_7788">
                        <rect width="14" height="14" fill="white"/>
                        </clipPath>
                        </defs>
                        </svg>
                    </span>
                    <span class="text-center">
                        <p class="text-red-600 text-xs font-medium relative">
    If you do not join by {{ infoMessageTime }}, the meeting will be canceled and server will be available to others.</p>
                    </span>
</div>
                <div><button class="bg-[#1c64f2] w-[140px] text-white p-2 rounded-md" @click="reloadPage()">Okay</button></div>
            </div>
        </Modal>
    </div>
</template>

<style  scoped>
::v-deep(.multiselect){
  border-radius: 8px !important;
}
::v-deep(.multiselect .multiselect__select::before) {
border-left: 2px solid #2563eb !important;
border-bottom: 2px solid #2563eb !important;
top: 3px;
}

::v-deep(.multiselect__tags){
  border: 0 !important;
}
::v-deep(.multiselect__single){
  margin-top: 3px;
  position: relative;
  left: 20%;
  background-color: transparent !important;
  color: #1c64f2 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}
::v-deep(.multiselect__select::before){
  color: #1c64f2 !important;
}
::v-deep(.multiselect__placeholder){
  color: #1c64f2 !important;
  position: relative;
  left: 20%;
  font-size: 14px !important;
  font-weight: 500 !important;
}
.rotate-45 {
  transform: rotate(45deg);
}

.no-scrollbar {
  overflow-x: auto;
  -ms-overflow-style: none;  /* Hide scrollbar for IE and Edge */
  scrollbar-width: none;     /* Hide scrollbar for Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Hide scrollbar for Chrome, Safari, and Opera */
}
::v-deep(.vuecal){
    border: none !important;
    border-radius: 20px;
    box-shadow: none;
}
::v-deep(.vuecal__title-bar){
    background-color: #f3f4f6 !important;
    font-size: 16px;
    color: #9ca3af;
    height: 50px;
}
::v-deep(.vuecal__now-line) {color: #1c64f2;}
/* ::v-deep(.vuecal__flex .weekday-label){
     background-color: #f3f4f6 !important;
    font-size: 16px;
    color: #9ca3af;
} */
/* ::v-deep(.vuecal__flex .vuecal__weekdays-headings){
     background-color: #f3f4f6 !important;
     border-top: 1px solid #e5e5e5;
} */
::v-deep(.vuecal__arrow){
    cursor: pointer;
    position: relative;
    top: .5px;
    z-index: 1;
    background: white;
    white-space: nowrap;
    border-top: 1px solid #e5e5e5;
    height: 40px !important;
    width: 50px;
    margin: 0 !important;
}
::v-deep(.vuecal__arrow--next){
     border-right: 1px solid #e5e5e5;
     border-left: 1px solid #e5e5e5;
    border-radius: 10px;
}
::v-deep(.vuecal__arrow--prev){
    border-right: 1px solid #e5e5e5;
    border-left: 1px solid #e5e5e5;
    border-radius: 10px;
}
::v-deep(.angle){
    color: black;
}

::v-deep(.vuecal__flex::-webkit-scrollbar),
.vuecal__body::-webkit-scrollbar {
  width: 2px !important;  /* Width for vertical scrollbar */
  height: 8px !important; /* Height for horizontal scrollbar */
}
/* Scrollbar Track */
::v-deep(.vuecal__flex)::-webkit-scrollbar-track,
.vuecal__body::-webkit-scrollbar-track {
  background: #f1f1f1 !important; /* Light gray background */
  border-radius: 5px !important;
}

::v-deep(.vuecal__cell-events){
    font-size: 12px;
    cursor: pointer;
}
::v-deep(.closed) {
  background:
    #fff7f0
    repeating-linear-gradient(
      -45deg,
      rgba(255, 162, 87, 0.25),
      rgba(255, 162, 87, 0.25) 5px,
      rgba(255, 255, 255, 0) 5px,
      rgba(255, 255, 255, 0) 15px
    );
  color: #f6984c;
}

::v-deep(.available){
    background-color: #e1effe !important;
    color: #1c64f2;
    font-weight: 700;
    font-size: 12px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
::v-deep(.not_available){
    background-color: #E5E7EB !important;
    color: #4B5563;
    font-weight: 700;
    font-size: 12px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
::v-deep(.vuecal__event-time) {
    font-size: 9px;
}
::v-deep(.vuecal__now-line){
  display: none
}

  .infinite_scrollbar::-webkit-scrollbar {
    width: 2px;
    height: 3px;
}
</style>
