<script setup>
// Import ForgetModal from "../components/ModalComponents/ForgetModal.vue";
// Import installPrompt from "../components/installPrompt.vue";
import { signInWithEmailAndPassword, setPersistence, indexedDBLocalPersistence, signInWithPopup, createUserWithEmailAndPassword, fetchSignInMethodsForEmail, onAuthStateChanged } from 'firebase/auth';

import { ref, onMounted, onUnmounted } from 'vue';
import { doc, getDoc, setDoc, serverTimestamp, getFirestore } from "firebase/firestore";
const db = getFirestore();
import { useRouter } from 'vue-router';
// Import DarkLogo from '../../assets/logo/logo_1.png';
// Import LightLogo from '../../assets/logo/logo.png';
// import { uiOperations } from '../../store/uiOperations';
import { ErrorMessage, Form, Field} from 'vee-validate';
import {signInSchema, signUpSchema} from '../../validationSchema/user';
import Button from '../../components/common/Button.vue';
import { auth, googleProvider, generateToken } from '../../firebaseConfig';
import { UserStore } from '../../store';
import { deleteCookie, setCookie, getCookie } from '../../helpers/domhelper';
// const uiStore = uiOperations();
const router = useRouter();
const showPassword = ref(false); // default
const showConfirmPassword = ref(true);
const store = UserStore();
const loginuserId = ref(null);
const loginPassword = ref(null);
// Const rememberMe = ref(false);
const signUpView = ref(false);

const Errormsg = ref({
  isShow: false,
  message: null,
});

const isloading = ref(false);
// Const SubmitBtn = ref(false);

/* Methods */

const checkIfUserExists = async (email) => {
  try {
    const signInMethods = await fetchSignInMethodsForEmail(auth, email);
    if (signInMethods.length > 0) {
      // User already exists
      return true;
    }
    // User does not exist
    return false;

  } catch (error) {
    console.error("Error checking user existence:", error);
    throw error;
  }
};
// Toggle Forms
const handleToggleForms = () => {
  Errormsg.value.isShow = false;
  signUpView.value = !signUpView.value;
  // Reset
  showPassword.value = false;
  if (!signUpView.value){
    if (localStorage.getItem('userName') !== 'null' && localStorage.getItem('userName') ){

      loginuserId.value = localStorage.getItem('userName');
    }

    if (localStorage.getItem('password') !== 'null' && localStorage.getItem('password') ){

      loginPassword.value = localStorage.getItem('password');
    }
  }
  showConfirmPassword.value = false;
};
// SignUp
const handleSignUp = async (values) => {
  const { email, password, first_name, last_name } = values;

  try {
    // Check if user exists
    const userExists = await checkIfUserExists(email);
    if (userExists) {
      throw new Error("User already exists. Please use a different email.");
    }

    // Firebase Authentication sign-up
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Ensure user token is refreshed before proceeding
    const idToken = await user.getIdToken(true);
    setCookie("accessToken", idToken, 30);
    console.log("access token" +idToken);
    console.log("Token retrieved from cookies:", getCookie("accessToken"));

    // Store user in Firestore
    const userRef = doc(db, "users", user.uid);
    await setDoc(userRef, {
      uid: user.uid,
      email: user.email,
      displayName: `${first_name} ${last_name}`,
      createdAt: serverTimestamp(),
    });

    console.log("User registered successfully:", user.email);
    handleToggleForms();

    // Prepare user data for API
    const userData = { email: user.email, first_name, last_name, uid: user.uid };

    console.log(" Sending user data to API:", userData);
    await store.CreateUser(userData);

  } catch (error) {
    Errormsg.value.isShow = true;
    Errormsg.value.message = error.message || "Sign-Up failed. Please try again.";
  }
};

// SignIn
const handleSignIn = async (values) => {
  console.log('-------------');
  isloading.value = true;
  deleteCookie('pushToken');
  const pushToken = await generateToken();
  console.log(pushToken);
  if (pushToken){
    setCookie('pushToken', pushToken);
  }
  deleteCookie('organization');
  deleteCookie('accessToken');
  deleteCookie('refreshToken');

  if (values.email && values.password) {
    try {
      // Set persistence (optional, based on your requirements)
      await setPersistence(auth, indexedDBLocalPersistence);

      // Sign in with Firebase
      const userCredential = await signInWithEmailAndPassword(auth, values.email, values.password);
      const user = userCredential.user;

      // Get the ID token
      const accessToken = await user.getIdToken();
      setCookie('accessToken', accessToken);

      // Clear error messages
      Errormsg.value.isShow = false;
      Errormsg.value.message = null;

      // Handle redirect
      const urlParams = new URLSearchParams(window.location.search);
      const redirectUri = urlParams.get('redirect_uri');
      if (redirectUri) {
        const decodedRedirectUri = decodeURIComponent(redirectUri);
        if (decodedRedirectUri.startsWith('/')) {
          window.location.href = decodedRedirectUri;
        } else {
          router.push('/sessions');
        }
      } else {
        router.push('/sessions');
      }

      // Fetch user auth data (if needed)
      await store.GetAuth();
    } catch (error) {
      // Handle errors
      Errormsg.value.isShow = true;
      switch (error.code) {
        case 'auth/invalid-email':
          Errormsg.value.message = 'Invalid email!';
          break;
        case 'auth/user-not-found':
          Errormsg.value.message = 'No account with that email was found!';
          break;
        case 'auth/wrong-password':
          Errormsg.value.message = 'Incorrect password';
          break;
        default:
          Errormsg.value.message = 'Email or password was incorrect';
      }
    } finally {
      isloading.value = false;
    }
  }
};

// google auth sso login

const handleGoogleSignIn = async () => {
  try {
    isloading.value = true;

    // Sign in with Google
    const result = await signInWithPopup(auth, googleProvider);
    const user = result?.user;

    if (!user || !user.email) {
      throw new Error("Google authentication failed.");
    }

    console.log("Google User Signed In:", user.email);

    // Get ID Token and save it in cookies
    const accessToken = await user.getIdToken();
    setCookie("accessToken", accessToken);

    // Firestore reference
    const userRef = doc(db, "users", user.uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      // Automatically create the user in Firestore
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName || "User",
        role: "user",
        createdAt: serverTimestamp(),
      });
      console.log("New user created in Firestore.");
    } else {
      console.log("Existing user detected.");
    }

    // Call store.GetAuth() but don’t let it block navigation
    store
      .GetAuth()
      .then(() => console.log(" store.GetAuth() executed successfully."))
      .catch((authError) => console.error(" Error in store.GetAuth():", authError));

    // Ensure navigation to /sessions
    console.log("Navigating to /sessions...");
    await router.push("/sessions");
    console.log("Navigation successful!");

  } catch (error) {
    console.error(" Google Sign-In error:", error);
    Errormsg.value.isShow = true;
    Errormsg.value.message = error.message || "Google Sign-In failed!";
  } finally {
    isloading.value = false;
  }
};

onMounted(() => {

  if (localStorage.getItem('userName') !== 'null' && localStorage.getItem('userName') ){

    loginuserId.value = localStorage.getItem('userName');
  }

  if (localStorage.getItem('password') !== 'null' && localStorage.getItem('password') ){

    loginPassword.value = localStorage.getItem('password');
  }

});
let unsubscribe = () => {};

onMounted(() => {
  unsubscribe = onAuthStateChanged(auth, async (user) => {
    if (user) {
      const accessToken = await user.getIdToken();
      setCookie('accessToken', accessToken);
      // console.log("---------------------------")
      // Handle redirect
      const urlParams = new URLSearchParams(window.location.search);
      const redirectUri = urlParams.get('redirect_uri');
      if (redirectUri) {
        const decodedRedirectUri = decodeURIComponent(redirectUri);
        if (decodedRedirectUri.startsWith('/')) {
          window.location.href = decodedRedirectUri;
        } else {
          router.push('/sessions');
        }
      } else {
        router.push('/sessions');
      }
    }
  });
});

onUnmounted(() => {
  unsubscribe();
});
</script>
<template>

                        <!-- ThemeSwitcher -->
                        <!-- <ThemeSwitcher position="fixed"/> -->

                        <div class=" h-screen w-full relative bg-bg-1000 dark:bg-bg-default overflow-y-auto flex md:flex-row flex-col ">

                                              <!-- <div class="block lg:hidden w-full pt-3 pl-3"> -->

                                                            <!-- <img v-if="uiStore.toggleTheme.toLowerCase() === 'light'" alt="Logo" class="h-6 lg:h-7 w-auto" :src="DarkLogo" /> -->

                                                            <!-- <img v-else alt="Logo" class="h-6 lg:h-7 w-auto" :src="LightLogo" /> -->

                                              <!-- </div> -->

                                                <!-- Image Col -->
                                                <div class="h-auto md:w-[85vw] w-full relative flex justify-center items-center  ">
                                                        <div class=" w-fit h-[238.27px] md:top-auto top-5 md:left-auto left-5 flex-col justify-center items-center absolute md:m-[72px]">
                                                          <div class="justify-start items-center gap-[10.37px] inline-flex top-5">
                                                             <div class="w-[135.62px] h-[35px] relative">
                                                                <img class=" left-0 top-0 absolute" src="../../assets/logo/logo.png" />
                                                            </div>
                                                          </div>
                                                        <div class="self-stretch h-[126px] flex-col justify-start items-start gap-4 lg:flex hidden">
                                                          <div class="self-stretch h-[62px] text-white text-2xl font-extrabold font-[Inter] leading-[30px] ">
                                                            <span>Discover the Ultimate Platform for Property<br></span>
                                                            <span>Experience Creation & Management.</span>
                                                          </div>
                                                          <div class="self-stretch text-gray-100 text-base font-normal font-['Inter'] leading-normal">Millions of developers and Agents manages their leads & properties with us  - the home to the world’s best developers and Agents professionals.</div>
                                                          <div class="self-stretch justify-start items-center gap-6 inline-flex">
                                                           <div class="flex-col justify-start items-start gap-1 inline-flex">
                                                             <div class="text-white text-sm font-normal font-['Inter'] leading-[21px]">Rated Best By Top Developers</div>
                                                           </div>
                                                <div class="w-[29.27px] h-[0px] rotate-90 border border-gray-200"></div>
                                                  <div class="w-[287px] h-[27px] pl-[3.87px] pr-[1.92px] pt-[3.87px] pb-[3.78px] justify-center items-center flex overflow-hidden">
                                                      <div class="justify-start items-start gap-[20.90px] inline-flex">
                                                          <div class="justify-start items-center gap-[20.90px] flex">
                                                              <div class ="w-full">
                                                                <img src="../../assets/logo/kafd.png" />
                                                              </div>
                                                              <div class ="w-full">
                                                                <img src="../../assets/logo/godrej.png" />
                                                              </div>
                                                              <div class ="w-full">
                                                                <img  src="../../assets/logo/damacLogo.png " />
                                                              </div>
                                                              <div class ="w-full">
                                                                <img  src="../../assets/logo/rustomjee.png" />
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                                                  </div>
                                                </div>

                                              </div>
                                                   <img class="object-cover h-full w-full" src="../../assets/loginFront.jpg"/>

</div>
                                                <!-- Form -->
                                                <div class="h-auto w-[-webkit-fill-available]  flex justify-center items-center">
                                                            <div class="md:relative absolute md:bottom-auto bottom-0 md:w-fit w-[-webkit-fill-available]">
                                                                <div class="md:w-fit w-[-webkit-fill-available] h-fit md:min-w-[576px] min-w-auto p-6 bg-white rounded-lg shadow-md  border border-gray-200 flex-col justify-center items-center gap-5 inline-flex ">

                            <!-- Headers -->

                                    <!--   <h3 class="text-center text-neutral-800 text-lg font-bold border-b-2 border-[#262626] w-[50%] py-2"> Login as Admin </h3> -->
                                    <div class="self-stretch text-gray-900 text-2xl leading-[30px] font-bold font-sans">{{ !signUpView ? 'Welcome Back' : 'Welcome'}} </div>
                                    <div class="self-stretch text-gray-900 text-xl leading-[25px] font-bold font-sans">{{ !signUpView ? 'Sign In' : 'Sign Up'}}</div>

                            <!-- SignIn -->
                            <div class="w-full"  v-if="!signUpView">

                                <!-- Form -->
                                <Form  v-slot="{errorBag}" :validation-schema="signInSchema" @submit="handleSignIn" class="w-full flex flex-col gap-5 !m-0 ">

                                        <!-- Email -->

                                        <div class="self-stretch h-[71px] flex-col justify-start items-start gap-2 flex ">
                                          <div class="self-stretch text-[#111928] text-sm font-medium font-sans leading-[21px]">Email</div>
                                          <div class="self-stretch px-4 py-3 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex "
                                            :class =" errorBag?.email || Errormsg.isShow  ? '!border-2 !border-red-300  text-[#c81e1e] text-sm bg-red-50 ': ' border !border-gray-300  text-txt-default dark:text-txt-1000 bg-gray-50'"
                                          >
                                            <div class="  h-[18px]  w-full justify-start items-center gap-2.5 flex">
                                              <Field as="input" v-model="loginuserId" type="email"
                                                id="sign_in_Email" name="email"
                                                class=" w-full bg-transparent text-sm font-sans text-start placeholder:text-left  placeholder:text-gray-500 placeholder:text-sm placeholder:font-medium placeholder:font-sans text-txt-default dark:text-txt-1000"
                                                placeholder="<EMAIL>"/>

                                            </div>
                                           </div>
                                           <div class="min-h-6 relative bottom-1">
                                              <ErrorMessage class="flex justify-center items-center" name="email" as="p" v-slot="{ message }">
                                                <i class="fa fa-exclamation-circle mr-1  text-red-600" aria-hidden="true"></i>
                                                <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
                                              </ErrorMessage>
                                           </div>
                                          </div>

                                        <!-- Password -->
                                        <div class="w-full h-fit mt-3 flex flex-col justify-start items-start relative gap-2">
                                          <div class="self-stretch text-[#111928] text-sm leading-[21px] font-sans">Password</div>
                                            <div class="self-stretch px-4 py-3 rounded-lg justify-between items-center inline-flex "
                                             :class ="errorBag?.password || Errormsg.isShow ? 'border-2 !border-red-300  text-[#c81e1e] text-sm bg-red-50 ': ' border !border-gray-300  text-txt-default dark:text-txt-1000 bg-gray-50'"
                                            >

                                                <Field as="input" v-model="loginPassword"  :type="showPassword ? 'text' : 'password'"
                                                id="sign_in_password" name="password"
                                                class=" h-fit w-[90%] bg-transparent text-start text-sm font-sans placeholder:text-gray-500 placeholder:text-left placeholder:font-sans"
                                                placeholder="Password" />

                                                <div class="flex justify-center items-center">

                                                    <svg v-if="showPassword" @click="() => showPassword = !showPassword"  class="w-6 h-6 fill-white cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                    :fill=" errorBag?.password || Errormsg.isShow  ? '#c81e1e' : '#5B616E'"
                                                    d="M23.1853 11.6963C23.1525 11.6222 22.3584 9.86062 20.5931 8.09531C18.2409 5.74312 15.27 4.5 12 4.5C8.72999 4.5 5.75905 5.74312 3.40687 8.09531C1.64155 9.86062 0.843741 11.625 0.814679 11.6963C0.772035 11.7922 0.75 11.896 0.75 12.0009C0.75 12.1059 0.772035 12.2097 0.814679 12.3056C0.847491 12.3797 1.64155 14.1403 3.40687 15.9056C5.75905 18.2569 8.72999 19.5 12 19.5C15.27 19.5 18.2409 18.2569 20.5931 15.9056C22.3584 14.1403 23.1525 12.3797 23.1853 12.3056C23.2279 12.2097 23.25 12.1059 23.25 12.0009C23.25 11.896 23.2279 11.7922 23.1853 11.6963ZM12 18C9.11437 18 6.59343 16.9509 4.50655 14.8828C3.65028 14.0313 2.92179 13.0603 2.34374 12C2.92164 10.9396 3.65014 9.9686 4.50655 9.11719C6.59343 7.04906 9.11437 6 12 6C14.8856 6 17.4066 7.04906 19.4934 9.11719C20.3514 9.9684 21.0815 10.9394 21.6609 12C20.985 13.2619 18.0403 18 12 18ZM12 7.5C11.11 7.5 10.2399 7.76392 9.49993 8.25839C8.7599 8.75285 8.18313 9.45566 7.84253 10.2779C7.50194 11.1002 7.41282 12.005 7.58646 12.8779C7.76009 13.7508 8.18867 14.5526 8.81801 15.182C9.44735 15.8113 10.2492 16.2399 11.1221 16.4135C11.995 16.5872 12.8998 16.4981 13.7221 16.1575C14.5443 15.8169 15.2471 15.2401 15.7416 14.5001C16.2361 13.76 16.5 12.89 16.5 12C16.4988 10.8069 16.0242 9.66303 15.1806 8.81939C14.337 7.97575 13.1931 7.50124 12 7.5ZM12 15C11.4066 15 10.8266 14.8241 10.3333 14.4944C9.83993 14.1648 9.45542 13.6962 9.22835 13.148C9.00129 12.5999 8.94188 11.9967 9.05764 11.4147C9.17339 10.8328 9.45911 10.2982 9.87867 9.87868C10.2982 9.45912 10.8328 9.1734 11.4147 9.05764C11.9967 8.94189 12.5999 9.0013 13.148 9.22836C13.6962 9.45542 14.1648 9.83994 14.4944 10.3333C14.824 10.8266 15 11.4067 15 12C15 12.7956 14.6839 13.5587 14.1213 14.1213C13.5587 14.6839 12.7956 15 12 15Z" />
                                                    </svg>

                                                <svg v-else @click="() => showPassword = !showPassword"
                                                    class="w-6 h-6 fill-white cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                        :fill=" errorBag?.password || Errormsg.isShow ? '#c81e1e' : '#5B616E'"
                                                         d="M21.375 16.4063C21.2894 16.4551 21.1949 16.4866 21.0971 16.4989C20.9992 16.5112 20.8999 16.5041 20.8048 16.478C20.7097 16.4518 20.6207 16.4072 20.5429 16.3467C20.4651 16.2861 20.3999 16.2108 20.3513 16.125L18.57 13.0125C17.5344 13.7127 16.3921 14.2402 15.1875 14.5744L15.7378 17.8763C15.7541 17.9735 15.751 18.0729 15.7288 18.1689C15.7065 18.2649 15.6656 18.3556 15.6083 18.4358C15.551 18.516 15.4785 18.5841 15.3949 18.6363C15.3113 18.6884 15.2182 18.7236 15.121 18.7397C15.081 18.7462 15.0405 18.7497 15 18.75C14.8226 18.7498 14.651 18.6866 14.5157 18.5718C14.3805 18.4569 14.2903 18.2979 14.2613 18.1228L13.7203 14.881C12.5796 15.0397 11.4223 15.0397 10.2816 14.881L9.74064 18.1228C9.71153 18.2982 9.62109 18.4575 9.48545 18.5724C9.3498 18.6872 9.17776 18.7502 9.00001 18.75C8.95856 18.7498 8.91718 18.7464 8.87626 18.7397C8.77904 18.7236 8.68596 18.6884 8.60233 18.6363C8.5187 18.5841 8.44617 18.516 8.38888 18.4358C8.33159 18.3556 8.29067 18.2649 8.26845 18.1689C8.24623 18.0729 8.24315 17.9735 8.25939 17.8763L8.81251 14.5744C7.60842 14.2391 6.4667 13.7107 5.43189 13.0097L3.65626 16.125C3.55681 16.2983 3.39258 16.425 3.19971 16.4772C3.00684 16.5294 2.80113 16.5029 2.62783 16.4035C2.45452 16.304 2.32783 16.1398 2.27561 15.9469C2.22339 15.754 2.24993 15.5483 2.34939 15.375L4.22439 12.0938C3.56579 11.5248 2.96019 10.8972 2.41501 10.2188C2.34702 10.1429 2.29522 10.0539 2.26276 9.95729C2.2303 9.86069 2.21787 9.75849 2.22622 9.65693C2.23457 9.55536 2.26353 9.45657 2.31133 9.36657C2.35914 9.27657 2.42478 9.19726 2.50426 9.13347C2.58373 9.06969 2.67537 9.02277 2.77359 8.99558C2.8718 8.96839 2.97452 8.9615 3.07548 8.97533C3.17645 8.98916 3.27353 9.02342 3.36082 9.07602C3.4481 9.12862 3.52374 9.19846 3.58314 9.28127C5.13939 11.2069 7.86189 13.5 12 13.5C16.1381 13.5 18.8606 11.2041 20.4169 9.28127C20.4756 9.19676 20.5511 9.12525 20.6386 9.07117C20.7262 9.01708 20.8239 8.9816 20.9257 8.96691C21.0276 8.95222 21.1314 8.95865 21.2306 8.98579C21.3299 9.01293 21.4225 9.06021 21.5027 9.12468C21.5829 9.18915 21.649 9.26943 21.6968 9.36054C21.7447 9.45164 21.7732 9.55161 21.7808 9.65423C21.7883 9.75686 21.7747 9.85993 21.7407 9.95706C21.7067 10.0542 21.653 10.1433 21.5831 10.2188C21.038 10.8972 20.4324 11.5248 19.7738 12.0938L21.6488 15.375C21.6991 15.4606 21.732 15.5552 21.7454 15.6535C21.7589 15.7519 21.7527 15.8519 21.7273 15.9478C21.7018 16.0437 21.6576 16.1337 21.5971 16.2123C21.5366 16.291 21.4611 16.357 21.375 16.4063Z" />
                                                    </svg>

                                                </div>

                                            </div>
                                            <ErrorMessage v-if="!errorBag" class="flex justify-center items-center" name="password" as="p" v-slot="{ message }">
                                                <i class="fa fa-exclamation-circle mr-1  text-red-600" aria-hidden="true"></i>
                                                <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
                                              </ErrorMessage>

                                       <!-- Api Error -->
                                      <div class= "flex justify-space w-full">
                                        <div class="min-h-8 flex items-center justify-space w-full">
                                           <p v-if="Errormsg.isShow" class="text-sm font-normal not-italic text-left text-red-600 font-sans w-max">
                                            <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
                                            {{ Errormsg.message }}
                                           </p>

                                              <!-- <button type="button" @click="handleForgotPassword" class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer">
                                            Forgot password?
                                          </button> -->
                                        </div>

                                        <!-- Links -->
                                        <div class="flex justify-end items-center w-full">
                                          <!-- <button type="button" @click="handleForgotPassword" class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer">
                                            Forgot password?
                                          </button> -->

                                          <button
  type="button"
  @click="$router.push('/reset-password')"
  class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer"
>
  Forgot password?
</button>
                                        </div>
                                      </div>

                                    </div>

                                        <!-- <div class="flex justify-between items-center">

                                          <div class="flex items-center h-fit">
                                             <input checked id="default-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded ">
                                             <label for="default-checkbox" class=" text-sm  m-0 font-bold text-gray-500 dark:text-gray-300">Remember me</label>
                                          </div>

                                      </div> -->

                                        <!-- SignIn Btn -->
                                        <Button id="submit" type="submit" theme="primary" title="Sign In" class="w-full bg-blue-600 font-sans text-sm"></Button>

                                        <button @click="handleGoogleSignIn"
                                          class="px-4 py-2 w-full text-gray-900 outline outline-1 outline-gray-200 rounded-lg md flex items-center gap-2 justify-center">
                                          <img src="https://www.google.com/images/branding/googleg/1x/googleg_standard_color_128dp.png"
                                           alt="Google Logo" class="w-6 h-6 bg-white rounded-full p-1" />
                                             <p class=" text-gray-900 text-sm font-medium font-sans ">Sign In with Google</p>
                                        </button>
                                </Form>

                            </div>

                            <!-- Sign Up -->
                            <div v-else class="w-full m-0">

                                <!-- Form -->
                                <div>
    <!-- Signup Form -->
    <Form v-slot="{errorBag}" :validation-schema="signUpSchema" @submit="handleSignUp" class="w-full m-0 ">
<div class="flex flex-col gap-8">

<!-- FirstName -->

<div class="self-stretch h-[71px] flex-col justify-start items-start gap-2 flex ">
    <div class="self-stretch text-[#111928] text-sm font-medium font-sans leading-[21px]">First Name</div>
    <div class="self-stretch px-4 py-3 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex "
    :class =" errorBag?.first_name || Errormsg.isShow  ? '!border-2 !border-red-300  text-[#c81e1e] text-sm bg-red-50 ': ' border !border-gray-300  text-txt-default dark:text-txt-1000 bg-gray-50'"
    >
      <div class="  h-[18px]  w-full justify-start items-center gap-2.5 flex">
        <Field as="input" type="text"
            id="first_name" name="first_name"
            class=" w-full bg-transparent text-sm font-sans text-start placeholder:text-left  placeholder:text-gray-500 placeholder:text-sm placeholder:font-medium placeholder:font-sans text-txt-default dark:text-txt-1000"
            placeholder="First Name"/>
      </div>
    </div>
    <div class="min-h-6 relative bottom-1">
      <ErrorMessage name="first_name" as="p" class="flex items-center" v-slot="{ message }">
        <i class="fa fa-exclamation-circle mr-1  text-red-600" aria-hidden="true"></i>
       <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
      </ErrorMessage>
    </div>
  </div>

<!-- LastName -->

<div class="self-stretch h-[71px] flex-col justify-start items-start gap-2 flex ">
    <div class="self-stretch text-[#111928] text-sm font-medium font-sans leading-[21px]">Last Name</div>
    <div class="self-stretch px-4 py-3 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex"
    :class =" errorBag?.last_name || Errormsg.isShow  ? '!border-2 !border-red-300  text-[#c81e1e] text-sm bg-red-50 ': ' border !border-gray-300  text-txt-default dark:text-txt-1000 bg-gray-50'"
    >
      <div class="  h-[18px]  w-full justify-start items-center gap-2.5 flex">
        <Field as="input" type="text"
            id="last_name" name="last_name"
            class=" w-full bg-transparent text-sm font-sans text-start placeholder:text-left  placeholder:text-gray-500 placeholder:text-sm placeholder:font-medium placeholder:font-sans text-txt-default dark:text-txt-1000"
            placeholder="Last Name"/>
      </div>
    </div>
    <div class="min-h-6 relative bottom-1">
      <ErrorMessage name="last_name" as="p"  class="flex items-center" v-slot="{ message }">
        <i class="fa fa-exclamation-circle mr-1  text-red-600" aria-hidden="true"></i>
       <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
      </ErrorMessage>
    </div>
  </div>

<!-- Email -->

<div class="self-stretch h-[71px] flex-col justify-start items-start gap-2 flex ">
    <div class="self-stretch text-[#111928] text-sm font-medium font-sans leading-[21px]">Email</div>
    <div class="self-stretch px-4 py-3 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex"
    :class =" errorBag?.email || Errormsg.isShow  ? '!border-2 !border-red-300  text-[#c81e1e] text-sm bg-red-50 ': ' border !border-gray-300  text-txt-default dark:text-txt-1000 bg-gray-50'"
    >
      <div class="  h-[18px]  w-full justify-start items-center gap-2.5 flex">
        <Field as="input" type="email"
            id="email" name="email"
            class=" w-full bg-transparent text-sm font-sans text-start placeholder:text-left  placeholder:text-gray-500 placeholder:text-sm placeholder:font-medium placeholder:font-sans text-txt-default dark:text-txt-1000"
            placeholder="Email"/>
      </div>
    </div>
    <div class="min-h-6 relative bottom-1">
      <ErrorMessage name="email"  class="flex items-center text-red-600" as="p" v-slot="{ message }">
        <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
        <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
      </ErrorMessage>
    </div>
  </div>

<!-- Password -->

<div class="self-stretch h-[71px] flex-col justify-start items-start gap-2 flex ">
    <div class="self-stretch text-[#111928] text-sm font-medium font-sans leading-[21px]">Password</div>
    <div class="self-stretch px-4 py-3 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex "
    :class =" errorBag?.password || Errormsg.isShow  ? '!border-2 !border-red-300  text-[#c81e1e] text-sm bg-red-50 ': ' border !border-gray-300  text-txt-default dark:text-txt-1000 bg-gray-50'"

    >
      <div class="  h-[18px]  w-full justify-start items-center gap-2.5 flex">
        <Field as="input" type="text"
            id="password" name="password"
            class=" w-full bg-transparent text-sm font-sans text-start placeholder:text-left  placeholder:text-gray-500 placeholder:text-sm placeholder:font-medium placeholder:font-sans text-txt-default dark:text-txt-1000"
            placeholder="Password"/>

            <div class="flex justify-center items-center">

<svg  v-if="showPassword" @click="() => showPassword = !showPassword" class="w-6 h-6 fill-white cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.1853 11.6963C23.1525 11.6222 22.3584 9.86062 20.5931 8.09531C18.2409 5.74312 15.27 4.5 12 4.5C8.72999 4.5 5.75905 5.74312 3.40687 8.09531C1.64155 9.86062 0.843741 11.625 0.814679 11.6963C0.772035 11.7922 0.75 11.896 0.75 12.0009C0.75 12.1059 0.772035 12.2097 0.814679 12.3056C0.847491 12.3797 1.64155 14.1403 3.40687 15.9056C5.75905 18.2569 8.72999 19.5 12 19.5C15.27 19.5 18.2409 18.2569 20.5931 15.9056C22.3584 14.1403 23.1525 12.3797 23.1853 12.3056C23.2279 12.2097 23.25 12.1059 23.25 12.0009C23.25 11.896 23.2279 11.7922 23.1853 11.6963ZM12 18C9.11437 18 6.59343 16.9509 4.50655 14.8828C3.65028 14.0313 2.92179 13.0603 2.34374 12C2.92164 10.9396 3.65014 9.9686 4.50655 9.11719C6.59343 7.04906 9.11437 6 12 6C14.8856 6 17.4066 7.04906 19.4934 9.11719C20.3514 9.9684 21.0815 10.9394 21.6609 12C20.985 13.2619 18.0403 18 12 18ZM12 7.5C11.11 7.5 10.2399 7.76392 9.49993 8.25839C8.7599 8.75285 8.18313 9.45566 7.84253 10.2779C7.50194 11.1002 7.41282 12.005 7.58646 12.8779C7.76009 13.7508 8.18867 14.5526 8.81801 15.182C9.44735 15.8113 10.2492 16.2399 11.1221 16.4135C11.995 16.5872 12.8998 16.4981 13.7221 16.1575C14.5443 15.8169 15.2471 15.2401 15.7416 14.5001C16.2361 13.76 16.5 12.89 16.5 12C16.4988 10.8069 16.0242 9.66303 15.1806 8.81939C14.337 7.97575 13.1931 7.50124 12 7.5ZM12 15C11.4066 15 10.8266 14.8241 10.3333 14.4944C9.83993 14.1648 9.45542 13.6962 9.22835 13.148C9.00129 12.5999 8.94188 11.9967 9.05764 11.4147C9.17339 10.8328 9.45911 10.2982 9.87867 9.87868C10.2982 9.45912 10.8328 9.1734 11.4147 9.05764C11.9967 8.94189 12.5999 9.0013 13.148 9.22836C13.6962 9.45542 14.1648 9.83994 14.4944 10.3333C14.824 10.8266 15 11.4067 15 12C15 12.7956 14.6839 13.5587 14.1213 14.1213C13.5587 14.6839 12.7956 15 12 15Z" fill="#5B616E"/>
</svg>

<svg v-else @click="() => showPassword = !showPassword"
class="w-6 h-6 fill-white cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21.375 16.4063C21.2894 16.4551 21.1949 16.4866 21.0971 16.4989C20.9992 16.5112 20.8999 16.5041 20.8048 16.478C20.7097 16.4518 20.6207 16.4072 20.5429 16.3467C20.4651 16.2861 20.3999 16.2108 20.3513 16.125L18.57 13.0125C17.5344 13.7127 16.3921 14.2402 15.1875 14.5744L15.7378 17.8763C15.7541 17.9735 15.751 18.0729 15.7288 18.1689C15.7065 18.2649 15.6656 18.3556 15.6083 18.4358C15.551 18.516 15.4785 18.5841 15.3949 18.6363C15.3113 18.6884 15.2182 18.7236 15.121 18.7397C15.081 18.7462 15.0405 18.7497 15 18.75C14.8226 18.7498 14.651 18.6866 14.5157 18.5718C14.3805 18.4569 14.2903 18.2979 14.2613 18.1228L13.7203 14.881C12.5796 15.0397 11.4223 15.0397 10.2816 14.881L9.74064 18.1228C9.71153 18.2982 9.62109 18.4575 9.48545 18.5724C9.3498 18.6872 9.17776 18.7502 9.00001 18.75C8.95856 18.7498 8.91718 18.7464 8.87626 18.7397C8.77904 18.7236 8.68596 18.6884 8.60233 18.6363C8.5187 18.5841 8.44617 18.516 8.38888 18.4358C8.33159 18.3556 8.29067 18.2649 8.26845 18.1689C8.24623 18.0729 8.24315 17.9735 8.25939 17.8763L8.81251 14.5744C7.60842 14.2391 6.4667 13.7107 5.43189 13.0097L3.65626 16.125C3.55681 16.2983 3.39258 16.425 3.19971 16.4772C3.00684 16.5294 2.80113 16.5029 2.62783 16.4035C2.45452 16.304 2.32783 16.1398 2.27561 15.9469C2.22339 15.754 2.24993 15.5483 2.34939 15.375L4.22439 12.0938C3.56579 11.5248 2.96019 10.8972 2.41501 10.2188C2.34702 10.1429 2.29522 10.0539 2.26276 9.95729C2.2303 9.86069 2.21787 9.75849 2.22622 9.65693C2.23457 9.55536 2.26353 9.45657 2.31133 9.36657C2.35914 9.27657 2.42478 9.19726 2.50426 9.13347C2.58373 9.06969 2.67537 9.02277 2.77359 8.99558C2.8718 8.96839 2.97452 8.9615 3.07548 8.97533C3.17645 8.98916 3.27353 9.02342 3.36082 9.07602C3.4481 9.12862 3.52374 9.19846 3.58314 9.28127C5.13939 11.2069 7.86189 13.5 12 13.5C16.1381 13.5 18.8606 11.2041 20.4169 9.28127C20.4756 9.19676 20.5511 9.12525 20.6386 9.07117C20.7262 9.01708 20.8239 8.9816 20.9257 8.96691C21.0276 8.95222 21.1314 8.95865 21.2306 8.98579C21.3299 9.01293 21.4225 9.06021 21.5027 9.12468C21.5829 9.18915 21.649 9.26943 21.6968 9.36054C21.7447 9.45164 21.7732 9.55161 21.7808 9.65423C21.7883 9.75686 21.7747 9.85993 21.7407 9.95706C21.7067 10.0542 21.653 10.1433 21.5831 10.2188C21.038 10.8972 20.4324 11.5248 19.7738 12.0938L21.6488 15.375C21.6991 15.4606 21.732 15.5552 21.7454 15.6535C21.7589 15.7519 21.7527 15.8519 21.7273 15.9478C21.7018 16.0437 21.6576 16.1337 21.5971 16.2123C21.5366 16.291 21.4611 16.357 21.375 16.4063Z" fill="#5B616E"/>
</svg>

</div>
      </div>
    </div>
    <div class="min-h-6 relative bottom-1">
      <ErrorMessage name="password"  class="flex items-center" as="p" v-slot="{ message }">
       <i class="fa fa-exclamation-circle mr-1  text-red-600" aria-hidden="true"></i>
       <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
      </ErrorMessage>
    </div>
  </div>

<!-- ConfirmPassword -->

<div class="self-stretch h-[71px] flex-col justify-start items-start gap-2 flex ">
    <div class="self-stretch text-[#111928] text-sm font-medium font-sans leading-[21px]">Confirm Password</div>
    <div class="self-stretch px-4 py-3 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex "
    :class =" errorBag?.confirmPassword || Errormsg.isShow  ? '!border-2 !border-red-300  text-[#c81e1e] text-sm bg-red-50 ': ' border !border-gray-300  text-txt-default dark:text-txt-1000 bg-gray-50'"

    >
      <div class="  h-[18px]  w-full justify-start items-center gap-2.5 flex">
        <Field as="input" type="text"
            id="confirmPassword" name="confirmPassword"
            class=" w-full bg-transparent text-sm font-sans text-start placeholder:text-left  placeholder:text-gray-500 placeholder:text-sm placeholder:font-medium placeholder:font-sans text-txt-default dark:text-txt-1000"
            placeholder="Confirm Password"/>

            <div class="flex justify-center items-center">

<svg  v-if="showConfirmPassword" @click="() => showConfirmPassword = !showConfirmPassword" class="w-6 h-6 fill-white cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.1853 11.6963C23.1525 11.6222 22.3584 9.86062 20.5931 8.09531C18.2409 5.74312 15.27 4.5 12 4.5C8.72999 4.5 5.75905 5.74312 3.40687 8.09531C1.64155 9.86062 0.843741 11.625 0.814679 11.6963C0.772035 11.7922 0.75 11.896 0.75 12.0009C0.75 12.1059 0.772035 12.2097 0.814679 12.3056C0.847491 12.3797 1.64155 14.1403 3.40687 15.9056C5.75905 18.2569 8.72999 19.5 12 19.5C15.27 19.5 18.2409 18.2569 20.5931 15.9056C22.3584 14.1403 23.1525 12.3797 23.1853 12.3056C23.2279 12.2097 23.25 12.1059 23.25 12.0009C23.25 11.896 23.2279 11.7922 23.1853 11.6963ZM12 18C9.11437 18 6.59343 16.9509 4.50655 14.8828C3.65028 14.0313 2.92179 13.0603 2.34374 12C2.92164 10.9396 3.65014 9.9686 4.50655 9.11719C6.59343 7.04906 9.11437 6 12 6C14.8856 6 17.4066 7.04906 19.4934 9.11719C20.3514 9.9684 21.0815 10.9394 21.6609 12C20.985 13.2619 18.0403 18 12 18ZM12 7.5C11.11 7.5 10.2399 7.76392 9.49993 8.25839C8.7599 8.75285 8.18313 9.45566 7.84253 10.2779C7.50194 11.1002 7.41282 12.005 7.58646 12.8779C7.76009 13.7508 8.18867 14.5526 8.81801 15.182C9.44735 15.8113 10.2492 16.2399 11.1221 16.4135C11.995 16.5872 12.8998 16.4981 13.7221 16.1575C14.5443 15.8169 15.2471 15.2401 15.7416 14.5001C16.2361 13.76 16.5 12.89 16.5 12C16.4988 10.8069 16.0242 9.66303 15.1806 8.81939C14.337 7.97575 13.1931 7.50124 12 7.5ZM12 15C11.4066 15 10.8266 14.8241 10.3333 14.4944C9.83993 14.1648 9.45542 13.6962 9.22835 13.148C9.00129 12.5999 8.94188 11.9967 9.05764 11.4147C9.17339 10.8328 9.45911 10.2982 9.87867 9.87868C10.2982 9.45912 10.8328 9.1734 11.4147 9.05764C11.9967 8.94189 12.5999 9.0013 13.148 9.22836C13.6962 9.45542 14.1648 9.83994 14.4944 10.3333C14.824 10.8266 15 11.4067 15 12C15 12.7956 14.6839 13.5587 14.1213 14.1213C13.5587 14.6839 12.7956 15 12 15Z" fill="#5B616E"/>
</svg>

<svg v-else @click="() => showConfirmPassword = !showConfirmPassword"
class="w-6 h-6 fill-white cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21.375 16.4063C21.2894 16.4551 21.1949 16.4866 21.0971 16.4989C20.9992 16.5112 20.8999 16.5041 20.8048 16.478C20.7097 16.4518 20.6207 16.4072 20.5429 16.3467C20.4651 16.2861 20.3999 16.2108 20.3513 16.125L18.57 13.0125C17.5344 13.7127 16.3921 14.2402 15.1875 14.5744L15.7378 17.8763C15.7541 17.9735 15.751 18.0729 15.7288 18.1689C15.7065 18.2649 15.6656 18.3556 15.6083 18.4358C15.551 18.516 15.4785 18.5841 15.3949 18.6363C15.3113 18.6884 15.2182 18.7236 15.121 18.7397C15.081 18.7462 15.0405 18.7497 15 18.75C14.8226 18.7498 14.651 18.6866 14.5157 18.5718C14.3805 18.4569 14.2903 18.2979 14.2613 18.1228L13.7203 14.881C12.5796 15.0397 11.4223 15.0397 10.2816 14.881L9.74064 18.1228C9.71153 18.2982 9.62109 18.4575 9.48545 18.5724C9.3498 18.6872 9.17776 18.7502 9.00001 18.75C8.95856 18.7498 8.91718 18.7464 8.87626 18.7397C8.77904 18.7236 8.68596 18.6884 8.60233 18.6363C8.5187 18.5841 8.44617 18.516 8.38888 18.4358C8.33159 18.3556 8.29067 18.2649 8.26845 18.1689C8.24623 18.0729 8.24315 17.9735 8.25939 17.8763L8.81251 14.5744C7.60842 14.2391 6.4667 13.7107 5.43189 13.0097L3.65626 16.125C3.55681 16.2983 3.39258 16.425 3.19971 16.4772C3.00684 16.5294 2.80113 16.5029 2.62783 16.4035C2.45452 16.304 2.32783 16.1398 2.27561 15.9469C2.22339 15.754 2.24993 15.5483 2.34939 15.375L4.22439 12.0938C3.56579 11.5248 2.96019 10.8972 2.41501 10.2188C2.34702 10.1429 2.29522 10.0539 2.26276 9.95729C2.2303 9.86069 2.21787 9.75849 2.22622 9.65693C2.23457 9.55536 2.26353 9.45657 2.31133 9.36657C2.35914 9.27657 2.42478 9.19726 2.50426 9.13347C2.58373 9.06969 2.67537 9.02277 2.77359 8.99558C2.8718 8.96839 2.97452 8.9615 3.07548 8.97533C3.17645 8.98916 3.27353 9.02342 3.36082 9.07602C3.4481 9.12862 3.52374 9.19846 3.58314 9.28127C5.13939 11.2069 7.86189 13.5 12 13.5C16.1381 13.5 18.8606 11.2041 20.4169 9.28127C20.4756 9.19676 20.5511 9.12525 20.6386 9.07117C20.7262 9.01708 20.8239 8.9816 20.9257 8.96691C21.0276 8.95222 21.1314 8.95865 21.2306 8.98579C21.3299 9.01293 21.4225 9.06021 21.5027 9.12468C21.5829 9.18915 21.649 9.26943 21.6968 9.36054C21.7447 9.45164 21.7732 9.55161 21.7808 9.65423C21.7883 9.75686 21.7747 9.85993 21.7407 9.95706C21.7067 10.0542 21.653 10.1433 21.5831 10.2188C21.038 10.8972 20.4324 11.5248 19.7738 12.0938L21.6488 15.375C21.6991 15.4606 21.732 15.5552 21.7454 15.6535C21.7589 15.7519 21.7527 15.8519 21.7273 15.9478C21.7018 16.0437 21.6576 16.1337 21.5971 16.2123C21.5366 16.291 21.4611 16.357 21.375 16.4063Z" fill="#5B616E"/>
</svg>

</div>

      </div>
    </div>
    <div class="min-h-6 relative bottom-1">
      <ErrorMessage name="confirmPassword"  class="flex items-center" as="p" v-slot="{ message }">
        <i class="fa fa-exclamation-circle mr-1  text-red-600" aria-hidden="true"></i>
       <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
      </ErrorMessage>
    </div>
  </div>

</div>

<!-- Api Error -->
<div class="min-h-[24px] relative -bottom-3">
  <p v-if="Errormsg.isShow" class="text-sm not-italic tracking-normal text-left mt-1 text-red-600 font-sans">
    <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
    {{ Errormsg.message }}
  </p>
</div>

    <!-- SignUp Btn -->
     <div class="gap-5 flex flex-col">

        <Button id="submit" type="submit" theme="primary" title="Sign Up" class="w-full bg-blue-600 mt-4 font-sans text-sm"></Button>

        <!-- Google Signup Button -->
        <button @click="handleGoogleSignIn" class="px-4 py-2 w-full text-gray-900 outline outline-1 outline-gray-200 rounded-lg md flex items-center gap-2 justify-center">
      <img src="https://www.google.com/images/branding/googleg/1x/googleg_standard_color_128dp.png"
      alt="Google Logo" class="w-6 h-6 bg-white rounded-full p-1" />
        <p class=" text-gray-900 text-sm font-medium font-sans">Sign Up with Google</p>
    </button>
  </div>

</Form>

  </div>

                            </div>

                            <div class="w-full flex flex-col justify-between items-center">

                                        <!-- signup by google -->

                                        <!-- <Button theme="secondary" title="Sign Up with Google" class=" w-full">
                                                <template v-slot:svg>
                                                    <svg class="h-5 w-[21px]" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M19.3866 8.26138L11.1687 8.26099C10.8058 8.26099 10.5116 8.55294 10.5116 8.91317V11.5192C10.5116 11.8794 10.8058 12.1714 11.1686 12.1714H15.7965C15.2897 13.4769 14.3439 14.5703 13.1372 15.2649L15.1105 18.6559C18.2759 16.8386 20.1473 13.65 20.1473 10.0805C20.1473 9.57228 20.1096 9.20896 20.0341 8.79985C19.9768 8.48903 19.7049 8.26138 19.3866 8.26138Z" fill="#1C74D0"/>
                                                        <path d="M10.0731 16.087C7.80834 16.087 5.83122 14.8586 4.76934 13.0409L1.35349 14.9954C3.09179 17.9861 6.3482 20 10.0731 20C11.9004 20 13.6247 19.5116 15.11 18.6605V18.6558L13.1367 15.2648C12.2341 15.7845 11.1895 16.087 10.0731 16.087Z" fill="#12B347"/>
                                                        <path d="M15.1113 18.6604V18.6558L13.138 15.2648C12.2354 15.7844 11.1909 16.0869 10.0744 16.0869V20C11.9018 20 13.6261 19.5116 15.1113 18.6604Z" fill="#0F993E"/>
                                                        <path d="M3.9419 10.0001C3.9419 8.89195 4.24655 7.8552 4.76995 6.95922L1.35409 5.00476C0.49196 6.47457 0 8.18152 0 10.0001C0 11.8187 0.49196 13.5257 1.35409 14.9955L4.76995 13.041C4.24655 12.145 3.9419 11.1083 3.9419 10.0001Z" fill="#FFD500"/>
                                                        <path d="M10.0731 3.91305C11.55 3.91305 12.9066 4.43399 13.9661 5.30052C14.2275 5.51427 14.6075 5.49884 14.8469 5.26114L16.707 3.41465C16.9787 3.14497 16.9593 2.70352 16.6691 2.4536C14.8938 0.924728 12.584 0 10.0731 0C6.3482 0 3.09179 2.01395 1.35349 5.00466L4.76934 6.95911C5.83122 5.14141 7.80834 3.91305 10.0731 3.91305Z" fill="#FF4B26"/>
                                                        <path d="M13.9675 5.30052C14.2289 5.51427 14.6088 5.49884 14.8483 5.26114L16.7084 3.41465C16.98 3.14497 16.9606 2.70352 16.6705 2.4536C14.8952 0.924689 12.5853 0 10.0744 0V3.91305C11.5513 3.91305 12.9079 4.43399 13.9675 5.30052Z" fill="#D93F21"/>
                                                    </svg>
                                                </template>
                                        </Button> -->

                                        <p class="w-full text-center">
                                            <span class="text-gray-900 text-sm font-normal font-sans"> {{ signUpView ? "Already have an account? " : "Don't have an account yet? "}} </span>
                                            <span class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer font-sans" @click=" handleToggleForms()"> {{ signUpView ? "Login" : "Sign Up" }} </span>
                                        </p>

                            </div>

                                                                </div>

                                                </div>

                        </div>
                      </div>

</template>

<style scoped>

/* Layout */
.gridLayout{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
}

/* CheckBox */
input[type=checkbox] {
    position: relative;
    border: 1px solid #737373;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    line-height: 0;
    margin: 0 .3em 0 0;
    outline: 0;
    padding: 0 !important;
    vertical-align: text-top;
    height: 1.25rem;
    width: 1.25rem;
    -webkit-appearance: none;
    /* opacity: .5; */
}

input[type=checkbox]:checked {
    background-color: #2563eb;
    opacity: 1;
}

input[type=checkbox]:checked:before {
    content: '';
    position: absolute;
    right: 50%;
    top: 50%;
    width: 4px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    margin: -1px -1px 0 -1px;
    transform: rotate(45deg) translate(-70%, -40%);
    z-index: 2;
}

input:-webkit-autofill {
  background-color: transparent !important;
  box-shadow: 0 0 0px 1000px white inset !important;
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
}

/* Media Query */

@media only screen and (max-width:992.98px) {
        /* Layout */
        .gridLayout{

            grid-template-columns: repeat(1, 1fr);

        }
}

</style>
