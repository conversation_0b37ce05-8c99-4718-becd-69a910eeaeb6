<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/svg+xml"
      href="https://cdn.propvr.tech/images/favicon.ico"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PropVR</title>
    <link
      rel="preload"
      as="style"
      href="https://fonts.googleapis.com/css?family=Open Sans"
      onload="this.rel='stylesheet'"
    />
    <link
      rel="icon"
      type="image/png"
      href="https://cdn.propvr.tech/images/favicon.ico"
    />
    <link
      rel="preload"
      as="style"
      href="https://cdn.propvr.tech/css/material-kit.min.css?v=2.0.7"
      onload="this.rel='stylesheet'"
    />
    <link
      rel="stylesheet"
      href="https://cdn.propvr.tech/css/font-awesome.min.css"
    />
    <script
      src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"
      type="text/javascript"
    ></script>
    <script
      src="https://cdn.propvr.tech/js/magflip.min.js"
      type="text/javascript"
    ></script>
    <script src="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.js"></script>
    <!-- google charts -->
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script> 
    <!-- Aframe & Click-drag-components -->
    <script src="https://cdn.propvr.tech/creationtool/v2/js/build.min.js"></script>  <!-- Aframe & Click-Drag-Components -->
    <script>
      registerAframeClickDragComponent(window.AFRAME); // Click-Drag-Components register
    </script>

  <!-- Latest compiled and minified CSS -->
  <link href="https://cdn.propvr.tech/css/magflip.min.css" rel="stylesheet"  type="text/css"/>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css" />
  <script src="https://static.matterport.com/showcase-sdk/latest.js"></script>
    <link href="https://unpkg.com/vue-cal/dist/vuecal.css" rel="stylesheet">
</head>

<body>
  <div id="app"></div>
  <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
  <script src="http://maps.google.com/maps/api/js?key=AIzaSyBG6UgW1aqtSjhcrfR3Y0dItZj5Fk3o79o"></script>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>