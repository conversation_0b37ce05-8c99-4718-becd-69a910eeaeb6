<script setup>
import router from '@/router';
import { ProjectStore } from '../../../../store/project';
import { nextTick, ref } from 'vue';
import { useRoute } from 'vue-router';
import CreateImageTourModal from '../CreateImageTourModal.vue';
import { addImageToTour, AddTourLabel, deleteImageFromTour, getTourById, updateTourImage, UpdateTourLabel } from '@/api/projects/tours';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import ProgressLoader from '@/components/common/ProgressLoader.vue';
import { DeleteTourLabel } from '../../../../api/projects/tours';
import { getSortArrayFromObjects, resizeImage } from '../../../../helpers/helpers';
import NestedReOrder from '@/components/common/NestedReOrder.vue';

const route = useRoute();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const projectStore = ProjectStore();
const emits = defineEmits(['openHotspot', 'closeHotspot']);
const isopenImageModal = ref(false);
const createImageProgressLoader = ref({
  percentage: 0,
  status: false,
});
const deleteImageId = ref(null);
const deleteImageLoader = ref(false);
const isopenDeleteImageModal = ref(false);
const deleteLabelId = ref(null);
const deleteLabelLoader = ref(false);
const isopenDeleteLabelModal = ref(false);
const listOfLabels = ref(null);
const reOrderLoader = ref(false);
console.log("MleTourLeftSideBar", tourId.value);
const clickTimer = ref(null);
const editLabelId = ref(null);
const inputEditLabelRef = ref(null);
const replaceImageId = ref(null);

/* Methods */

// SetUp
const setUpData = (data) => {
  console.log("setUpData", data);
  if (data){
    let labels = data;

    const modifiedLabelsObject = {};
    Object.keys(labels).forEach((key) => {
      const cloneLabelObj = {...labels[key], id: key};
      modifiedLabelsObject[key] = cloneLabelObj;
    });
    labels = modifiedLabelsObject;

    /* Convert to Array of Order */
    const convertedData = [...getSortArrayFromObjects(labels, null)];
    console.log(convertedData);

    listOfLabels.value = convertedData; // Converted Data
  }
};

// Compare Values
const compareValues = (source, compareObj) => {
  const finalObj = {};
  console.log("compareValues", source, compareObj);

  for (const key in source){
    if (key === 'camera_position' || key === 'controls_target' ){
      for (const subKey in source[key]){
        if (source[key][subKey] !== compareObj[key][subKey]){ // If one key & value pair is changed then bind all together.
          finalObj[key] = {
            ...source[key],
          };
        }
      }
    } else {
      if (source[key] !== compareObj[key]){
        finalObj[key] = source[key];
      }
    }

  }
  console.log(finalObj);
  return finalObj;
};

// Get List of AffectItems By Order for Deletion of Labels
const getAffectedItemsOrderByDeletion = (listOfItems, deleteItemId ) => {
  const affectedItems = [];

  if (listOfItems && deleteItemId){
    console.log(listOfItems, deleteItemId);

    const findOrderOfDeleteItem = listOfItems.find((item) => item.id === deleteItemId).order; // get the order of delete Item.
    console.log(findOrderOfDeleteItem);

    if (findOrderOfDeleteItem){
      listOfItems.forEach((item) => {
        if (item.order > findOrderOfDeleteItem){
          const itemClone = {
            'label_id': item.id,
            'order': item.order,
          };
          itemClone.order -=  1; // order update
          affectedItems.push(itemClone); // push
        }
      });
    }
  }

  return affectedItems.length > 0 ? affectedItems : false;
};

// Create Image
const handleCreateImage = async (listOfImagesFiles) => {
  console.log("Form submitted", listOfImagesFiles);
  let currentOrderLength = projectStore.virtualtours[tourId.value]?.images ? Object.values(projectStore.virtualtours[tourId.value].images).length : 0; // Get the current length image in store
  console.log(currentOrderLength, "Current Length");

  const images = Object.values({...listOfImagesFiles}).map((item) => {
    currentOrderLength++;
    return {
      ...item,
      'order': Number(currentOrderLength),
    };
  });

  const frameParams = {
    project_id: projectId.value,
    tour_id: tourId.value,
    images: images,
  };

  isopenImageModal.value = false; // reset the modal

  // open the loader
  createImageProgressLoader.value.status = true;
  createImageProgressLoader.value.percentage = 5;

  const imageResponse = await addImageToTour(frameParams); // Image Api
  console.log(imageResponse, "Result");

  if (imageResponse){
    console.log("imageResponse");
    // close the loader
    createImageProgressLoader.value.percentage = 100;
    setTimeout(() => {
      createImageProgressLoader.value.status = false; // close the loader
      window.location.reload();// refresh hit
    }, 700);
  }
};
// Image delete
const handleImageDelete = () => {
  deleteImageLoader.value = true;

  const frameParams = {
    project_id: projectId.value,
    tour_id: tourId.value,
    image_id: deleteImageId.value,
  };

  deleteImageFromTour(frameParams).then(async (res) => {
    console.log(res, "DeleteImageFromTour");
    deleteImageId.value = null; // reset the value
    deleteImageLoader.value = false; // close the loader
    isopenDeleteImageModal.value = false; // close the modal
    window.location.reload();// refresh hit
  });
};
// Label delete
const handleLabelDelete = () => {
  deleteLabelLoader.value = true;
  const getTheAffectedItems = getAffectedItemsOrderByDeletion(listOfLabels.value, deleteLabelId.value);
  console.log("GetTheAffectedItems", getTheAffectedItems);

  const frameParams = {
    project_id: projectId.value,
    tour_id: tourId.value,
    label_id: deleteLabelId.value,
  };

  DeleteTourLabel(frameParams).then(async (delResult) => {
    console.log(delResult, "DeleteTourLabel");
    if (getTheAffectedItems){
      console.log("If");
      const frameParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        query: getTheAffectedItems,
      };
      console.log("Delete Label Update orders", frameParams);

      await UpdateTourLabel(frameParams).then((res) => {
        console.log("UpdateTourLabel Result", res);
        getTourById( projectId.value, tourId.value).then((result) => {
          projectStore.SyncMultipleVirtualTours({ [result._id]: result}); // Sync the Store
          setUpData(result.labels);
        });
      });
    } else {
      projectStore.SyncMultipleVirtualTours({[delResult._id]: delResult}); // Sync the store
      if (delResult?.labels){
        setUpData(delResult.labels);
      } else {
        listOfLabels.value = null;
      }
    }
    if (route.query.label_id === deleteLabelId.value){ // If same, reset  the route
      router.push({ path: route.path, query: {} });
    }
    deleteLabelId.value = null; // reset the value
    deleteLabelLoader.value = false; // close the loader
    isopenDeleteLabelModal.value = false; // close the modal
  });
};

// Modify the image name/key
const modifyImageKey = (name) => {
  return name.replace(/\.[^/.]+$/, "");
};

// Image Selection
const handleImageSelection = (key) => {
  const imageId = modifyImageKey(key);
  const labelMatches = route.query.label_id &&
    projectStore.virtualtours[tourId.value].labels[route.query.label_id].camera_name === imageId;

  const shouldUpdate = route.query.image_id !== imageId;

  if (shouldUpdate) {
    const newQuery = labelMatches
      ? { ...route.query, image_id: imageId }
      : { image_id: imageId };

    router.push({ path: route.path, query: newQuery });
  }
};
// Child Sort Emit (Re-orders)
const handleChildSortEmit = (val) => {
  console.log("handleChildSortEmit", val);
  reOrderLoader.value = true;
  const queryItems = [...val.sortedItems, val.draggedItem].map((item) => {
    return {
      'label_id': item.id,
      'order': item.order,
    };
  });
  const frameParams = {
    project_id: projectId.value,
    tour_id: tourId.value,
    query: queryItems,
  };
  console.log("Update Label Order Params", frameParams);

  UpdateTourLabel(frameParams).then((res) => {
    console.log("UpdateTourLabel Result", res);
    getTourById( projectId.value, tourId.value).then((result) => {
      console.log("getTourById Result", result);
      projectStore.SyncMultipleVirtualTours({ [result._id]: result}); // Sync the Store
      reOrderLoader.value = false; // reset the loader
    });
  });
};

// Create Label
const handleCreateLabel = () => {
  let currentHotspotLabelValues = null;
  document.dispatchEvent(new CustomEvent('mleCurrentLabelValues', { // CustomEvent
    detail: {
      callback: (value) => {
        currentHotspotLabelValues = value;
        console.log("Returned hotspotLabel value:", value);
      },
    },
  }));
  emits('openHotspot');
  if (currentHotspotLabelValues){
    let currentLength = projectStore.virtualtours[tourId.value]?.labels ? Object.keys(projectStore.virtualtours[tourId.value]?.labels).length : 0;
    currentLength++;
    const frameParams = {
      name: `unnamed${currentLength}`,
      ...currentHotspotLabelValues,
      project_id: projectId.value,
      tour_id: tourId.value,
      order: currentLength,
    };
    console.log("Create Label Params", frameParams);
    // AddTourLabel
    AddTourLabel(frameParams).then((res) => {
      console.log("Result of AddTourLabel", res);
      projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
      console.log(res.labels);
      setUpData(res.labels);
      emits('closeHotspot');
    });
  }
};

// Label Selection
const handleLabelSelection = (e, key) => {
  if (e.detail === 1){ // make sure only one click
    clickTimer.value = setTimeout(() => {
      const labelId = key;
      const labelMatches = route.query.image_id &&
            projectStore.virtualtours[tourId.value].labels[key].camera_name === route.query.image_id;

      const shouldUpdate = route.query.label_id !== labelId;

      if (shouldUpdate) {
        const newQuery = labelMatches
          ? { ...route.query, label_id: labelId }
          : { label_id: labelId };

        router.push({ path: route.path, query: newQuery });
      }
      clickTimer.value = null; // reset the value
    }, 300);
  }
};

// Edit Label (handleEditLabelName)
const handleEditLabelName = (id) => {
  console.log("Yes handleEditLabelName");
  console.log("No");
  if (clickTimer.value){
    clearTimeout(clickTimer.value); // clear it timeout
    clickTimer.value = null; // reset the value
  }

  editLabelId.value =  id;

  nextTick(() => {
    if (inputEditLabelRef.value){
      inputEditLabelRef.value.focus();
    }
  });
};

// Edit label (FocusOutEditLabelName)
const focusOutEditLabelName = (e, preValue) => {
  console.log("focusOutEditLabelName", e);
  console.log(e.target.value);

  if (editLabelId.value){
    const newValue = e.target.value.trim();
    const oldValue = preValue.trim();
    console.log(oldValue, newValue);

    if (newValue !== oldValue){ // If changes,
      console.log("yes different", newValue.trim());
      const frameParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        query: [
          {
            name: newValue,
            label_id: editLabelId.value,
          },
        ],
      };
      console.log("Edit Label Params", frameParams);
      UpdateTourLabel(frameParams).then((res) => {
        console.log("UpdateTourLabel Result", res);
        getTourById( projectId.value, tourId.value).then((result) => {
          console.log("getTourById Result", result);
          projectStore.SyncMultipleVirtualTours({ [result._id]: result}); // Sync the Store
          setUpData(result.labels);
          editLabelId.value = null; // reset values
          inputEditLabelRef.value = null;
        });
      });
    } else {
      editLabelId.value = null; // reset values
      inputEditLabelRef.value = null;
    }
  }
};

// Update Label camera pos & Controls target
const handleUpdateLabelCamAndControls = (prevData) => {
  const previousData = {
    camera_position: prevData.camera_position,
    controls_target: prevData.controls_target,
  };
  let currentCamAndControlValues = null;
  document.dispatchEvent(new CustomEvent('mleCurrentLabelValues', { // CustomEvent
    detail: {
      callback: (value) => {
        console.log(value);
        currentCamAndControlValues = {
          camera_position: value.camera_position,
          controls_target: value.controls_target,
        };
        console.log("Returned hotspotLabel value:", currentCamAndControlValues, previousData);
      },
    },
  }));

  const comparedResult = compareValues(currentCamAndControlValues, previousData);

  if (Object.keys(comparedResult).length > 0){
    console.log("Compared Result", comparedResult);
    const frameParams = {
      project_id: projectId.value,
      tour_id: tourId.value,
      query: [
        {
          ...comparedResult,
          label_id: route.query.label_id,
        },
      ],
    };
    console.log("Edit Label Params", frameParams);
    UpdateTourLabel(frameParams).then((res) => {
      console.log("UpdateTourLabel Result", res);
      getTourById( projectId.value, tourId.value).then((result) => {
        console.log("getTourById Result", result);
        projectStore.SyncMultipleVirtualTours({ [result._id]: result}); // Sync the Store
        setUpData(result.labels);
      });
    });
  }

};

// Replace Image
const handleReplaceImage = async (file, replaceImgId) => {
  console.log("handleReplaceImage", file, replaceImgId);
  replaceImageId.value = replaceImgId;
  const frameParms = {
    project_id: projectId.value,
    tour_id: tourId.value,
    image_id: replaceImgId,
    thumbnail: await resizeImage(file, 1280, 720),
    url: file,
  };
  console.log(frameParms);

  // Form Data
  const formData = new FormData();
  for (const key in frameParms){
    console.log("key", key);
    formData.append(key, frameParms[key]);
  }

  updateTourImage(formData).then((res) => {
    console.log("updateTourImage", res);
    projectStore.SyncMultipleVirtualTours({[res._id]: res}); // Sync the store
    replaceImageId.value = null; // reset
    window.location.reload();// refresh hit
  }).catch((err) => {
    console.log(err, "UpdateTourImage Error");
  });
};

setUpData(projectStore.virtualtours[tourId.value]?.labels); // Initialize

/* --- Menu's --- */
const handleCloseMenu = () => {
  const groupMenuElement = document.querySelector("[data-name='mleImageMenu']");
  groupMenuElement.remove(); // remove directly from dom
  console.log("handleCloseMenu");
};

const templateMenu = (left, top, item ) => {
  const div = document.createElement("div");
  div.setAttribute('data-name', 'mleImageMenu');
  div.classList.add(...['w-[144px]', 'absolute', 'text-xs', 'bg-white', 'z-20', 'py-1', 'rounded-lg', 'whitespace-normal', '-translate-x-[40%]', 'mt-1', 'flex', 'flex-col', 'justify-start', 'items-start', 'shadow-md', 'outline-1', 'outline-gray-200']);
  div.style.left = `${left + 80}px`;
  div.style.top = `${top - 30}px`;
  div.innerHTML =  `
      <label for="mleImageReplace" class="block mb-0 text-gray-700 text-sm font-normal cursor-pointer px-4 py-2 w-full text-left" id="replaceImage_ByMlePopMenu">
        Replace
        <input id="mleImageReplace" type="file" class="hidden"/>  
      </label> 
      <button class="text-red-600 text-sm font-normal cursor-pointer  px-4 py-2 w-full text-left" id="deleteImage_ByMlePopMenu">Delete</button>
  `;

  nextTick(() => {
    // Events for menu item's
    const replaceImage = document.getElementById('replaceImage_ByMlePopMenu');
    const deleteImage = document.getElementById('deleteImage_ByMlePopMenu');
    const fileUploadInp = document.getElementById("mleImageReplace");

    // Replace Image
    if (replaceImage){
      fileUploadInp.addEventListener('change', (val) => {
        const file = val.target.files[0]; // file
        handleReplaceImage(file, item.id);
      });
    }

    // DeleteImage Image
    if (deleteImage){
      deleteImage.addEventListener("click", () => {
        isopenDeleteImageModal.value = true;
        deleteImageId.value = item.id;
      });
    }
  });
  div.addEventListener("mouseleave", () => handleCloseMenu()); // Close
  return div;
};

const handleOpenMenu = (e, item) => {
  document.body.appendChild(templateMenu(e.clientX, e.clientY + 15, item));
};

const handleMenus = (e, item) => {
  console.log(e, item);// Parms(event, item)
  console.log(document);
  if (document.querySelector('[data-name="mleImageMenu"]')){ // If any open
    console.log("Close");
    handleCloseMenu(); // Close, thne open
    nextTick(() => { // let the dom update
      console.log("After the close");
      handleOpenMenu(e, item);
    });
  } else {
    handleOpenMenu(e, item);
  }
};

</script>
<template>
   <div :class="['h-full w-full flex flex-col justify-start items-start bg-white p-2 py-3 gap-2 relative rounded-t-lg']">
    <div class="shrink-0 w-full flex items-center px-[6.1px] border-b border-gray-200 gap-1 pb-[7px]">
   <!--     <button class="!w-auto h-full bg-transparent rounded flex justify-start items-center"
          @click="() => router.push(`/projects/${projectId}/design/tours`)">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-gray-600" viewBox="0 0 24 24">
            <g data-name="Layer 2">
              <g data-name="arrow-back">
                <rect width="24" height="24" transform="rotate(90 12 12)" opacity="0" />
                <path
                  d="M19 11H7.14l3.63-4.36a1 1 0 1 0-1.54-1.28l-5 6a1.19 1.19 0 0 0-.09.15c0 .05 0 .08-.07.13A1 1 0 0 0 4 12a1 1 0 0 0 .07.36c0 .05 0 .08.07.13a1.19 1.19 0 0 0 .09.15l5 6A1 1 0 0 0 10 19a1 1 0 0 0 .64-.23 1 1 0 0 0 .13-1.41L7.14 13H19a1 1 0 0 0 0-2z" />
              </g>
            </g>
          </svg>
        </button>  -->

        <div class="w-full flex flex-row justify-between items-center">
          <div class="flex justify-start items-center gap-2 w-fit">
              <svg class="w-8 h-8 cursor-pointer" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" @click="() => router.push(`/projects/${projectId}/design/tours`)">
              <rect width="32" height="32" rx="8" fill="#F3F4F6"/>
              <path d="M14.042 16.0002L19.6829 10.4805C20.0859 10.0855 20.1079 9.4224 19.7309 8.99918C19.3549 8.57595 18.7199 8.55395 18.3179 8.94889L12.617 14.5283C12.219 14.918 12 15.4397 12 16.0002C12 16.5607 12.219 17.0823 12.616 17.4721L18.3169 23.0515C18.5099 23.2401 18.7549 23.3333 18.9999 23.3333C19.2679 23.3333 19.5339 23.2222 19.7299 23.0012C20.1069 22.578 20.0849 21.9159 19.6819 21.5199L14.042 16.0002Z" fill="#4B5563"/>
              </svg>
               <h2 class="!text-gray-600 !text-lg font-medium w-24 block text-ellipsis whitespace-nowrap overflow-hidden select-none"> {{ projectStore.virtualtours[tourId].name }}</h2>
          </div>

         <!--   <Button title="Image" theme="secondary" class="!py-1 !px-3 w-fit" @handleClick="() => {isopenImageModal = true;}">
            <template v-slot:svg>
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_308_25527)">
                  <path
                    d="M6 12C5.8446 12 5.69556 11.9383 5.58568 11.8284C5.47579 11.7185 5.41406 11.5695 5.41406 11.4141V0.585938C5.41406 0.430537 5.47579 0.281502 5.58568 0.171617C5.69556 0.0617325 5.8446 0 6 0C6.1554 0 6.30444 0.0617325 6.41432 0.171617C6.52421 0.281502 6.58594 0.430537 6.58594 0.585938V11.4141C6.58594 11.5695 6.52421 11.7185 6.41432 11.8284C6.30444 11.9383 6.1554 12 6 12Z"
                    fill="black" />
                  <path
                    d="M11.4141 6.58594H0.585938C0.430537 6.58594 0.281502 6.52421 0.171617 6.41432C0.0617325 6.30444 0 6.1554 0 6C0 5.8446 0.0617325 5.69556 0.171617 5.58568C0.281502 5.47579 0.430537 5.41406 0.585938 5.41406H11.4141C11.5695 5.41406 11.7185 5.47579 11.8284 5.58568C11.9383 5.69556 12 5.8446 12 6C12 6.1554 11.9383 6.30444 11.8284 6.41432C11.7185 6.52421 11.5695 6.58594 11.4141 6.58594Z"
                    fill="black" />
                </g>
                <defs>
                  <clipPath id="clip0_308_25527">
                    <rect width="12" height="12" fill="black" />
                  </clipPath>
                </defs>
              </svg>
            </template>
        </Button> -->

        <svg class="w-4 h-4 fill-[#4B5563] cursor-pointer"  viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg" @click="() => {isopenImageModal = true;}">
          <path d="M13.689 7.78886H8.71121V2.81109C8.71121 2.62249 8.63629 2.44161 8.50293 2.30826C8.36957 2.1749 8.1887 2.09998 8.0001 2.09998C7.8115 2.09998 7.63063 2.1749 7.49727 2.30826C7.36391 2.44161 7.28899 2.62249 7.28899 2.81109V7.78886H2.31121C2.12261 7.78886 1.94174 7.86378 1.80838 7.99714C1.67502 8.1305 1.6001 8.31138 1.6001 8.49998C1.6001 8.68857 1.67502 8.86945 1.80838 9.00281C1.94174 9.13617 2.12261 9.21109 2.31121 9.21109H7.28899V14.1889C7.28899 14.3775 7.36391 14.5583 7.49727 14.6917C7.63063 14.8251 7.8115 14.9 8.0001 14.9C8.1887 14.9 8.36957 14.8251 8.50293 14.6917C8.63629 14.5583 8.71121 14.3775 8.71121 14.1889V9.21109H13.689C13.8776 9.21109 14.0585 9.13617 14.1918 9.00281C14.3252 8.86945 14.4001 8.68857 14.4001 8.49998C14.4001 8.31138 14.3252 8.1305 14.1918 7.99714C14.0585 7.86378 13.8776 7.78886 13.689 7.78886Z" />
        </svg>

        </div>

    </div>

    <div class="flex-1 h-full flex flex-col justify-start items-start overflow-hidden gap-2 w-full">
     <p class="italic text-xs text-gray-600 font-normal" v-if="reOrderLoader"> Updating orders.... </p>

    <div v-if="projectStore.virtualtours[tourId]?.images" :class="['bg-transparent flex flex-col justify-start items-start overflow-hidden w-full relative max-h-[50%] min-h-0  pb-3', 'border-b border-gray-200' ] ">
     <!-- <h3 class="text-gray-600  text-sm font-medium  w-full p-0 flex justify-start items-center gap-2 cursor-pointer select-none"  @click="() => showImages = !showImages"  >
         <svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6" fill="none">
             <path d="M0.266744 1.35016L5.35589 5.76842C5.52673 5.9167 5.75842 6 6 6C6.24158 6 6.47327 5.9167 6.64411 5.76842L11.7333 1.35016C11.8606 1.23954 11.9474 1.09862 11.9825 0.945213C12.0176 0.791806 11.9996 0.632798 11.9307 0.488291C11.8617 0.343785 11.745 0.220267 11.5952 0.133353C11.4454 0.0464399 11.2693 3.33974e-05 11.0891 0L0.910859 0C0.730698 3.33974e-05 0.554593 0.0464399 0.404807 0.133353C0.255021 0.220267 0.138279 0.343785 0.0693395 0.488291C0.000400064 0.632798 -0.0176418 0.791806 0.0174953 0.945213C0.0526324 1.09862 0.139371 1.23954 0.266744 1.35016Z" fill="#6B7280"/>
        </svg>
        Images :
      </h3> -->
      <div class="w-full min-h-0 overflow-y-auto" > <!-- v-if="showImages" -->
        <!-- <p  class="text-gray-600 w-full pt-1"> No images ! </p> -->
        <div class="bg-transparent flex flex-col justify-start items-start w-full pl-2">
            <ul class="list-none flex flex-col justify-start items-start w-full pb-2 gap-3 pe-2">
                <li @click="()=>handleImageSelection(item.name)" :key="key" v-for="item,key in projectStore.virtualtours[tourId].images"  :class="['rounded-lg relative flex bg-white flex-col justify-start items-start gap-0 shrink-0 overflow-hidden border-none w-full  shadow-md cursor-default', route.query.image_id === modifyImageKey(item.name) ? 'outline outline-2 outline-offset-0 outline-blue-600' : ' outline-none']" >
                       <div class="h-28 w-full mb-0 relative">
                           <img :src="item.thumbnail" alt="" class="w-full h-full object-cover"/>
                           <div v-if="replaceImageId === item.id" class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-[rgba(0,0,0,0.6)]">
                              <span class="loader"></span>
                           </div>
                       </div>
                        <div class="w-full h-6 flex justify-between items-center bg-transparent">
                                <p class="h-5 ps-2 text-black text-xs font-medium flex items-center justify-center mb-0"> {{ item.name }} </p>
                                <svg @click.stop="(e) => handleMenus(e,item)" class="w-6 h-6 cursor-pointer" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12.5 16C12.7761 16 13 16.2239 13 16.5C13 16.7761 12.7761 17 12.5 17C12.2239 17 12 16.7761 12 16.5C12 16.2239 12.2239 16 12.5 16ZM12.5 12C12.7761 12 13 12.2239 13 12.5C13 12.7761 12.7761 13 12.5 13C12.2239 13 12 12.7761 12 12.5C12 12.2239 12.2239 12 12.5 12ZM12.5 8C12.7761 8 13 8.22386 13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8Z" fill="#6B7280" stroke="#6B7280"/>
                                </svg>
                        </div>
                </li>
            </ul>
        </div>
      </div>
    </div>

    <div v-if="projectStore.virtualtours[tourId]?.images" class="pl-1 flex flex-col justify-start items-start overflow-hidden w-full relative bg-transparent mb-1 max-h-[50%] min-h-0">
          <div class="w-full flex justify-between items-center px-2 py-1">
                  <h3 class="text-gray-600  text-sm font-medium w-full p-0 flex justify-start items-center gap-2 select-none"> <!--  @click="() => showLabels = !showLabels" -->
                    Hotspots
                  </h3>
                   <svg :class="['w-4 h-4 fill-[#4B5563] cursor-pointer' ]"  viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg"  @click="handleCreateLabel">
                    <path d="M13.689 7.78886H8.71121V2.81109C8.71121 2.62249 8.63629 2.44161 8.50293 2.30826C8.36957 2.1749 8.1887 2.09998 8.0001 2.09998C7.8115 2.09998 7.63063 2.1749 7.49727 2.30826C7.36391 2.44161 7.28899 2.62249 7.28899 2.81109V7.78886H2.31121C2.12261 7.78886 1.94174 7.86378 1.80838 7.99714C1.67502 8.1305 1.6001 8.31138 1.6001 8.49998C1.6001 8.68857 1.67502 8.86945 1.80838 9.00281C1.94174 9.13617 2.12261 9.21109 2.31121 9.21109H7.28899V14.1889C7.28899 14.3775 7.36391 14.5583 7.49727 14.6917C7.63063 14.8251 7.8115 14.9 8.0001 14.9C8.1887 14.9 8.36957 14.8251 8.50293 14.6917C8.63629 14.5583 8.71121 14.3775 8.71121 14.1889V9.21109H13.689C13.8776 9.21109 14.0585 9.13617 14.1918 9.00281C14.3252 8.86945 14.4001 8.68857 14.4001 8.49998C14.4001 8.31138 14.3252 8.1305 14.1918 7.99714C14.0585 7.86378 13.8776 7.78886 13.689 7.78886Z" />
                  </svg>
          </div>

      <div class="w-full p-1 overflow-y-auto min-h-0" > <!-- v-if="showLabels" -->
        <div v-if="listOfLabels" class="bg-transparent flex flex-col justify-start items-start w-full ">
            <NestedReOrder commonClasses="!p-0" v-model="listOfLabels" :allowChildReparenting="false" :allowChildSort="true" uniqueKey="id" animationMilliSec="450" sortReferenceKey="id" @handleChildSort="(val) => handleChildSortEmit(val)" class="w-full">
              <template #default="{item}">
                      <!-- reOrderLoader ? 'border-none' : listOfLabels.length !== item.order ? 'border-b' : 'border-none'  -->
                      <div :class="[route.query.label_id === item.id ? 'bg-blue-50 rounded-lg' : 'bg-transparent', listOfLabels.length !== item.order ? 'mb-2' : 'mb-0', editLabelId !== item.id ? 'ps-2 py-[4px]' : 'ps-0 py-0' ,'pe-1 h-8 overflow-hidden user-select-none text-black flex justify-between items-center gap-1']" @click="(e) => handleLabelSelection(e,item.id)" @dblclick="() => handleEditLabelName(item.id)">
                          <p v-if="editLabelId !== item.id" class="text-gray-900 text-sm font-medium select-none flex-1 overflow-hidden whitespace-normal text-ellipsis"> {{ item.name }}</p>
                          <input v-else ref="inputEditLabelRef" @click.stop @blur="(e) => {e.stopPropagation(); focusOutEditLabelName(e, item.name);}" type="text" name="mleTourLabelName" :value="item.name" :class="[`text-gray-900 text-sm font-medium px-2 py-[5.5px] bg-blue-50  rounded-lg select-text w-full` ]"/>
                          <div class="flex items-center justify-start gap-3">
                            <button v-if="route.query.label_id === item.id" @click.stop="() => handleUpdateLabelCamAndControls(item)" class="border-none outline-none w-[52px] h-6 bg-[#1C64F2] !rounded-lg flex justify-center items-center text-white !text-xs !font-medium cursor-pointer">
                                 Save
                            </button>
                             <svg class="shrink-0 w-3.5 h-3.5 cursor-pointer" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" @click.stop="() => { isopenDeleteLabelModal= true; deleteLabelId = item.id }">
                                <g>
                                <path d="M12.7039 2.94737H9.85201V1.47368C9.85201 1.08284 9.70178 0.708001 9.43437 0.431632C9.16696 0.155263 8.80427 0 8.42609 0L5.57424 0C5.19606 0 4.83337 0.155263 4.56596 0.431632C4.29854 0.708001 4.14831 1.08284 4.14831 1.47368V2.94737H1.29646C1.10737 2.94737 0.926025 3.025 0.792318 3.16318C0.658612 3.30137 0.583496 3.48879 0.583496 3.68421C0.583496 3.87963 0.658612 4.06705 0.792318 4.20524C0.926025 4.34342 1.10737 4.42105 1.29646 4.42105H2.00942V12.5263C2.00942 12.9172 2.15965 13.292 2.42707 13.5684C2.69448 13.8447 3.05717 14 3.43535 14H10.565C10.9432 14 11.3058 13.8447 11.5733 13.5684C11.8407 13.292 11.9909 12.9172 11.9909 12.5263V4.42105H12.7039C12.893 4.42105 13.0743 4.34342 13.208 4.20524C13.3417 4.06705 13.4168 3.87963 13.4168 3.68421C13.4168 3.48879 13.3417 3.30137 13.208 3.16318C13.0743 3.025 12.893 2.94737 12.7039 2.94737ZM5.57424 1.47368H8.42609V2.94737H5.57424V1.47368ZM6.2872 11.0526C6.2872 11.2481 6.21208 11.4355 6.07838 11.5737C5.94467 11.7118 5.76333 11.7895 5.57424 11.7895C5.38515 11.7895 5.2038 11.7118 5.0701 11.5737C4.93639 11.4355 4.86127 11.2481 4.86127 11.0526V5.89474C4.86127 5.69931 4.93639 5.5119 5.0701 5.37371C5.2038 5.23553 5.38515 5.15789 5.57424 5.15789C5.76333 5.15789 5.94467 5.23553 6.07838 5.37371C6.21208 5.5119 6.2872 5.69931 6.2872 5.89474V11.0526ZM9.13905 11.0526C9.13905 11.2481 9.06394 11.4355 8.93023 11.5737C8.79652 11.7118 8.61518 11.7895 8.42609 11.7895C8.237 11.7895 8.05565 11.7118 7.92195 11.5737C7.78824 11.4355 7.71313 11.2481 7.71313 11.0526V5.89474C7.71313 5.69931 7.78824 5.5119 7.92195 5.37371C8.05565 5.23553 8.237 5.15789 8.42609 5.15789C8.61518 5.15789 8.79652 5.23553 8.93023 5.37371C9.06394 5.5119 9.13905 5.69931 9.13905 5.89474V11.0526Z" fill="#C81E1E"/>
                                </g>
                             </svg>

                          </div>
                      </div>
              </template>
            </NestedReOrder>
        </div>
      </div>
    </div>
    </div>

  <!-- Create Image -->
  <Modal :open="isopenImageModal" :preventOverflow="true">
            <CreateImageTourModal
             @closeModal="(e) => {isopenImageModal = false;}"
             @handleCreate="handleCreateImage"
            />
  </Modal>

  <!-- Delete Image -->
  <Modal :open="isopenDeleteImageModal">
            <DeleteModalContent
                :trash="false"
                :loader="deleteImageLoader"
                @closeModal="(e) => {isopenDeleteImageModal = false; deleteImageId = null;}"
                @handleDelete="handleImageDelete"
                dataName="Tour Image"
            />
  </Modal>

    <!-- Delete Label -->
    <Modal :open="isopenDeleteLabelModal">
            <DeleteModalContent
                :trash="false"
                :loader="deleteLabelLoader"
                @closeModal="(e) => {isopenDeleteLabelModal = false; deleteLabelId = null;}"
                @handleDelete="handleLabelDelete"
                dataName="Tour Label"
            />
  </Modal>

  <!-- ProgressLoader -->
  <ProgressLoader v-if="createImageProgressLoader.status" msg="Uploading images" :percentage="createImageProgressLoader.percentage"/>

  </div>
</template>

<style scoped>
.loader {
  border: 2px solid #2d2d2e;
  border-radius: 50%;
  border-top: 2px solid white;
  width: 40px;
  height: 40px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
