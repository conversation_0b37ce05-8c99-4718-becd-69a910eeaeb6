<script setup>
import { Dialog, TransitionChild, TransitionRoot } from '@headlessui/vue';
import Navbar from '../Navbar.vue';

defineProps({
  open: Boolean,
  preventOverflow: Boolean,
});

</script>
<template>

    <TransitionRoot as="template" :show="open">
      <Dialog as="div" class="relative" >
        <div class="fixed inset-0" :class="[`${preventOverflow ? 'overflow-hidden' : 'overflow-y-auto'} z-50`]">
          <div class="bg-[#00000063] w-screen h-full absolute flex justify-center items-center backdrop-blur-sm">
            <div class="absolute w-full top-0"><Navbar/></div>
            <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200" leave-from="opacity-100 translate-y-0 sm:scale-100" leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                 <slot>
                </slot>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

  </template>

<style></style>
