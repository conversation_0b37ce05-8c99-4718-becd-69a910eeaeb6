import type { transformedSVG, updatePayload } from '@/types/masterSVG';
import { PostRequestWithHeaders, GetRequestWithHeaders } from '../../../helpers/apihelper';
import type { masterSVG } from '@/types/masterSVG';

const api_url = import.meta.env.VITE_API_URL;

export async function GetSvg (id:string):Promise<transformedSVG | null> {

  return new Promise((resolve, reject) => {
    const sceneId = id;
    GetRequestWithHeaders({ url: `${api_url}/masterSVG/GetSvg/${sceneId}` }).then((res) => {
      resolve(res as transformedSVG);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function updateLayers (payload: updatePayload ):Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterSVG/updateLayers`, body: payload }).then((res) => {
      resolve(res as object);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function createLayers (payload: updatePayload): Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterSVG/createLayers`, body: payload }).then((res) => {
      resolve(res as object);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function updatesvgLayer (payload: updatePayload): Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterSVG/updatesvgLayer`, body: payload }).then((res) => {
      resolve(res as object);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function createSVG (data:object):Promise<masterSVG | string> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterSVG/createSVG`, body: data }).then((res) => {
      resolve(res as masterSVG | string);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function moveSvgToTrash (payload: object): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterSVG/moveToTrash`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateLayersVideoTag (payload:updatePayload) {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterSVG/updateLayersVideoTag`, body: payload }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function deleteLayers (payload: updatePayload ):Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterSVG/deleteLayer`, body: payload }).then((res) => {
      resolve(res as object);
    }).catch((err) => {
      reject(err);
    });
  });
}
