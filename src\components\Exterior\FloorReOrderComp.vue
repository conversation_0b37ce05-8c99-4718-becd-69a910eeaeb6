<script setup>
import { getSortArrayFromObjects } from '../../helpers/helpers';
// import FloorData from "./floorReOrderData.json";
import NestedReOrder from '../../components/common/NestedReOrder.vue';
import { ref, defineEmits, defineProps, inject } from 'vue';
import { addNewFloor, renameFloor } from '@/api/projects/buildings';
import { useRoute } from 'vue-router';

const route = useRoute();
const props = defineProps({
  floorData: {type: Object, default: () => ({})},
  buildingId: {type: String, default: ''},
});

const emit = defineEmits(['sortedData', 'deleteFloor', 'updateData']);

const dumpData = ref(null);
const newFloorId = ref(null);
const rowRefs = ref({});
const dirtyMap = ref({}), showLoader = ref(false); // Track dirty state per floor
const projectId = route.params.project_id;
const isTowerDataRefreshed = inject('isTowerDataRefreshed');

const setupData = (data) => {
  if (data){
    // Conversion of Objets to Array values then sort it respectively.
    const convertedData = [...getSortArrayFromObjects(data, null)];

    convertedData.forEach((item, index) => {
      item.sno = index + 1;
      // item.floor_id = Number(item.floor_id); // Convert string to number
    });

    // Update the your component reference
    dumpData.value = convertedData;
  }
};

// Initial
setupData({...props.floorData});

const handleDelete = ( item ) => {
  emit('deleteFloor', item);
};

function onInputChange (item) {
  dirtyMap.value[item.floor_id] = item.floor_name !== (props.floorData[item.floor_id]?.floor_name || '');
}

const handleNameChange = (item) => {
  showLoader.value = true;
  if (item.isNew){
    newFloorId.value = item.floor_id; // Set the new floor id to scroll to
    // emit('addNewFloor', item);
    const new_floor = item.floor_name.split(" ")[1];
    const newObj = {
      building_id: props.buildingId,
      project_id: projectId,
      floor_name: new_floor,
    };

    addNewFloor(newObj).then(() => {
      showLoader.value = false;
      emit('updateData');
    }).catch((err) => {
      console.log('err', err);
    });

  } else {
    // emit('renameFloor', item);
    const new_name = item.floor_name.split(" ")[1];
    const newObj = {
      building_id: props.buildingId,
      project_id: projectId,
      floor_id: item.floor_id,
      name: new_name,
    };

    renameFloor(newObj).then(() => {
      showLoader.value = false;
      emit('updateData');
    }).catch((err) => {
      console.log('error', err);
    });
  }
  dirtyMap.value[item.floor_id] = false; // Reset dirty state after save
};

const handleChildSortEmit = (val) => {
  const sortedObj = {};

  // Add the dragged item first
  if (val.draggedItem) {
    sortedObj[val.draggedItem.floor_id] = val.draggedItem;
  }

  // Add the rest of the sorted items
  if (val.sortedItems && Array.isArray(val.sortedItems)) {
    val.sortedItems.forEach((item) => {
      sortedObj[item.floor_id] = item;
    });
  }

  emit('sortedData', sortedObj);
};
</script>

<template>
  <div class="!w-full h-full" v-if="dumpData">
    <div class="relative !w-full h-fit  flex flex-col justify-center items-center rounded shadow bg-white">
      <div class="sticky top-0 w-full bg-gray-50">
        <div class="justify-center items-center gap-3 w-full flex border-b border-gray-200 hover:bg-gray-50">
            <div class="!w-[50px] p-3 text-sm text-gray-900">
              <p>S.no</p>
            </div>
            <div class="!w-20 flex-1 p-2">
              <p>Floor name</p>
            </div>
            <!-- <div class="!w-[200px] p-2 text-sm text-gray-500">
               <p>modified</p>
            </div> -->
            <div class="!w-16 p-2">
              <p></p>
            </div>
          </div>
      </div>
      <NestedReOrder
        v-model="dumpData"
        groupName="floors"
        :allowChildReparenting="false"
        :allowChildSort="true"
        uniqueKey="id"
        ghostClass="sampe_ghost"
        animationMilliSec="450"
        sortReferenceKey="floor_id"
        tag="div"
        @handleChildSort="(val) => handleChildSortEmit(val)"
        class="block w-full"
      >
        <template #default="{item}">
          <div
            class=" overflow-y-auto w-full flex justify-center items-center gap-3 bg-white border-b border-gray-200 hover:bg-gray-50"
            :ref="el => { if (!rowRefs.value) rowRefs.value = {}; rowRefs.value[item.floor_id] = el }"
          >
            <div class="!w-[50px] p-3 text-sm text-gray-900">{{ item.sno }}</div>
            <div class="!w-[100px] flex-1 p-2">
              <div class="text-sm text-gray-900">
                <div class="w-3/4 flex items-center gap-2">
                  <input
                    v-model="item.floor_name"
                    type="text"
                    class="border rounded px-2 py-1 flex-1 capitalize"
                    @input="onInputChange(item)"
                  />
                  <button
                    v-if="(dirtyMap[item.floor_id] || item.isNew) && !(isTowerDataRefreshed.value)"
                    @click="handleNameChange(item)"
                    class="bg-blue-500 text-white px-3 py-2 rounded text-xs hover:bg-blue-600 flex gap-2 items-center"
                  >
                    <div v-if="showLoader" class="loader !h-3 !w-3 !border-2"></div>
                    Save
                  </button>
                </div>
              </div>
            </div>
            <!-- <div class="!w-[200px] p-2 text-sm text-gray-500 !whitespace-nowrap">
              <div>
                {{ item.updated_at ? formatTimestamp(item.updated_at) : ''}}
              </div>
            </div> -->
            <div class="!w-16">
              <button @click="handleDelete(item)" class="text-gray-400 p-2 rounded-lg bg-gray-200 hover:text-red-500">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 4h12M5.333 4V2.667a1.333 1.333 0 011.334-1.334h2.666a1.333 1.333 0 011.334 1.334V4m2 0v9.333a1.333 1.333 0 01-1.334 1.334H4.667a1.333 1.333 0 01-1.334-1.334V4h9.334z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </template>
      </NestedReOrder>
    </div>
  </div>
</template>

<style scoped>
.sampe_ghost {
  opacity: 0.1;
}
</style>
