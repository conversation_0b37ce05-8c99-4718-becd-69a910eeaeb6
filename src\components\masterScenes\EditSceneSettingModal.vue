<script setup>
import { onMounted, ref, watch } from 'vue';
import Spinner from '../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { EditProjectSceneSetting } from '../../validationSchema/scene';
import { useRoute, useRouter } from 'vue-router';
import { Org_Store } from '../../store/organization';
import Modal from '../common/Modal/Modal.vue';
import { updateMasterScene } from '../../api/masterScene/earth';

const loader = ref(false);
const newOrganizationStore = Org_Store();
const route = useRoute();
const router = useRouter();
const sceneId = ref(route.params.scene_id);
const selectedType = ref();
const typeList = [{ name: 'earth', value: 'earth' }, { name: 'image', value: 'image' }];
const active = ref();
const root = ref();
const clouds = ref();
const name = ref();
const info_text = ref();
const parent = ref();

const handleInitialValue = () => {
  if (newOrganizationStore.masterScenes?.[sceneId.value]?.sceneData) {
    const initialValue = newOrganizationStore.masterScenes[sceneId.value].sceneData;
    if (initialValue.type) {
      selectedType.value = initialValue.type;
    }
    active.value = initialValue.active;
    root.value = initialValue.root;
    clouds.value = initialValue.clouds;
    parent.value = initialValue.parent;
    name.value = initialValue.name;
    info_text.value = initialValue.info_text;
  }
};
onMounted(() => {
  handleInitialValue();
});

watch(() => newOrganizationStore.masterScenes, () => {
  handleInitialValue();
});

const handleSubmit = (values) => {

  const initialValue = newOrganizationStore.masterScenes[sceneId.value].sceneData;

  // Changin undefined or null to false for toggle
  if (values.active === undefined || values.active === null) {
    values.active = false;
  }
  if (values.root === undefined || values.root === null) {
    values.root = false;
  }
  if (values.clouds === undefined || values.clouds === null) {
    values.clouds = false;
  }
  if (values.parent === undefined || values.parent === null || values.parent === '') {
    delete values.parent;
  }

  // Update files
  const formData = new FormData();

  values.lowResolutionFile ? formData.append('lowRes', values.lowResolutionFile):{};
  values.highResolutionFile ? formData.append('highRes', values.highResolutionFile):{};
  values.videoFile ? formData.append('video', values.videoFile):{};
  initialValue.type !== values.type ? formData.append('type', values.type): {};
  initialValue.name !== values.name ? formData.append('name', values.name) : {};
  initialValue.parent !== values.parent ? formData.append('parent', values.parent) : {};
  initialValue.info_text !== values.info_text ? formData.append('info_text', values.info_text) : {};
  initialValue.root !== values.root ? formData.append('root', values.root)  : {};
  initialValue.active !== values.active ? formData.append('active', values.active)  : {};
  initialValue.clouds !== values.clouds ? formData.append('clouds', values.clouds) : {};

  if (!formData.entries().next().done){ // Checks if form data is empty or not
    formData.append('_id', sceneId.value);

    updateMasterScene(formData).then(() => { // Func to update master scene
      router.go(-1);
    }).catch(() => {
    });
  }

};

</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary">
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>

      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Edit master Scene settings.</h1>
          <p class="modal-subheading-primary">
            Fill details below to edit Master Scene settings.
          </p>
        </div>

        <Form
          @submit="handleSubmit"
          :validation-schema="EditProjectSceneSetting"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label for="name" class="label-primary"> Name</label>
              <Field
                as="input"
                type="text"
                v-model="name"
                name="name"
                autocomplete
                id="name"
                class="input-primary"
                :placeholder="`Enter Master Scene Name`"
              />
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="name"
              />
            </div>

            <div class="col-span-auto">
              <label for="type" class="label-primary"> Type</label>
              <Field
                v-model="selectedType"
                as="select"
                type="text"
                disabled
                name="type"
                id="type"
                autocomplete="type"
                class="select-primary"
                :placeholder="`Enter scene Type`"
              >
                <option value="" disabled>Choose</option>
                <option value="" disabled v-if="!typeList">
                  No Type found !
                </option>
                <option
                  v-else
                  :value="option.value"
                  v-for="(option, index) in typeList"
                  :key="index"
                  class="text-black"
                >
                  {{ option.name }}
                </option>
              </Field>
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="type"
              />
            </div>

            <div class="col-span-auto">
              <label for="lowResolutionFile" class="label-primary"
                >Upload Low Resolution File</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  name="lowResolutionFile"
                  id="lowResolutionFile"
                  class="input-primary"
                  placeholder="Upload Low Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="lowResolutionFile"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="highResolutionFile" class="label-primary"
                >Upload High Resolution File</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  name="highResolutionFile"
                  id="highResolutionFile"
                  autocomplete="highRes"
                  class="input-primary"
                  placeholder="Upload High Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="highResolutionFile"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="videoFile" class="label-primary"
                >Upload video file</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  name="videoFile"
                  id="videoFile"
                  autocomplete="videoFile"
                  class="input-primary"
                  placeholder="Upload video file"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="videoFile"
                />
              </div>
            </div>

            <div
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
            >
              <label for="parent" class="label-primary"
                >select Parent Scene</label
              >
              <Field
                v-model="parent"
                as="select"
                id="parent"
                name="parent"
                class="select-primary"
              >
                <option value="" class="text-gray-700">Choose</option>
                <option
                  value=""
                  disabled
                  v-if="
                    !newOrganizationStore.masterScenes ||
                    Object.keys(newOrganizationStore.masterScenes).length === 0
                  "
                >
                  No Data found !
                </option>
                <option
                  :value="option.sceneData._id"
                  v-for="(option, index) in newOrganizationStore.masterScenes"
                  :key="index"
                  class="text-black"
                >
                  {{ option.sceneData.name }}
                </option>
              </Field>
              <ErrorMessage
                name="parent"
                class="text-sm text-rose-500 mt-1"
                as="p"
              />
            </div>

            <div class="col-span-auto">
              <label for="info_text" class="label-primary"> Info Text</label>
              <Field
                as="input"
                type="text"
                v-model="info_text"
                name="info_text"
                class="input-primary"
                :placeholder="`Enter Info Text`"
              />
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="info_text"
              />
            </div>

            <div class="col-span-full mt-2">
              <div class="flex justify-start items-start gap-2 w-auto">
                <label for="active" class="label-primary"> isActive</label>
                <div
                  class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                >
                  <div class="relative mb-0 p-0">
                    <Field
                      id="active"
                      class="sr-only peer"
                      name="active"
                      type="checkbox"
                      v-model="active"
                      :value="true"
                    />
                    <label
                      for="active"
                      class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                    >
                    </label>
                  </div>
                  <ErrorMessage
                    as="p"
                    class="text-sm text-rose-500 mt-1"
                    name="active"
                  />
                </div>
              </div>
            </div>

            <div class="col-span-full mt-2">
              <div class="flex justify-start items-start gap-2 w-auto">
                <label for="clouds" class="label-primary"> isCloude</label>
                <div
                  class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                >
                  <div class="relative mb-0 p-0">
                    <Field
                      id="clouds"
                      v-model="clouds"
                      class="sr-only peer"
                      name="clouds"
                      type="checkbox"
                      :value="true"
                    />
                    <label
                      for="clouds"
                      class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                    >
                    </label>
                  </div>
                  <ErrorMessage
                    as="p"
                    class="text-sm text-rose-500 mt-1"
                    name="clouds"
                  />
                </div>
              </div>
            </div>

            <div class="col-span-full mt-2">
              <div class="flex justify-start items-start gap-2 w-auto">
                <label for="root" class="label-primary"> isRootScene</label>
                <div
                  class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                >
                  <div class="relative mb-0 p-0">
                    <Field
                      id="root"
                      v-model="root"
                      class="sr-only peer"
                      name="root"
                      type="checkbox"
                      :value="true"
                    />
                    <label
                      for="root"
                      class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                    >
                    </label>
                  </div>
                  <ErrorMessage
                    as="p"
                    class="text-sm text-rose-500 mt-1"
                    name="root"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="() => router.go(-1)"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="proceed-btn-primary"
            >
              Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
