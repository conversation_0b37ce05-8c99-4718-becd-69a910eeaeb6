<script setup>
import Deepzoom from '../../../components/scenes/Deepzoom/Deepzoom.vue';
import { UserStore } from '../../../store/index';
import { ref } from 'vue';
import { ProjectStore } from '../../../store/project';
import { useRoute } from 'vue-router';
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
import designMenuBar from '@/components/scenes/DesignMenuBar.vue';

const userStore = UserStore();
const projectStore = ProjectStore();
const route = useRoute();
const projectId = ref(route.params.project_id);

// Inialization
projectStore.RefreshScenes(projectId.value);

</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar />
        <div
            class="h-full overflow-hidden w-full">
            <div v-if="userStore.user_data"
                class="pt-0 bg-transparent h-full overflow-y-auto w-full flex-1 bg-gray-100 flex flex-col overflow-hidden">
                   <div class="flex justify-evenly h-full w-full overflow-hidden gap-2 border">
                   <designMenuBar/>
                   <Deepzoom/>
                   </div>
                <router-view></router-view>
            </div>
        </div>
    </div>
</template>
