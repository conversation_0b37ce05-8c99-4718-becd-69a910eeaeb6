<script setup>
import { ref } from 'vue';
import { PlusIcon, XMarkIcon } from '@heroicons/vue/20/solid';
import { UpdateProjectSettings, uploadSettingFiles } from '../../../api/projects/settings/index.ts';
import { projectGeneralSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import moment from 'moment-timezone';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { formatTimeTimezone } from '../../../helpers/domhelper';
import VueTimepicker from 'vue3-timepicker';
import 'vue3-timepicker/dist/VueTimepicker.css';
import Multiselect from 'vue-multiselect';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const availableSlots = ref([]);
const timePickerRef = ref(null);
const brandingLogoDarkFileRef = ref({
  preview: null,
  fileData: null,
});
const brandingLogoFileUploadRef = ref({
  preview: null,
  fileData: null,
});
const timezones = moment.tz.names();
const selected_timezone = ref(null);

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const handleCreateObjectURL = (val) => {
  return URL.createObjectURL(val); // Local blob url
};

const timeFormatValidation = (time) => {
  if (time.length > 0) {
    return true;
  }
  return false;
};

const handleAddSlot = () => {
  if (timePickerRef.value) {
    console.log(timePickerRef.value);
    // Validate the time inputs
    if (
      timeFormatValidation(timePickerRef.value.hh) &&
      timeFormatValidation(timePickerRef.value.mm) &&
      timeFormatValidation(timePickerRef.value.A)
    ) {
      // Convert to 24-hour format
      const format = `${timePickerRef.value.hh}:${timePickerRef.value.mm} ${timePickerRef.value.A}`;
      const [time, period] = format.split(' ');
      const [hours, minutes] = time.split(':');
      let hours24 = parseInt(hours, 10);

      // Adjust hours based on AM/PM period
      if (period.toUpperCase() === 'PM' && hours24 !== 12) {
        hours24 += 12;
      } else if (period.toUpperCase() === 'AM' && hours24 === 12) {
        hours24 = 0;
      }

      // Get the timezone from project settings
      const timezone = selected_timezone.value || previousData.value.timezone;

      // Create a moment object in the specified timezone
      const now = moment.tz(timezone);
      console.log(now);

      // Set the desired time on the current date
      now.set({ hour: hours24, minute: minutes, second: 0, millisecond: 0 });

      // Convert to ISO string
      const isoString = now.toISOString();
      console.log(isoString);

      // Add the ISO string to available slots
      availableSlots.value.push(isoString);
    }
  }
};

const setupDataCallBack = (values) => {
  if (values) {
    console.log("setupDataCallBack general");

    const data = values;

    // Previous Data
    previousData.value =  {
      is_enabled: data.projectSettings?.general?.is_enabled ? data.projectSettings?.general?.is_enabled : false,
      hideStatus: (data.projectSettings?.general?.hideStatus ? data.projectSettings?.general?.hideStatus : false),
      branding_logo: (data.projectSettings?.general?.branding_logo ? data.projectSettings?.general?.branding_logo : null),
      branding_logo_dark: (data.projectSettings?.general?.branding_logo_dark ? data.projectSettings?.general?.branding_logo_dark : null),
      timezone: (data.projectSettings?.general?.timezone ? data.projectSettings.general.timezone : null),
      slots: (data.projectSettings?.general?.slots ? data.projectSettings.general.slots : null),
      lat: (data.projectSettings?.general?.lat ? data.projectSettings?.general.lat : ''),
      long: (data.projectSettings?.general?.long ? data.projectSettings?.general.long : ''),
    };

    // Form Initial Values
    initialData.value = {
      is_enabled: (data.projectSettings?.general?.is_enabled ? data.projectSettings?.general?.is_enabled : false),
      hideStatus: (data.projectSettings?.general?.hideStatus ? data.projectSettings?.general?.hideStatus : false),
      branding_logo: null,
      branding_logo_dark: null,
      lat: (data.projectSettings?.general?.lat ? data.projectSettings?.general?.lat : ''),
      long: (data.projectSettings?.general?.long ? data.projectSettings?.general?.long : ''),
    };
    brandingLogoFileUploadRef.value.preview = data.projectSettings?.general?.branding_logo;
    brandingLogoFileUploadRef.value.fileData = null;
    brandingLogoDarkFileRef.value.preview = data.projectSettings?.general?.branding_logo_dark;
    brandingLogoDarkFileRef.value.fileData = null;

    if (data.projectSettings?.general?.slots) {
      availableSlots.value = [...data.projectSettings.general.slots];
    }

    if (data.projectSettings?.general?.timezone) {
      selected_timezone.value = (data.projectSettings.general.timezone);
    }

  }
};

const handleSubmit = async (val) => {
  return new Promise((resolve) => {
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...val }; // form values

    console.log(prevData);
    console.log(newCompareObj);

    // delete some values
    delete prevData.branding_logo;
    delete prevData.branding_logo_dark;
    delete newCompareObj.branding_logo;
    delete newCompareObj.branding_logo_dark;

    // formData
    const formData = new FormData();
    formData.append('project_id', project_Id.value); // projectId

    const parms = frameParms(prevData, newCompareObj);

    /* if (Object.keys(parms).length > 0) {
            parms.is_enabled !== undefined && formData.append('is_enabled', parms.is_enabled);
            parms.hideStatus !== undefined && formData.append('hideStatus', parms.hideStatus);
            parms.slots && formData.append('slots', parms.slots);
        } */

    val.branding_logo && formData.append('branding_logo', val.branding_logo);
    val.branding_logo_dark && formData.append('branding_logo_dark', val.branding_logo_dark);

    if (Object.keys(parms).length > 0 || (val.branding_logo !== null && val.branding_logo !== undefined) || (val.branding_logo_dark !== null && val.branding_logo_dark !== undefined)) {

      if (Object.prototype.hasOwnProperty.call(parms, 'hideStatus')){
        parms.hideStatus =  parms.hideStatus ? true : false;
      }

      if ((val.branding_logo !== null && val.branding_logo !== undefined) || (val.branding_logo_dark !== null && val.branding_logo_dark !== undefined)) {
        // Normal data with logo's
        uploadSettingFiles(formData).then((res) => {
          if (Object.keys(res).length > 0) {
            const payload = {}; // Object to store values to be sent
            payload.project_id = project_Id.value;
            payload.query = {
              [projectSettingsFormTypes.GENERAL]: {},
            };

            // Add file upload results to general object
            Object.keys(res).forEach((key) => {
              payload.query.general[key] = res[key];
            });

            // Add form data to general object
            if (Object.keys(parms).length > 0) {
              Object.keys(parms).forEach((key) => {
                payload.query.general[key] = parms[key];
              });
            }

            UpdateProjectSettings(payload).then((result) => {
              if (result){
                projectStore.settings.projectSettings[projectSettingsFormTypes.GENERAL] = result.projectSettings[projectSettingsFormTypes.GENERAL]; // update to store
                setupDataCallBack(result); // update the values
                resolve(result);
              }
            });
          }
        });
      } else if (Object.keys(parms).length > 0 && (val.branding_logo === null || val.branding_logo === undefined) && (val.branding_logo_dark === null || val.branding_logo_dark === undefined)){
        // Only Normal data's
        const payload = {}; // Object to store values to be sent
        payload.project_id = project_Id.value;
        payload.query = {
          [projectSettingsFormTypes.GENERAL]: {},
        };

        // Add all form data to general object
        Object.keys(parms).forEach((key) => {
          payload.query.general[key] = parms[key];
        });

        UpdateProjectSettings(payload).then((result) => {
          console.log(projectStore.settings.projectSettings[projectSettingsFormTypes.GENERAL]);
          console.log(result.projectSettings[projectSettingsFormTypes.GENERAL]);
          if (result){
            projectStore.settings.projectSettings[projectSettingsFormTypes.GENERAL] = result.projectSettings[projectSettingsFormTypes.GENERAL]; // update to store
            setupDataCallBack(result); // update the values
            resolve(result);
          }
        });
      }
    } else {
      brandingLogoFileUploadRef.value.preview = null;
      brandingLogoDarkFileRef.value.preview = null;
      resolve();
    }

  });
};

// Initialize
if (projectStore.settings){
  console.log("I'm from General settings");
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
         <!-- General Settings -->
         <div class="flex flex-col justify-start items-start my-3">

            <!-- Headers -->
            <div class="flex justify-between items-center w-full mb-4">

            <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> General Settings: </p>

            <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
                @handle-click="() => isEdit = !isEdit">
                <template v-slot:svg>
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                    d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                    fill="white" />
                </svg>
                </template>
            </Button>

            <div v-if="isEdit" class="flex justify-start items-center gap-3">
                <Button title="Reset" type="button" theme="secondary"
                @handle-click="() => isEdit = !isEdit"> </Button>
                <label for="editGeneralSettings"
                :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer']">
                Save </label>
            </div>

            </div>

            <!-- View -->
            <div v-if="!isEdit" class="grid grid-cols-3 gap-8 w-full mt-3">

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Enabled: </label>
                <p v-if="previousData?.is_enabled"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.is_enabled }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Hide Status: </label>
                <p v-if="previousData?.hideStatus"
                class="font-medium text-sm text-txt-default">
                 {{ previousData?.hideStatus }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                false
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                    <label class="font-semibold text-sm text-txt-50"> Timezone: </label>
                    <p class="font-medium text-sm text-txt-default capitalize"> {{
                    previousData?.timezone ?
                    previousData.timezone : '-' }} </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">

                <label class="font-semibold text-sm text-txt-50"> Avaliable Slots: </label>
                <div class=" flex justify-start items-center gap-2 flex-wrap  min-h-[auto] max-h-56 overflow-y-auto" v-if="previousData">
                <p class="font-medium text-sm text-txt-default bg-transparent border-[1px] p-2 rounded-3xl border-[grey] "
                    v-for="item in previousData?.slots" :key="item"> {{
                    formatTimeTimezone(item,
                        previousData?.timezone) }} </p>
                </div>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Branding Logo: </label>

                <img v-if="previousData?.branding_logo"
                :src="previousData?.branding_logo" class="w-52 h-fit" />
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Branding Logo Dark: </label>
                <img v-if="previousData?.branding_logo_dark"
                :src="previousData?.branding_logo_dark" class="w-52 h-fit" />
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

                <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Lat: </label>
                <p>{{ previousData?.lat }}</p>
                </div>

                <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Long: </label>
                <p>{{ previousData?.long }}</p>
                </div>

            </div>

            <!-- Form -->
            <Form v-else class=" flex flex-col justify-start items-start w-full mt-3"
            @submit="(val) => handleSubmit(val).then(() => isEdit = false)"
            :initial-values="initialData" :validation-schema="projectGeneralSettingsSchema">

            <div class="grid grid-cols-3 gap-8 w-full">

                <div class="flex flex-col justify-start items-start gap-2 w-full">

                <Field v-slot="{ field }" name="is_enabled" type="checkbox" :value="true" :unchecked-value="false">
                    <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" />
                    Enable <strong>*</strong>
                    </label>
                </Field>

                <ErrorMessage name="is_enabled" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

                </div>
                <div class="flex flex-col justify-start items-start gap-2 w-full">
                <div class="flex  justify-start items-start gap-2 w-auto p-1">
                    <label for="hideStatus" class="cursor-pointer text-sm text-bg-50 font-semibold px-2 py-0">
                    Hide Status</label>
                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                    <div class="relative mb-0 p-0">
                        <Field id="hideStatus" class="sr-only peer" name="hideStatus" type="checkbox" :value="true" />
                        <label for="hideStatus"
                        class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                        </label>
                    </div>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="hideStatus" />
                    </div>
                </div>
                </div>

                <div class="flex flex-col justify-start items-start gap-2 w-full">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Timezone
                    <strong>*</strong>
                </label>
                <Field name="timezone" :model-value="selected_timezone" v-slot="{ field }">

                    <Multiselect :allow-empty="false" v-bind="field" v-model="selected_timezone" :searchable="false"
                    :close-on-select="true" :show-labels="false" placeholder="Choose" :options="timezones"
                    maxHeight="250">
                    </Multiselect>

                </Field>

                <ErrorMessage name="timezone" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

                </div>
                <div class="flex flex-col justify-start items-start gap-2 w-full">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-4"> Available Slots
                    <strong>*</strong> </label>

                <Field name="slots" :model-value="availableSlots" v-slot="{ field }">

                    <div v-bind="field">
                    <div class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-56 overflow-y-auto">
                        <template v-if="availableSlots.length > 0">
                        <span
                            class="font-medium text-sm text-txt-default bg-transparent border-[1px] p-2 rounded-3xl border-[grey]  flex justify-start items-center gap-2"
                            v-for="slots, index in availableSlots" :key="index">
                            {{ formatTimeTimezone(slots, selected_timezone) }}
                            <XMarkIcon class="text-red-600 w-5 cursor-pointer"
                            @click="() => availableSlots.splice(index, 1)" />
                        </span>
                        </template>
                        <span v-else>
                        -
                        </span>
                    </div>
                    <div class="flex gap-2 items-center justify-stretch my-2">
                        <vue-timepicker v-model="timePickerRef" id="timePicker" format="hh:mm A"></vue-timepicker>

                        <!-- :disabled="timePickerRef ? false : true" -->
                        <Button type="button" class="w-40" title="Add Slots" @handle-click="handleAddSlot">
                        <template v-slot:svg>
                            <PlusIcon class="w-5" />
                        </template>
                        </Button>
                    </div>
                    </div>

                </Field>

                <ErrorMessage name="slots" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

                </div>

                <div class="flex flex-col items-start justify-start gap-2 w-full">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0"> Branding Logo
                    <strong>*</strong> </label>

                <div class="flex flex-row justify-start gap-2 items-start w-full h-64">

                    <div class="w-96 h-full overflow-hidden">
                    <img v-if="brandingLogoFileUploadRef.preview" :src="brandingLogoFileUploadRef.preview" alt=""
                        class="h-[inherit] rounded-md bg-gray-100 w-full">
                    <div v-else
                        class="flex justify-center items-center gap-2  rounded-md h-full bg-gray-50 border-[1px] border-gray-300">
                        <p class="text-txt-black text-base font-medium"> Preview Area </p>
                    </div>
                    </div>

                    <div class="w-96 h-full mb-0">

                    <Field name="branding_logo" :model-value="brandingLogoFileUploadRef.fileData" v-slot="{ field }">
                        <div v-bind="field" class="h-full">
                        <input type="file" id="branding_logo" class="hidden" @input="(e) => {
                            brandingLogoFileUploadRef.fileData = e.target.files[0];
                            brandingLogoFileUploadRef.preview = handleCreateObjectURL(e.target.files[0]);
                        }
                            ">
                        <label for="branding_logo"
                            class="text-txt-50 border-[1px] border-dashed border-bg-550 rounded-md h-[inherit] flex flex-col justify-center items-center cursor-pointer">
                            <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M31.4999 16C31.5062 18.3815 30.7336 20.6997 29.2999 22.6013C29.2209 22.7063 29.1221 22.7948 29.0089 22.8617C28.8958 22.9285 28.7705 22.9724 28.6404 22.9909C28.5103 23.0093 28.3778 23.002 28.2505 22.9692C28.1233 22.9365 28.0037 22.879 27.8986 22.8C27.7936 22.7211 27.7051 22.6222 27.6383 22.5091C27.5714 22.3959 27.5275 22.2707 27.5091 22.1406C27.4906 22.0105 27.4979 21.878 27.5307 21.7507C27.5634 21.6234 27.6209 21.5038 27.6999 21.3988C28.8734 19.8441 29.5056 17.9479 29.4999 16C29.4999 13.6131 28.5517 11.3239 26.8638 9.63607C25.176 7.94825 22.8868 7.00004 20.4999 7.00004C18.1129 7.00004 15.8238 7.94825 14.1359 9.63607C12.4481 11.3239 11.4999 13.6131 11.4999 16C11.4999 16.2653 11.3945 16.5196 11.207 16.7071C11.0195 16.8947 10.7651 17 10.4999 17C10.2347 17 9.98031 16.8947 9.79278 16.7071C9.60524 16.5196 9.49988 16.2653 9.49988 16C9.4994 14.9909 9.63778 13.9865 9.91113 13.015C9.77489 13 9.63738 13 9.49988 13C7.90859 13 6.38246 13.6322 5.25724 14.7574C4.13203 15.8826 3.49988 17.4087 3.49988 19C3.49988 20.5913 4.13203 22.1175 5.25724 23.2427C6.38246 24.3679 7.90859 25 9.49988 25H12.4999C12.7651 25 13.0195 25.1054 13.207 25.2929C13.3945 25.4805 13.4999 25.7348 13.4999 26C13.4999 26.2653 13.3945 26.5196 13.207 26.7071C13.0195 26.8947 12.7651 27 12.4999 27H9.49988C8.4001 27.0003 7.31208 26.7738 6.30377 26.3347C5.29546 25.8955 4.38853 25.2532 3.6396 24.4478C2.89068 23.6425 2.31585 22.6913 1.95103 21.6538C1.58621 20.6163 1.43923 19.5147 1.51926 18.4179C1.59929 17.321 1.90462 16.2524 2.41619 15.2788C2.92775 14.3053 3.63455 13.4477 4.49245 12.7595C5.35035 12.0714 6.34092 11.5675 7.40229 11.2794C8.46366 10.9913 9.57304 10.9251 10.6611 11.085C11.7691 8.86898 13.5928 7.09188 15.8368 6.04158C18.0808 4.99127 20.6136 4.72928 23.025 5.29804C25.4365 5.8668 27.5853 7.233 29.1234 9.17535C30.6616 11.1177 31.4989 13.5224 31.4999 16ZM20.2074 15.2925C20.1145 15.1996 20.0042 15.1258 19.8828 15.0755C19.7614 15.0252 19.6313 14.9992 19.4999 14.9992C19.3685 14.9992 19.2383 15.0252 19.1169 15.0755C18.9955 15.1258 18.8853 15.1996 18.7924 15.2925L14.7924 19.2925C14.6995 19.3854 14.6258 19.4957 14.5755 19.6171C14.5252 19.7385 14.4993 19.8686 14.4993 20C14.4993 20.1314 14.5252 20.2615 14.5755 20.3829C14.6258 20.5043 14.6995 20.6146 14.7924 20.7075C14.98 20.8952 15.2345 21.0006 15.4999 21.0006C15.6313 21.0006 15.7614 20.9747 15.8828 20.9244C16.0042 20.8741 16.1145 20.8004 16.2074 20.7075L18.4999 18.4138V26C18.4999 26.2653 18.6052 26.5196 18.7928 26.7071C18.9803 26.8947 19.2347 27 19.4999 27C19.7651 27 20.0195 26.8947 20.207 26.7071C20.3945 26.5196 20.4999 26.2653 20.4999 26V18.4138L22.7924 20.7075C22.8853 20.8004 22.9956 20.8741 23.117 20.9244C23.2384 20.9747 23.3685 21.0006 23.4999 21.0006C23.6313 21.0006 23.7614 20.9747 23.8828 20.9244C24.0042 20.8741 24.1145 20.8004 24.2074 20.7075C24.3003 20.6146 24.374 20.5043 24.4243 20.3829C24.4746 20.2615 24.5004 20.1314 24.5004 20C24.5004 19.8686 24.4746 19.7385 24.4243 19.6171C24.374 19.4957 24.3003 19.3854 24.2074 19.2925L20.2074 15.2925Z"
                                fill="#5B616E" />
                            </svg>

                            Branding Logo
                            <span v-if="brandingLogoFileUploadRef.fileData" class="text-txt-50 font-semibold text-center">
                            {{ brandingLogoFileUploadRef.fileData.name }}
                            </span>
                        </label>
                        </div>
                    </Field>

                    <ErrorMessage name="branding_logo" as="p" v-slot="{ message }"
                        class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                            fill="#B3261E" />
                        </svg>
                        <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

                    </div>

                </div>

                </div>

                <div class="flex flex-col items-start justify-start gap-2 w-full">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0"> Branding Logo Dark
                    <strong>*</strong>
                </label>

                <div class="flex flex-row justify-start gap-2 items-start w-full h-64">

                    <div class="w-96 h-full overflow-hidden">
                    <img v-if="brandingLogoDarkFileRef.preview" :src="brandingLogoDarkFileRef.preview" alt=""
                        class=" h-[inherit] rounded-md bg-gray-100 w-full">
                    <div v-else
                        class="flex justify-center items-center gap-2  rounded-md h-full bg-gray-50 border-[1px] border-gray-300">
                        <p class="text-txt-black text-base font-medium"> Preview Area </p>
                    </div>
                    </div>

                    <div class="w-96 h-full mb-0">

                    <Field name="branding_logo_dark" :model-value="brandingLogoDarkFileRef.fileData" v-slot="{ field }">
                        <div v-bind="field" class="h-full">
                        <input type="file" id="branding_logo_dark" class="hidden" @input="(e) => {
                            brandingLogoDarkFileRef.fileData = e.target.files[0];
                            brandingLogoDarkFileRef.preview = handleCreateObjectURL(e.target.files[0]);
                        }">
                        <label for="branding_logo_dark"
                            class="text-txt-50 border-[1px] border-dashed border-bg-550 rounded-md h-[inherit] flex flex-col justify-center items-center cursor-pointer">
                            <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M31.4999 16C31.5062 18.3815 30.7336 20.6997 29.2999 22.6013C29.2209 22.7063 29.1221 22.7948 29.0089 22.8617C28.8958 22.9285 28.7705 22.9724 28.6404 22.9909C28.5103 23.0093 28.3778 23.002 28.2505 22.9692C28.1233 22.9365 28.0037 22.879 27.8986 22.8C27.7936 22.7211 27.7051 22.6222 27.6383 22.5091C27.5714 22.3959 27.5275 22.2707 27.5091 22.1406C27.4906 22.0105 27.4979 21.878 27.5307 21.7507C27.5634 21.6234 27.6209 21.5038 27.6999 21.3988C28.8734 19.8441 29.5056 17.9479 29.4999 16C29.4999 13.6131 28.5517 11.3239 26.8638 9.63607C25.176 7.94825 22.8868 7.00004 20.4999 7.00004C18.1129 7.00004 15.8238 7.94825 14.1359 9.63607C12.4481 11.3239 11.4999 13.6131 11.4999 16C11.4999 16.2653 11.3945 16.5196 11.207 16.7071C11.0195 16.8947 10.7651 17 10.4999 17C10.2347 17 9.98031 16.8947 9.79278 16.7071C9.60524 16.5196 9.49988 16.2653 9.49988 16C9.4994 14.9909 9.63778 13.9865 9.91113 13.015C9.77489 13 9.63738 13 9.49988 13C7.90859 13 6.38246 13.6322 5.25724 14.7574C4.13203 15.8826 3.49988 17.4087 3.49988 19C3.49988 20.5913 4.13203 22.1175 5.25724 23.2427C6.38246 24.3679 7.90859 25 9.49988 25H12.4999C12.7651 25 13.0195 25.1054 13.207 25.2929C13.3945 25.4805 13.4999 25.7348 13.4999 26C13.4999 26.2653 13.3945 26.5196 13.207 26.7071C13.0195 26.8947 12.7651 27 12.4999 27H9.49988C8.4001 27.0003 7.31208 26.7738 6.30377 26.3347C5.29546 25.8955 4.38853 25.2532 3.6396 24.4478C2.89068 23.6425 2.31585 22.6913 1.95103 21.6538C1.58621 20.6163 1.43923 19.5147 1.51926 18.4179C1.59929 17.321 1.90462 16.2524 2.41619 15.2788C2.92775 14.3053 3.63455 13.4477 4.49245 12.7595C5.35035 12.0714 6.34092 11.5675 7.40229 11.2794C8.46366 10.9913 9.57304 10.9251 10.6611 11.085C11.7691 8.86898 13.5928 7.09188 15.8368 6.04158C18.0808 4.99127 20.6136 4.72928 23.025 5.29804C25.4365 5.8668 27.5853 7.233 29.1234 9.17535C30.6616 11.1177 31.4989 13.5224 31.4999 16ZM20.2074 15.2925C20.1145 15.1996 20.0042 15.1258 19.8828 15.0755C19.7614 15.0252 19.6313 14.9992 19.4999 14.9992C19.3685 14.9992 19.2383 15.0252 19.1169 15.0755C18.9955 15.1258 18.8853 15.1996 18.7924 15.2925L14.7924 19.2925C14.6995 19.3854 14.6258 19.4957 14.5755 19.6171C14.5252 19.7385 14.4993 19.8686 14.4993 20C14.4993 20.1314 14.5252 20.2615 14.5755 20.3829C14.6258 20.5043 14.6995 20.6146 14.7924 20.7075C14.98 20.8952 15.2345 21.0006 15.4999 21.0006C15.6313 21.0006 15.7614 20.9747 15.8828 20.9244C16.0042 20.8741 16.1145 20.8004 16.2074 20.7075L18.4999 18.4138V26C18.4999 26.2653 18.6052 26.5196 18.7928 26.7071C18.9803 26.8947 19.2347 27 19.4999 27C19.7651 27 20.0195 26.8947 20.207 26.7071C20.3945 26.5196 20.4999 26.2653 20.4999 26V18.4138L22.7924 20.7075C22.8853 20.8004 22.9956 20.8741 23.117 20.9244C23.2384 20.9747 23.3685 21.0006 23.4999 21.0006C23.6313 21.0006 23.7614 20.9747 23.8828 20.9244C24.0042 20.8741 24.1145 20.8004 24.2074 20.7075C24.3003 20.6146 24.374 20.5043 24.4243 20.3829C24.4746 20.2615 24.5004 20.1314 24.5004 20C24.5004 19.8686 24.4746 19.7385 24.4243 19.6171C24.374 19.4957 24.3003 19.3854 24.2074 19.2925L20.2074 15.2925Z"
                                fill="#5B616E" />
                            </svg>

                            Branding Logo Dark
                            <span v-if="brandingLogoDarkFileRef.fileData" class="text-txt-50 font-semibold">
                            {{ brandingLogoDarkFileRef.fileData.name }}
                            </span>
                        </label>
                        </div>
                    </Field>

                    <ErrorMessage name="branding_logo_dark" as="p" v-slot="{ message }"
                        class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                            fill="#B3261E" />
                        </svg>
                        <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

                    </div>

                </div>

                </div>

                <div class="flex flex-col justify-start items-start gap-2 w-full">
                    <label class="font-semibold text-sm text-txt-50" for="lat"> Lat</label>
                    <Field type="number" name="lat" id="lat" class="input-primary" placeholder="Enter Lat" />
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="lat" />
                </div>
                <div class="flex flex-col justify-start items-start gap-2 w-full">
                    <label class="font-semibold text-sm text-txt-50" for="long"> Long </label>
                    <Field type="number" name="long" id="long" class="input-primary" placeholder="Enter Long" />
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="long" />
                </div>

            </div>

            <Button id="editGeneralSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>

            </Form>

        </div>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}

/* Time Picker */
.vue__time-picker input.vue__time-picker-input {
  background-color: transparent;
  text-align: left;
  color: #323232;
  border-radius: 0.375rem;
}

.vue__time-picker input.vue__time-picker-input::placeholder {
  text-align: left;
}

.vue__time-picker .clear-btn {
  color: red;
}

.vue__time-picker .vue__time-picker-input {
  height: 43px !important;
}

</style>
