import type { transformedSVG } from "./masterSVG";

export enum masterSceneType {
    IMAGE = 'image',
    IDENTICAL_UNITPLAN = 'identical_unitplan',
    GSPLAT = 'gsplat',
    CAESIUM = 'caesium',
    NEIGHBOURHOOD = 'neighbourhood',
    ROTATABLE_IMAGE = 'rotatable_image',
    ROTATABLE_IMAGE_FRAME = 'rotatable_image_frame',
    DEEP_ZOOM = 'deep_zoom',
}
export enum  deep_zoom_status{
    NOT_STARTED = 'not_started',
    PROCESSING = 'processing',
    PROCESS_SUCCESS = 'process_success',
    PROCESS_FAILED = 'process_failed',
}
export type coordinate = {
    lat: number,
    lng: number,
    name: string,
    link?: string,
    linkType?: string,
    active: boolean,
    masterSceneId:string,
}
export type updateProjectSceneFrame = {
    order: number;
    id: string;
};

export type updateBulkSceneType = {
    query: updateProjectSceneFrame[];
};

export type updateCoordinate = {
    coordinateId:string,
    lat: number,
    lng: number,
    name: string,
    link?: string,
    linkType?: string,
    active: boolean,
    masterSceneId:string,
}

export type deleteCoordinate = {
    coordinateId:string,
    targetSceneId:string,
}
/* export type masterScene={
    _id:string,
    organization_id:string,
    type:masterSceneType,
    name:string,
    background:object,
    video:string,
    active:boolean,
    info_icon:string,
    parent:string,
    info_text:string,
    coordinates: coordinate[],
    coordinate_settings: object,
    root:boolean,
    clouds:boolean,
    earth_position:object,
}
 */

export type masterScene = {
    _id: string;
    organization_id: string;
    type: masterSceneType;
    name: string;
    background: object;
    active: boolean;
    info_icon: string;
    parent: string;
    info_text: string;
    root: boolean;
    building_id: string;
    floor_ids: string[];
    clouds: boolean;
    video: string;
    gsplat_link: string;
    order: number;
    deep_zoom_status:deep_zoom_status;
    deep_zoom_failed_info:string;
    earth_position:object;
    coordinates: coordinate[];
    coordinate_settings: object;
};

/* export type updateMasterScene={
    _id:string,
    type?:masterSceneType,
    name?:string,
    highRes?:File,
    lowRes?:File,
    video?:File,
    active?:boolean,
    info_icon?:string,
    parent?:string,
    info_text?:string,
    coordinates?: coordinate[],
    coordinate_settings?: object,
    earth_position?:object,
    x_axis?:string,
    y_axis?:string,
    z_axis?:string,
} */

export type updateMasterScene = {
    type?: string;
    name?: string;
    lowRes?: string;
    highRes?: string;
    file_url?:string;
    active?: boolean;
    info_icon?: string;
    parent?: number;
    info_text?: string;
    root?: boolean;
    building_id?: string;
    clouds?: boolean;
    video?: string;
    gsplat_link?: string;
    category?: string;
    frame_id?: string;
    order?: number;
    _id?: string;
    position?: object;
    polar_angle?: object;
    distance?: object;
    auto_rotate?: boolean;
    deep_zoom_status?:{
      type:string,
      enum:deep_zoom_status
    }
    deep_zoom_failed_info?:string;
    coordinates?: coordinate[];
    coordinate_settings?: object;
    earth_position?:object;
};

export type coordinateObj = {
    lat: number,
    lng: number,
    name: string,
    link?: string,
    linkType?: string
}

export type transformedMasterScene = {
    [key: string]: {
        sceneData: masterScene,
        svgData:transformedSVG
    }
}

export type convertDeepZoom = {
    scene_id: string,
    toType: string,
    fromType: string,
    high_resolution: string,
    action:string
}
