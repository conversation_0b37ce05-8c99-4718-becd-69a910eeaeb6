<script setup>

import { ref, defineEmits, defineProps, watch} from 'vue';

const emit =defineEmits(['selected']);
const props = defineProps({
  status: Array,
  initialValue: String,
});
const selectedOption = ref(props.initialValue ? props.initialValue :null);
console.log(props.initialValue);
console.log(selectedOption.value);
const selectOption = (option) => {
  selectedOption.value = option;
  // Emit an event with the selected option value
  emit('selected', selectedOption);

};

const labelColor = {new: "bg-green-100 border-lime-600", hot: "bg-orange-100 border-orange-500", cold: "bg-cyan-50 border-cyan-500", warm: "bg-yellow-100 border-amber-400", not_interested: "bg-slate-200 border-slate-400"};
const getClassForStatus = (statusItem) => {
  return selectedOption.value === statusItem ? labelColor[statusItem] : '';
};

watch(() => props.initialValue, () => {
  console.log(props.initialValue);
  console.log(selectedOption.value);
});
</script>

<template>
    <div class="w-fit flex flex-wrap gap-3">
        <label v-for="(statusItem, index) in status" :key="index"
         :class="['radio-label border-2 capitalize', getClassForStatus(statusItem)]"
         @click="selectOption(statusItem)"
          class=" w-auto h-[2.3rem] bg-bg-900 flex justify-center items-center rounded-[3.75rem] px-4 py-[0.56rem] cursor-pointer">
          <input type="radio" :value="statusItem" v-model="selectedOption"/>
          <span class="text-txt-default text-base leading-normal font-normal not-italic whitespace-nowrap">{{ statusItem === 'not_interested' ? 'Not Interested' : statusItem }}</span>
        </label>
      </div>
  </template>

<style scoped>
    .radio-label input {
      display: none;
    }

    .radio-label input:checked + span {
      color: #000000;
    }
</style>
