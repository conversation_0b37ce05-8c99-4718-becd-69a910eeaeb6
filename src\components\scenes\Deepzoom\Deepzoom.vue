<script setup>
import { ref, watch } from 'vue';
import DeepzoomOverlay from './DeepzoomOverlay.vue';
import SafeAreaHighlight from '../SafeAreaHighlight.vue';
import DeepzoomLeftSideBar from './DeepzoomLeftSideBar.vue';
import { useRoute } from 'vue-router';
import { ProjectStore } from '@/store/project';
import { Org_Store } from '@/store/organization';
import UpdateLayers from '../UpdateLayers.vue';
import { isMasterScenePath } from '@/helpers/helpers';
import { getCategories as getAmenitiesCategories } from '../../../api/projects/amenties/index.ts';
import { getScene as getMasterScene} from '../../../api/masterScene/index';
import { getScene } from '../../../api/projects/scene/index.ts';

const route = useRoute();
const safeFrame = ref(false);
const projectStore = ProjectStore();
const newOrganizationStore = Org_Store();
const categoryList = ref();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const updateResize = ref(null); // Update resize ref
const updateScale = ref(null); // Update scale
const updateLayersPosition = ref(null); // Deep zooom update layer ref
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const sceneKey = ref(0); // Key for deep zoom component
const sceneDetails = ref(); // Scene details
const currentZooomLevel = ref(0);

console.log('inside deep zoom');

const getSceneById = async () => {
  console.log(isMasterScene.value);

  if (isMasterScene.value && sceneId.value) {
    try {
      const res = await getMasterScene(sceneId.value);
      sceneDetails.value = res;
    } catch (error) {
      console.error('Error fetching master scene:', error);
    }
  } else if (projectId.value && sceneId.value) {
    try {
      const newObject = {};
      const res = await getScene(projectId.value, sceneId.value);
      sceneDetails.value = res;
      newObject[res.sceneData._id] = sceneDetails;
      projectStore.SyncMultipleScenes(newObject);
      console.log(res);
    } catch (error) {
      console.error('Error fetching scene:', error);
    }
  }
};

if (!isMasterScene.value){
  getAmenitiesCategories(projectId.value).then((res) => { // Get list of amenities categories
    console.log('res ->', res);
    categoryList.value = res.map((elem) => {
      return {name: elem.category};
    });
  }).catch((err) => {
    console.log('output->err', err);
    uiStore.handleApiErrorMessage(err.message);
  });
}

// Api calls
if (!isMasterScene.value){
  projectStore.RefreshLandmarks(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshAmenities(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshCommunities(projectId.value);
}

newOrganizationStore.RefreshProjects();
newOrganizationStore.RefreshMasterScenes();

const reRenderDeepZoom = (newSceneId) => {
  sceneId.value = newSceneId;
  getSceneById().then(() => {

    if (sceneDetails.value?.sceneData.type ==='deep_zoom'){
      sceneKey.value++;
    }
  });
};

watch(() => route.params.scene_id, (newSceneId) => {
  if (newSceneId) {
    console.log('rerender deep zoom');
    reRenderDeepZoom(route.params.scene_id);
  } else {
    console.log('No sceneId in current route.');
  }
});

document.addEventListener('refreshAfterSceneEdit', () => { // After editing scene
  reRenderDeepZoom(sceneId.value);
});

document.addEventListener('refreshDeepZoomScene', () => { // After editing layer
  reRenderDeepZoom(sceneId.value);
});

// drag to resize
const container = ref(null);
const left = ref(null);
const right = ref(null);

const BASE = 200;
const MIN = 200;
const MAX = 600;
const MIDDLE_MIN = 50;

let isDraggingLeft = false;
let isDraggingRight = false;

const onMouseMove = (e) => {
  if (isDraggingLeft || isDraggingRight) {
    e.preventDefault(); // Prevent default selection behavior
    if (!container.value) {
      return;
    }

    const totalWidth = container.value.clientWidth;
    const leftWidth = left.value.offsetWidth;
    const rightWidth = right.value.offsetWidth;

    if (isDraggingLeft) {
      const containerLeft = container.value.getBoundingClientRect().left;
      const newLeft = Math.min(Math.max(e.clientX - containerLeft, MIN), MAX);
      const delta = newLeft - leftWidth;

      let newRight = rightWidth;

      if (delta > 0) {
        // left is expanding
        if (rightWidth > BASE) {
          newRight = Math.max(MIN, rightWidth - delta);
          const totalUsed = newLeft + newRight;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            left.value.style.width = `${newLeft}px`;
            right.value.style.width = `${newRight}px`;
          }
        } else {
          // right is already at base, don't shrink it
          const totalUsed = newLeft + rightWidth;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            left.value.style.width = `${newLeft}px`;
          }
        }
      } else {
        // shrinking left
        const totalUsed = newLeft + rightWidth;
        if (totalWidth - totalUsed >= MIDDLE_MIN) {
          left.value.style.width = `${newLeft}px`;
        }
      }
    }

    if (isDraggingRight) {
      const containerRight = container.value.getBoundingClientRect().right;
      const newRight = Math.min(Math.max(containerRight - e.clientX, MIN), MAX);
      const delta = newRight - rightWidth;

      let newLeft = leftWidth;

      if (delta > 0) {
        // right is expanding
        if (leftWidth > BASE) {
          newLeft = Math.max(MIN, leftWidth - delta);
          const totalUsed = newLeft + newRight;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            right.value.style.width = `${newRight}px`;
            left.value.style.width = `${newLeft}px`;
          }
        } else {
          // left is already at base
          const totalUsed = leftWidth + newRight;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            right.value.style.width = `${newRight}px`;
          }
        }
      } else {
        // shrinking right
        const totalUsed = leftWidth + newRight;
        if (totalWidth - totalUsed >= MIDDLE_MIN) {
          right.value.style.width = `${newRight}px`;
        }
      }
    }
  }
};

const stopDrag = () => {
  isDraggingLeft = false;
  isDraggingRight = false;
  document.body.classList.remove('no-select');
  window.removeEventListener('mousemove', onMouseMove);
  window.removeEventListener('mouseup', stopDrag);
};

const startDrag = (side, e) => {
  if (e) {
    e.preventDefault();
  } // Prevent text selection on mousedown
  if (side === 'left') {
    isDraggingLeft = true;
  }
  if (side === 'right') {
    isDraggingRight = true;
  }
  document.body.classList.add('no-select');
  window.addEventListener('mousemove', onMouseMove);
  window.addEventListener('mouseup', stopDrag);
};

</script>

<template>
      <div ref="container" class="flex-1 flex justify-evenly h-full w-full overflow-hidden gap-2 relative z-10">
      <div class="w-[200px] h-full relative z-20">
        <div class="h-full flex absolute top-0 left-0 z-100">
      <div ref="left" class="w-full min-w-[200px]">
      <DeepzoomLeftSideBar />
        </div>
    <div class="w-2 cursor-col-resize bg-gray-300 relative flex justify-center items-center z-10" @mousedown="(e) => startDrag('left', e)" >
    <div class="w-3 h-8 rounded-sm bg-gray-200 absolute z-30 flex justify-evenly items-center">
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
    </div>
  </div>
  </div>
  </div>

<!-- scene section -->
<div class="flex-1 h-full overflow-hidden flex flex-col justify-start items-start bg-white gap-2">
    <div class="h-[90%] w-full flex-none">
  <div   class="h-full overflow-hidden">
    <div  class="bg-neutral-900 h-full w-full text-white">
        <div class="h-full flex justify-center items-center text-white relative z-0"
           >
           <SafeAreaHighlight :show="safeFrame" :safePadding="{ top: '70px', bottom: '70px', left: '80px', right: '80px' }">

            <DeepzoomOverlay :key="sceneKey" :updateScale="updateScale" :updateResize="updateResize" @currentZooomLevel="(val)=>currentZooomLevel = val" @updateLayersPosition="(val) => updateLayersPosition = val"/>
              </SafeAreaHighlight>
      </div>
    </div>
  </div>
   </div>
  <div class="h-[10%] w-full flex flex-col justify-center flex-none">
  <div class="w-full flex-1 min-h-20 bg-white-500 flex justify-end items-center px-3">
    <div class="flex items-center gap-3">
<div class="flex items-center gap-2">
  <div class="flex justify-between items-center gap-2 rounded-b-md cursor-pointer hover:bg-blue-50">

                                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                            <input id="safe_area" v-model="safeFrame" class="sr-only peer" name="safe_area"
                                                type="checkbox" :value="true" />
                                            <label for="safe_area"
                                                class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:rounded-full after:h-[19px] after:w-[19px] after:transition-all peer-checked:bg-blue-600 cursor-pointer">
                                            </label>
                                        </div>
                                        <label for="safe_area" class="text-sm text-gray-900 mb-0">Safe Frame</label>
                  </div>
  <svg class="h-4 w-4 fill-gray-500" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_16947)">
<path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z"/>
</g>
<defs>
<clipPath id="clip0_723_16947">
<rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

</div>
    </div>
  </div>
  </div>
</div>
<!-- Right side -->
 <div class="w-[200px] h-full relative z-20">
        <div class="h-full flex absolute top-0 right-0 z-100">
          <div class="w-2 cursor-col-resize bg-gray-300 relative flex justify-center items-center z-10" @mousedown="(e) => startDrag('right', e)" >
    <div class="w-3 h-7 rounded-sm bg-gray-200 absolute z-30 flex justify-evenly items-center">
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
    </div>
  </div>
      <div ref="right" class="h-full bg-white p-2 w-full min-w-[200px]">
        <div  class="w-full h-full overflow-y-auto">

              <div v-if="route.query.layerId && (!isMasterScene ? projectStore.buildings && projectStore.communities && projectStore.units && projectStore.amenities && categoryList && newOrganizationStore.projects && newOrganizationStore.masterScenes && projectStore.landmarks:true)"  :open="route.query.layerId">

                  <UpdateLayers
                                  :landmarks=" isMasterScene ? false : projectStore.landmarks"
                                  :zoomLevel="currentZooomLevel"
                                  :svgData=" isMasterScene ? newOrganizationStore.masterScenes[sceneId].svgData : projectStore.scenes[sceneId].svgData"
                                  :scenes=" isMasterScene ? newOrganizationStore.masterScenes : projectStore.scenes"
                                  :projects="newOrganizationStore.projects"
                                  :projectId=" isMasterScene ? false : projectId"
                                  :defaultPostion="updateLayersPosition"
                                  :categoryList=" isMasterScene ? false : categoryList"
                                  @updateResize="(val) => updateResize = val"
                                  @updateScale="(val) => updateScale = val"
                                  />

              </div>
       </div>
 </div>
      </div>
      </div>
      </div>
</template>

<style scoped>
.left {
  width: 200px;
}

.right {
  width: 200px;
}

.no-select {
  user-select: none !important;
}
</style>
