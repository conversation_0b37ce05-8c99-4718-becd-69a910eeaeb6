<script setup>
import { ref } from 'vue';
import { getSortArrayFromObjects } from '../../helpers/helpers';
import SampleData from "./nestedReOrderData.json";
import NestedReOrder from '../../components/common/NestedReOrder.vue';

const dumpData = ref(null);
// Only have this Reference for Not Draggable Item which is not sorted/cannot be moved/ not draggable (Scenario)
const sortReferenceIdNotDraggableItem = ref('677d255acd3658d364356106');

/* ---------------- Methods --------------------- */

/* Setup Data (Mandatory) */

// setupData (Modify based on your needs inside logic)
const setupData = (data) => {
  console.log("setupData", data);
  if (data){
    // Structuring your data's in array formats

    // Conversion of Objets to Array values then sort it respectively to your needs.
    const convertedData = [...getSortArrayFromObjects(data, 'subgroups', true)];

    // This Scenario for only not draggable item specific
    if (convertedData){
      const foundIndex = convertedData.findIndex((item) => item._id === sortReferenceIdNotDraggableItem.value);
      convertedData[foundIndex].draggable = false;
    }

    console.log(convertedData);

    // Update the your component reference
    dumpData.value = convertedData;
  }
};

// Initial
setupData({...SampleData['6769079c75a05593b8727ad3'].groups});

/* Emits Handler */

// Child Sort
const handleChildSortEmit = (val) => {
  console.log("-----------------------------");
  console.log('Parent~ChildSort', val);
  console.log("-----------------------------");

  /* This Scenario is only for specific item not draggable and nont moved strictions */
  // If, not draggable items was got moved, then check and reset its position by order
  const check = val.sortedItems.find((item) => item._id === sortReferenceIdNotDraggableItem.value);
  console.log(check);
  // Check, you run the check point for all child sort (i.e is for roots items only or all items)
  if (check){
    // Reset by 'sort' again with it's original order
    const resetSortDataItems = [...dumpData.value].sort((item1, item2) => item1.order - item2.order);
    // set timeout
    setTimeout(() => {
      console.log("Set TimeOut");
      dumpData.value = resetSortDataItems;
    }, 500);
  } else {
    // Else
    // setup data call With Api's
  }
};

// Child Reparenting Sort
const handleChildReparentingSortEmit = (val) => {
  console.log("-----------------------------");
  console.log('Parent~ChildReparentingSort', val);
  console.log("-----------------------------");
};

</script>

<template>
  <div class="w-full overflow-y-auto p-2" v-if="dumpData">
    <!-- Normal Nested (Scenario),  Nested With Specifc Item not draggable (Scenario). etc... -->
    <div class="!w-[60%] bg-teal-300 p-2 rounded" >
               <!-- v-model = (equals too) :modelValue="parentList" @update:modelValue="parentList = $event"  -->
                <NestedReOrder v-model="dumpData" groupName="tours" :allowChildReparenting="true" :allowChildSort="true" uniqueKey="_id" nestedChildKey="subgroups" ghostClass="sampe_ghost" animationMilliSec="450" sortReferenceKey="_id" @handleChildSort="(val) => handleChildSortEmit(val) " @handleChildReparentingSort="(val) => handleChildReparentingSortEmit(val)">
                    <template #default="{item}">
                        <div class="w-full text-left p-2 bg-teal-600 rounded capitalize mb-2">
                              {{ item.name }} ({{ item._id }}) [ order ~ {{ item.order }} ]
                        </div>
                    </template>
                </NestedReOrder>
    </div>

  </div>
</template>

<style scoped>
.sampe_ghost {
  opacity: 0.1;
}
</style>
