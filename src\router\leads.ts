import type { RouteRecordRaw } from "vue-router";
export default [
  {
    path: '/leads',
    name: 'leads',
    component: () => import('../views/leads/Index.vue'),
    children: [
      {
        path: 'add',
        name: 'leads_addLead',
        meta: {
          is_history: true,
        },
        components: {
          modal: () =>
            import('../components/leads/AddLead.vue'),
        },
      },
      {
        path: ':leadId/edit',
        name: 'leads_updateLead',
        meta: {
          is_history: true,
        },
        components: {
          modal: () =>
            import('../components/leads/EditLead.vue'),
        },
      },
    ],
  },
] as Array<RouteRecordRaw>;
