import * as yup from 'yup';
import { fileValidation, imageSizeValidation, isValidFileType, isValidVideoFileType, onlyNumber, RegXEmailPattern, urlRegEx, validateAndProcessFile} from '../../helpers/validationSchemaHelpers';

// Embed v1 Settings and Sales Tool Settings
export const EmbedToolSettingsSchema = yup.object({
  is_enabled: yup.boolean().required(),
});

// Sales Tool Settings
export const SalesToolSettingsSchema = yup.object({
  is_enabled: yup.boolean().required(),
  default_experience: yup.string().nullable(),
  // Timezone: yup.string().required(),
  tags: yup.array().min(0),
});

// Theme Tool
export const ThemeToolSettingsSchema = yup.object({
  theme: yup
    .string()
    .oneOf(['dark', 'light', 'custom'], 'Theme must be one of the following: "dark", "light", or "custom"')
    .nullable(),
  primary: yup.string().when('theme', {
    is: (val:string) => val === 'custom',
    then: () => yup.string().required('Primary color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  secondary: yup.string().when('theme', {
    is: (val:string) => val === 'custom',
    then: () => yup.string().required('Secondary color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  primary_text: yup.string().when('theme', {
    is: (val:string) => val === 'custom',
    then: () => yup.string().required('primaryText color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  secondary_text: yup.string().when('theme', {
    is: (val:string) => val === 'custom',
    then: () => yup.string().required('secondaryText color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  font_type: yup.string().required(),
  font_url: yup .mixed().test('is-valid-type', 'Not a valid font format',
    function (value) {
      const { font_type } = this.parent;
      if (font_type === 'custom') {
        return validateAndProcessFile(value as File | Blob);
      }
      return true;
    },
  ),
});

// Organization Setttings
export const organizationSettingsSchema = yup.object({
  name: yup.string().required(),
  founding_date: yup.string().required(),
  contact_email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required(),
  phone_number: yup.string().matches(onlyNumber, 'Invalid Phone Number').max(10).required(),
  address: yup.string().required(),
  max_users: yup.string().matches(onlyNumber, 'Only Numbers').required(),
  // thumbnail: yup.mixed().required().test('is-valid-type', 'Not a valid image format', (value) => isValidFileType(value as File|Blob)),
  // measurement_id: yup.string(),
});

export const currencySchema = yup.object({
  baseCurrency: yup.string().required('Base currency is required'),
  exchangeRatios: yup.array().of(
    yup.object({
      currency: yup.string().required('Currency is required'),
      rate: yup
        .number()
        .typeError('Rate must be a number')
        .positive('Rate must be positive')
        .required('Rate is required'),
    }),
  ),
});

/* Project Settings */

// General Settings
export const projectGeneralSettingsSchema = yup.object({
  slots: yup.array().min(1, 'Atleast one'),
  timezone: yup.string().required(),
  is_enabled: yup.boolean().required(),
  hideStatus: yup.boolean(),
  branding_logo: yup.mixed().nullable().test('branding_logo', 'Not a valid image format', (value) => isValidFileType(value as File|Blob)),
  branding_logo_dark: yup.mixed().nullable().test('ibranding_logo_dark', 'Not a valid image format', (value) => isValidFileType(value as File|Blob)),
  lat: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  long: yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
});

// Pixel Streaming Settings
export const projectPixelStreamingSettingsSchema = yup.object({
  is_enabled: yup.boolean().required(),
  pixel_streaming_endpoint: yup.string().matches(urlRegEx, 'Invalid URL').required(),
  max_concurrent_sessions: yup.string().matches(onlyNumber, 'Only Numbers').required(),
  session_duration: yup.string().matches(onlyNumber, 'Only Numbers').required(),
  auto_scale: yup.boolean(),
  resource_group: yup.string().when('auto_scale', {
    is: (val: boolean) => val === true,
    then: () => yup.string().required('Resource Group is required'),
    otherwise: () => yup.string().nullable(),
  }),
  vm_scaleset_name: yup.string().when('auto_scale', {
    is: (val: boolean) => val === true,
    then: () => yup.string().required('VM scaleSet is required'),
    otherwise: () => yup.string().nullable(),
  }),
  min_instances: yup.number().when('auto_scale', {
    is: (val: boolean) => val === true,
    then: () => yup.number().positive('Must be a positive number').integer('Must be an integer').required('Minimum Instances is required'),
    otherwise: () => yup.number().nullable().transform((value, original) => (original === '' ? null : value)),
  }),
  application_id: yup.object().nullable(),
});

// Ale Settings
export const AleSettingsSchema = yup.object({
  is_enabled: yup.boolean().required(),
  initial_scene_type: yup.string().required(),
  initial_scene_id: yup.object().nullable(),
  cta_type: yup.string().nullable(),
  cta_name: yup.string().nullable(),
  is_cta_enabled: yup.boolean().nullable(),
  video: yup.mixed().nullable().test('videoFile-type', 'Not a valid Video type', (value) => isValidVideoFileType(value as File|Blob)),
  thumbnail: yup.mixed().nullable().test('low_resolution-type', 'Not a valid image type', (value) => isValidFileType(value as File | Blob)),
  currency_support: yup.boolean(),
});

// project setting
export const hologramSettingsSchema = yup.object({
  type: yup.string().nullable(),
  file: yup.mixed().required().when('type', ([type], schema) => {
    switch (type) {
      case 'video':
        return schema.test('file', 'Not a valid video type', (value) => fileValidation(value as File, 'video'));
      case 'hologram':
        return schema.test('file', 'Not a valid 3D file type', (value) => fileValidation(value as File, 'threeD'));
      default:
        return schema;
    }
  }),
  // name: yup.string().required(),
  project_location: yup.string().required(),
  amount: yup.string().required(),
  project_type: yup.string().required(),
  bedrooms: yup.mixed().required(),
  thumbnail: yup.mixed().test('file', 'Invalid thumbnail', function (value) {
    if (typeof value === 'string') {
      return true;
    }
    return fileValidation(value as File, 'image') && imageSizeValidation(value  as File);

  }).required(),
  project_logo: yup.mixed().test('file', 'Invalid logo', function (value) {
    if (typeof value === 'string') {
      return true;
    }
    return fileValidation(value as File, 'image');

  }).required(),
  tags: yup.array().min(1, 'Atleast one tag is required'),
});

export const CardDetailsSchema = yup.object({
  customize_type: yup.string().required(),
  type: yup.boolean(),
  measurement: yup.boolean(),
  bedrooms: yup.boolean(),
  bathrooms: yup.boolean(),
  status: yup.boolean(),
  price: yup.boolean(),
  style: yup.boolean(),
  floor_id: yup.boolean(),
  building_id: yup.boolean(),
  maid: yup.boolean(),
  view: yup.boolean(),
  units: yup.boolean(),
  favIcon: yup.boolean(),
});

export const AboutDetailsSchema = yup.object({
  description: yup.string(),
  dark_mode: yup.boolean(),
  light_mode: yup.boolean(),
  custom_mode: yup.boolean(),
  modes: yup.mixed().test(
    'at-least-one-mode',
    'At least one mode (Dark, Light, or Custom) must be enabled',
    function () {
      const { dark_mode, light_mode, custom_mode } = this.parent;
      return dark_mode || light_mode || custom_mode;
    },
  ),
  lightBrandingLogo: yup.mixed().nullable()
    .when(['custom_mode', 'light_mode'], {
      is: (custom_mode: boolean | undefined, light_mode: boolean | undefined) => !!custom_mode || !!light_mode,
      then: () => yup.mixed().nullable().test('lightBrandingLogo', 'Invalid image format', (value) => {
        if (!value) {
          return true;
        }
        return isValidFileType(value as File | Blob);
      }),
      otherwise: () => yup.mixed().nullable(),
    }),

  darkBrandingLogo: yup.mixed().nullable()
    .when('dark_mode', {
      is: true,
      then: () => yup.mixed().nullable().test('darkBrandingLogo', 'Invalid image format', (value) => {
        if (!value) {
          return true;
        }
        return isValidFileType(value as File | Blob);
      }),
      otherwise: () => yup.mixed().nullable(),
    }),
  updatedfile: yup
    .mixed()
    .nullable()
    .test('updatedfile', 'Not a valid file type', (value) => {
      if (!value) {
        return true;
      }

      if ((value as File | Blob).type?.startsWith('image/')) {
        return isValidFileType(value as File | Blob);
      } else if ((value as File | Blob).type?.startsWith('video/')) {
        return isValidVideoFileType(value as File | Blob);
      }
      return false;
    }),
  primary: yup.string().when('custom_mode', {
    is: true,
    then: () => yup.string().required('Primary color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  secondary: yup.string().when('custom_mode', {
    is: true,
    then: () => yup.string().required('Secondary color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  primary_text: yup.string().when('custom_mode', {
    is: true,
    then: () => yup.string().required('primaryText color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  secondary_text: yup.string().when('custom_mode', {
    is: true,
    then: () => yup.string().required('secondaryText color is required'),
    otherwise: () => yup.string().nullable(),
  }),
  font_type: yup.string(),
  font_url: yup .mixed().test('is-valid-type', 'Not a valid font format',
    function (value) {
      const { font_type } = this.parent;
      if (font_type === 'custom') {
        return validateAndProcessFile(value as File | Blob);
      }
      return true;
    },
  ),
  // font_url: yup.mixed().nullable().test('is-valid-type', 'Not a valid image format', (value) => validateAndProcessFile(value as File | Blob)),
});
