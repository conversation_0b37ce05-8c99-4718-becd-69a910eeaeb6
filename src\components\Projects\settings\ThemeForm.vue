<script setup>
import { ref } from 'vue';
import { UpdateProjectSettings } from '../../../api/projects/settings/index.ts';
import { ThemeToolSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import Multiselect from 'vue-multiselect';
import { fetchGoogleFonts } from '../../../helpers/themehelper';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const selected_theme = ref();
const themes = ['dark', 'light', 'custom'];
const selected_fontType = ref();
const selected_fontFamily = ref();
const fontFamilyList = ref([]);
const selected_fontFormat = ref();

fetchGoogleFonts().then((res) => {
  fontFamilyList.value = res;
  fontFamilyList.value.unshift('default', 'custom');
}).catch((error) => {
  console.log('error', error);
});

const getFontFamily = (link) => {

  const parser = new DOMParser();
  const doc = parser.parseFromString(link, "text/html");
  const linkElement = doc.querySelector('link[href*="fonts.googleapis.com/"]');

  if (linkElement) {
    const fontLink = linkElement.getAttribute("href");
    const familyMatch = fontLink.match(/family=([^&]*)/);
    return familyMatch
      ? familyMatch[1].split(":")[0].replace(/\+/g, " ")
      : null;
  }

  return null;
};

const extractFileName = (url) => {
  const decodedUrl = decodeURIComponent(url);
  const match = decodedUrl.match(/\/([^/?#]+)\?alt=/);

  if (match) {
    let fileName = match[1];
    fileName = fileName.replace(/_\d+(?=\.\w+$)/, '');
    return fileName;
  }
  return null;
};
/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const setupDataCallBack = (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      theme: (data.projectSettings?.theme?.theme ? data.projectSettings?.theme?.theme : null),
      primary: (data.projectSettings?.theme?.primary ? data.projectSettings?.theme?.primary : null),
      secondary: (data.projectSettings?.theme?.secondary ? data.projectSettings?.theme?.secondary : null),
      primary_text: (data.projectSettings?.theme?.primary_text ? data.projectSettings?.theme?.primary_text : null),
      secondary_text: (data.projectSettings?.theme?.secondary_text ? data.projectSettings?.theme?.secondary_text : null),
      font_type: (data.projectSettings?.theme?.font_type  ? data.projectSettings?.theme?.font_type  : null),
      font_url: (data.projectSettings?.theme?.font_url  ? data.projectSettings?.theme?.font_url  : null),
    };

    // Form Initial Values
    initialData.value = {
      primary: (data.projectSettings?.theme?.primary ? data.projectSettings?.theme?.primary : null),
      secondary: (data.projectSettings?.theme?.secondary ? data.projectSettings?.theme?.secondary : null),
      primary_text: (data.projectSettings?.theme?.primary_text ? data.projectSettings?.theme?.primary_text : null),
      secondary_text: (data.projectSettings?.theme?.secondary_text ? data.projectSettings?.theme?.secondary_text : null),
    };

    if (data.projectSettings?.theme?.theme) {
      selected_theme.value = data.projectSettings.theme.theme;
    }

    if (data.projectSettings?.theme?.font_type){
      selected_fontType.value = data.projectSettings.theme.font_type;
    }

    if (data.projectSettings?.theme?.font_url){
      selected_fontFamily.value = getFontFamily(data.projectSettings.theme.font_url);
    }

  }
};

const handleSubmit = async (val) => {
  return new Promise((resolve) => {
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...val }; // form values

    newCompareObj.primary = newCompareObj.primary ? newCompareObj.primary : null;
    newCompareObj.primary_text = newCompareObj.primary_text ? newCompareObj.primary_text : null;
    newCompareObj.secondary = newCompareObj.secondary ? newCompareObj.secondary : null;
    newCompareObj.secondary_text = newCompareObj.secondary_text ? newCompareObj.secondary_text : null;

    const parms = frameParms(prevData, newCompareObj);

    if (Object.keys(parms).length > 0 || (val.font_type === 'custom' && val.font_url !== undefined && val.font_url !== null)) {
      const payload = {
        project_id: project_Id.value,
        query: {
          [projectSettingsFormTypes.THEME]: {},
        },
      };

      // Add all fields to the theme object
      Object.keys(parms).forEach((key) => {
        payload.query.theme[key] = parms[key];
      });

      // Handle custom font type
      if (val.font_type === 'custom') {
        payload.query[projectSettingsFormTypes.THEME].font_type = val.font_type;
        if (val.font_url) {
          payload.query[projectSettingsFormTypes.THEME].font_url = val.font_url;
        }
      } else {
        payload.query[projectSettingsFormTypes.THEME].font_url = null;
      }

      UpdateProjectSettings(payload).then((result) => {
        if (result){
          projectStore.settings.projectSettings[projectSettingsFormTypes.THEME] = result.projectSettings[projectSettingsFormTypes.THEME]; // update to store
          setupDataCallBack(result);  // update the data value
          resolve(result);
        }
      });
    } else {
      resolve();
    }
  });
};

// Initialize
if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
        <div class="flex flex-col justify-start items-start my-3">

            <!-- Headers -->
            <div class="flex justify-between items-center w-full mb-4">

            <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> Theme: </p>

            <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
                @handle-click="() => isEdit = !isEdit">
                <template v-slot:svg>
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                    d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                    fill="white" />
                </svg>
                </template>
            </Button>

            <div v-if="isEdit" class="flex justify-start items-center gap-3">
                <Button title="Reset" type="button" theme="secondary" @handle-click="() => isEdit = !isEdit">
                </Button>
                <label for="editThemeSettings"
                :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer']">
                Save </label>
            </div>

            </div>

            <!-- View -->
            <div v-if="!isEdit" class="grid grid-cols-3 gap-8 w-full mt-3 mb-5">

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Theme: </label>
                <p class="font-medium text-sm text-txt-default"> {{
               previousData?.theme ?
                    previousData.theme : '-' }} </p>
            </div>
            <div class="flex flex-col justify-start items-start gap-2 w-full"
                v-if="previousData?.theme === 'custom'">
                <label class="font-semibold text-sm text-txt-50"> Primary: </label>
                <p class="font-medium text-sm text-txt-default"> {{
               previousData?.primary
                    ?
                    previousData.primary : '-' }} </p>
                <label class="h-4 w-12 rounded-full"
                :style="{ backgroundColor:previousData?.primary || '#000' }"></label>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full"
                v-if="previousData?.theme === 'custom'">
                <label class="font-semibold text-sm text-txt-50"> PrimaryText: </label>
                <p class="font-medium text-sm text-txt-default"> {{
               previousData?.primary_text ?
                    previousData.primary_text : '-' }} </p>
                <label class="h-4 w-12 rounded-full"
                :style="{ backgroundColor:previousData?.primary_text || '#000' }"></label>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full"
                v-if="previousData?.theme === 'custom'">
                <label class="font-semibold text-sm text-txt-50"> Secondary: </label>
                <p class="font-medium text-sm text-txt-default"> {{
               previousData?.secondary ?
                    previousData.secondary : '-' }} </p>
                <label class="h-4 w-12 rounded-full"
                :style="{ backgroundColor:previousData?.secondary || '#000' }"></label>

            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full"
                v-if="previousData?.theme === 'custom'">
                <label class="font-semibold text-sm text-txt-50"> SecondaryText: </label>
                <p class="font-medium text-sm text-txt-default"> {{
               previousData?.secondary_text ?
                    previousData.secondary_text : '-' }} </p>
                <label class="h-4 w-12 rounded-full"
                :style="{ backgroundColor:previousData?.secondary_text || '#000' }"></label>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Font Type </label>
                <p class="font-medium text-sm text-txt-default"> {{
                previousData?.font_type ? previousData.font_type : '-' }} </p>
            </div>

            <div  v-if="previousData.font_type === 'custom'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Font Format </label>
                <div class="flex gap-1">
                    <svg  width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 3L13.7071 2.29289C13.5196 2.10536 13.2652 2 13 2V3ZM19 9H20C20 8.73478 19.8946 8.48043 19.7071 8.29289L19 9ZM13.109 8.45399L14 8V8L13.109 8.45399ZM13.546 8.89101L14 8L13.546 8.89101ZM10 13C10 12.4477 9.55228 12 9 12C8.44772 12 8 12.4477 8 13H10ZM8 16C8 16.5523 8.44772 17 9 17C9.55228 17 10 16.5523 10 16H8ZM8.5 9C7.94772 9 7.5 9.44772 7.5 10C7.5 10.5523 7.94772 11 8.5 11V9ZM9.5 11C10.0523 11 10.5 10.5523 10.5 10C10.5 9.44772 10.0523 9 9.5 9V11ZM8.5 6C7.94772 6 7.5 6.44772 7.5 7C7.5 7.55228 7.94772 8 8.5 8V6ZM9.5 8C10.0523 8 10.5 7.55228 10.5 7C10.5 6.44772 10.0523 6 9.5 6V8ZM17.908 20.782L17.454 19.891L17.454 19.891L17.908 20.782ZM18.782 19.908L19.673 20.362L18.782 19.908ZM5.21799 19.908L4.32698 20.362H4.32698L5.21799 19.908ZM6.09202 20.782L6.54601 19.891L6.54601 19.891L6.09202 20.782ZM6.09202 3.21799L5.63803 2.32698L5.63803 2.32698L6.09202 3.21799ZM5.21799 4.09202L4.32698 3.63803L4.32698 3.63803L5.21799 4.09202ZM12 3V7.4H14V3H12ZM14.6 10H19V8H14.6V10ZM12 7.4C12 7.66353 11.9992 7.92131 12.0169 8.13823C12.0356 8.36682 12.0797 8.63656 12.218 8.90798L14 8C14.0293 8.05751 14.0189 8.08028 14.0103 7.97537C14.0008 7.85878 14 7.69653 14 7.4H12ZM14.6 8C14.3035 8 14.1412 7.99922 14.0246 7.9897C13.9197 7.98113 13.9425 7.9707 14 8L13.092 9.78201C13.3634 9.92031 13.6332 9.96438 13.8618 9.98305C14.0787 10.0008 14.3365 10 14.6 10V8ZM12.218 8.90798C12.4097 9.2843 12.7157 9.59027 13.092 9.78201L14 8V8L12.218 8.90798ZM8 13V16H10V13H8ZM8.5 11H9.5V9H8.5V11ZM8.5 8H9.5V6H8.5V8ZM13 2H8.2V4H13V2ZM4 6.2V17.8H6V6.2H4ZM8.2 22H15.8V20H8.2V22ZM20 17.8V9H18V17.8H20ZM19.7071 8.29289L13.7071 2.29289L12.2929 3.70711L18.2929 9.70711L19.7071 8.29289ZM15.8 22C16.3436 22 16.8114 22.0008 17.195 21.9694C17.5904 21.9371 17.9836 21.8658 18.362 21.673L17.454 19.891C17.4045 19.9162 17.3038 19.9539 17.0322 19.9761C16.7488 19.9992 16.3766 20 15.8 20V22ZM18 17.8C18 18.3766 17.9992 18.7488 17.9761 19.0322C17.9539 19.3038 17.9162 19.4045 17.891 19.454L19.673 20.362C19.8658 19.9836 19.9371 19.5904 19.9694 19.195C20.0008 18.8114 20 18.3436 20 17.8H18ZM18.362 21.673C18.9265 21.3854 19.3854 20.9265 19.673 20.362L17.891 19.454C17.7951 19.6422 17.6422 19.7951 17.454 19.891L18.362 21.673ZM4 17.8C4 18.3436 3.99922 18.8114 4.03057 19.195C4.06287 19.5904 4.13419 19.9836 4.32698 20.362L6.10899 19.454C6.0838 19.4045 6.04612 19.3038 6.02393 19.0322C6.00078 18.7488 6 18.3766 6 17.8H4ZM8.2 20C7.62345 20 7.25117 19.9992 6.96784 19.9761C6.69617 19.9539 6.59545 19.9162 6.54601 19.891L5.63803 21.673C6.01641 21.8658 6.40963 21.9371 6.80497 21.9694C7.18864 22.0008 7.65645 22 8.2 22V20ZM4.32698 20.362C4.6146 20.9265 5.07354 21.3854 5.63803 21.673L6.54601 19.891C6.35785 19.7951 6.20487 19.6422 6.10899 19.454L4.32698 20.362ZM8.2 2C7.65645 2 7.18864 1.99922 6.80497 2.03057C6.40963 2.06287 6.01641 2.13419 5.63803 2.32698L6.54601 4.10899C6.59545 4.0838 6.69617 4.04612 6.96784 4.02393C7.25117 4.00078 7.62345 4 8.2 4V2ZM6 6.2C6 5.62345 6.00078 5.25117 6.02393 4.96784C6.04612 4.69617 6.0838 4.59545 6.10899 4.54601L4.32698 3.63803C4.13419 4.01641 4.06287 4.40963 4.03057 4.80497C3.99922 5.18864 4 5.65645 4 6.2H6ZM5.63803 2.32698C5.07354 2.6146 4.6146 3.07354 4.32698 3.63803L6.10899 4.54601C6.20487 4.35785 6.35785 4.20487 6.54601 4.10899L5.63803 2.32698Z" fill="#000000"/>
                    </svg>
                    <p class="font-medium text-sm text-txt-default">
                        {{ previousData?.font_url ? extractFileName(previousData.font_url) : '-' }}
                    </p>
               </div>
            </div>

            </div>

            <!-- Form -->
            <Form v-else class="grid grid-cols-3 gap-3 mt-3 w-full"
            @submit="(val) => handleSubmit(val).then(() => isEdit = false)"
            :initial-values="initialData" :validation-schema="ThemeToolSettingsSchema">

            <div class="relative">
                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Theme
                <strong>*</strong>
                </label>

                <Field name="theme" :model-value="selected_theme" v-slot="{ field }">
                <Multiselect :allow-empty="false" v-bind="field" v-model="selected_theme" :searchable="false"
                    :close-on-select="true" :show-labels="false" placeholder="Choose" :options="themes" maxHeight="250">
                </Multiselect>
                </Field>

                <ErrorMessage name="theme" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="flex justify-start items-center gap-4 relative" v-if="selected_theme === 'custom'">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Primary
                <strong>*</strong>
                </label>

                <Field name="primary" type="color" id="primary">
                </Field>

                <ErrorMessage name="primary" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="flex justify-start items-center gap-4 relative" v-if="selected_theme === 'custom'">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> PrimaryText
                    <strong>*</strong>
                </label>

                <Field name="primary_text" type="color" id="primary_text">
                </Field>

                <ErrorMessage name="primary_text" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="flex justify-start items-center gap-4 relative" v-if="selected_theme === 'custom'">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Secondary
                <strong>*</strong>
                </label>

                <Field name="secondary" type="color" id="secondary">
                </Field>

                <ErrorMessage name="secondary" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="flex justify-start items-center gap-4 relative" v-if="selected_theme === 'custom'">

                    <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> SecondaryText
                        <strong>*</strong>
                    </label>

                    <Field name="secondary_text" type="color" id="secondary_text">
                    </Field>

                    <ErrorMessage name="secondary_text" as="p" v-slot="{ message }"
                        class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                            fill="#B3261E" />
                        </svg>
                        <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

            </div>

            <div class="flex flex-col justify-start items-start gap-1 relative">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2 text-nowrap"> Font Type
                </label>

                <Field  name="font_type" v-slot="{ field }" :model-value="selected_fontType">

                        <Multiselect
                        class="multi"
                        v-bind="field"
                        v-model="selected_fontType"
                        placeholder="Search or add type"
                        :options="fontFamilyList"
                        :multiple="false"
                        :maxHeight="200" >
                        </Multiselect>
                </Field>

                <ErrorMessage name="font_type" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <div class="w-60 h-[75px] mb-0"  v-if="selected_fontType === 'custom'">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2 text-nowrap"> Font format
                </label>

                <Field name="font_url" :model-value="selected_fontFormat" v-slot="{ field }">
                <div v-bind="field" class="h-full">
                    <input type="file" id="font_url" class="hidden" @input="(e) => {
                    selected_fontFormat = e.target.files[0];
                    }">
                    <label for="font_url"
                    class="text-txt-50 border-[1px] border-dashed border-bg-550 rounded-md h-max !p-3 flex  justify-center items-center gap-3 cursor-pointer">
                        <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                            d="M31.4999 16C31.5062 18.3815 30.7336 20.6997 29.2999 22.6013C29.2209 22.7063 29.1221 22.7948 29.0089 22.8617C28.8958 22.9285 28.7705 22.9724 28.6404 22.9909C28.5103 23.0093 28.3778 23.002 28.2505 22.9692C28.1233 22.9365 28.0037 22.879 27.8986 22.8C27.7936 22.7211 27.7051 22.6222 27.6383 22.5091C27.5714 22.3959 27.5275 22.2707 27.5091 22.1406C27.4906 22.0105 27.4979 21.878 27.5307 21.7507C27.5634 21.6234 27.6209 21.5038 27.6999 21.3988C28.8734 19.8441 29.5056 17.9479 29.4999 16C29.4999 13.6131 28.5517 11.3239 26.8638 9.63607C25.176 7.94825 22.8868 7.00004 20.4999 7.00004C18.1129 7.00004 15.8238 7.94825 14.1359 9.63607C12.4481 11.3239 11.4999 13.6131 11.4999 16C11.4999 16.2653 11.3945 16.5196 11.207 16.7071C11.0195 16.8947 10.7651 17 10.4999 17C10.2347 17 9.98031 16.8947 9.79278 16.7071C9.60524 16.5196 9.49988 16.2653 9.49988 16C9.4994 14.9909 9.63778 13.9865 9.91113 13.015C9.77489 13 9.63738 13 9.49988 13C7.90859 13 6.38246 13.6322 5.25724 14.7574C4.13203 15.8826 3.49988 17.4087 3.49988 19C3.49988 20.5913 4.13203 22.1175 5.25724 23.2427C6.38246 24.3679 7.90859 25 9.49988 25H12.4999C12.7651 25 13.0195 25.1054 13.207 25.2929C13.3945 25.4805 13.4999 25.7348 13.4999 26C13.4999 26.2653 13.3945 26.5196 13.207 26.7071C13.0195 26.8947 12.7651 27 12.4999 27H9.49988C8.4001 27.0003 7.31208 26.7738 6.30377 26.3347C5.29546 25.8955 4.38853 25.2532 3.6396 24.4478C2.89068 23.6425 2.31585 22.6913 1.95103 21.6538C1.58621 20.6163 1.43923 19.5147 1.51926 18.4179C1.59929 17.321 1.90462 16.2524 2.41619 15.2788C2.92775 14.3053 3.63455 13.4477 4.49245 12.7595C5.35035 12.0714 6.34092 11.5675 7.40229 11.2794C8.46366 10.9913 9.57304 10.9251 10.6611 11.085C11.7691 8.86898 13.5928 7.09188 15.8368 6.04158C18.0808 4.99127 20.6136 4.72928 23.025 5.29804C25.4365 5.8668 27.5853 7.233 29.1234 9.17535C30.6616 11.1177 31.4989 13.5224 31.4999 16ZM20.2074 15.2925C20.1145 15.1996 20.0042 15.1258 19.8828 15.0755C19.7614 15.0252 19.6313 14.9992 19.4999 14.9992C19.3685 14.9992 19.2383 15.0252 19.1169 15.0755C18.9955 15.1258 18.8853 15.1996 18.7924 15.2925L14.7924 19.2925C14.6995 19.3854 14.6258 19.4957 14.5755 19.6171C14.5252 19.7385 14.4993 19.8686 14.4993 20C14.4993 20.1314 14.5252 20.2615 14.5755 20.3829C14.6258 20.5043 14.6995 20.6146 14.7924 20.7075C14.98 20.8952 15.2345 21.0006 15.4999 21.0006C15.6313 21.0006 15.7614 20.9747 15.8828 20.9244C16.0042 20.8741 16.1145 20.8004 16.2074 20.7075L18.4999 18.4138V26C18.4999 26.2653 18.6052 26.5196 18.7928 26.7071C18.9803 26.8947 19.2347 27 19.4999 27C19.7651 27 20.0195 26.8947 20.207 26.7071C20.3945 26.5196 20.4999 26.2653 20.4999 26V18.4138L22.7924 20.7075C22.8853 20.8004 22.9956 20.8741 23.117 20.9244C23.2384 20.9747 23.3685 21.0006 23.4999 21.0006C23.6313 21.0006 23.7614 20.9747 23.8828 20.9244C24.0042 20.8741 24.1145 20.8004 24.2074 20.7075C24.3003 20.6146 24.374 20.5043 24.4243 20.3829C24.4746 20.2615 24.5004 20.1314 24.5004 20C24.5004 19.8686 24.4746 19.7385 24.4243 19.6171C24.374 19.4957 24.3003 19.3854 24.2074 19.2925L20.2074 15.2925Z"
                            fill="#5B616E" />
                        </svg>
                        <span v-if="!selected_fontFormat && extractFileName(previousData.font_url) === null ">  Upload File </span>
                        <span class="text-txt-50 font-semibold text-center">
                        {{ selected_fontFormat?.name ? selected_fontFormat.name : previousData?.font_url ? extractFileName(previousData.font_url):'-' }}
                       </span>
                    </label>
                </div>
                </Field>

                <ErrorMessage name="font_url" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <Button id="editThemeSettings" class="hidden" title="Submit" type="submit" > </Button>

            </Form>

        </div>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}
</style>
