<script setup>
import Navbar from '../../components/common/Navbar.vue';
// import SideBar from '../../components/Projects/SideBar.vue';
import ProjectDetail from '../../components/Projects/ProjectDetail.vue';
import { UserStore } from '../../store/index';
import router from '../../router';
import SideNavBar from '@/components/common/SideNavBar.vue';
const userStore = UserStore();
router.push({name: 'project_unitplans', params: {'project_id': router.currentRoute.value.params.id}});
</script>

<template>
    <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar/>
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
            <ProjectDetail/>
        </div>
    </div>
</div>
</template>
