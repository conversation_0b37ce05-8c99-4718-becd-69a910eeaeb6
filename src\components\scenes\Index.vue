<script setup>
import { ref } from 'vue';
import noDataFound from '../../assets/noDataFound.png';
import { useRoute } from 'vue-router';
import { ProjectStore } from '../../store/project';
import { getListofScenes, moveSceneToTrash } from '../../api/projects/scene/index';
import Button from '../common/Button.vue';
import { useRouter } from 'vue-router';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import Modal from '../common/Modal/Modal.vue';
import { uiOperations } from '../../store/uiOperations';
import {sceneCardsMenuItems} from '../../enum.ts';
import { Org_Store } from '@/store/organization';
import { getAllScenes, moveMasterSceneToTrash } from '@/api/masterScene';
import { isMasterScenePath } from '../../helpers/helpers';
import TrashedData from '../Projects/trash/Index.vue';

const router = useRouter();
const uiStore = uiOperations();
const projectStore = ProjectStore();
const organizationStore = Org_Store();
const route = useRoute();
console.log(route);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
console.log(isMasterScene.value);
const projectId = ref(route.params.project_id);
const scenes = ref(null);
const SceneToDelete = ref();
const openDeleteModal = ref(false);
const deleteLoader = ref(false);
const parentContainerRef = ref(null);
const sceneCardsMenuItemsRef = ref({});
const trashedDataRef = ref(null);
const showTrash = ref(false);

/* Methods */

const getScenes = () => {
  console.log("getScenes");

  if (isMasterScene.value){
    getAllScenes().then((res) => {
      scenes.value = res;
      organizationStore.SyncMultipleMasterScenes(res);// Org store
    }).catch((err) => {
      uiStore.handleApiErrorMessage(err.message);
    });
  } else {
    getListofScenes(projectId.value).then((res) => {
      scenes.value = res;
      projectStore.SyncMultipleScenes(res); // Project store
    }).catch((err) => {
      uiStore.handleApiErrorMessage(err.message);
    });
  }
};

// Initial
if (isMasterScene.value ? organizationStore.masterScenes === null :  projectStore.scenes === null ){
  getScenes();
} else {
  // Project scene
  scenes.value = isMasterScene.value ? organizationStore.masterScenes : projectStore.scenes;
}

document.addEventListener('getScenes', () => {
  getScenes();
});

const handleCreateScene = () => {
  if (isMasterScene.value) {
    router.push(`/masterscenes/create`);
  } else {
    router.push(`/projects/${projectId.value}/scenes/create`);
  }
};

const handleCardClick = (scene) => {
  if (isMasterScene.value) {
    console.log("MasterScene card was clicked");

    router.push(`/masterscenes/${scene.sceneData._id}${scene.sceneData.type === 'gsplat' || scene.sceneData.type === 'deep_zoom' ? `/${scene.sceneData.type.replaceAll('_', '')}` : ''}`);
  } else {
    console.log("Project scene card was clicked");
    router.push(`/projects/${projectId.value}/scenes/${scene.sceneData._id}${scene.sceneData.type === 'gsplat' || scene.sceneData.type === 'deep_zoom' ? `/${scene.sceneData.type.replaceAll('_', '')}` : ''}`);
  }
};

const handleMoveToTrash = () => {
  deleteLoader.value = true;
  const obj = {
    ...( isMasterScene.value ? { scene_Ids: [SceneToDelete.value]} : {scene_id: [SceneToDelete.value]}),
    timeStamp: Date.now(),
  };

  if (isMasterScene.value){
    // Master scene
    moveMasterSceneToTrash(obj).then(() => {
      deleteLoader.value = false;
      getScenes();
      trashedDataRef.value?.fetchTrashData();
      openDeleteModal.value = false;
    }).catch((err) => {
      deleteLoader.value = false;
      console.log(err);
    });
  } else {
    // Project scene
    moveSceneToTrash(obj, projectId.value).then(() => {
      deleteLoader.value = false;
      getScenes();
      trashedDataRef.value?.fetchTrashData();
      openDeleteModal.value = false;
    }).catch((err) => {
      deleteLoader.value = false;
      console.log(err);
    });
  }
};

// Three dot menu popup's
const handleMenuPopup = (e, sceneId, sceneType) => {
  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];

  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      element.classList.add('right-[5px]');   // both x and y are exceeded the parent container or only y has exceded the parent container
      if (sceneType !== 'image' || isMasterScene.value) {
        element.classList.add(`top-[-85px]`);
      } else {
        element.classList.add(`top-[-122px]`);
      }
    } else if (xPosition > calcParentWidth && yPosition < calcParentHeight){
      element.classList.add('right-[20px]');  // Only x has exceed the parent container
    }
  };

  if (sceneCardsMenuItemsRef.value[sceneId]){
    // active item
    sceneCardsMenuItemsRef.value[sceneId].classList.remove(...listOfRemovalClass); // remove class
    dynamicAbsolutePositions(sceneCardsMenuItemsRef.value[sceneId]); // apply absolute positions depending on the current position (i.e is based on parent container)
    sceneCardsMenuItemsRef.value[sceneId].classList.add('flex');   // add class
  }
};

const handleCloseMenuPopup = (sceneId) => {
  if (sceneCardsMenuItemsRef.value ? sceneCardsMenuItemsRef.value[sceneId].classList.contains('flex') : false){
    const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
    sceneCardsMenuItemsRef.value[sceneId].classList.remove(...listOfRemovalClass); // remove class
    sceneCardsMenuItemsRef.value[sceneId].classList.add('hidden');   // add class
  }
};

</script>

<template>
  <div class="">
    <main
      class="h-[92.8vh] w-full bg-bg-1000 dark:bg-bg-default relative">
      <div
        class="flex items-center justify-between py-3 px-8">
        <div class="min-w-0 flex-1">
          <h2
            class="font-bold -tracking-2 leading-7 text-txt-100 dark:text-txt-1000 sm:truncate sm:text-3xl sm:tracking-tight mb-3">
          {{ isMasterScene ? 'Master' : '' }}  Scenes
          </h2>
          <p class="text-txt-600 dark:text-txt-650">
            Lorem ipsum dolor sit amet consectetur
            adipisicing
            elit.
          </p>
        </div>
        <div class="flex justify-start items-center gap-2">
          <Button title="Create New Scene" theme="primary"
            class="h-10"
            @click="() => handleCreateScene()">
            <template v-slot:svg>
              <svg xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                class="h-6 w-6 fill-txt-1000 dark:fill-txt-50">
                <g data-name="Layer 2">
                  <g data-name="plus">
                    <rect width="24" height="24"
                      transform="rotate(180 12 12)"
                      opacity="0" />
                    <path
                      d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                  </g>
                </g>
              </svg>
            </template>
          </Button>
          <Button title="Trashed Scenes" @click="showTrash = !showTrash" theme="primary"></Button>
        </div>
      </div>
      <!-- <BreadCrumb /> -->
      <!-- list of Scene -->
      <div class="mt-3 text-white px-8" v-if="!showTrash">
        <div ref="parentContainerRef" :class="[
           scenes && Object.values(scenes).length !== 0 &&
          'grid sm:grid-cols-4 gap-x-3 gap-y-3 max-h-[68vh] h-inherit overflow-y-auto grid-container pb-10 px-1',
          'w-full text-white',
        ]">
          <div
            v-if="!scenes || Object.values(scenes).length === 0"
            class="flex w-full m-auto justify-center p-4">
            <div class="w-full">
              <img class="w-72 m-auto" :src="noDataFound"
                alt="" />
              <p
                class="text-xs text-center text-black mt-2">
                Oops! No Data Found, Contact admin
                to add scenes
              </p>
            </div>
          </div>

          <div v-else v-for="scene, sceneId in scenes"
            :key="sceneId"
            :onmouseleave="() => handleCloseMenuPopup(sceneId)"
            class="max-sm:rounded-sm rounded-md cursor-pointer max-w-[420px] border-[1px] border-bg-900">
            <div
              @click="() => handleCardClick(scene)">
              <img
                :src="scene.sceneData.background ? scene.sceneData.background.low_resolution : scene.sceneData.icon"
                alt="img" class="rounded-t-md w-full"
                style="height: 200px; object-fit: cover" />
              <div class="flex justify-between items-center py-1">
                <p class="text-txt-default dark:text-txt-1000 p-2">
                  {{ scene.sceneData.name }}
                </p>
                <div class="relative">
                  <button class="p-0.5 rounded-sm mr-2 fill-rose-400 hover:fill-rose-500" @click.stop="(e) => handleMenuPopup(e,sceneId,scene.sceneData.type)">
                         <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 5 20" fill="none">
                           <path d="M4.29259 0.585785C5.18159 1.36683 5.18159 2.63317 4.29259 3.41421C3.40359 4.19526 1.96224 4.19526 1.07324 3.41421C0.184245 2.63317 0.184245 1.36683 1.07324 0.585785C1.9622 -0.195262 3.40356 -0.195262 4.29259 0.585785Z" fill="#0F0F0F"/>
                           <path d="M4.29259 8.58579C5.18159 9.36683 5.18159 10.6332 4.29259 11.4142C3.40359 12.1953 1.96224 12.1953 1.07324 11.4142C0.184245 10.6332 0.184245 9.36683 1.07324 8.58579C1.9622 7.80474 3.40356 7.80474 4.29259 8.58579Z" fill="#0F0F0F"/>
                           <path d="M4.29259 16.5858C5.18159 17.3668 5.18159 18.6332 4.29259 19.4142C3.40359 20.1953 1.96224 20.1953 1.07324 19.4142C0.184245 18.6332 0.184245 17.3668 1.07324 16.5858C1.9622 15.8047 3.40356 15.8047 4.29259 16.5858Z" fill="#0F0F0F"/>
                          </svg>
                  </button>
                  <ul :ref="el => { if(el) sceneCardsMenuItemsRef[sceneId] = el}" @click.stop="" @onmouseover.stop="() => {sceneCardsMenuItemsRef[sceneId].classList.remove('hidden'); sceneCardsMenuItemsRef[sceneId].classList.add('flex');}" @onmouseleave.stop="() => handleCloseMenuPopup(sceneId)" :key="'sceneCardMenuItem'+sceneId" class="hidden absolute rounded-xl border-[#d8dbdf] bg-white shadow border p-4 flex-col justify-start items-start gap-4 list-none cursor-default transition-all">
                          <li v-for="menuItem, menuItemKey in sceneCardsMenuItems" :key="menuItemKey" @click.stop="() => {if(menuItemKey === 'Delete') {openDeleteModal = true; SceneToDelete = scene.sceneData._id;} else { if(isMasterScene) router.push(`/masterscenes/${sceneId}/convertdeepzoom`); else router.push(`/projects/${projectId}/scenes/${sceneId}/convertdeepzoom`); }} " :class="['text-nowrap text-[#0f0f0f] text-base font-medium justify-start items-center gap-2 cursor-pointer' , isMasterScene && menuItem.label !== 'Delete' ? 'hidden' : menuItem.label !== 'Delete' && scene.sceneData.type !== 'image' ? 'hidden' : 'flex' ]">
                              <span v-html="menuItem.svg" class="w-5 h-5"></span>
                              {{ menuItem.label }}
                          </li>
                  </ul>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
      <TrashedData
        ref="trashedDataRef"
        v-if="showTrash"
        :type="'scenes'"
        :entity="'projectScene'"
        :action="'restoreScenes'"
        @refreshDataList="getScenes"
      />
    </main>
    <Modal :open="openDeleteModal">
          <DeleteModalContent
           :trash="true"
           :loader="deleteLoader"
            @closeModal="(e) => openDeleteModal = false"
            @handleDelete="handleMoveToTrash"
            :dataName="'Project Scene'" />
        </Modal>
  </div>
</template>
