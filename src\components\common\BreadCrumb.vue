<script setup>
/* Import { onMounted, ref } from 'vue';
    import { useRoute, useRouter } from 'vue-router'; */

/* States and Refs */
/*  Const router = useRouter();
    const activePath = ref(null);
    const previousPath = ref([]); */

/* Methods */
/*  Const makeBreadcrumbPath = (currentPath)  => {
        console.log(currentPath);
        let reFromatPath = currentPath.path.replace('/','');
        console.log(reFromatPath);
  } */

/* Hooks */
/*   OnMounted(() => {
                makeBreadcrumbPath(router);
  }) */

</script>

<template>
    <div class="bg-transparent border-t-[1px] border-b-[1px] border-bg-900 w-full flex justify-start items-center gap-2 py-2 px-8">

                        <!-- Previous -->
                        <span class="flex justify-start items-center gap-2">
                            <p class="text-base font-normal text-txt-400 dark:"> Dump </p>
                            <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.09588 5.18648L15.5118 11.5498C15.5715 11.6089 15.6188 11.6791 15.6511 11.7563C15.6834 11.8336 15.7 11.9164 15.7 12C15.7 12.0836 15.6834 12.1664 15.6511 12.2437C15.6188 12.3209 15.5715 12.3911 15.5118 12.4502L9.09588 18.8135C8.97549 18.9329 8.81221 19 8.64195 19C8.4717 19 8.30841 18.9329 8.18802 18.8135C8.06763 18.6941 8 18.5322 8 18.3633C8 18.1945 8.06763 18.0325 8.18802 17.9131L14.1508 12L8.18802 6.08689C8.12841 6.02777 8.08113 5.95758 8.04887 5.88033C8.0166 5.80309 8 5.7203 8 5.63669C8 5.55307 8.0166 5.47028 8.04887 5.39304C8.08113 5.31579 8.12841 5.2456 8.18802 5.18648C8.24763 5.12736 8.3184 5.08046 8.39629 5.04846C8.47417 5.01647 8.55765 5 8.64195 5C8.72625 5 8.80973 5.01647 8.88762 5.04846C8.9655 5.08046 9.03627 5.12736 9.09588 5.18648Z" fill="#8E95A2"/>
                            </svg>
                        </span>

                        <!-- Current -->
                        <p class="text-txt-150 dark:text-txt-1000 text-base font-normal"> Dump </p>

    </div>
</template>
