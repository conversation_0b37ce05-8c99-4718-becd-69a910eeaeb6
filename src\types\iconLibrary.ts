export enum  iconLibrary_types{
  SVG = 'svg',

}
export interface iconLibrary{
    _id: string;
    name:string,
    type:iconLibrary_types,
    category:string,
    iconURL: string,
    iconHeight: number,
    iconWidth: number
}

export interface updateIconLibrary{
    icon_id?:string;
    name?:string,
    type?:iconLibrary_types,
    category?:string,
}

export type allIcons = {
  [key: string]: updateIconLibrary;
};

export type QueryParams = {
  searchText?: string;
}
