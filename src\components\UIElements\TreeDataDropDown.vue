<script setup>
import { ref  } from 'vue';

/*
 Data - array of Objects || Objects of objects,
 selectedParent - should be what every you have given to displayKey,
 selectedSubChild - should be what every you have given to subCategoryDisplayKey
*/

const props = defineProps({
  data: Array || Object,
  displayKey: String,
  subCategoryKey: String,
  subCategoryDisplayKey: String,
  restrictedDefaultValue: Boolean,
  selectedParent: String,
  selectedSubChild: String,
});
const emits = defineEmits(['updateSelectedItem']);
const isListOpen = ref(false);
const ListItemRef = ref(null);
const selectionBoxRef = ref(null);
const selectedItem = ref({
  parentId: null,
  childId: null,
});
const isListFloatTop = ref(false);
const listBoxHeight = ref(150);
const realData = ref(props.data ? Array.isArray(props.data) ? props.data : Object.values(props.data) : null);
const filterList = ref(realData.value);

if (props.selectedParent || props.selectedSubChild || props.restrictedDefaultValue){

  if (props.restrictedDefaultValue){
    selectedItem.value = {
      parentId: filterList.value[0][props.displayKey],
      childId: null,
    };
  }

  if (props.selectedParent && props.selectedSubChild){
    selectedItem.value = {
      parentId: props.selectedParent,
      childId: props.selectedSubChild,
    };
  } else if (props.selectedParent) {
    selectedItem.value = {
      parentId: props.selectedParent,
      childId: null,
    };
  }
}

/* Methods */

const handleFilterList = (e) => {
  const value = e.target.value;
  console.log(realData.value);

  if (value.length > 0) {
    filterList.value = realData.value.filter((val) => val[props.displayKey].toLowerCase().includes(value.toLowerCase()));
    console.log(filterList.value);

  } else {
    filterList.value = props.data;
  }
};

const handleClick = () => {
  const currentListDropDownTop = Math.round((selectionBoxRef.value.getBoundingClientRect().top + selectionBoxRef.value.getBoundingClientRect().height) + Number(listBoxHeight.value));
  const originalScreenHeight = document.documentElement.scrollHeight; // screen origin height

  if (currentListDropDownTop >= ((75/100)*originalScreenHeight)){
    console.log("Yes");
    isListFloatTop.value = true;
  } else {
    console.log("No");
    isListFloatTop.value = false;
  }
  isListOpen.value = !isListOpen.value;
};

const handleListItemClick = (value, parentIndex, childIndex) => {
  if (value){
    selectedItem.value = value;
    // emit
    if (value.parentId && value.childId){
      // subId
      const selectedChildObject = Array.isArray(realData.value[parentIndex][props.subCategoryKey]) ?  realData.value[parentIndex][props.subCategoryKey][childIndex] : Object.values(realData.value[parentIndex][props.subCategoryKey])[childIndex];
      emits('updateSelectedItem', {parent: realData.value[parentIndex], child: selectedChildObject});
    } else {
      emits('updateSelectedItem', {parent: realData.value[parentIndex], child: null});
    }
  } else {
    selectedItem.value = {
      parentId: null,
      childId: null,
    };
    emits('updateSelectedItem', {parent: null, child: null});
  }
  isListOpen.value = !isListOpen.value; // reset to default
  filterList.value = realData.value; // reset filter
  isListFloatTop.value = false; // reset to default
};

</script>

<template>
    <!-- This component is based on single level tree structure. -->
    <div class="w-full h-fit relative font-medium">

        <div ref="selectionBoxRef" class="w-full flex justify-between items-center p-2 border border-gray-600 text-black rounded cursor-pointer mb-1" @click="handleClick">

              <div  v-if="selectedItem.parentId || selectedItem.childId ">
                    <p class="select-none text-base" v-if="selectedItem.parentId && selectedItem.childId">
                        {{ selectedItem.childId }} <i class="text-gray-500 text-sm">({{ selectedItem.parentId }})</i>
                    </p>
                    <p class="select-none text-base" v-else> {{ selectedItem.parentId }}</p>
              </div>
              <p class="select-none text-base" v-else>Choose</p>
             <svg  xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 transition-all" :class="[isListOpen? 'rotate-180' : 'rotate-145']" viewBox="0 0 24 24" fill="none">
                <path d="M5.70711 9.71069C5.31658 10.1012 5.31658 10.7344 5.70711 11.1249L10.5993 16.0123C11.3805 16.7927 12.6463 16.7924 13.4271 16.0117L18.3174 11.1213C18.708 10.7308 18.708 10.0976 18.3174 9.70708C17.9269 9.31655 17.2937 9.31655 16.9032 9.70708L12.7176 13.8927C12.3271 14.2833 11.6939 14.2832 11.3034 13.8927L7.12132 9.71069C6.7308 9.32016 6.09763 9.32016 5.70711 9.71069Z" fill="#0F0F0F"/>
            </svg>
        </div>

        <div v-if="isListOpen" :class="[`bg-white ${isListFloatTop ? 'bottom-[50px]' :'bottom-auto'} custom-shadow rounded border-none outline-none text-black text-sm absolute z-50 w-full h-fit overflow-hidden p-2`]" @mouseleave="() => {isListOpen = !isListOpen; isListFloatTop = false}">
                    <input v-if="filterList && displayKey" @input="handleFilterList" type="text" :placeholder="'type the '+ displayKey+'...'" class="border p-2 rounded outline-none focus:outline-none focus:border-none w-full mb-1 text-start placeholder:text-start placeholder:italic" />
                    <p v-if="!restrictedDefaultValue" data-value="" class="hover:bg-[rgba(0,0,0,0.1)] w-full px-1 py-2 select-none  flex justify-start items-center gap-1 mb-0  "  @click="handleListItemClick(null,null,null)">
                            <span> Choose <i class="text-gray-400">(default)</i> </span>
                    </p>
                    <hr class="h-[1px] bg-gray-100 block w-full my-1"/>
                    <ul :class="[`flex flex-col justify-start items-start gap-0 list-none w-full overflow-y-scroll custom-scrollBar pe-2`]" :style="{height:`${listBoxHeight}px`}">
                        <li v-if="filterList === null" data-value="" class="hover:bg-[rgba(0,0,0,0.1)] w-full px-1 py-2 select-none  flex justify-start items-center gap-1" >
                            <p> No data found ! </p>
                        </li>

                        <li ref="ListItemRef" v-else data-value="" class="w-full select-none flex flex-col justify-start items-start" :key="item[displayKey]+index" v-for="item,index in filterList" >
                            <div class="flex justify-start items-center gap-1 w-full ">
                            <svg v-if="subCategoryKey && item[subCategoryKey]"  @click="() => {
                                if(ListItemRef[index].children[1].getAttribute('data-toggle') === 'hide'){
                                    // show
                                    ListItemRef[index].children[0].children[0].classList.remove('-rotate-90');
                                    ListItemRef[index].children[0].children[0].classList.add('-rotate-180');
                                    ListItemRef[index].children[1].classList.remove('hidden');
                                    ListItemRef[index].children[1].classList.add('block');
                                    ListItemRef[index].children[1].setAttribute('data-toggle','show');
                                } else {
                                     // hide
                                    ListItemRef[index].children[0].children[0].classList.remove('-rotate-180');
                                    ListItemRef[index].children[0].children[0].classList.add('-rotate-90');
                                    ListItemRef[index].children[1].classList.remove('block');
                                    ListItemRef[index].children[1].classList.add('hidden');
                                    ListItemRef[index].children[1].setAttribute('data-toggle','hide');
                                }
                            }"  xmlns="http://www.w3.org/2000/svg" class="w-4 h-4  transition-all cursor-pointer -rotate-90" viewBox="0 0 24 24" fill="none" >
                                <path d="M5.70711 9.71069C5.31658 10.1012 5.31658 10.7344 5.70711 11.1249L10.5993 16.0123C11.3805 16.7927 12.6463 16.7924 13.4271 16.0117L18.3174 11.1213C18.708 10.7308 18.708 10.0976 18.3174 9.70708C17.9269 9.31655 17.2937 9.31655 16.9032 9.70708L12.7176 13.8927C12.3271 14.2833 11.6939 14.2832 11.3034 13.8927L7.12132 9.71069C6.7308 9.32016 6.09763 9.32016 5.70711 9.71069Z" fill="#0F0F0F"/>
                            </svg>
                            <p v-if="displayKey && item[displayKey]" @click="handleListItemClick({'parentId':item[displayKey],'childId':null},index,null)" :class="[`${selectedItem.parentId ? selectedItem.parentId.toLowerCase() === item[displayKey].toLowerCase() ? 'bg-[#221f1f] text-white' : 'bg-transparent text-black' : 'bg-transparent text-black' } px-1 py-[8px] w-full hover:!bg-[rgba(0,0,0,0.1)] hover:!text-black` ]"> {{ item[displayKey] }} <span v-if="subCategoryKey && item[subCategoryKey]" class="text-gray-400 italic text-xs">(subgroups)</span> </p>
                           </div>
                            <div v-if="subCategoryKey && item[subCategoryKey]" data-toggle="hide" :data-name="item[displayKey]+'subgroup_list'" class="w-full h-fit overflow-hidden hidden ps-7 py-1">
                                        <ul class="list-none h-[inherit]">
                                            <li data-value="" :class="[` ${selectedItem.childId ? selectedItem.childId.toLowerCase() === subItem[subCategoryDisplayKey].toLowerCase() && selectedItem.parentId.toLowerCase() === item[displayKey].toLowerCase() ? 'bg-[#353232] text-white' : 'bg-transparent text-black' : 'bg-transparent text-black' } hover:!bg-[rgba(0,0,0,0.1)] hover:!text-black w-full px-1 py-2 select-none  flex justify-start items-center gap-1 border-b border-gray-300 last-of-type:border-none`]" :key="subItem[subCategoryDisplayKey]+itemId+'child'" v-for="subItem,itemId in Array.isArray(item[subCategoryKey]) ? item[subCategoryKey] : Object.values(item[subCategoryKey])" @click="handleListItemClick({'parentId':item[displayKey],'childId':subItem[subCategoryDisplayKey]},index,itemId)">
                                                    {{ subItem[subCategoryDisplayKey] }}
                                            </li>
                                        </ul>
                            </div>
                        </li>
                    </ul>
        </div>
    </div>
</template>

<style scoped>

/* scrollbar */
ul::-webkit-scrollbar {
  width: 3px;
}

ul::-webkit-scrollbar-thumb {
  background: rgb(94, 87, 87);
  border-radius: 30px;
}

.custom-shadow {
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

</style>
