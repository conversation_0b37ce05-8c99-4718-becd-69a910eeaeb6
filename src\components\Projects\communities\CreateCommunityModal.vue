<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { UserStore } from '../../../store';
import { notAuthorized } from '../../../enum';
import { Field, ErrorMessage, Form } from 'vee-validate';
import Modal from '../../common/Modal/Modal.vue';
import Multiselect from 'vue-multiselect';
import { createCommunity } from '../../../api/projects/communities/index';
import { createCommunitySchema } from '../../../validationSchema/community';
import { resizeImage } from '../../../helpers/helpers';
const auth = UserStore();
const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.project_id);
/* State */
const erroMsg = ref({
  isShow: false,
  message: null,
});
const categoryList = ref([{ name: 'villa' }, { name: 'tower' }]);
const catRef = ref();
const category = ref(categoryList.value[0]);
/* Methods */

const handleAddCommunity = async (values) => {
  const resizedThumbnail = await resizeImage(values.file, 720, 720);

  const formData = new FormData();
  formData.append('name', values.name);
  formData.append('project_id', projectId.value);
  formData.append('thumbnail', resizedThumbnail);
  formData.append('category', category.value.name);

  createCommunity(formData)
    .then(() => {
      erroMsg.value.isShow = false;
      erroMsg.value.message = null;
      document.dispatchEvent(new Event('refreshcommunities'));
      router.go(-1);
    })
    .catch((error) => {
      if (auth.verifyAuth()) {
        router.push('/login');
      } else {
        erroMsg.value.isShow = true;
        if (error.message) {
          erroMsg.value.message = error.message;
        } else {
          if (notAuthorized.toLowerCase() !== error.error.toLowerCase()) {
            erroMsg.value.message = error.error;
          }
        }
      }
    });
};
</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary sm:max-w-lg">
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Add Community</h1>
          <p class="modal-subheading-primary">
            Lorem ipsum dolor sit amet elit.
          </p>
        </div>
        <Form :validation-schema="createCommunitySchema" @submit="handleAddCommunity" class="w-full mb-1">
          <div class="">
            <div class="flex">
              <div class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                <div class="col-span-auto w-full">
                  <label for="name" class="label-primary">
                    Name</label>
                  <Field as="input" type="text" name="name" autocomplete id="name" class="input-primary"
                    :placeholder="`Enter Name`" />
                  <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="name" />
                </div>
              </div>
            </div>
            <div class="col-span-auto">
              <label for="category" class="label-primary">Category</label>
              <div class="mt-2">
                <Field name="category" :model-value="catRef" v-slot="{ category }">
                  <Multiselect v-bind="category" v-model="catRef" placeholder="Choose Category" label="name"
                    track-by="name" :multiple="false" :taggable="false" :options="categoryList" maxHeight="250">
                  </Multiselect>
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="category" />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="file" class="label-primary">Upload {{ type }}
                File</label>
              <div class="mt-2">
                <Field type="file" name="file" id="file" autocomplete="highRes" class="input-primary"
                  placeholder="Upload High Resulation Image" />
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="file" />
              </div>
            </div>
            <p v-if="erroMsg.isShow"
              class="text-base text-bold not-italic tracking-normal text-left mt-1 text-[#ff4070]">
              <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
              {{ erroMsg.message }}
            </p>
            <div class="text-center mt-3 flex justify-center items-center gap-3">
              <button @click="() => router.go(-1)" class="cancel-btn-primary">
                Discard
              </button>
              <button id="submit" type="submit" class="proceed-btn-primary">
                Save Changes
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </Modal>
</template>

<style></style>
