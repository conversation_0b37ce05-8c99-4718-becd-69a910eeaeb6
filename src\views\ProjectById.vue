<script setup>
import { ref } from 'vue';

import UnitsView from './ProjectSubViews/UnitsView.vue';
import UnitplansView from './ProjectSubViews/UnitplansView.vue';

const selectedMenu = ref('units');
</script>

<template>
  <div>
    <div class="lg:pl-72">
      <UnitsView v-if="selectedMenu == 'units'" />
      <UnitplansView v-if="selectedMenu == 'unitplans'" />
    </div>
  </div>
</template>

<style scoped>
.active-button {
  border-bottom: 2px solid #1c74d0;
}

.active-text {
  color: #1c74d0;
}

.grid-container::-webkit-scrollbar {
  width: 0.001rem;
}

@media (max-width: 640px) {
  .createButton {
    position: absolute;
    width: 70%;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 20px;
    justify-content: center;
  }

  .grid-container::-webkit-scrollbar {
    width: 0.25rem;
  }

  .grid-container::-webkit-scrollbar-track {
    background: #404040;
  }

  .grid-container::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
  }

  .grid-container::-webkit-scrollbar-thumb:hover {
    background: #939393;
  }
}
</style>
