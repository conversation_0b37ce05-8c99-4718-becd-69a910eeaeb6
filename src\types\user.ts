import { UserRole } from './organization';

export type StsTokenMananger = {
  refreshToken: string;
  accessToken: string;
  expirationTime: number;
};

export type UserClaims = {
  oganization_id: string;
  role: UserRole;
}[];

export type Users = {
  user_id: string;
  role: UserRole;
  email: string;
  first_name?: string;  // Add first_name
  last_name?: string;   // Add last_name (if needed)
  phone_number?: string;       // Add phone
  profilePicture?: string;
}

export type CreateUserInput = {
  first_name: string,
  last_name?:string,
  email:string,
  uid:string,
}

export type assignRoleObj = {
  user_id: string,
  organizationId: string,
  roleId: UserRole,
  email: string
}
export type userObj={
  _id:string,
  email:string,
  organization_id:string[]
}

export type  Organization = {
  roles: any[];
  _id: string;
  name?: string;
  founding_date?: Date;
  contact_email?: string;
  domain?: string;
  // Add any other optional properties as needed
}

export type userObjResponse = {
  _id: any;
  email?: string;
  first_name?: string;  // Add first_name
  last_name?: string;   // Add last_name (if needed)
  phone_number?: string;       // Add phone
  profilePicture?: string;  // Add profilePicture
  organization_id: Organization[];
  fcmToken?: string[]; // Add
};

export interface FcmTokenPayload {
  user_id: string;
  fcmToken: string;
  action: 'add' | 'remove';
}
