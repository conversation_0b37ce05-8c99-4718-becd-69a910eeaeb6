<script setup>

import { ref, watch} from 'vue';
import DeepzoomComp from './DeepzoomComp.vue';
import { useRoute } from 'vue-router';
import { Org_Store } from '../../../store/organization.ts';
import { ProjectStore } from '../../../store/project.ts';
import { cdn } from '../../../helpers/index.ts';
import { getCategories as getAmenitiesCategories } from '../../../api/projects/amenties/index.ts';
import { uiOperations } from '../../../store/uiOperations.ts';
import { getScene } from '../../../api/projects/scene/index.ts';
import { getScene as getMasterScene } from '../../../api/masterScene/index.ts';
import { isMasterScenePath } from '../../../helpers/helpers.ts';

const route = useRoute();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const tileSource = ref(null); // Deep zooom image ref
const overlayLayers = ref(null); // Deep zooom overlay layer ref
const categoryList = ref();
// const updateResize = ref(null); // Update resize ref
// const updateScale = ref(null); // Update scale
const uiStore = uiOperations();
const loader = ref(null);
const isMasterScene = ref(isMasterScenePath(route.fullPath));

defineEmits([ 'currentZooomLevel', 'updateLayerPosition']);
defineProps({
  updateResize: {
    type: Object,
  },
  updateScale: {
    type: Object,
  },
});

if (!isMasterScene.value){
  getAmenitiesCategories(projectId.value).then((res) => { // Get list of amenities categories
    console.log('res ->', res);
    categoryList.value = res.map((elem) => {
      return {name: elem.category};
    });
  }).catch((err) => {
    console.log('output->err', err);
    uiStore.handleApiErrorMessage(err.message);
  });
}

// Api calls
if (!isMasterScene.value){
  projectStore.RefreshLandmarks(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshAmenities(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshCommunities(projectId.value);
}
newOrganizationStore.RefreshProjects();
newOrganizationStore.RefreshMasterScenes();

function callBackGetScene () {
  console.log('callBackGetScene');
  let status;

  const interval = setInterval(() => {
    const getSceneInfo = isMasterScene.value ?  getMasterScene(sceneId.value) : getScene(projectId.value, sceneId.value);

    getSceneInfo.then((res) => {
      console.log('Api calll' + res.sceneData.deep_zoom_status);
      loader.value = res.sceneData.deep_zoom_status;
      status = res.sceneData.deep_zoom_status;
      console.log(status);
      // Clear interval, If the status is success
      if (status === 'process_success'){
        clearInterval(interval); // Clear the interval
        // Api call
        if (isMasterScene.value){
          newOrganizationStore.ForceRefreshMasterScenes();
        } else {
          projectStore.ForceRefreshScenes(projectId.value);
        }
      }
    });
  }, 30000 ); // 30s once

}

function getTheSceneSource (sceneInfo) {
  if (sceneInfo){

    const status = sceneInfo[route.params.scene_id]?.sceneData?.deep_zoom_status;
    if (status === undefined){
      tileSource.value = cdn(sceneInfo[route.params.scene_id].sceneData.background.high_resolution);
      // Overlays
      if (Object.values(sceneInfo[route.params.scene_id].svgData).length > 0){
        overlayLayers.value = sceneInfo[route.params.scene_id].svgData;
      }
    } else {
      if (status === 'process_success'){
      // Dzi
        loader.value = null;
        tileSource.value = cdn(sceneInfo[route.params.scene_id].sceneData.background.high_resolution);
        // Overlays
        if (Object.values(sceneInfo[route.params.scene_id].svgData).length > 0){
          overlayLayers.value = sceneInfo[route.params.scene_id].svgData;
        }
      } else {
        loader.value = status;
        callBackGetScene();
      }
    }
  }
}

const getTheSceneSourceDependsOnScene = () => {
  if (isMasterScene.value){
    getTheSceneSource(newOrganizationStore.masterScenes);
  } else {
    getTheSceneSource(projectStore.scenes);
  }
};

getTheSceneSourceDependsOnScene(); // initialize

watch(() => {
  const sceneData = !isMasterScene.value ? projectStore.scenes : newOrganizationStore.masterScenes;
  return sceneData;
}, () => {
  getTheSceneSourceDependsOnScene();
});

</script>

<template>
     <!-- Loading -->
     <div class="w-full h-full flex flex-col justify-center items-center text-center text-white bg-black" v-if="loader !== null">
              Please be patient,
              <span class="flex justify-start items-center flex-row gap-2">
                The tilesource conversion is {{loader}}
                  <span class="jumpyDots flex justify-start items-center gap-2">
                      <span class="w-[7px] h-[7px] bg-white inline-block rounded-[50%]"></span>
                      <span class="w-[7px] h-[7px] bg-white inline-block rounded-[50%]"></span>
                      <span class="w-[7px] h-[7px] bg-white inline-block rounded-[50%]"></span>
                  </span>
              </span>

     </div>

     <div v-else class="h-full w-full flex justify-center items-center text-white relative overflow-auto">
      <div class="w-full h-full" v-if="tileSource">
              <DeepzoomComp :tileSource="tileSource"  :overlayLayers="overlayLayers" :resize="updateResize" :scale="updateScale" @updateCurrentZoomLevel="(val) => $emit('currentZooomLevel',val)" @updateLayerPosition="(val) => $emit('updateLayersPosition',val)"/>
      </div>
        <!-- Edit -->
        <!-- <div class="h-full" v-if="route.query.layerId">
              <RightSidebar  :open="route.query.layerId"  @closeModal="handleCloseRightSideBar">

                  <UpdateLayers
                                  :landmarks=" isMasterScene ? false : projectStore.landmarks"
                                  :zoomLevel="currentZooomLevel"
                                  :svgData=" isMasterScene ? newOrganizationStore.masterScenes[sceneId].svgData : projectStore.scenes[sceneId].svgData"
                                  :scenes=" isMasterScene ? newOrganizationStore.masterScenes : projectStore.scenes"
                                  :projects="newOrganizationStore.projects"
                                  :projectId=" isMasterScene ? false : projectId"
                                  :defaultPostion="updateLayersPosition"
                                  :categoryList=" isMasterScene ? false : categoryList"
                                  @updateResize="(val) => updateResize = val"
                                  @updateScale="(val) => updateScale = val"
                                  />

              </RightSidebar>
       </div> -->
     </div>
</template>

<style scoped>

.jumpyDots span{
    animation:jumpy-dots 1.3s ease-in-out infinite;
}

.jumpyDots span:nth-child(2){
  animation-delay: -1.1s;
}

.jumpyDots span:nth-child(3){
  animation-delay: -0.9s;
}

@keyframes jumpy-dots {
  0%,60%,100%{
    translate:0;
  }
  30% {
      translate: 0 -15px ;
  }
}

</style>
