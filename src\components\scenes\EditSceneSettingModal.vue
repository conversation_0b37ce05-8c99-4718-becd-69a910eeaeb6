<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import Spinner from '../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { EditProjectSceneSetting } from '../../validationSchema/scene';
import Multiselect from 'vue-multiselect';
import { useRoute } from 'vue-router';
import { Org_Store } from '../../store/organization';
import { ProjectStore } from '../../store/project';
import { getCookie } from '../../helpers/domhelper';
import { updateProjectSceneFloors, updateScene, updateSceneFiles } from '../../api/projects/scene/index';
import {  updateMasterScene, updateSceneFiles as updateMasterSceneFiles } from '../../api/masterScene/index';
import { updateLayers } from '../../api/projects/scene/svg/index';
import { resizeImage, getPropertiesOfImage, isMasterScenePath } from '../../helpers/helpers';
import { getStorage, ref as referenece, getDownloadURL, uploadBytesResumable } from 'firebase/storage';
import { getScene } from '../../api/projects/scene/index.ts';
import { getScene as getMasterScene } from '../../api/masterScene/index';
import * as slider from '@zag-js/slider';
import { useMachine, normalizeProps } from '@zag-js/vue';

const props = defineProps({
  sceneId: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(['close']);

const projectStore = ProjectStore();
const loader = ref(false);
const newOrganizationStore = Org_Store();
const route = useRoute();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const selectedType = ref();
const typeList = [{ name: 'image', value: 'image' }, { name: 'deep zoom', value: 'deep_zoom' }, { name: 'identical_unitplan', value: 'identical_unitplan' }, { name: 'gsplat', value: 'gsplat' }, { name: 'Rotatable Image', value: 'rotatable_image' }];
const active = ref();
const root = ref();
const clouds = ref();
const name = ref();
const info_text = ref();
const category = ref();
const position = ref({
  'x': 0,
  'y': 0,
  'z': 0,
});
const auto_rotate = ref();
const polar_angle = ref({
  'max': 0,
  'min': 0,
});
const distance = ref({
  'max': 0,
  'min': 0,
});

const projectId = ref(route.params.project_id);
const selectedFloors = ref();
const parent = ref();
const building = ref();
const floorData = ref([]);
const uploadProgess = ref(null);
const storage = getStorage();
const uploadedImageFile = ref();
const imagePreviewUrl = ref();

if (isMasterScene.value){
  typeList.push({ name: 'earth', value: 'earth' }); // Master scene
} else {
  typeList.push({ name: 'identical_unitplan', value: 'identical_unitplan' }); // Project scene
}

watch(building, () => {
  if (!isMasterScene.value){
    console.log(building);
    if (building.value) {
      floorData.value = Object.values(projectStore.buildings[building.value].floors)
        .map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
    }
  }
});

newOrganizationStore.RefreshProjects();

if (!isMasterScene.value){
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshSidebarOptions(projectId.value);
}

async function getUploadedStorageLink (file, storagePath) {
  return new Promise((resolve, reject) => {
    const storageRef = referenece(storage, storagePath); // Reference for storage
    loader.value = true;
    // Upload file on task with monitored progress
    const uploadTask = uploadBytesResumable(storageRef, file);
    // State_changed observer
    uploadTask.on('state_changed',
      (snapshot) => {
        // Observe state change events such as progress, pause, and resume
        // Get task progress, including the number of bytes uploaded and the total number of bytes to be uploaded
        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        uploadProgess.value = Math.round(progress);
      },
      (error) => {
        // Handle unsuccessful uploads
        console.log(error);
        reject(error);
      },
      () => {
        // Handle successful uploads on complete
        getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
          console.log(`File available at ${downloadURL}`);
          resolve(downloadURL);
        });
      },
    );

  });
}

const getCurrentCameraPosition = () => {
  if (window.camera){
    return  window.camera.position;
  }
  return null;
};
/* const getPolarAngles = () => {
  return window.controls ? {
    max: Number.isFinite(window.controls.maxPolarAngle) ? window.controls.maxPolarAngle : 0,
    min: Number.isFinite(window.controls.minPolarAngle) ? window.controls.minPolarAngle : 0,
  } : { max: 0, min: 0 };
}; */

// const getDistances = () => {
//   return window.controls ? {
//     max: Number.isFinite(window.controls.maxDistance) ? window.controls.maxDistance : 5,
//     min: Number.isFinite(window.controls.minDistance) ? window.controls.minDistance : 0,
//   } : { max: 0, min: 0 };
// };

/* const updateCameraPosition = () => {
  const cameraPosition = getCurrentCameraPosition();
  console.log(cameraPosition);
  position.value.x = cameraPosition.x;
  position.value.y = cameraPosition.y;
  position.value.z = cameraPosition.z;
}; */
/* const updatePolarAngles = () => {
  const angles = getPolarAngles();
  polar_angle.value.max = angles.max;
  polar_angle.value.min = angles.min;
}; */

/* const updateDistances = () => {
  const distances = getDistances();
  distance.value.max = distances.max;
  distance.value.min = distances.min;
}; */

// Min max slider logic

const minandmaxRef = ref(null);
const [minandmaxState, minandmaxsend] = useMachine(
  slider.machine({
    id: 'minandmax',
    name: 'minandmax',
    value: [1, 10], // Default value
    min: 1,
    max: 10,
  }),
);

const minandmaxComputed = computed(() => slider.connect(minandmaxState.value, minandmaxsend, normalizeProps));
const minandmaxZoomLevel = computed(() => minandmaxState.value.context.value);

const handleInitialValue = () => {
  if (  isMasterScene.value ? newOrganizationStore.masterScenes?.[props.sceneId]?.sceneData : projectStore.scenes?.[props.sceneId]?.sceneData) {
    const initialValue = isMasterScene.value ?  newOrganizationStore.masterScenes[props.sceneId].sceneData : projectStore.scenes[props.sceneId].sceneData ;

    if (!isMasterScene.value){
      if (initialValue.building_id && projectStore.buildings) {
        building.value = initialValue.building_id;
        floorData.value = Object.values(projectStore.buildings[initialValue.building_id].floors)
          .map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
      }
      if (initialValue.floor_ids && initialValue.floor_ids.length !== 0) {
        selectedFloors.value = initialValue.floor_ids.map((elem) => {
          return { '_id': elem, 'name': projectStore.buildings[initialValue.building_id].floors[elem].floor_name };
        });
      }
      initialValue.category ? category.value = initialValue.category :{};
    }

    if (initialValue.type) {
      selectedType.value = initialValue.type;
    }

    if (initialValue.maxZoomLevel && initialValue.minZoomLevel){
      minandmaxsend({ type: 'SET_VALUE', value: [initialValue.minZoomLevel, initialValue.maxZoomLevel] }); // Update the min and max value
    }

    active.value = initialValue.active;
    root.value = initialValue.root;
    clouds.value = initialValue.clouds;
    parent.value = initialValue.parent;
    name.value = initialValue.name;
    info_text.value = initialValue.info_text;
    auto_rotate.value = initialValue.auto_rotate;
    position.value.x = initialValue?.position?.x ;
    position.value.y = initialValue?.position?.y ;
    position.value.z = initialValue?.position?.z ;
    distance.value.max = initialValue?.distance?.max ;
    distance.value.min = initialValue?.distance?.min ;
    polar_angle.value.max = initialValue?.polar_angle?.max ;
    polar_angle.value.min = initialValue?.polar_angle?.min ;
    imagePreviewUrl.value = initialValue?.background?.low_resolution;

  }
};
onMounted(() => {
  if (props.sceneId){
    handleInitialValue();
  }
  // If()

  // UpdateDistances();
  // UpdatePolarAngles();
});

watch(() => {
  const sceneData = !isMasterScene.value ? projectStore.scenes : newOrganizationStore.masterScenes;
  return sceneData;
}, () => {
  if (props.sceneId){
    handleInitialValue();
  }
});

watch(() => props.sceneId, () => {
  if (props.sceneId){
    handleInitialValue();
  }
});

watch(() => getCurrentCameraPosition(), (newPosition) => {
  position.value = { ...newPosition };
}, { deep: true });

watch(() => building.value, () => {
  if (!isMasterScene.value){
    if (building.value === projectStore.scenes[props.sceneId].sceneData.building_id) {
      if (projectStore.scenes?.[props.sceneId].sceneData.floor_ids && projectStore.scenes?.[props.sceneId].sceneData.floor_ids.length !== 0) {
        selectedFloors.value = projectStore.scenes?.[props.sceneId].sceneData.floor_ids.map((elem) => {
          return { '_id': elem, 'name': projectStore.buildings[projectStore.scenes?.[props.sceneId].sceneData.building_id].floors[elem].floor_name };
        });
      }
    } else {
      selectedFloors.value = null;
    }
  }
});

const deepzoomLayersPromises = async (layers, imgChecker) => {
  return new Promise((resolve, reject) => {
    const layersResults = []; // results
    const modifiedLayers = layers;
    (async () => {
      try {
      // Use for...of for sequential processing
        for (const key of Object.keys(modifiedLayers)) {
          const elem = modifiedLayers[key];
          if (Object.keys(elem.layers).length > 0) {
          // Use for...of for sequential processing of layers
            for (const layerKey of Object.keys(elem.layers)) {
              const objParams = {
                project_id: projectId.value,
                svg_id: elem.svg_id,
                layer_id: layerKey,
                viewbox: imgChecker,
                ...elem.layers[layerKey],
              };
              console.log(objParams);
              try {
              // Await each API call sequentially
                const result = await updateLayers(objParams);
                layersResults.push(result);
              } catch (layerError) {
              // Optional: Decide how to handle individual layer errors
                console.error(`Error processing layer ${layerKey}:`, layerError);
                // Choose to continue or reject entire promise
                reject(layerError);
                return;
              }
            }
          }
        }
        // Resolve with all successful results
        resolve(layersResults);
      } catch (error) {
      // Catch any overall processing errors
        reject(error);
      }
    })(); // IIFE (Immediately Invoked Function Expression)
  });
};

function conversionCalc (imageObject, viewboxObject) {
  const valW = imageObject.width / viewboxObject.width ;
  const valH = imageObject.height / viewboxObject.height;
  console.log(valW, valH);
  return { valW, valH };
}

function layersSVGModification (convertValues) {
  console.log("inside LayersSVG Modification");
  console.log(convertValues);
  const newSampleObj = {};  // refined objects
  Object.keys(projectStore.scenes[props.sceneId].svgData).forEach((key1) => {
    newSampleObj[key1] = {
      svg_id: key1,
      viewbox: projectStore.scenes[props.sceneId].svgData[key1].viewbox,
      layers: {},
    };
    if (projectStore.scenes[props.sceneId].svgData[key1]?.layers ? Object.keys(projectStore.scenes[props.sceneId].svgData[key1].layers).length > 0 : false){
      Object.keys(projectStore.scenes[props.sceneId].svgData[key1].layers).forEach((layer) => {
        newSampleObj[key1].layers[layer] = {
          query: {},
        };
        const layerData = projectStore.scenes[props.sceneId].svgData[key1].layers[layer]; // prev values
        const newLayer = {}; // layer changes keys & pair
        newLayer.x = layerData.x * convertValues.valW; // x
        newLayer.y = layerData.y * convertValues.valH; // y
        // scale
        if (layerData.scale) {
          newLayer.scale = {
            x: layerData.scale.x * convertValues.valW,
            y: layerData.scale.y * convertValues.valH,
          };
        } else {
          newLayer.scale = {
            x: convertValues.valW,
            y: convertValues.valH,
          };
        }
        newSampleObj[key1].layers[layer].query = newLayer;
      });
    }
  });
  console.log(newSampleObj);
  return newSampleObj;
}

function viewBoxChecker (imgCheckerObject) {
  console.log("Inside Image Checker");
  console.log(imgCheckerObject.width, imgCheckerObject.height);
  let convertValues = null; // default value has null
  if (projectStore.scenes !== null){
    Object.values(projectStore.scenes[props.sceneId]?.svgData).forEach((element) => {
      const viewboxObj = element?.viewbox;
      if (viewboxObj && viewboxObj.height !== imgCheckerObject.height && viewboxObj.width !== imgCheckerObject.width) {
        console.log("inside Mismatch");
        convertValues = conversionCalc(imgCheckerObject, viewboxObj);
        console.log(convertValues);
      }
    });
  }

  return convertValues;
}

async function  handleSubmit (values) {
  console.log(values);

  const funcArr =[]; // To add functions which need to call

  const initialValue = isMasterScene.value ? newOrganizationStore.masterScenes[props.sceneId].sceneData : projectStore.scenes[props.sceneId].sceneData;

  // Changin undefined or null to false for toggle
  if (values.isActive === undefined || values.isActive === null || values.isActive !== true) {
    values.isActive = false;
  }
  if (values.root === undefined || values.root === null || values.root !== true) {
    values.root = false;
  }
  if (values.clouds === undefined || values.clouds === null || values.clouds !== true) {
    values.clouds = false;
  }
  if (values.auto_rotate === undefined || values.auto_rotate === null) {
    values.auto_rotate = false;
  }
  if (values.parent === undefined || values.parent === null || values.parent === '') {
    delete values.parent;
  }

  // Update files
  const formData = new FormData();
  console.log('Yes');
  console.log(values);
  //  Values.videoFile ? formData.append('video', values.videoFile):{};
  if (values.file || values.gsplat) {
    console.log('Yes');
    console.log(values);

    // Adding project_id and scene_id
    if (!isMasterScene.value) {
      formData.append('project_id', projectId.value);
    }
    formData.append('scene_id', props.sceneId);

    // Gsplat
    if (values.type === 'gsplat') {
      formData.append('gsplat', values.gsplat);
      funcArr.push(isMasterScene.value ?  updateMasterSceneFiles(formData) : updateSceneFiles(formData));
    }  else {
      const resizedThumbnail = await resizeImage(values.file, 1280, 720); // Getting resised image
      formData.append('lowRes', resizedThumbnail);

      const fileToURL = async () => {
        // File to url conversion
        const organization = getCookie('organization');
        const stampFileName = `${new Date().getTime()}${values.file.name}`; // Stamp file name
        const filepath = `CreationtoolAssets/${organization}/projects/${projectId.value}/deepzoom/${stampFileName}`; // File path formation
        const response = await getUploadedStorageLink(values.file, filepath);
        if (response) {
          formData.append('file_url', response);
          uploadProgess.value = 'Link successfully created';
        }
      };
      // Deepzoom
      if (values.type === 'deep_zoom') {
        const imgChecker = await getPropertiesOfImage(values.file); // check
        const conversionValues = viewBoxChecker(imgChecker); // conversion values
        console.log(conversionValues);

        if (conversionValues) {
          // Different
          const modifiedLayers = layersSVGModification(conversionValues);
          console.log(modifiedLayers);
          if (Object.keys(modifiedLayers).length > 0) {
            await deepzoomLayersPromises(modifiedLayers, imgChecker);
            await fileToURL();
            funcArr.push(isMasterScene.value ? updateMasterSceneFiles(formData) : updateSceneFiles(formData));

          }
        } else {
          // Same
          await fileToURL();
          funcArr.push(isMasterScene.value ? updateMasterSceneFiles(formData) : updateSceneFiles(formData));
        }
      }

      if (values.type !== 'rotatable_image' && values.type !== 'gsplat' && values.type !== 'deep_zoom') {
        formData.append('highRes', values.file);
        funcArr.push(isMasterScene.value ?  updateMasterSceneFiles(formData) : updateSceneFiles(formData));
      }
    }
  } else if (!formData.entries().next().done){ // Checks if form data is empty or not
    // Adding project_id and scene_id
    if (!isMasterScene.value) {
      formData.append('project_id', projectId.value);
    }
    formData.append('scene_id', props.sceneId);
    funcArr.push(isMasterScene.value ?  updateMasterSceneFiles(formData) : updateSceneFiles(formData));
  }

  // Update details
  const detailsObj = {};

  initialValue.type !== values.type ? detailsObj.type = values.type : {};
  if (!isMasterScene.value){
    initialValue.category !== values.category ? detailsObj.category = values.category : {};
  }
  initialValue.name !== values.name ? detailsObj.name = values.name : {};
  initialValue.parent !== values.parent ? detailsObj.parent = values.parent : {};
  !initialValue.info_text && !values.info_text ? {}: ( initialValue.info_text !== values.info_text ? detailsObj.info_text = values.info_text : {});
  initialValue.root !== values.root ? detailsObj.root = values.root : {};
  initialValue.active !== values.isActive ? detailsObj.active = values.isActive : {};
  initialValue.clouds !== values.clouds ? detailsObj.clouds = values.clouds : {};
  initialValue.auto_rotate !== values.auto_rotate ? detailsObj.auto_rotate = values.auto_rotate : {};

  if (values.type==='deep_zoom'){
    initialValue.minZoomLevel !== values.minandmax[0] ? detailsObj.minZoomLevel = values.minandmax[0] : {};
    initialValue.maxZoomLevel !== values.minandmax[1] ? detailsObj.maxZoomLevel = values.minandmax[1] : {};
  }

  // Gsplat details
  if (initialValue.type==='gsplat'){
    if (position.value.x && initialValue.position.x !== position.value.x) {
      if (!detailsObj.position) {
        detailsObj.position = {};
      }
      detailsObj.position.x = position.value.x;
    }

    if (position.value.y && initialValue.position.y !== position.value.y) {
      if (!detailsObj.position) {
        detailsObj.position = {};
      }
      detailsObj.position.y = position.value.y;
    }

    if (position.value.z && initialValue.position.z !== position.value.z) {
      if (!detailsObj.position) {
        detailsObj.position = {};
      }
      detailsObj.position.z = position.value.z;
    }
    if (distance.value.max && initialValue.distance.max !== distance.value.max) {
      if (!detailsObj.distance) {
        detailsObj.distance = {};
      }
      detailsObj.distance.max = distance.value.max;
    }
    if (distance.value.min && initialValue.distance.min !== distance.value.min){
      if (!detailsObj.distance) {
        detailsObj.distance = {};
      }
      detailsObj.distance.min = distance.value.min;
    }
    if (polar_angle.value.max && initialValue.polar_angle.max !== polar_angle.value.max) {
      if (!detailsObj.polar_angle) {
        detailsObj.polar_angle = {};
      }
      detailsObj.polar_angle.max = polar_angle.value.max;
    }
    if ( polar_angle.value.min && initialValue.polar_angle.min !== polar_angle.value.min) {
      if (!detailsObj.polar_angle) {
        detailsObj.polar_angle = {};
      }
      detailsObj.polar_angle.min = polar_angle.value.min;
    }
  }

  // Update BuildingFloors
  if (values.building_id && !isMasterScene.value) {
    const floor_ids = ref();
    if (selectedFloors.value && selectedFloors.value.length !== 0) {
      floor_ids.value = selectedFloors.value.map((elem) => elem._id.toString());
    }
    const buildingFloorsObj = { floor_ids: floor_ids.value, scene_id: props.sceneId, building_id: building.value };
    funcArr.push(updateProjectSceneFloors(projectId.value, buildingFloorsObj)); // Adding funtion to fuctArr
  }

  if (Object.keys(detailsObj).length !== 0) {
    if (!isMasterScene.value) {
      detailsObj.project_id = projectId.value;
    } else {
      detailsObj.organization_id = getCookie('organization');
    }
    detailsObj.scene_id = props.sceneId;
    funcArr.push(isMasterScene.value ? updateMasterScene(detailsObj) : updateScene(detailsObj) );
  }

  // Api calls
  new Promise((outerResolve, outerReject) => {
    loader.value = true;
    Promise.all(funcArr)
      .then(() => {
        if (funcArr.length > 0){
          console.log('inside promise');
          // Refersh the scene data
          // if (values.type === 'deep_zoom'){
          //   uploadProgess.value = null;
          // }

          // if (isMasterScene.value) {
          //   newOrganizationStore.ForceRefreshMasterScenes(); // Master scene
          // } else {
          //   projectStore.ForceRefreshScenes(projectId.value); // Project scene
          // }
          // window.location.reload(); // Reload the page

          const newObject = {};
          if (!isMasterScene.value){
            getScene(projectId.value, props.sceneId).then((sceneDetails) => {
              newObject[props.sceneId] = sceneDetails;
              projectStore.SyncMultipleScenes(newObject);
              uploadProgess.value = null;
              emits('close');
              document.dispatchEvent(new Event('refreshAfterSceneEdit'));
            });
          } else {
            getMasterScene(props.sceneId).then((sceneDetails) => {
              newObject[props.sceneId] = sceneDetails;
              newOrganizationStore.SyncMultipleMasterScenes(newObject);
              uploadProgess.value = null;
              emits('close');
              console.log('Master scene updated');
              document.dispatchEvent(new Event('refreshAfterSceneEdit'));
            });
          }
        }
        // location.href = location.href;
        outerResolve();
      }).catch((err) => {
        console.log('Error creating Scenes', err);
        outerReject();
      }).finally(() => {
        loader.value = false;
      });
  });
}

const handleFileUpload = () => {

  const file = event.target.files[0];
  if (file) {
    imagePreviewUrl.value = URL.createObjectURL(file);
  }

};

const removeUnits = (index) => {
  selectedFloors.value.splice(index, 1);
};

</script>

<template>
        <div
            class="bg-white border border-gray-200 rounded-lg w-[536px]"
            >
            <div class="">
                <div class="h-9  flex justify-between items-center px-2.5 border-b border-gray-200 rounded-t-lg">
                    <p class="text-sm font-medium text-gray-900">Scene settings</p>
                    <button  class="fill-gray-400" @click="$emit('close')">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
                    </button>

                </div>

                <Form @submit="handleSubmit"
                    :validation-schema="EditProjectSceneSetting">
                    <div
                    class="p-2.5 grid grid-cols-2 gap-4">

                        <div class="col-span-auto">
                            <label for="name"
                                class="text-sm font-medium text-gray-900">
                                Name</label>
                            <Field as="input" type="text"
                                v-model="name" name="name"
                                autocomplete id="name"
                                class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal"
                                :placeholder="`Enter Project Scene Name`" />
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="name" />
                        </div>

                        <div class="col-span-auto">
                            <label for="type"
                                class="text-sm font-medium text-gray-900">
                                Type</label>
                            <Field v-model="selectedType"
                                as="select" type="text"
                                disabled
                                name="type" id="type"
                                autocomplete="type"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                :placeholder="`Enter scene Type`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!typeList">
                                    No Type found ! </option>
                                <option v-else
                                    :value="option.value"
                                    v-for="option, index in  typeList"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="type" />
                        </div>

                        <div v-if="!isMasterScene" class="col-span-auto">
                            <label for="category"
                                class="text-sm font-medium text-gray-900">
                                Category</label>
                            <Field
                            v-model="category"
                                as="select" type="text"
                                name="category" id="category"
                                autocomplete="category"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                :placeholder="`Select Category`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" class="text-rose-500" disabled
                                    v-if="!projectStore.sidebarOptions">
                                    please create category first.</option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.sidebarOptions"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="category" />
                        </div>

                        <div v-if="selectedType!=='rotatable_image' && selectedType!=='gsplat' && selectedType!=='earth'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit row-span-2">
                            <label for="file"
                                class="text-sm font-medium text-gray-900">Upload Image
                                File</label>

                            <div class="w-full">
                                <div class="w-full rounded-lg relative" v-if="imagePreviewUrl">
                                <img class="h-28 w-full  object-fill rounded-lg" :src="imagePreviewUrl" alt="">
                                <div class="h-full w-full absolute left-0 top-0 flex justify-center items-center"><button @click="()=>{imagePreviewUrl=null,uploadedImageFile=null}" class="absolute h-10 px-3 rounded-lg bg-white">Change</button></div>

                            </div>
                                    <label v-show="!imagePreviewUrl" class="mb-0 w-full h-28 rounded-lg border-2 border-dashed border-gray-200 py-3 px-1 cursor-pointer">
              <div class="flex justify-center items-center gap-2">
                <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_22140)">
<path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
<path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
</g>
<defs>
<clipPath id="clip0_723_22140">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
           </svg>
           <p class="text-xs font-medium text-gray-500 mt-2">Upload</p>
</div>
<div>
  <p class="text-xs font-medium text-gray-500 text-center">{{'16:9 Resolution 3840 x 2160px 4k jpeg ( < 10 mb )'}}</p>
  <Field @change="(e)=>handleFileUpload(e)" type="file"
                                    v-model="uploadedImageFile"
                                    name="file"
                                    id="file"
                                    autocomplete="highRes"
                                    class="hidden"
                                    placeholder="Upload Image" />
</div>
            </label>
                                <ErrorMessage as="p"
                                    class="ml-1 text-xs text-rose-500 mt-1"
                                    name="file" />
                            </div>
                            <div class="mt-2 w-full" v-if="uploadProgess">
                            <div  v-if="!isNaN(uploadProgess)" class="w-full h-[12px] overflow-hidden bg-slate-300 rounded">
                                <div class="h-fit text-[9.5px] flex justify-center items-center text-white bg-[#36f] hover:bg-[#4572fc]"
                                        :style="{ width: uploadProgess + '%' }">
                                       {{ uploadProgess }}%
                                 </div>
                            </div>
                            <div v-else class=" w-full h-fit text-xs text-start text-success bg-transparent">
                                     {{ uploadProgess }}
                               </div>

                            </div>
                        </div>

                <!--         <div class="col-span-auto">
                            <label for="videoFile"
                                class="text-sm font-medium text-gray-900">Upload video file</label>
                            <div class="mt-2">
                                <Field type="file"
                                    name="videoFile"
                                    id="videoFile"
                                    autocomplete="videoFile"
                                    class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal"
                                    placeholder="Upload video file" />
                                <ErrorMessage as="p"
                                    class="ml-1 text-xs text-rose-500 mt-1"
                                    name="videoFile" />
                            </div>
                        </div> -->

                        <div
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label for="parent"
                                class="text-sm font-medium text-gray-900">select
                                Parent Scene</label>
                            <Field v-model="parent" as="select"
                                id="parent" name="parent"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                                <option value=""
                                    class="text-gray-700">
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="(!newOrganizationStore.masterScenes || Object.keys(newOrganizationStore.masterScenes).length === 0) && (!projectStore.scenes || Object.keys(projectStore.scenes).length === 0)">
                                    No Data found ! </option>
                                <option
                                    :value="option.sceneData._id"
                                    v-for="option, index in  newOrganizationStore.masterScenes"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.sceneData.name }}
                                </option>
                                <template v-if="!isMasterScene">
                                  <option
                                      :value="option.sceneData._id"
                                      v-for="option, index in  projectStore.scenes"
                                      :key="index"
                                      class="text-black">
                                      {{
                                          option.sceneData.name }}
                                  </option>
                                </template>
                            </Field>
                            <ErrorMessage name="parent"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>

                        <div class="col-span-auto">
                            <label for="info_text"
                                class="text-sm font-medium text-gray-900">
                                Info Text</label>
                            <Field as="input" type="text"
                                v-model="info_text"
                                name="info_text"
                                class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal"
                                :placeholder="`Enter Info Text`" />
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="info_text" />
                        </div>

                                                    <div v-show="selectedType==='deep_zoom'" class="relative ">
                          <label
                            class="mb-2 text-xs text-gray-500">
                            min and max (zoom level) <br> <span class="italic font-semibold"> min: {{ minandmaxZoomLevel[0] }}</span>  <span class="italic font-semibold"> max: {{ minandmaxZoomLevel[1] }}</span>
                          </label>
                          <div class="w-full pr-2">
                              <Field name="minandmax" v-model="minandmaxZoomLevel" v-slot="{ field }">

                                                    <div ref="minandmaxRef" v-bind="field">
                                                      <div  v-bind="minandmaxComputed.getRootProps()">
                                                          <div v-bind="minandmaxComputed.getControlProps()">
                                                            <div v-bind="minandmaxComputed.getTrackProps()">
                                                              <div v-bind="minandmaxComputed.getRangeProps()" />
                                                            </div>
                                                            <div
                                                              v-for="(_, index) in minandmaxComputed.value"
                                                              :key="index"
                                                              v-bind="minandmaxComputed.getThumbProps({ index })"
                                                            >
                                                              <input v-bind="minandmaxComputed.getHiddenInputProps({ index })" />
                                                            </div>
                                                          </div>
                                                    </div>
                                                  </div>

                                                    <ErrorMessage
                                                  name="minandmax"
                                                  as="p"
                                                  class="text-sm text-rose-500 mt-1" />
                                  </Field>
                          </div>

      </div>
                        <div v-if="!isMasterScene && (selectedType === 'identical_unitplan' || selectedType === 'deep_zoom')"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label for="building_id"
                                class="text-sm font-medium text-gray-900">select
                                Building Id</label>
                            <Field v-model="building"
                                as="select" id="building_id"
                                name="building_id"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                                <option value=""
                                    class="text-gray-700">
                                    Choose
                                </option>
                                <option value=""
                                    v-if="projectStore.buildings && Object.keys(projectStore.buildings).length === 0">
                                    No Data found ! </option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.buildings"
                                    :key="index"
                                    class="text-black">
                                    {{ option.name }} </option>
                            </Field>
                            <ErrorMessage name="building_id"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>

                        <div v-if="!isMasterScene && (selectedType === 'identical_unitplan' || selectedType === 'deep_zoom') && building"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2 col-span-2">
                            <label :for="floor_ids"
                                class="text-sm font-medium text-gray-900">select
                                Floor</label>
                            <Field as="input"
                                v-model="selectedFloors"
                                class="sr-only"
                                name="floor_ids">
                            </Field>
                            <Multiselect
                                v-model="selectedFloors"
                                :options="floorData"
                                :searchable="true"
                                :multiple="true"
                                :taggable="false"
                                placeholder="floor_ids"
                                :close-on-select="false"
                                label="name" track-by="_id"
                                open-direction="top"
                                :max-height="150">

      <!-- Custom Option Template (showing just selected items count) -->
                                <template #selection="{ values, isOpen }">
        <span class=""
              v-if="values.length"
              v-show="!isOpen">{{ values.length }} options selected</span>
      </template>

      <!-- No Search Results Message -->
      <template v-slot:noResult>Oops! No Units found.</template>
                            </Multiselect>
                            <ErrorMessage name="floor_ids"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />

            <!-- Selected Units Tag -->
                                <div name="unit_lists" class="">
                        <div class="outside-tags mt-2 flex flex-wrap gap-2 max-h-24 py-1 px-2 overflow-y-auto">
      <div
        class="bg-[#10b981] py-1 px-2 rounded-md text-white text-xs w-fit h-fit flex items-center gap-2"
        v-for="(unit, index) in selectedFloors"
        :key="unit._id"
      >
        {{ unit.name }}
        <button class="remove-tag" @click="removeUnits(index)">
<svg class="h-4 w-4 fill-[#f6f6f6]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
        </div>
    </div>
    </div>
                        </div>

                        <div v-if="selectedType === 'gsplat'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit row-span-2">
                            <label for="gsplat"
                                class="text-sm font-medium text-gray-900 capitalize"> Upload gsplat zip file </label>
                                    <label class="mb-0 w-full h-28 rounded-lg border-2 border-dashed border-gray-200 py-3 px-1 cursor-pointer">
              <div class="flex justify-center items-center gap-2">
                <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_22140)">
<path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
<path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
</g>
<defs>
<clipPath id="clip0_723_22140">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
           </svg>
           <p class="text-xs font-medium text-gray-500 mt-2">Upload</p>
</div>
<div>
  <p class="text-xs font-medium text-gray-500 text-center">16:9 Resolution 3840 x 2160px 4k jpeg,mp4 or 360 Image Sequence Zip</p>
  <Field type="file"
                                    name="gsplat"
                                    id="gsplat"
                                    autocomplete="gsplat"
                                    class="hidden"
                                    placeholder="Upload gspalt zip" />
</div>
            </label>

                            <ErrorMessage name="gsplat"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>

                        <div v-if="selectedType === 'gsplat'"
                      class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                      <label for="gsplat_x" class="text-sm font-medium text-gray-900 capitalize">Positions</label>
                      <div class="flex w-full space-x-2">
                          <div class="flex flex-col w-1/3">
                              <Field type="number" name="gsplat_x" id="gsplat_x" autocomplete="gsplat_x"
                                  class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="X-axis" v-model="position.x" />
                              <ErrorMessage name="gsplat_x" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                          </div>
                          <div class="flex flex-col w-1/3">
                              <Field type="number" name="gsplat_y" id="gsplat_y" autocomplete="gsplat_y"
                                  class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Y-axis" v-model="position.y" />
                              <ErrorMessage name="gsplat_y" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                          </div>
                          <div class="flex flex-col w-1/3">
                              <Field type="number" name="gsplat_z" id="gsplat_z" autocomplete="gsplat_z"
                                  class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Z-axis" v-model="position.z" />
                              <ErrorMessage name="gsplat_z" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                          </div>
                      </div>
                  </div>

                        <div v-if="selectedType === 'gsplat'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label class="text-sm font-medium text-gray-900 capitalize">Polar Angle</label>
                            <div class="flex w-full space-x-2">
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="polar_angle_max" id="polar_angle_max"
                                        autocomplete="polar_angle_max" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Max"  v-model="polar_angle.max"/>
                                    <ErrorMessage name="polar_angle_max" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="polar_angle_min" id="polar_angle_min"
                                        autocomplete="polar_angle_min" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Min" v-model="polar_angle.min"/>
                                    <ErrorMessage name="polar_angle_min" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                            </div>
                        </div>

                        <div v-if="selectedType === 'gsplat'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label class="text-sm font-medium text-gray-900 capitalize">Distance</label>
                            <div class="flex w-full space-x-2">
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="distance_max" id="distance_max"
                                        autocomplete="distance_max" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Max" v-model="distance.max"/>
                                    <ErrorMessage name="distance_max" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="distance_min" id="distance_min"
                                        autocomplete="distance_min" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Min" v-model="distance.min"/>
                                    <ErrorMessage name="distance_min" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                            </div>
                        </div>

                            <div class="flex  justify-between items-center gap-2 w-auto mr-3">
                                    <label for="isActive" class="mb-0 text-sm font-medium text-gray-900">
                                      publish</label>
                                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                        <div class="relative mb-0 p-0">
                                            <Field id="isActive" class="sr-only peer" name="isActive" type="checkbox"
                                                :value="true" v-model="active" />
                                            <label for="isActive"
                                                class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                            </label>
                                        </div>
                                        <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="isActive" />
                                    </div>
                                </div>

                                <div v-if="selectedType === 'gsplat' || selectedType === 'rotatable_image'" class="flex  justify-between items-center gap-2 w-auto mr-3">
                                    <label for="auto_rotate" class="mb-0 text-sm font-medium text-gray-900">autoRotate</label>
                                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                        <div class="relative mb-0 p-0">
                                            <Field id="auto_rotate" class="sr-only peer" name="auto_rotate"
                                                type="checkbox" :value="true" v-model="auto_rotate"/>
                                            <label for="auto_rotate"
                                                class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                            </label>
                                        </div>
                                        <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="auto_rotate" />
                                    </div>
                                </div>

                                <div class="flex  justify-between items-center gap-2 w-auto mr-3">
                                <label for="clouds" class="mb-0 text-sm font-medium text-gray-900">
                                    isCloude</label>
                                <div
                                    class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                    <div
                                        class="relative mb-0 p-0">
                                        <Field id="clouds"
                                            v-model="clouds"
                                            class="sr-only peer"
                                            name="clouds"
                                            type="checkbox"
                                            :value="true" />
                                        <label for="clouds"
                                            class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                        </label>
                                    </div>
                                    <ErrorMessage as="p"
                                        class="ml-1 text-xs text-rose-500 mt-1"
                                        name="clouds" />
                                </div>
                            </div>

                            <div class="flex  justify-between items-center gap-2 w-auto mr-3">
                                <label for="root" class="mb-0 text-sm font-medium text-gray-900">
                                    isRootScene</label>
                                <div
                                    class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                    <div
                                        class="relative mb-0 p-0">
                                        <Field id="root"
                                        v-model="root"
                                            class="sr-only peer"
                                            name="root"
                                            type="checkbox"
                                            :value="true" />
                                        <label for="root"
                                            class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                        </label>
                                    </div>
                                    <ErrorMessage as="p"
                                        class="ml-1 text-xs text-rose-500 mt-1"
                                        name="root" />
                                </div>
                            </div>

                    </div>
                    <div
                        class="px-2.5">
                        <button type="submit"
                            :disabled="loader"
                            class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">{{loader ? '':'Save'}}
                            <Spinner v-if="loader" class="text-slate-400 fill-white"/>
                        </button>
                    </div>
                </Form>

            </div>
        </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
</style>
