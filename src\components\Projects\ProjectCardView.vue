<script setup>
import { projectCardsMenuItems, projectCardsMenuItemsArchivePage, projectCardsMenuItemsMobileArchivePage, projectCardsMenuItemsMobileView } from '@/enum';
import { activeControlButtonSVGs, controlButtonSVGs, controlButtonSVGsMobileView, experienceTypesMenu } from '@/helpers/constants';
import { ref} from 'vue';
import { useRouter } from 'vue-router';
import InfiniteScroll from '../common/InfiniteScroll.vue';
import { UserStore } from '@/store';
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    required: true,
  },
  section: {
    type: String,
    required: false,
  },
});

const emit = defineEmits(['openModal', 'callApiFunction', 'openDeleteTrashModal']);
const controlButtons = ref(['Analytics', 'Settings', 'Creator']);
const controlButtonsMobileView = ref(['Analytics', 'Settings']);
const projectCardsMenuItemsRef = ref({});
const parentContainerRef = ref(null);
const projectContainerRef = ref({});
const imageRef = ref({});
const buttonRef = ref({});
const activeMenuId = ref(null);
const menuActive = ref(false);
const handeActiveSvg = ref(false);
const router = useRouter();
const Store = UserStore();

function handleControlButtons (projectId){
  if (projectContainerRef.value ? projectContainerRef.value[projectId].classList.contains('hidden') : false){
    const listOfRemovalClass = ['hidden'];
    const listOfAddingClass = ['absolute', 'flex'];
    projectContainerRef.value[projectId].classList.remove(...listOfRemovalClass); // remove class
    projectContainerRef.value[projectId].classList.add(...listOfAddingClass);   // add class
  }
  if (imageRef.value ? imageRef.value[projectId].classList.contains('opacity-100') : false){
    const listOfRemovalClass = ['opacity-100'];
    const listOfAddingClass = ['opacity-70', 'bg-black/50'];
    imageRef.value[projectId].classList.remove(...listOfRemovalClass); // remove class
    imageRef.value[projectId].classList.add(...listOfAddingClass);   // add class
  }

}
function editProjectMenu (e, projectId){
  e.preventDefault();

  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      console.log("upper");
      element.classList.add('right-[1px]');
      element.classList.add(`top-[-85px]`); // both x and y are exceeded the parent container or only y has exceded the parent container

    } else {
      console.log("lower");
      element.classList.add(`top-[25px]`);
      element.classList.add('right-[0px]');  // Only x has exceed the parent container
    }
  };

  if (projectCardsMenuItemsRef.value[projectId]){
    // active item
    projectCardsMenuItemsRef.value[projectId].classList.remove(...listOfRemovalClass); // remove class
    dynamicAbsolutePositions(projectCardsMenuItemsRef.value[projectId]); // apply absolute positions depending on the current position (i.e is based on parent container)
    projectCardsMenuItemsRef.value[projectId].classList.add('flex');   // add class
  }
}
function editProjectMenuMobileView (e, projectId){
  e.preventDefault();
  if (menuActive.value){
    if (activeMenuId.value !== null && activeMenuId.value !== projectId){
      console.log("second box");

      if (projectCardsMenuItemsRef.value[activeMenuId.value]){
        console.log("removing first box");
        const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
        projectCardsMenuItemsRef.value[activeMenuId.value].classList.remove(...listOfRemovalClass); // remove class
        projectCardsMenuItemsRef.value[activeMenuId.value].classList.add('hidden');   // add class
      }
      menuActive.value = true;
      activeMenuId.value = projectId;

    } else {
      console.log("first box");
      activeMenuId.value = null;
      menuActive.value = false;
    }
  } else {
    console.log("first box");
    activeMenuId.value = projectId;
    menuActive.value = true;
  }

  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      element.classList.add('right-[1px]');
      console.log("upper");
      // both x and y are exceeded the parent container or only y has exceded the parent container
      props.section === 'Archive'?element.classList.add(`top-[-130px]`):element.classList.add(`top-[-125px]`);
    } else {
      console.log("lower");

      props.section === 'Archive'? element.classList.add(`top-[35px]`):element.classList.add(`top-[29px]`);
      element.classList.add('right-[0px]');  // Only x has exceed the parent container
    }
  };
  if (menuActive.value){
    if (projectCardsMenuItemsRef.value[projectId]){
      console.log("displayed");
      // active item
      projectCardsMenuItemsRef.value[projectId].classList.remove(...listOfRemovalClass); // remove class
      dynamicAbsolutePositions(projectCardsMenuItemsRef.value[projectId]); // apply absolute positions depending on the current position (i.e is based on parent container)
      projectCardsMenuItemsRef.value[projectId].classList.add('flex');   // add class
    }
  } else {
    if (projectCardsMenuItemsRef.value ? projectCardsMenuItemsRef.value[projectId].classList.contains('flex') : false){
      console.log("hidden");
      const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
      projectCardsMenuItemsRef.value[projectId].classList.remove(...listOfRemovalClass); // remove class
      projectCardsMenuItemsRef.value[projectId].classList.add('hidden');   // add class
    }
  }

}

const handleCloseMenuPopup = (projectId) => {
  console.log("leaving mouse");

  if (projectCardsMenuItemsRef.value ? projectCardsMenuItemsRef.value[projectId].classList.contains('flex') : false){
    const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
    projectCardsMenuItemsRef.value[projectId].classList.remove(...listOfRemovalClass); // remove class
    projectCardsMenuItemsRef.value[projectId].classList.add('hidden');   // add class
  }
  if (projectContainerRef.value ? projectContainerRef.value[projectId].classList.contains('flex') : false){
    const listOfRemovalClass = ['hidden'];
    const listOfAddingClass = ['absolute', 'flex'];
    projectContainerRef.value[projectId].classList.remove(...listOfAddingClass); // remove class
    projectContainerRef.value[projectId].classList.add(...listOfRemovalClass);   // add class
  }

  if (imageRef.value ? imageRef.value[projectId].classList.contains('opacity-70') : false){
    const listOfRemovalClass = ['opacity-100'];
    const listOfAddingClass = ['opacity-70', 'bg-black/50'];
    imageRef.value[projectId].classList.remove(...listOfAddingClass); // remove class
    imageRef.value[projectId].classList.add(...listOfRemovalClass);   // add class
  }
};
</script>
<template>
      <div ref="parentContainerRef" class=" h-full select-none">
         <InfiniteScroll v-if="!Store.isMobile" :loading="loading" type="card" @load-data="(val)=>{if(val){emit('callApiFunction',true)}}">
          <div
            v-for="(project, projectId) in data"
            :onmouseenter="()=>handleControlButtons(projectId)"
            :onmouseleave="()=> handleCloseMenuPopup(projectId)"
            :key="projectId"
            class="max-sm:rounded-sm rounded-lg shadow-[0px_4px_6px_0px_rgba(0,0,0,0.05)] border border-gray-50  cursor-pointer max-h-[240px]  ">
                <div
                    class="!h-full relative flex flex-col "
                    >
                      <div
                        :ref="el => { if(el) projectContainerRef[projectId] = el}"
                        class="hidden h-[140px] w-full bg-black/45 rounded-t-md z-10">
                            <div class="flex gap-1 w-full mx-auto h-full justify-center items-center">
                                 <button v-for="item in controlButtons" :key="item" :ref="el => { if(el) buttonRef[item] = el}" class="bg-white group flex h-8  py-2 px-2 items-center gap-[6px] rounded-lg  !border-[#1c64f2]
                                   hover:!bg-[#e1effe] active:!bg-[#1c64f2] text-[#1c64f2] text-sm font-medium active:!text-white "
                                  @click.stop="(e)=>{e.preventDefault();handeActiveSvg = true; controlButtonSVGs.find(obj=>obj.name === item)?.name === 'Settings'?router.push(`/projects/${project._id}/settings`) :
                                    controlButtonSVGs.find(obj=>obj.name === item)?.name === 'Analytics'?router.push(`/projects/${project._id}/analytics`):controlButtonSVGs.find(obj=>obj.name === item)?.name === 'Creator'?router.push(`/projects/${project._id}/design`):''
                                  }">
                                            <span
                                              v-html="controlButtonSVGs.find(obj => obj.name === item)?.svg"
                                              class="group-active:hidden"
                                            ></span>
                                            <span
                                              v-html="activeControlButtonSVGs.find(obj => obj.name === item)?.svg"
                                              class="hidden group-active:inline"
                                            ></span>
                                            {{ controlButtonSVGs.find(obj=>obj.name === item)?.name }}
                                 </button>
                            </div>

                        </div>
                        <img :src="project.project_thumbnail
                             ? project.project_thumbnail
                             : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR_579Csb4H3KiRQIBwjhkPXwYk0qvVYJ-UvQ&usqp=CAU'
                             " alt="img"
                             class="rounded-t-md w-full opacity-100"
                             :ref="el => { if(el) imageRef[projectId] = el}"
                             style="height: 140px; object-fit: cover ;" />
                                 <div
                                    class="min-h-[100px] h-[100px] max-h-[95px] flex flex-col justify-between items-start py-2 gap-1">
                                       <div v-if="project.experience?.length > 0"  class="h-[50%] flex gap-2 pl-2  items-end w-full">
                                            <div v-for="item in project.experience" :key="item"  class="h-[25px] w-auto  px-1 rounded-md justify-start items-center gap-1 inline-flex"
                                            :class="item === 'propvr_360' ? 'bg-[#e1effe] hover:bg-[#c3ddfd] active:bg-[#a4cafe]':item === 'embed_v1'?' bg-[#fce8f3] hover:bg-[#fad1e8] active:bg-[#f8b4d9]':item === 'rt_application'?'bg-[#edebfe] hover:bg-[#dcd7fe] active:bg-[#cabffd]':''">
                                                <p class=" text-center text-[#1e429f] text-xs font-medium "
                                                :class="item === 'propvr_360'  ? 'text-[#1e429f]':item === 'embed_v1'?'text-[#99154b]':item === 'rt_application'?'text-[#5521b5]':''"
                                                >  {{
                                                        experienceTypesMenu.find(field => field.name === item)?.value || ''
                                                    }}
                                                </p>
                                                <span v-html="experienceTypesMenu.find(field => field.name === item)?.svg || ''">
                                                </span>
                                            </div>
                                        </div>

                                        <div class="w-full h-[100%] flex flex-col "
                                        :class="!project.experience ? 'mt-3':''"
                                        >
                                            <div :class="!project.experience ? 'mt-2':''">
                                                <p
                                                class="text-txt-default dark:text-txt-1000 pl-2 pr-2 text-lg font-semibold capitalize">
                                                {{ project.name }}
                                            </p>
                                            </div>
                                            <div class="w-full flex gap-5 pl-2 pr-2 justify-between">
                                                <div class="flex gap-4 items-center">
                                                    <div v-if="project.country" class="h-full flex gap-2 items-end">
                                                    <span class="relative top-[-3px]">
                                                        <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g id="map-pin" clip-path="url(#clip0_977_23233)">
                                                        <path id="Vector" d="M8.00077 0.500003C6.78532 0.498863 5.59272 0.817031 4.55217 1.42004C3.51162 2.02304 2.66281 2.88789 2.09768 3.92088C1.53255 4.95388 1.27267 6.11563 1.34618 7.28028C1.41969 8.44493 1.82379 9.56806 2.51471 10.528C2.54236 10.5802 2.5759 10.6293 2.61471 10.6744L2.71472 10.7912C2.80805 10.9072 2.90389 11.0192 2.98639 11.1112L7.35741 16.2104C7.43578 16.3012 7.5341 16.3743 7.64533 16.4243C7.75655 16.4744 7.87793 16.5002 8.00077 16.5C8.12395 16.5001 8.24562 16.4739 8.35701 16.4234C8.4684 16.3729 8.56674 16.2993 8.64497 16.208L12.8885 11.244C13.0604 11.0666 13.2212 10.8796 13.3702 10.684L13.476 10.56C13.5163 10.513 13.5505 10.4614 13.5777 10.4064C14.2362 9.44108 14.6107 8.32278 14.6614 7.16989C14.7121 6.01699 14.4371 4.87239 13.8656 3.85723C13.2941 2.84206 12.4474 1.99411 11.4149 1.40311C10.3825 0.812119 9.20282 0.500073 8.00077 0.500003ZM8.00077 4.5C8.49524 4.5 8.97862 4.64076 9.38976 4.90447C9.8009 5.16819 10.1213 5.54302 10.3106 5.98156C10.4998 6.4201 10.5493 6.90266 10.4528 7.36822C10.3564 7.83377 10.1183 8.26141 9.76861 8.59706C9.41897 8.9327 8.97349 9.16128 8.48852 9.25389C8.00354 9.34649 7.50086 9.29896 7.04402 9.11731C6.58719 8.93566 6.19672 8.62805 5.92201 8.23337C5.64729 7.83869 5.50067 7.37468 5.50067 6.9C5.50067 6.26348 5.76407 5.65303 6.23293 5.20295C6.70179 4.75286 7.3377 4.5 8.00077 4.5Z" fill="#6B7280"/>
                                                        </g>
                                                        <defs>
                                                        <clipPath id="clip0_977_23233">
                                                        <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                                                        </clipPath>
                                                        </defs>
                                                        </svg>
                                                    </span>
                                                    <p class="text-gray-500 text-[14px]">{{ project.country }}</p>
                                                    </div>
                                                    <div v-if="project.property_type" class="h-full flex gap-2 items-end">
                                                        <span class="relative top-[-3px]">
                                                            <svg v-if="project.property_type === 'tower'" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <g id="building-01">
                                                                <path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M3.40039 0.96V16H13.0004V12.7503H9.80059C9.38638 12.7503 9.05059 12.4145 9.05059 12.0003C9.05059 11.5861 9.38638 11.2503 9.80059 11.2503H13.0004V9.5503H9.80059C9.38638 9.5503 9.05059 9.21452 9.05059 8.8003C9.05059 8.38609 9.38638 8.0503 9.80059 8.0503H13C12.9981 7.09227 12.9863 6.54436 12.9118 6.07367C12.9031 6.01888 12.8938 5.9643 12.8839 5.90993C12.766 6.16966 12.5044 6.3503 12.2006 6.3503H9.80059C9.38638 6.3503 9.05059 6.01452 9.05059 5.6003C9.05059 5.18609 9.38638 4.8503 9.80059 4.8503H12.2006C12.373 4.8503 12.5318 4.90846 12.6584 5.00621C11.8405 2.44611 9.64161 0.518646 6.92671 0.08864C6.36705 0 5.69816 0 4.36039 0C3.77717 0 3.40039 0.33908 3.40039 0.96Z" fill="#6B7280"/>
                                                                </g>
                                                            </svg>
                                                            <svg v-if="project.property_type === 'villa'"  width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                              <g id="home" clip-path="url(#clip0_977_23849)">
                                                              <path id="Vector" d="M15.7752 7.93431L14.1732 6.33429L8.5663 0.734232C8.41609 0.584253 8.21239 0.5 8 0.5C7.78761 0.5 7.58391 0.584253 7.4337 0.734232L1.82681 6.33429L0.224846 7.93431C0.07894 8.08519 -0.0017947 8.28728 3.02793e-05 8.49704C0.00185526 8.70679 0.0860939 8.90745 0.234603 9.05578C0.383112 9.2041 0.584009 9.28824 0.794024 9.29006C1.00404 9.29188 1.20637 9.21125 1.35744 9.06552L1.59213 8.83112V14.9C1.59213 15.3243 1.7609 15.7313 2.06133 16.0314C2.36176 16.3314 2.76923 16.5 3.19409 16.5H5.59705C5.80948 16.5 6.01322 16.4157 6.16343 16.2657C6.31364 16.1157 6.39803 15.9122 6.39803 15.7V12.5C6.39803 12.2878 6.48242 12.0843 6.63263 11.9343C6.78285 11.7842 6.98658 11.6999 7.19902 11.6999H8.80098C9.01342 11.6999 9.21715 11.7842 9.36737 11.9343C9.51758 12.0843 9.60197 12.2878 9.60197 12.5V15.7C9.60197 15.9122 9.68636 16.1157 9.83657 16.2657C9.98678 16.4157 10.1905 16.5 10.403 16.5H12.8059C13.2308 16.5 13.6382 16.3314 13.9387 16.0314C14.2391 15.7313 14.4079 15.3243 14.4079 14.9V8.83112L14.6426 9.06552C14.7936 9.21125 14.996 9.29188 15.206 9.29006C15.416 9.28824 15.6169 9.2041 15.7654 9.05578C15.9139 8.90745 15.9981 8.70679 16 8.49704C16.0018 8.28728 15.9211 8.08519 15.7752 7.93431Z" fill="#6B7280"/>
                                                              </g>
                                                              <defs>
                                                                <clipPath id="clip0_977_23849">
                                                                <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                                                                </clipPath>
                                                              </defs>
                                                            </svg>

                                                        </span>
                                                        <p class="text-gray-500 text-[14px] capitalize">{{ project.property_type }}</p>
                                                    </div>
                                                </div>

                                                <div class="relative">
                                                    <button class="w-7 h-7 rounded-full border border-gray-200 flex items-center justify-center"
                                                    @click.stop="(e) => editProjectMenu(e,projectId)">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            strokeWidth="{1.5}"
                                                            stroke="currentColor"
                                                            class="w-6 h-6  stroke-bg-default dark:stroke-bg-1000">
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                                        </svg>
                                                    </button>

                                                    <ul
                                                    v-if="section !== 'Archive'"
                                                        :ref="el => { if(el) projectCardsMenuItemsRef[projectId] = el}"
                                                        @onmouseleave.stop="() => handleCloseMenuPopup(projectId)"
                                                        :key="'projectCardMenuItem'+ projectId"
                                                        class="hidden absolute h-fit rounded-xl border-[#d8dbdf] bg-white shadow border w-[100px] flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">

                                                        <li v-for="menuItem, menuItemKey in projectCardsMenuItems" :key="menuItemKey" @click.stop="(e) => {if(menuItem.label === 'Delete') {e.preventDefault(); emit('openModal',true,project._id)}else{e.preventDefault(); router.push({ path: `/projects/edit/${project._id}` })}} " :class="['text-nowrap text-[#0f0f0f] text-base flex font-medium justify-start items-center gap-2 cursor-pointer w-full p-2',menuItem.label === 'Delete'?'text-[#c81e1e] hover:bg-gray-200 active:bg-[#e1effe] rounded-b-lg':'',menuItem.label === 'Edit'? 'hover:bg-gray-200 active:bg-[#e1effe] rounded-t-lg':'']">
                                                                <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                                                {{ menuItem.label }}
                                                        </li>

                                                    </ul>
                                                    <ul
                                                        v-if="section === 'Archive'"
                                                        :ref="el => { if(el) projectCardsMenuItemsRef[projectId] = el}"
                                                        @onmouseleave.stop="() => handleCloseMenuPopup(projectId)"
                                                        :key="'projectCardMenuItem'+ projectId"
                                                        class="hidden absolute h-fit rounded-xl border-[#d8dbdf] bg-white shadow border w-[120px] flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">

                                                        <li v-for="menuItem, menuItemKey in projectCardsMenuItemsArchivePage" :key="menuItemKey"
                                                        @click.stop="(e) => {if(menuItem.label === 'Delete') {e.preventDefault(); emit('openDeleteTrashModal',project._id)}else if(menuItem.label === 'Edit'){e.preventDefault(); router.push({ path: `/projects/edit/${project._id}` })}else if(menuItem.label === 'Unarchive'){e.preventDefault(); emit('openModal',project._id)}} "
                                                        :class="['text-nowrap text-[#0f0f0f] text-base flex font-medium justify-start items-center gap-2 cursor-pointer w-full p-2',menuItem.label === 'Delete'?'text-[#c81e1e] hover:bg-gray-200 active:bg-[#e1effe] rounded-b-lg':'',menuItem.label === 'Edit'? 'hover:bg-gray-200 active:bg-[#e1effe] rounded-t-lg':'',
                                                          menuItem.label === 'Unarchive'? 'hover:bg-gray-200 active:bg-[#e1effe] rounded-t-lg':''
                                                        ]">
                                                                <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                                                {{ menuItem.label }}
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                </div>
          </div>
         </InfiniteScroll>

         <InfiniteScroll v-else :loading="loading" type="card" @load-data="(val)=>{if(val){emit('callApiFunction',true)}}">
          <div
            v-for="(project, projectId) in data"
            :key="projectId"
            class="rounded-lg shadow-[0px_4px_6px_rgba(0,0,0,0.05),0px_2px_2px_-3px_rgba(0,0,0,0.05)] border  cursor-pointer max-h-[158px] min-h-[158px] w-[95%] mx-auto ">
                <div
                    class="!h-full relative flex flex-row "
                    >
                        <img :src="project.project_thumbnail
                             ? project.project_thumbnail
                             : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR_579Csb4H3KiRQIBwjhkPXwYk0qvVYJ-UvQ&usqp=CAU'
                             " alt="img"
                             class="rounded-tl-lg rounded-bl-lg w-[35%] opacity-100"
                             :ref="el => { if(el) imageRef[projectId] = el}"
                             style="height: 100%; object-fit: cover ;" />
                                 <div class="my-auto w-[65%] flex flex-col justify-between items-start px-2 gap-[2px]"
                                 :class="project.experience?.length === 3?'h-[95%]':'h-[85%]'">
                                       <div class="h-[20%]" :class="!project.experience ? 'mt-2':''">
                                                <p
                                                class="text-txt-default dark:text-txt-1000 pl-2 pr-2 text-lg font-semibold capitalize">
                                                {{ project.name }}
                                            </p>
                                       </div>
                                       <div v-if="project.experience && Array.isArray(project.experience)"  class="h-fit w-[98%] grid grid-cols-2 gap-x-1 gap-y-1 pl-2 items-center">
                                            <div v-for="item in project.experience" :key="item"  class="h-[25px] w-fit  px-1 rounded-md justify-start items-center inline-flex"
                                            :class="item === 'propvr_360' ? 'bg-[#e1effe] hover:bg-[#c3ddfd] active:bg-[#a4cafe]':item === 'embed_v1'?' bg-[#fce8f3] hover:bg-[#fad1e8] active:bg-[#f8b4d9]':item === 'rt_application'?'bg-[#edebfe] hover:bg-[#dcd7fe] active:bg-[#cabffd]':''">
                                                <p class=" text-center text-[#1e429f] text-[12px] font-medium "
                                                :class="item === 'propvr_360'  ? 'text-[#1e429f]':item === 'embed_v1'?'text-[#99154b]':item === 'rt_application'?'text-[#5521b5]':''"
                                                >  {{
                                                        experienceTypesMenu.find(field => field.name === item)?.value || ''
                                                    }}
                                                </p>
                                                <span v-html="experienceTypesMenu.find(field => field.name === item)?.svg || ''">
                                                </span>
                                            </div>
                                       </div>

                                        <div class="w-full h-[20%] flex flex-col items-center "
                                        :class="!project.experience ? 'mt-3':''"
                                        >

                                            <div class="w-full h-full flex gap-5 pl-2 pr-2 justify-between items-center">
                                                <div class="flex gap-4 items-center">
                                                    <div v-if="project.country" class="h-full flex gap-2 items-end">
                                                    <span class="relative top-[-3px]">
                                                        <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g id="map-pin" clip-path="url(#clip0_977_23233)">
                                                        <path id="Vector" d="M8.00077 0.500003C6.78532 0.498863 5.59272 0.817031 4.55217 1.42004C3.51162 2.02304 2.66281 2.88789 2.09768 3.92088C1.53255 4.95388 1.27267 6.11563 1.34618 7.28028C1.41969 8.44493 1.82379 9.56806 2.51471 10.528C2.54236 10.5802 2.5759 10.6293 2.61471 10.6744L2.71472 10.7912C2.80805 10.9072 2.90389 11.0192 2.98639 11.1112L7.35741 16.2104C7.43578 16.3012 7.5341 16.3743 7.64533 16.4243C7.75655 16.4744 7.87793 16.5002 8.00077 16.5C8.12395 16.5001 8.24562 16.4739 8.35701 16.4234C8.4684 16.3729 8.56674 16.2993 8.64497 16.208L12.8885 11.244C13.0604 11.0666 13.2212 10.8796 13.3702 10.684L13.476 10.56C13.5163 10.513 13.5505 10.4614 13.5777 10.4064C14.2362 9.44108 14.6107 8.32278 14.6614 7.16989C14.7121 6.01699 14.4371 4.87239 13.8656 3.85723C13.2941 2.84206 12.4474 1.99411 11.4149 1.40311C10.3825 0.812119 9.20282 0.500073 8.00077 0.500003ZM8.00077 4.5C8.49524 4.5 8.97862 4.64076 9.38976 4.90447C9.8009 5.16819 10.1213 5.54302 10.3106 5.98156C10.4998 6.4201 10.5493 6.90266 10.4528 7.36822C10.3564 7.83377 10.1183 8.26141 9.76861 8.59706C9.41897 8.9327 8.97349 9.16128 8.48852 9.25389C8.00354 9.34649 7.50086 9.29896 7.04402 9.11731C6.58719 8.93566 6.19672 8.62805 5.92201 8.23337C5.64729 7.83869 5.50067 7.37468 5.50067 6.9C5.50067 6.26348 5.76407 5.65303 6.23293 5.20295C6.70179 4.75286 7.3377 4.5 8.00077 4.5Z" fill="#6B7280"/>
                                                        </g>
                                                        <defs>
                                                        <clipPath id="clip0_977_23233">
                                                        <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                                                        </clipPath>
                                                        </defs>
                                                        </svg>
                                                    </span>
                                                    <p class="text-gray-500 text-[14px]">{{ project.country }}</p>
                                                    </div>
                                                    <div v-if="project.property_type" class="h-full flex gap-2 items-end">
                                                        <span class="relative top-[-3px]">
                                                            <svg v-if="project.property_type === 'tower'" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <g id="building-01">
                                                                <path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M3.40039 0.96V16H13.0004V12.7503H9.80059C9.38638 12.7503 9.05059 12.4145 9.05059 12.0003C9.05059 11.5861 9.38638 11.2503 9.80059 11.2503H13.0004V9.5503H9.80059C9.38638 9.5503 9.05059 9.21452 9.05059 8.8003C9.05059 8.38609 9.38638 8.0503 9.80059 8.0503H13C12.9981 7.09227 12.9863 6.54436 12.9118 6.07367C12.9031 6.01888 12.8938 5.9643 12.8839 5.90993C12.766 6.16966 12.5044 6.3503 12.2006 6.3503H9.80059C9.38638 6.3503 9.05059 6.01452 9.05059 5.6003C9.05059 5.18609 9.38638 4.8503 9.80059 4.8503H12.2006C12.373 4.8503 12.5318 4.90846 12.6584 5.00621C11.8405 2.44611 9.64161 0.518646 6.92671 0.08864C6.36705 0 5.69816 0 4.36039 0C3.77717 0 3.40039 0.33908 3.40039 0.96Z" fill="#6B7280"/>
                                                                </g>
                                                            </svg>
                                                            <svg v-if="project.property_type === 'villa'"  width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                              <g id="home" clip-path="url(#clip0_977_23849)">
                                                              <path id="Vector" d="M15.7752 7.93431L14.1732 6.33429L8.5663 0.734232C8.41609 0.584253 8.21239 0.5 8 0.5C7.78761 0.5 7.58391 0.584253 7.4337 0.734232L1.82681 6.33429L0.224846 7.93431C0.07894 8.08519 -0.0017947 8.28728 3.02793e-05 8.49704C0.00185526 8.70679 0.0860939 8.90745 0.234603 9.05578C0.383112 9.2041 0.584009 9.28824 0.794024 9.29006C1.00404 9.29188 1.20637 9.21125 1.35744 9.06552L1.59213 8.83112V14.9C1.59213 15.3243 1.7609 15.7313 2.06133 16.0314C2.36176 16.3314 2.76923 16.5 3.19409 16.5H5.59705C5.80948 16.5 6.01322 16.4157 6.16343 16.2657C6.31364 16.1157 6.39803 15.9122 6.39803 15.7V12.5C6.39803 12.2878 6.48242 12.0843 6.63263 11.9343C6.78285 11.7842 6.98658 11.6999 7.19902 11.6999H8.80098C9.01342 11.6999 9.21715 11.7842 9.36737 11.9343C9.51758 12.0843 9.60197 12.2878 9.60197 12.5V15.7C9.60197 15.9122 9.68636 16.1157 9.83657 16.2657C9.98678 16.4157 10.1905 16.5 10.403 16.5H12.8059C13.2308 16.5 13.6382 16.3314 13.9387 16.0314C14.2391 15.7313 14.4079 15.3243 14.4079 14.9V8.83112L14.6426 9.06552C14.7936 9.21125 14.996 9.29188 15.206 9.29006C15.416 9.28824 15.6169 9.2041 15.7654 9.05578C15.9139 8.90745 15.9981 8.70679 16 8.49704C16.0018 8.28728 15.9211 8.08519 15.7752 7.93431Z" fill="#6B7280"/>
                                                              </g>
                                                              <defs>
                                                                <clipPath id="clip0_977_23849">
                                                                <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                                                                </clipPath>
                                                              </defs>
                                                            </svg>

                                                        </span>
                                                        <p class="text-gray-500 text-[14px] capitalize">{{ project.property_type }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="w-[90%] h-[25%] mx-auto flex  items-center justify-between">
                                              <div class="h-[40px] w-[60%]"
                                              >
                                                  <div class="flex gap-2 w-full h-full justify-start items-center  ">
                                                      <button v-for="item in controlButtonsMobileView" :key="item" :ref="el => { if(el) buttonRef[item] = el}" class="bg-white active:!bg-[#ebf5ff] flex h-[32px] w-[32px]  p-2 items-center gap-2 rounded-lg border !border-[#1c64f2]"
                                                          @click.stop="(e)=>{e.preventDefault(); controlButtonSVGsMobileView.find(obj=>obj.name === item)?.name === 'Settings'?router.push(`/projects/${project._id}/settings`) :
                                                          controlButtonSVGsMobileView.find(obj=>obj.name === item)?.name === 'Analytics'?router.push(`/projects/${project._id}/analytics`):''
                                                          }">
                                                                  <span v-html="controlButtonSVGsMobileView.find(obj=>obj.name === item)?.svg"></span>

                                                      </button>
                                                  </div>

                                              </div>

                                                <div class="relative">
                                                    <button class="w-7 h-7 rounded-full border border-gray-200 flex items-center justify-center"
                                                    @click="(e) => editProjectMenuMobileView(e,project._id)">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            strokeWidth="{1.5}"
                                                            stroke="currentColor"
                                                            class="w-6 h-6  stroke-bg-default dark:stroke-bg-1000">
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                                        </svg>
                                                    </button>

                                                    <ul
                                                    v-if="section !== 'Archive'"
                                                        :ref="el => { if(el) projectCardsMenuItemsRef[project._id] = el}"
                                                        :key="'projectCardPublishedMenuItem'+ project._id"
                                                        class="hidden absolute h-fit rounded-xl border-[#d8dbdf] bg-white shadow border w-[100px] flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">

                                                        <li v-for="menuItem, menuItemKey in projectCardsMenuItemsMobileView" :key="menuItemKey"
                                                         @click.stop="(e) => {if(menuItem.label === 'Delete') {e.preventDefault(); emit('openModal',true,project._id)}else if(menuItem.label === 'Edit'){e.preventDefault(); router.push({ path: `/projects/edit/${project._id}` })}else if(menuItem.label === 'Creator'){e.preventDefault();router.push({ path: `/projects/${project._id}`})}} " :class="['text-nowrap text-[#0f0f0f] text-base flex font-medium justify-start items-center gap-2 cursor-pointer w-full p-2',menuItem.label === 'Delete'?'text-[#c81e1e] hover:bg-gray-200 active:bg-[#e1effe] rounded-b-lg':'',menuItem.label === 'Edit'? 'hover:bg-gray-200 active:bg-[#e1effe] rounded-t-lg':
                                                          menuItem.label === 'Creator'? 'hover:bg-gray-200 active:bg-[#e1effe]':''
                                                         ]">
                                                                <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                                                {{ menuItem.label }}
                                                        </li>

                                                    </ul>
                                                    <ul
                                                        v-if="section === 'Archive'"
                                                        :ref="el => { if(el) projectCardsMenuItemsRef[project._id] = el}"
                                                        :key="'projectCardArchiveMenuItem'+ project._id"
                                                        class="hidden absolute h-fit rounded-xl border-[#d8dbdf] bg-white shadow border w-[120px] flex-col justify-start items-start gap-0 list-none cursor-default transition-all z-10">

                                                        <li v-for="menuItem, menuItemKey in projectCardsMenuItemsMobileArchivePage" :key="menuItemKey"
                                                        @click.stop="(e) => {if(menuItem.label === 'Delete') {e.preventDefault(); emit('openDeleteTrashModal',project._id)}else if(menuItem.label === 'Creator'){e.preventDefault();}else if(menuItem.label === 'Unarchive'){e.preventDefault(); emit('openModal',project._id)}} "
                                                        :class="['text-nowrap text-[#0f0f0f] text-base flex font-medium justify-start items-center gap-2 cursor-pointer w-full p-2',menuItem.label === 'Delete'?'text-[#c81e1e] hover:bg-gray-200 active:bg-[#e1effe] rounded-b-lg':'',menuItem.label === 'Creator'? 'hover:bg-gray-200 active:bg-[#e1effe] rounded-t-lg':'',
                                                          menuItem.label === 'Unarchive'? 'hover:bg-gray-200 active:bg-[#e1effe]':''
                                                        ]">
                                                                <span v-html="menuItem.svg" class="w-5 h-5 flex justify-center items-center"></span>
                                                                {{ menuItem.label }}
                                                        </li>

                                                    </ul>
                                                </div>
                                        </div>
                                 </div>

                </div>
          </div>
         </InfiniteScroll>

    </div>

</template>

<style>

</style>
