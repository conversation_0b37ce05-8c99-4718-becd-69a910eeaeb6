<script setup>
import { ref, nextTick } from 'vue';
import Modal from '../common/Modal/Modal.vue';
import { useRouter, useRoute } from 'vue-router';
import { ProjectStore } from '../../store/project';
import { deepZoomConversionActionTypes, deepZoomConversionStatusTypes } from '../../enum.ts';
import { convertDeepZoom } from '../../api/projects/scene/index';
import { Org_Store } from '@/store/organization';
import { isMasterScenePath } from '../../helpers/helpers';

const router = useRouter();
const route = useRoute();
const projectStore = ProjectStore();
const newOrganizationStore =  Org_Store();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const status = ref(deepZoomConversionStatusTypes.INITIAL);
const progressTextRef = ref(0);
const progressBarRef = ref(null);
const sceneInfo = ref(isMasterScene.value ? newOrganizationStore.masterScenes?.[sceneId.value]?.sceneData : projectStore.scenes?.[sceneId.value]?.sceneData);

/* Methods */
const handleConversion = async (conversionType) => {
  console.log(conversionType);
  status.value = deepZoomConversionStatusTypes.CONVERSION; // update the status

  await nextTick(); // wait for the dom updation
  console.log(progressBarRef.value);

  progressBarRef.value.classList.remove('notstarted');

  setTimeout(() => {
    console.log("yes");
    progressBarRef.value.classList.add('pending');
    progressTextRef.value = 50;
  }, 100);

  if (sceneInfo.value?.background?.high_resolution){

    const frameParms = {
      "scene_id": sceneId.value,
      "toType": "deep_zoom",
      "fromType": "image",
      ...(!isMasterScene.value && {"project_id": projectId.value}),
      "high_resolution": sceneInfo.value.background.high_resolution,
      "action": conversionType,
    };

    if (isMasterScene.value){
      // Master scene
    } else {
      // Project scene
      convertDeepZoom(frameParms).then(() => {
        document.dispatchEvent(new Event('getScenes')); // update scene in scene screen.

        setTimeout(() => {
          progressBarRef.value.classList.remove('pending');                 // update the progress bar
          progressBarRef.value.classList.add('completed');
          progressTextRef.value = 100;
        }, 200);

        setTimeout(() => {
          status.value = deepZoomConversionStatusTypes.COMPLETED; // update the status
        }, 800);
      });
    }

  }

};

</script>

<template>
  <Modal :open="true">
        <div
            class="modal-content-primary sm:max-w-lg !p-5 flex flex-col justify-start items-start gap-5">
            <div v-if="status !== deepZoomConversionStatusTypes.CONVERSION" class="text-end w-full">
                            <svg @click=" isMasterScene ? router.push(`/masterscenes`) : router.push(`/projects/${projectId}/scenes`)" class="w-3 h-3 inline-block cursor-pointer" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_2466_3092)">
                            <path d="M7.08898 5.99961L11.165 1.92359C11.2386 1.85256 11.2972 1.76758 11.3376 1.67363C11.3779 1.57968 11.3992 1.47863 11.4001 1.37638C11.401 1.27413 11.3815 1.17272 11.3428 1.07808C11.304 0.983445 11.2469 0.897464 11.1745 0.825159C11.1022 0.752855 11.0163 0.695674 10.9216 0.656954C10.827 0.618234 10.7256 0.59875 10.6233 0.599638C10.5211 0.600527 10.42 0.621771 10.3261 0.662129C10.2321 0.702488 10.1471 0.761155 10.0761 0.834705L6.0001 4.91072L1.92408 0.834705C1.77884 0.694429 1.58432 0.616809 1.38241 0.618564C1.1805 0.620319 0.987352 0.701307 0.844574 0.844085C0.701795 0.986864 0.620807 1.18001 0.619052 1.38192C0.617298 1.58383 0.694917 1.77835 0.835193 1.92359L4.91121 5.99961L0.835193 10.0756C0.761643 10.1467 0.702977 10.2316 0.662618 10.3256C0.622259 10.4195 0.601015 10.5206 0.600127 10.6228C0.599238 10.7251 0.618722 10.8265 0.657442 10.9211C0.696163 11.0158 0.753343 11.1018 0.825648 11.1741C0.897952 11.2464 0.983933 11.3035 1.07857 11.3423C1.17321 11.381 1.27462 11.4005 1.37687 11.3996C1.47912 11.3987 1.58017 11.3774 1.67412 11.3371C1.76807 11.2967 1.85304 11.2381 1.92408 11.1645L6.0001 7.0885L10.0761 11.1645C10.2213 11.3048 10.4159 11.3824 10.6178 11.3807C10.8197 11.3789 11.0128 11.2979 11.1556 11.1551C11.2984 11.0124 11.3794 10.8192 11.3811 10.6173C11.3829 10.4154 11.3053 10.2209 11.165 10.0756L7.08898 5.99961Z" fill="#9CA3AF"/>
                            </g>
                            <defs>
                            <clipPath id="clip0_2466_3092">
                            <rect width="12" height="12" fill="white"/>
                            </clipPath>
                            </defs>
                            </svg>
            </div>
            <!-- INITIAL -->
            <div v-if="status === deepZoomConversionStatusTypes.INITIAL" class="flex flex-col justify-start items-start gap-5 w-full transition-all" >

                        <div class="flex flex-col justify-center items-center gap-4 w-full">
                            <svg class="w-5 h-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_2466_3136)">
                                    <path d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34873 18.9426 4.80688 17.0679 2.93215C15.1931 1.05741 12.6513 0.0029116 10 0ZM10 15C9.80222 15 9.60888 14.9413 9.44443 14.8315C9.27998 14.7216 9.15181 14.5654 9.07612 14.3827C9.00044 14.2 8.98063 13.9989 9.01922 13.8049C9.0578 13.6109 9.15304 13.4327 9.2929 13.2929C9.43275 13.153 9.61093 13.0578 9.80491 13.0192C9.99889 12.9806 10.2 13.0004 10.3827 13.0761C10.5654 13.1518 10.7216 13.28 10.8315 13.4444C10.9414 13.6089 11 13.8022 11 14C11 14.2652 10.8946 14.5196 10.7071 14.7071C10.5196 14.8946 10.2652 15 10 15ZM11 11C11 11.2652 10.8946 11.5196 10.7071 11.7071C10.5196 11.8946 10.2652 12 10 12C9.73479 12 9.48043 11.8946 9.2929 11.7071C9.10536 11.5196 9 11.2652 9 11V6C9 5.73478 9.10536 5.48043 9.2929 5.29289C9.48043 5.10536 9.73479 5 10 5C10.2652 5 10.5196 5.10536 10.7071 5.29289C10.8946 5.48043 11 5.73478 11 6V11Z" fill="#262626"/>
                                    </g>
                                    <defs>
                                    <clipPath id="clip0_2466_3136">
                                    <rect width="20" height="20" fill="white"/>
                                    </clipPath>
                                    </defs>
                            </svg>

                            <p class="text-center text-black text-base font-normal leading-normal select-none"> How Do You Want To Convert it? </p>
                        </div>
                        <div class="flex justify-center gap-2 items-center w-full">
                                    <button @click="handleConversion(deepZoomConversionActionTypes.COPY)" class="px-3 py-2 flex justify-center items-center gap-2 bg-white rounded-lg border border-gray-200  " type="button">
                                        <svg class="w-4 h-4" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                         <path d="M6.5 10C6.5 8.1144 6.5 7.1716 7.08579 6.58579C7.6716 6 8.6144 6 10.5 6H11.1667C13.0523 6 13.9951 6 14.5809 6.58579C15.1667 7.1716 15.1667 8.1144 15.1667 10V10.6667C15.1667 12.5523 15.1667 13.4951 14.5809 14.0809C13.9951 14.6667 13.0523 14.6667 11.1667 14.6667H10.5C8.6144 14.6667 7.6716 14.6667 7.08579 14.0809C6.5 13.4951 6.5 12.5523 6.5 10.6667V10Z" stroke="#141B34" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M11.8332 5.99967C11.8316 4.02828 11.8018 3.00715 11.2279 2.30796C11.1171 2.17293 10.9933 2.04913 10.8583 1.93831C10.1207 1.33301 9.02492 1.33301 6.83325 1.33301C4.6416 1.33301 3.54577 1.33301 2.80821 1.93831C2.67318 2.04912 2.54937 2.17293 2.43856 2.30796C1.83325 3.04553 1.83325 4.14135 1.83325 6.33301C1.83325 8.52467 1.83325 9.62047 2.43856 10.3581C2.54937 10.4931 2.67318 10.6169 2.80821 10.7277C3.50739 11.3015 4.52853 11.3313 6.49992 11.3329" stroke="#141B34" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <span class="text-black text-base font-medium leading-normal"> Convert as a Copy </span>
                                    </button>
                                            <p class="text-black text-base font-medium leading-normal"> Or </p>
                                    <button @click="handleConversion(deepZoomConversionActionTypes.REPLACE)" class="px-3 py-2 flex justify-center items-center gap-2 bg-white rounded-lg border border-gray-200  " type="button">
                                        <svg class="w-4 h-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M11.3178 13C12.9345 11.9251 14 10.087 14 8C14 4.68629 11.3137 2 8 2C7.54173 2 7.09547 2.05137 6.66667 2.14868M11.3178 13V10.6667M11.3178 13H13.6667M4.66667 3.01037C3.05869 4.08671 2 5.91972 2 8C2 11.3137 4.68629 14 8 14C8.45827 14 8.90453 13.9486 9.33333 13.8513M4.66667 3.01037V5.33333M4.66667 3.01037H2.33333" stroke="#141B34" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <span class="text-black text-base font-medium leading-normal"> Replace This Scene </span>
                    </button>
                        </div>
            </div>
            <!-- CONVERSION -->
            <div v-if="status === deepZoomConversionStatusTypes.CONVERSION" class="flex flex-col justify-center items-center gap-4 w-full transition-all">
                <div class="text-center">

                    <div class="relative">
                        <svg class="w-[75px] h-[75px]" viewBox="0 0 76 75" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M75.5 37.5C75.5 58.2107 58.7107 75 38 75C17.2893 75 0.5 58.2107 0.5 37.5C0.5 16.7893 17.2893 0 38 0C58.7107 0 75.5 16.7893 75.5 37.5ZM7.31108 37.5C7.31108 54.449 21.051 68.1889 38 68.1889C54.949 68.1889 68.6889 54.449 68.6889 37.5C68.6889 20.551 54.949 6.81108 38 6.81108C21.051 6.81108 7.31108 20.551 7.31108 37.5Z" fill="#E5E7EB"/>
                        </svg>
                        <svg  viewBox="0 0 76 75" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-0 rotate-[-90deg] origin-center w-[75px] h-[75px]">
                            <circle ref="progressBarRef" class="origin-center notstarted progressIndicator" cx="38" cy="37.5" r="34"  fill="none" stroke="#1C64F2" stroke-width="6" stroke-dasharray="226" stroke-dashoffset="226"/>
                        </svg>
                        <p class="absolute translate-x-[-50%] translate-y-[-50%] top-[50%] left-[50%] text-center text-black text-base font-medium"> {{ progressTextRef }}%</p>
                    </div>

                </div>
                    <p class="text-center text-black text-base font-normal leading-normal select-none"> Converting The Scene To Deep Zoom </p>
            </div>
            <!-- COMPLETED -->
             <div v-if="status === deepZoomConversionStatusTypes.COMPLETED" class="flex flex-col justify-center items-center gap-5 w-full transition-all">
                    <div class="flex flex-col gap-4 justify-center items-center">

                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="none">
                            <g clip-path="url(#clip0_2568_562)">
                            <path d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34873 18.9426 4.80688 17.0679 2.93215C15.1931 1.05741 12.6513 0.0029116 10 0ZM13.707 8.707L9.707 12.707C9.51947 12.8945 9.26517 12.9998 9 12.9998C8.73484 12.9998 8.48053 12.8945 8.293 12.707L6.293 10.707C6.11085 10.5184 6.01005 10.2658 6.01233 10.0036C6.01461 9.7414 6.11978 9.49059 6.30518 9.30518C6.49059 9.11977 6.74141 9.0146 7.0036 9.01232C7.2658 9.01005 7.5184 9.11084 7.707 9.293L9 10.586L12.293 7.293C12.4816 7.11084 12.7342 7.01005 12.9964 7.01232C13.2586 7.0146 13.5094 7.11977 13.6948 7.30518C13.8802 7.49059 13.9854 7.7414 13.9877 8.0036C13.99 8.26579 13.8892 8.5184 13.707 8.707Z" fill="#046C4E"/>
                            </g>
                            <defs>
                            <clipPath id="clip0_2568_562">
                            <rect width="20" height="20" fill="white"/>
                            </clipPath>
                            </defs>
                        </svg>

                        <p class="text-center text-black text-base font-normal leading-normal select-none"> Scene Is Converted </p>
                    </div>

                    <div class="flex justify-center items-center gap-4 w-full">

                        <button @click="isMasterScene ? router.push(`/masterscenes/${sceneId}/deepzoom`) : router.push(`/projects/${projectId}/scenes/${sceneId}/deepzoom`)" type="button" class="px-3 py-2.5 bg-black rounded-lg justify-center items-center gap-2 flex text-white text-base font-medium leading-[18px]">
                            Open Scene
                        </button>

                        <button @click="isMasterScene ? router.push(`/masterscenes`) : router.push(`/projects/${projectId}/scenes`)" type="button" class="px-3 py-2.5 bg-white rounded-lg border border-gray-200 justify-center items-center gap-2 flex text-[#111928] text-base font-medium leading-[18px]">
                            Okay
                        </button>
                    </div>
             </div>
        </div>
    </Modal>
</template>

<style scoped>
   .progressIndicator {
      transition: all 1s linear;
    }

    .notstarted{
      stroke-dashoffset: 226;
    }

    .pending{
      stroke-dashoffset: 113;
    }

    .completed{
      stroke-dashoffset: 0;
    }

</style>
