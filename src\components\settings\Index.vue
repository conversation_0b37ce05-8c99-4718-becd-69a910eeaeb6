<script setup>
import { ref } from 'vue';
import { Checkunique_org_id, GetOrganization, UpdateOrganization } from '../../api/organization';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { formatDate } from '../../helpers/domhelper';
import Button from '../common/Button.vue';
import { organizationSettingsSchema } from '../../validationSchema/project/settings';
import { UserStore } from '../../store/index.ts';
import { Org_Store } from '../../store/organization.ts';
/* State and Emits */
const userStore = UserStore();
const projectOrgStore = Org_Store();
const previousData = ref();
const editFormIntialValues = ref(null);
const editDetails = ref(false);
const foundDateRef = ref(null);
const fileRef = ref(null);
// const imgSrc = ref(null);

/* Methods */

// Initialize
const setupDataCallBack = (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value = {
      name: (data?.name ? data?.name : null),
      founding_date: (data?.founding_date ? formatDate(data.founding_date) : null),
      contact_email: (data?.contact_email ? data?.contact_email : null),
      phone_number: (data?.phone_number ? data?.phone_number : null),
      address: (data?.address ? data?.address : null),
      max_users: (data?.max_users ? data?.max_users : null),
      domain: (data?.domain ? data?.domain : null),
      // thumbnail: (data?.thumbnail ? data?.thumbnail : null),
      unique_org_id: (data?.unique_org_id ? data?.unique_org_id : null),
      // measurement_id: (data?.measurement_id ? data?.measurement_id : null),
    };

    // Form Initial Values
    editFormIntialValues.value = {
      name: (data?.name ? data?.name : null),
      founding_date: (data?.founding_date ? formatDate(data.founding_date) : null),
      contact_email: (data?.contact_email ? data?.contact_email : null),
      phone_number: (data?.phone_number ? data?.phone_number : null),
      max_users: (data?.max_users ? data?.max_users : null),
      domain: (data?.domain ? data?.domain : null),
      address: (data?.address ? data?.address : null),
      unique_org_id: (data?.unique_org_id ? data?.unique_org_id : null),
      // measurement_id: (data?.measurement_id ? data?.measurement_id : null),
    };

    foundDateRef.value = formatDate(data.founding_date);

    fileRef.value = null;
    // imgSrc.value = data.thumbnail;

  }

};

userStore.GetOrganizationDetails();

if (!projectOrgStore.organization_data) {
  GetOrganization().then((res) => {
    projectOrgStore.organization_data = res;
    setupDataCallBack(res);
  });
} else {
  setupDataCallBack(projectOrgStore.organization_data);
}

const handleSubmit = async (val) => {
  console.log('Submitted Values:', val);

  const formData = new FormData();
  editFormIntialValues.value.name !== val.name && formData.append('name', val.name);
  const split = val.founding_date.split('/');
  editFormIntialValues.value.founding_date !== val.founding_date &&
    formData.append('founding_date', new Date(split.reverse()).toISOString());
  editFormIntialValues.value.contact_email !== val.contact_email && formData.append('contact_email', val.contact_email);
  editFormIntialValues.value.address !== val.address && formData.append('address', val.address);
  editFormIntialValues.value.unique_org_id !== val.unique_org_id && formData.append('unique_org_id', val.unique_org_id);
  editFormIntialValues.value.phone_number !== val.phone_number && formData.append('phone_number', val.phone_number);
  editFormIntialValues.value.max_users !== Number(val.max_users) && formData.append('max_users', val.max_users);
  // editFormIntialValues.value.measurement_id !== val.measurement_id && formData.append('measurement_id', val.measurement_id);
  editFormIntialValues.value.domain !== val.domain && formData.append('domain', val.domain);
  // formData.append('thumbnail', val.thumbnail);

  console.log('FormData Contents:');
  for (const [key, value] of formData.entries()) {
    console.log(`${key}: ${value}`);
  }

  try {
    const org_check = editFormIntialValues.value.unique_org_id === val.unique_org_id ? null : val.unique_org_id;
    const response = await Checkunique_org_id(org_check);

    console.log('Checkunique_org_id Response:', response);

    if (response.exists) {
      alert(`Unique User ID "${val.unique_org_id}" is already present. Please choose another.`);
      return;
    }
  } catch (error) {
    console.error('Error while checking unique_org_id:', error.message || error);
    return;
  }

  try {
    const res = await UpdateOrganization(formData);
    console.log('UpdateOrganization Response:', res);

    userStore.GetOrganizationDetails();
    editDetails.value = false;
  } catch (error) {
    console.error('Error updating organization:', error);
  }
};

</script>

<template>

  <div class="relative bg-transparent mb-5 h-full">
    <Form
      v-if="editFormIntialValues"
      @submit="handleSubmit"
      :initial-values="editFormIntialValues"
      :validation-schema="organizationSettingsSchema"
    >
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div class="flex flex-col gap-2">
          <span class="text-lg">Organization Settings</span>
          <span class="text-gray-500 text-xs">Add and update details.</span>
        </div>
        <Button title="Save" theme="primary" class="h-10" @click="handleSubmit"/>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
        <!-- Organization name -->
        <div>
          <label for="name" class="label-primary">Organization Name*</label>
          <div class="mt-1">
            <Field type="text" name="name" id="name" autocomplete="name" class="input-primary w-full !bg-gray-50" placeholder="Enter Organization name"/>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="name" />
          </div>
        </div>
        <!-- Email -->
        <div>
          <label for="contact_email" class="label-primary">Email ID*</label>
          <div class="mt-1">
            <Field type="text" name="contact_email" id="contact_email" autocomplete="contact_email" class="input-primary w-full !bg-gray-50" placeholder="Enter Email ID"/>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="contact_email" />
          </div>
        </div>
        <!-- Founded date -->
        <div>
          <label for="founding_date" class="label-primary">Founded Date*</label>
          <div class="mt-1">
            <Field type="text" name="founding_date" id="founding_date" autocomplete="founding_date" class="input-primary w-full !bg-gray-50" placeholder="Enter Founded Date"/>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="founding_date" />
          </div>
        </div>
        <!-- Address -->
        <div>
          <label for="address" class="label-primary">Address*</label>
          <div class="mt-1">
            <Field type="text" name="address" id="address" autocomplete="address" class="input-primary w-full !bg-gray-50" placeholder="Enter Address"/>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="address" />
          </div>
        </div>
        <!-- Phone number -->
        <div>
          <label for="phone_number" class="label-primary">Phone number*</label>
          <div class="mt-1">
            <Field type="text" name="phone_number" id="phone_number" autocomplete="phone_number" class="input-primary w-full !bg-gray-50" placeholder="Enter Phone number"/>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="phone_number" />
          </div>
        </div>
        <!-- Maximum User -->
        <div>
          <label for="max_users" class="label-primary">Maximum Users*</label>
          <div class="mt-1">
            <Field type="number" name="max_users" id="max_users" autocomplete="max_users" class="input-primary w-full !bg-gray-50" placeholder="Enter Maximum Users"/>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="max_users" />
          </div>
        </div>
      </div>
    </Form>

  </div>

</template>
