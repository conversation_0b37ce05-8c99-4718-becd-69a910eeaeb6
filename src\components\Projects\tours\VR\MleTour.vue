<script setup>
import { onMounted, onBeforeUnmount, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
// import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import * as TWEEN from 'three/examples/jsm/libs/tween.module.js';
import { getTourById, UpdateVirtualTour } from '@/api/projects/tours';
import { getCookie } from '@/helpers/domhelper';
import { ProjectStore } from '@/store/project';
import Button from '@/components/common/Button.vue';
import Spinner from '@/components/common/Spinner.vue';
import router from '@/router';

const route = useRoute();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const projectStore = ProjectStore();
const props = defineProps({
  openHotspot: {
    type: Boolean,
    default: false,
  },
});
// const isTourImagesAvailable = ref(projectStore.virtualtours[tourId.value]?.images && Object.keys(projectStore.virtualtours[tourId.value].images).length > 0 ? true : false);
const hotspotsVal = ref([]);
const hotspots = hotspotsVal.value;
const hotspotsMeshVal = ref([]);
const hotspotsMeshes = hotspotsMeshVal.value;
const containerVal = ref(null);
const modelRef = ref(null);
let model = modelRef.value;
const materialRef = ref(null);
let material = materialRef.value;
const imagenameRef = ref("");
let imagename = imagenameRef.value;
const imagePositionRef = ref(null);
let imagePosition = imagePositionRef.value;
const updateTextureRef = ref(false);
let updateTexture = updateTextureRef.value;
const startTransitionRef = ref(false);
const camera_position = ref(null), controls_target = ref(null);
const bucketName = import.meta.env.VITE_APP_BUCKET_CDN;
const modelURL = projectStore.virtualtours[tourId.value].model;
const cameraURL = projectStore.virtualtours[tourId.value].camera;
const storagePath = `https://${import.meta.env.VITE_APP_BUCKET_CDN}/CreationtoolAssets/${getCookie('organization')}/projects/${projectId.value}/tours/${tourId.value}`;
console.log("storagePath", storagePath);
let lods, loadedTiles, scene, renderer, dracoLoader, camera, controls, gltfloader;
const showLoader = ref(true);
const updateMaterial = ref(() => {});
const animate = ref(() => {});
const labelSetOn = ref(props.openHotspot);
const isMobileRef= ref(false);
const hotspotLabel = ref({
  camera_name: null,
  controls_target: {
    x: null,
    y: null,
    z: null,
  },
  camera_position: {
    x: null,
    y: null,
    z: null,
  },
}); // Hotspot(Label) values
console.log("material", material);
const isRotationSetOn = ref(false);
const rotationMin = -Math.PI; // Pi as min
const rotationMax = Math.PI; //  Pi as max
const rotationData = ref({
  x: null,
  y: null,
  z: null,
});
const rotationFormLoader = ref(false);
const viewerLoader = ref(false);

/* Methods */
// Compare Values
const compareValues = (source, compareObj) => {
  const finalObj = {};
  console.log("compareValues", source, compareObj);

  for (const key in source){
    if (key === 'camera_position' || key === 'controls_target' || key === 'initial_rotation'){
      for (const subKey in source[key]){
        if (source[key][subKey] !== compareObj[key][subKey]){ // If one key & value pair is changed then bind all together.
          finalObj[key] = {
            ...source[key],
          };
        }
      }
    } else {
      if (source[key] !== compareObj[key]){
        finalObj[key] = source[key];
      }
    }

  }
  console.log(finalObj);
  return finalObj;
};

// Reset Label Form values
const resetLabelFormValues = () => {
  console.log("resetLabelFormValues");
  hotspotLabel.value = {
    camera_name: null,
    controls_target: {
      x: null,
      y: null,
      z: null,
    },
    camera_position: {
      x: null,
      y: null,
      z: null,
    },
  }; // Disabled Field Feed Values
  labelSetOn.value = false; // lablelSetOn
};

/* Label */

// setCamAndControls
const setCamAndControls = () => {
  console.log("setCamAndControls");
  if (Object.keys(hotspotLabel.value).length > 0){
    hotspotLabel.value.camera_position =  {...camera.position.clone()}; // camera position
    hotspotLabel.value.controls_target =  {...controls.target.clone()}; // controls target
  }
};

// HotspotLabelValues
const HotspotLabelValues = () => {
  console.log("HotspotLabelValues", camera);
  if (Object.keys(hotspotLabel.value).length > 0){
    setCamAndControls();
    if (route.query.image_id) {
      hotspotLabel.value.camera_name = route.query.image_id;
    } else if (!hotspotLabel.value.camera_name){
      hotspotLabel.value.camera_name = hotspots[0].name;
    }
  }

  return hotspotLabel.value;
  // console.log(hotspots.filter((item) => item.name === route.query.image_id));
};

// GetCurrentHotspotLabelValues
const getCurrentHotspotLabelValues = (e) => {
  const callback = e.detail?.callback;
  const value = HotspotLabelValues();
  if (typeof callback === 'function') {
    callback(value); // return the value (i.e HotspotLabelValues)
  }
};

// prevData Setup (Edit Label)
const prevData = (data) => {
  hotspotLabel.value = {
    camera_name: data.camera_name,
    controls_target: {...data.controls_target},
    camera_position: {...data.camera_position},
  };
  const raypoint = new THREE.Vector3(data.camera_position.x, data.camera_position.y, data.camera_position.z);
  updateMaterial.value(data.camera_name, raypoint);
};

/* Rotation */
// Close rotation
const handleCloseRotation = () => {
  console.log("handleCloseRotation");
  const prevData = projectStore.virtualtours[tourId.value]?.initial_rotation ?? { x: 0, y: 0, z: 0 } ;  // get the prevData
  console.log(prevData);
  material.uniforms.uRotation.value.set(prevData.x, prevData.y, prevData.z); // set The Values
  rotationData.value = {
    x: null,
    y: null,
    z: null,
  }; // reset the values
  isRotationSetOn.value = false; // reset the modal
};

// Open rotation
const handleOpenRotation = () => {
  const referenceObj = {
    ...material.uniforms.uRotation.value,
  };
  console.log(referenceObj, "handleOpenRotation");
  rotationData.value = {
    x: referenceObj.x,
    y: referenceObj.y,
    z: referenceObj.z,
  };
  console.log(rotationData.value, "GUI Roation");
  isRotationSetOn.value = true; // open the modal
};

// Change Handler
const handleFieldRangeChanges = (e, key) => {
  const value = e.target.value;
  console.log(value);
  if (value.length === 0){
    console.log("yes length is 0");
    rotationData.value[key] = 0;
  }

  if ( Number(value) < rotationMin || Number(value) > rotationMax ){ // Outside the range
    console.log("Outside range", value, Number(value));
    const isNegative = Math.sign(Number(value)) === -1;
    console.log("isNegative", isNegative);
    rotationData.value[key] = isNegative ? rotationMin : rotationMax; // reset to the min & max
  }

};

// Save
const handleSaveRotation = () => {
  if (rotationData.value.x !== null && rotationData.value.y !== null && rotationData.value.z !== null){
    console.log("handleSaveRotation", rotationData.value);

    rotationFormLoader.value = true; // loader
    const prevData = projectStore.virtualtours[tourId.value]?.initial_rotation;
    let comparedData = {'initial_rotation': rotationData.value};

    if (prevData){ // If prevData is exist, then compare...
      comparedData = compareValues(comparedData, { 'initial_rotation': prevData});
    }

    console.log("comparedData", comparedData);
    if (Object.keys(comparedData).length > 0){
      console.log("If");
      const formData = new FormData();
      formData.append('project_id', projectId.value);
      formData.append('tour_id', tourId.value);
      formData.append('initial_rotation', JSON.stringify({
        'x': comparedData.initial_rotation.x,
        'y': comparedData.initial_rotation.y,
        'z': comparedData.initial_rotation.z,
      }));
      UpdateVirtualTour(formData).then((res) => {
        console.log("UpdateVirtualTour Result", res);
        projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
        rotationData.value = {
          x: null,
          y: null,
          z: null,
        }; // reset the values
        isRotationSetOn.value = false; // close the modal
        rotationFormLoader.value = false; // loader close
      });
    } else {
      console.log("Else");
      handleCloseRotation(); // Close it
      rotationFormLoader.value = false; // loader close
    }
  }
};

// Check for Image Status
const getTourCallBack = () => {
  console.log("getTourCallBack");
  return new Promise((resolve) => {
    const retryCallBack = () => {
      console.log("retryCallBack");
      getTourById( projectId.value, tourId.value).then((res) => {
        console.log('Api calll', res);
        const filterImageProcessing = Object.values(res?.images).filter((item) => item.tile_rendering_status !== 'process_success' ); // Filtered images considered as failure or processing
        console.log("filterImageProcessing", filterImageProcessing);
        if (filterImageProcessing.length === 0){
          console.log("Got result, If", res);
          projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
          resolve(res); // return the result
        } else {
          console.log("Else");
          retryCallBack(); // recursive retry
        }
      });
    };
    retryCallBack(); // start call
  });
};

/* Initial the scene, camera & tiles. */

// Vertex Shader
function vertexShader () {
  return `
  precision mediump float;

  varying vec3 vPosition; // Pass the transformed position to the fragment shader
  void main() {
    // Calculate the world position of the vertex
    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
    
    // Pass the world position to the fragment shader
    vPosition = worldPosition.xyz;
    
    // Project the position onto the screen
    gl_Position = projectionMatrix * viewMatrix * worldPosition;
  }
    `;
}

// Fragment Shader
function fragmentShader () {
  return `
  precision mediump float;

  uniform sampler2D uTexture1;    // First equirectangular texture
  uniform sampler2D uTexture2;    // Second equirectangular texture
  uniform vec3 uRotation;         // Rotation angles (X, Y, Z)
  uniform vec2 uOffset;           // Offset for U and V in texture space
  uniform vec3 uWorldOffset1;     // First world offset for texture 1 in world space
  uniform vec3 uWorldOffset2;     // Second world offset for texture 2 in world space
  uniform float uBlendFactor;     // Blend factor for mixing textures
  uniform float uWorldOffsetBlendFactor;  // Separate blend factor for world offsets
  varying vec3 vPosition;         // World position of the vertex
  uniform sampler2D uTexture2update;
  uniform float updateBlendFactor;
  varying vec2 vUv;

  const float PI = 3.14159265359;

  // Rotation matrices for X, Y, Z axes
  mat3 rotationX(float angle) {
    float s = sin(angle);
    float c = cos(angle);
    return mat3(1.0, 0.0, 0.0, 0.0, c, -s, 0.0, s, c);
  }

  mat3 rotationY(float angle) {
    float s = sin(angle);
    float c = cos(angle);
    return mat3(c, 0.0, s, 0.0, 1.0, 0.0, -s, 0.0, c);
  }

  mat3 rotationZ(float angle) {
    float s = sin(angle);
    float c = cos(angle);
    return mat3(c, -s, 0.0, s, c, 0.0, 0.0, 0.0, 1.0);
  }

  // Function to calculate spherical UV coordinates
  vec2 sphericalUV(vec3 v, vec3 worldOffset) {
    vec3 worldPosition = v + worldOffset;

    // Apply rotation to the world position
    worldPosition = rotationZ(uRotation.z) * rotationY(uRotation.y) * rotationX(uRotation.x) * worldPosition;

    // Calculate longitude and latitude for spherical mapping
    float longitude = atan(worldPosition.z, worldPosition.x);
    float latitude = asin(worldPosition.y / length(worldPosition));

    // Convert to equirectangular UV coordinates
    float u = (longitude / PI + 1.0) * 0.5;
    float vCoord = (latitude / (0.5 * PI) + 1.0) * 0.5;

    // Add texture offset and mirror if necessary
    vec2 uv = vec2(u, vCoord) + uOffset;

    return fract(uv);
  }


  void main() {
    // Blend world offsets using the separate world offset blend factor
    vec3 blendedWorldOffset = mix(uWorldOffset1, uWorldOffset2, uWorldOffsetBlendFactor);

    // Calculate UV coordinates based on the blended world offset
    vec2 uv1 = sphericalUV(vPosition, uWorldOffset1); // UV for texture 1
    vec2 uv2 = sphericalUV(vPosition, uWorldOffset2); // UV for texture 2

    // Sample both textures using their respective UV coordinates
    vec4 color1 = texture2D(uTexture1, uv1);
    vec4 color2 = texture2D(uTexture2, uv2);
    vec4 color2Update = texture2D(uTexture2update, uv2);

    vec4 updateColor = mix(color2, color2Update, updateBlendFactor);

    // Blend the two colors based on the texture blend factor
    vec4 blendedColor = mix(color1, updateColor, uBlendFactor);

    // Output the final color
    gl_FragColor = blendedColor;
  }

    `;
}
// Initial, If any route query are available.
const initLabel = () => { // Label (query)
  if (!labelSetOn.value){
    labelSetOn.value = true; // Open the modal
  }
  prevData(projectStore.virtualtours[tourId.value]?.labels[route.query.label_id]);
};

const initImage = () => { // Image (query)
  if (hotspotLabel.value.camera_name !== route.query.image_id){
    resetLabelFormValues();
    const hotspotItem = hotspots.filter((item) => item.name === route.query.image_id)[0];
    setTimeout(() => {
      updateMaterial.value(hotspotItem.name, hotspotItem.position);
    }, 100);
  }
};
// Mobile Check
if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
  isMobileRef.value=true;
} else {
  isMobileRef.value=false;
}

// Init
const init = async () => {
  console.log("init");
  if (projectStore.virtualtours[tourId.value]?.images && Object.keys(projectStore.virtualtours[tourId.value].images).length > 0 ){
    viewerLoader.value = true; // Loader

    // Check images status
    const filterImageProcessing = Object.values(projectStore.virtualtours[tourId.value].images).filter((item) => item.tile_rendering_status !== 'process_success' );
    console.log(filterImageProcessing, "filterImageProcessing");

    if (filterImageProcessing.length > 0){
      await getTourCallBack();
    }

    // get the original width & height of container (i.e is where the canvas is wrapper inside)
    const containerWidth = containerVal.value.offsetWidth;
    const containerHeight = containerVal.value.offsetHeight;

    // Define LOD configurations
    lods = [
      { level: 2, rows: 2, columns: 2 },
      { level: 1, rows: 4, columns: 4 },
      { level: 0, rows: 8, columns: 8 },
    ];
    loadedTiles = lods.map(() => []); // Array for each LOD
    scene = new THREE.Scene();
    renderer = new THREE.WebGLRenderer({ antialias: true, powerPreference: "high-performance" });

    // Optional: Provide a DRACOLoader instance to decode compressed mesh data
    dracoLoader = new DRACOLoader();
    camera = new THREE.PerspectiveCamera(90, containerWidth / containerHeight, 0.1, 1000);
    // camera.rotation.set(-1.7191564587487533, -1.4661877416432882, -1.7199599156056378);
    controls = new OrbitControls(camera, renderer.domElement);
    gltfloader = new GLTFLoader();

    // var startTransition = startTransitionRef.value;
    console.log(cameraURL, modelURL);

    let raycaster;

    let mouse;
    let needsUpdate = false;
    let animating = false;
    let mousedown = 0;
    let timerForRaycast;
    let updateMaterialtimer;
    let lastUpdateTime = 0;
    const updateInterval = 50; // Update every 250ms
    const batchSize = 8;
    const lodLoadDelay = 1000; // Delay between LOD loads in ms
    let currentLODIndex = 0;
    let currentTexture = null;
    let nextTexture = null;
    // let blendFactor = 0;
    // let isBlending = false;
    const container = containerVal.value;
    // document.body.appendChild(container);
    // const clock =  new THREE.Clock();
    // camera.position.set(0,0,1)
    renderer.setSize( containerWidth, containerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    container.appendChild(renderer.domElement);

    const maxAnisotropy = renderer.capabilities.getMaxAnisotropy();

    // controls.addEventListener('change', render); // use if there is no animation loop
    // controls.update();
    controls.enableDamping = true;
    controls.dampingFactor = 0.075;

    //   controls.addEventListener('end', () => {
    //     updateCameraOrbit();
    //   });

    const light = new THREE.AmbientLight('white', 2); // soft white light
    scene.add(light);

    // Load cursor Texture
    const textureLoader = new THREE.TextureLoader();
    const hotspotTexture = textureLoader.load('/assets/MLE/FloorHotspot_white.png');
    const cursorTexture = textureLoader.load('/assets/MLE/FloorHotspot_blue.png');
    const hotspotSelectedTexture = textureLoader.load('/assets/MLE/FloorHotspot_blueGlow.png');

    // Plane Geometry with Hotspot Texture
    const hotspotGeometry = new THREE.PlaneGeometry(0.4, 0.4);
    const cursorMaterial = new THREE.MeshBasicMaterial({ map: cursorTexture, depthTest: false, depthWrite: false, transparent: true, opacity: 0.75 });
    // const hotspotMaterial = new THREE.MeshBasicMaterial({ map: hotspotTexture, transparent: true, opacity: 0.75 })
    // Create a cursor object (optional: use a sprite for better visuals)
    // const cursorGeometry = new THREE.CircleGeometry(0.1, 32);
    // const cursorMaterial = new THREE.MeshBasicMaterial({ color: "white", side: THREE.DoubleSide, depthTest: false, depthWrite: false, transparent: true, opacity: 0.8 });
    const cursor = new THREE.Mesh(hotspotGeometry, cursorMaterial);
    cursor.rotation.x = -Math.PI / 2; // Align with the model's surface
    cursor.renderOrder = 999;
    scene.add(cursor);
    // console.log(cursor)
    dracoLoader.setDecoderPath(`https://${bucketName}/0AR/draco/`);
    gltfloader.setDRACOLoader(dracoLoader);
    const updateCameraOrbit = function () {
    // Update OrbitControls target to a point just in front of the camera

      var forward = new THREE.Vector3();
      camera.getWorldDirection(forward);
      const forwardwithOffset = forward.clone().multiplyScalar(0.0001);
      // forward.z=forward.z*0.000001
      controls.target.copy(camera.position).add(forwardwithOffset);
    };

    // update texture from one image to another
    updateMaterial.value = (name, position, setOn=false) => {
      if (!animating && !setOn) {
        clearTimeout(updateMaterialtimer);
        material.uniforms.uTexture1.value = material.uniforms.uTexture2.value;
        updateTexture = false;
        currentLODIndex = 0;
        lastUpdateTime = 0;
        loadedTiles = lods.map(() => []);

        // name = name.replace('(', '');
        // name = name.replace(')', '');
        // name = name.replace('_', '');
        // console.log(name, position)
        imagename = name;
        imagePosition = position;

        material.uniforms.uBlendFactor.value = 0;

        updateMaterialtimer = setTimeout(() => {

          updateTexture = true;
          material.uniforms.uWorldOffset2.value.set(imagePosition.x * -1, imagePosition.y * -1, imagePosition.z * -1);
        }, 200);

        // new THREE.TextureLoader().load('renders/' + name + '.jpg', (panoramicTexture) => {
        //     panoramicTexture.anisotropy = maxAnisotropy
        //     panoramicTexture.minFilter = THREE.LinearMipmapLinearFilter;
        //     panoramicTexture.magFilter = THREE.LinearFilter;
        //     material.uniforms.uTexture2.value = panoramicTexture;
        //     material.uniforms.uWorldOffset2.value.set(position.x * -1, position.y, position.z * -1);
        //     material.needsUpdate = true;
        //     callback(true)

      // })
      }
    };

    // Function to update the world offset blend
    const updateWorldOffset = function ({ worldOffsetBlendFactor }) {
      material.uniforms.uWorldOffsetBlendFactor.value = worldOffsetBlendFactor;
    };

    // Adding cameras gltf
    gltfloader.load(cameraURL, function (gltf) {
      const model = gltf.scene;
      console.log(model);

      model.traverse((child) => {

        if (child.type === "PerspectiveCamera") {
        // console.log(child)
        // child.userData.type = "hotspot"
          hotspots.push(child);
        }
      });
    });

    // Adding model gltf
    gltfloader.load(modelURL, function (gltf) {
      model = gltf.scene;
      model.userData.type = "sphere";
      // console.log('renders/' + hotspots[0].name + '.jpg')
      const initialposition = hotspots[0].position;
      // const initialrotation = props.initialRotation;
      const initialrotation = projectStore.virtualtours[tourId.value]?.initial_rotation ?? {x: 0, y: 0, z: 0};
      console.log(initialrotation, "Initial Rotation");
      // Sample Rotation Value ~ 0, 1.79699099785336, 0
      // Create shader material
      material = new THREE.ShaderMaterial({
        uniforms: {
          uTexture1: { type: 't', value: null },
          uTexture2: { type: 't', value: null },
          uTexture2update: { value: null },
          updateBlendFactor: { value: 1.0 },
          uRotation: { type: 'v3', value: new THREE.Vector3(initialrotation.x, initialrotation.y, initialrotation.z) },  // Rotation angles in radians
          uOffset: { type: 'v2', value: new THREE.Vector2(0.0, 0.0) },         // UV offset
          uWorldOffset1: { type: 'v3', value: new THREE.Vector3(0, 0, 0) }, // Initial world space offset (no offset)
          uWorldOffset2: { type: 'v3', value: new THREE.Vector3(0, 0, 0) },
          uZOffset: { type: 'f', value: 0.0 },  // Z-axis offset
          uMirrorU: { type: 'f', value: 0.0 },  // No horizontal mirroring
          uMirrorV: { type: 'f', value: 0.0 },  // No vertical mirroring
          uBlendFactor: { value: 0.0 },
          uWorldOffsetBlendFactor: { value: 0.0 },
        },
        vertexShader: vertexShader(),
        fragmentShader: fragmentShader(),
        side: 2,
      });

      // Create the GUI
      /*      const gui = new GUI();
    console.log("GUI", gui);  */

      // Create controls for rotation
      /*      const rotationFolder = gui.addFolder('Rotation');
    rotationFolder.add(material.uniforms.uRotation.value, 'x', -Math.PI, Math.PI).name('Rotation X'); // Value , label, min, max
    rotationFolder.add(material.uniforms.uRotation.value, 'y', -Math.PI, Math.PI).name('Rotation Y');
    rotationFolder.add(material.uniforms.uRotation.value, 'z', -Math.PI, Math.PI).name('Rotation Z');
    rotationFolder.open();  */

      // // Create controls for offset
      // const offsetFolder = gui.addFolder('Offset');
      // offsetFolder.add(material.uniforms.uOffset.value, 'x', -1, 1).name('Offset U');
      // offsetFolder.add(material.uniforms.uOffset.value, 'y', -1, 1).name('Offset V');
      // offsetFolder.open();

      // // Create controls for world offset
      // const worldOffsetFolder = gui.addFolder('World Offset');
      // worldOffsetFolder.add(material.uniforms.uWorldOffset1.value, 'x', -10, 10).name('World Offset X');
      // worldOffsetFolder.add(material.uniforms.uWorldOffset1.value, 'y', -10, 10).name('World Offset Y');
      // worldOffsetFolder.add(material.uniforms.uWorldOffset1.value, 'z', -10, 10).name('World Offset Z');
      // worldOffsetFolder.open();

      // // Create controls for Z offset
      // gui.add(material.uniforms.uZOffset, 'value', -10, 10).name('Z Offset');
      // gui.add(material.uniforms.uBlendFactor, 'value', 0, 1).name('Blend Factor');

      // // Create controls for mirroring
      // const mirrorFolder = gui.addFolder('Mirroring');
      // mirrorFolder.add(material.uniforms.uMirrorU, 'value', -1, 1).name('Mirror U').step(2); // -1 for mirror, 1 for normal
      // mirrorFolder.add(material.uniforms.uMirrorV, 'value', -1, 1).name('Mirror V').step(2); // -1 for mirror, 1 for normal
      // mirrorFolder.open();

      // Rotate texture by 90 degrees and offset 0.2 in U, 0.1 in V
      // material.uniforms.uRotation.value = 0; // 90-degree rotation
      // material.uniforms.uOffset.value.set(-4.390, 2.325);  // Offset U by 0.2, V by 0.1
      // material.uniforms.uZOffset.value = -0.021;
      // console.log(material)

      // Add outer sphere to fill gaps in model
      const geometry = new THREE.SphereGeometry(500, 32, 16);
      const sphere = new THREE.Mesh(geometry, material);
      model.traverse((child) => {
        if (child.isMesh) {
          child.material = material;
          sphere.position.copy(child.position);
        }
      });

      scene.add(model);

      scene.add(sphere);

      camera.position.set(initialposition.x, initialposition.y, initialposition.z);
      updateCameraOrbit();
      function projectHotspotsOnFloor (hotspots, model) {
        console.log("projectHotspotsOnFloor");

        const floorRaycaster = new THREE.Raycaster();
        const downward = new THREE.Vector3(0, -1, 0);
        hotspots.forEach((hotspot) => {
          console.log(hotspots );
          floorRaycaster.set(hotspot.position.clone(), downward); // Downward ray
          const intersects = floorRaycaster.intersectObject(model, true); // floorMesh is your 3D floor
          console.log(intersects);

          if (intersects.length > 0) {
            console.log("yes");

            hotspot.userData.floorPosition = intersects[0].point; // Save the projected position
          }
        });
      }
      function createHotspotMesh (point) {
      // hotspot geometry, create custom texture instead of this
        //  const hotspotGeometry = new THREE.CircleGeometry(0.1, 32); // Adjust size as needed
        const hotspotMaterial = new THREE.MeshBasicMaterial({ map: hotspotTexture, transparent: true, opacity: 0.5 });
        const hotspot = new THREE.Mesh(hotspotGeometry, hotspotMaterial);
        console.log(point.userData.floorPosition);
        hotspot.position.copy(point.userData.floorPosition); // Use floorPosition if projected
        hotspot.position.y= hotspot.position.y+0.002;
        hotspot.rotation.x = -Math.PI / 2; // Rotate to align with the floor
        hotspot.userData = { id: point.id, targetPosition: point.position, targetName: point.name, type: "hotspot"}; // Add metadata for interactivity
        return hotspot;
      }

      function addHotspotsToScene (hotspots, scene, hotspotsMeshes) {
        hotspots.forEach((point) => {
          const hotspot = createHotspotMesh(point);
          scene.add(hotspot);
          point.userData.geometry=hotspot;
          hotspotsMeshes.push(hotspot);
        });
        console.log("Finished");
        viewerLoader.value = false; // reset the loader
        if (route.query.label_id){
          initLabel();
        }
        if (route.query.image_id){
          initImage();
        }
      }

      // create hotspots on the floor
      setTimeout(() => {
        projectHotspotsOnFloor(hotspots, model);
        addHotspotsToScene(hotspots, scene, hotspotsMeshes);
      }, 500);

      updateMaterial.value(hotspots[0].name, initialposition);

    });

    // handle mouse movements
    const addRaycaster = () => {
      let mousemove = 0;

      // Create a raycaster
      raycaster = new THREE.Raycaster();

      // Create a mouse vector to store normalized device coordinates
      mouse = new THREE.Vector2();

      containerVal.value.addEventListener('mousedown', () => {
        mousedown = 1;
        clearTimeout(timerForRaycast);
        needsUpdate = false;

      });

      // Function to handle mouse move event
      function onMouseMove (event) {
        // Calculate normalized device coordinates (-1 to +1)
        const rect = renderer.domElement.getBoundingClientRect();
        mouse.x = (((event.clientX - rect.left) / rect.width) * 2) - 1;
        mouse.y = (-((event.clientY - rect.top) / rect.height) * 2) + 1;
        mousedown !== 0 ? mousemove = 1 : mousemove = 0;
        /*  mouse.x = ((event.clientX / containerWidth) * 2) - 1;
        mouse.y = -((event.clientY / containerHeight) * 2) + 1; */
        // mousemove=1
        // if (!mousedown && !animating) {
        //     timerForRaycast= setTimeout(()=>{needsUpdate = true;},1000)
        // }

        // doRayCasting();

      }

      // Add an event listener for the mouse move event
      containerVal.value.addEventListener('mousemove', onMouseMove);

      // Function to handle click event and transition to next hotspot
      function onClick () {
        console.log("onClick", "Yes");
        camera_position.value = null;
        controls_target.value = null;
        needsUpdate = false;

        if (!mousemove) {
          console.log("Mouse Move", "NO");
          console.log("Now value of labelSet & Rotation", labelSetOn.value, isRotationSetOn.value);
          raycaster.setFromCamera(mouse, camera);
          console.log(raycaster);
          console.log(scene.children);

          // Find all intersected objects
          const intersects = raycaster.intersectObjects(scene.children.filter((obj) => obj.userData.type === 'hotspot' || obj.userData.type === 'sphere'));
          console.log(intersects, "Intersects");
          // Check if any object was clicked
          if (intersects.length > 0) {
            const object = intersects[0].object; // intersectObjects() returns an array of hits, sorted by distance from the ray’s origin, the index 0 will the Closest away then other sorted items
            // const hotspot = object;
            if (object.userData.type === "hotspot") {

              if (!labelSetOn.value && !isRotationSetOn.value){
                hotspotLabel.value.camera_name = object.userData.targetName;
              }

              console.log(hotspotLabel.value);
              updateMaterial.value(object.userData.targetName, object.userData.targetPosition, labelSetOn.value || isRotationSetOn.value ?true:false);
              object.material.opacity = 0.7;
              object.material.map= hotspotSelectedTexture;
            // animateCameraPosition(camera.position, hotspot.userData.sphere.position, 1200);
            // animateSphere(hotspot)
            } else {
              const raypoint = intersects[0].point;
              const distances = [];
              hotspots.forEach((hotspot) => {
                const dist = raypoint.distanceTo(hotspot.position);
                distances.push(dist);
                hotspot.userData.distance = dist;
              });

              const minDist = Math.min.apply(null, distances);

              hotspots.forEach((hotspot) => {
                if (hotspot.userData.distance === minDist) { // && currentHotspot!=hotspot) {
                // model.visible=true;
                  console.log("Yes Min Dist", hotspot);

                  if (!labelSetOn.value && !isRotationSetOn.value){
                    hotspotLabel.value.camera_name = hotspot.name;
                  }

                  console.log(hotspotLabel.value);
                  cursor.visible = false;
                  hotspot.userData.geometry.material.opacity = 0.7;
                  hotspot.userData.geometry.material.map= hotspotSelectedTexture;
                  updateMaterial.value(hotspot.name, hotspot.position, labelSetOn.value || isRotationSetOn.value ?true:false);

                  // setTimeout(() => {

                  // }, 2000)

                // animateSphere(hotspot)
                }
              });

            }

          }

        }

        // mousemove=0;
        // hotspotsMeshes.forEach((hotspotmesh) => {
        //   hotspotmesh.visible=true;
        // });

      }

      containerVal.value.addEventListener('mouseup', () => {
        mousedown = 0;
        onClick();
        setTimeout(mousemove = 0, 2000);
        timerForRaycast = setTimeout(() => {
          if (!animating) {
            needsUpdate = true;
          }
        }, 500);

      });

    // Add an event listener for the click event
    // document.addEventListener('click', onClick);
    };

    // tracker the cursor with raycasting to trace agains the model. Runs every tick when needsupdate is true
    const doRayCasting = function () {
      if (needsUpdate && !isMobileRef.value) {
        raycaster.setFromCamera(mouse, camera);

        // Find all intersected objects
        const intersects = raycaster.intersectObjects(scene.children.filter((obj) => obj.userData.type === 'hotspot' || obj.userData.type === 'sphere'));
        // Check if any object was clicked
        if (intersects.length > 0) {
          const object = intersects[0].object;
          if (object.userData.type === "hotspot") {
          // console.log(object)
            object.material.opacity = 1;
            object.material.map= hotspotSelectedTexture;
            cursor.visible = false;
            containerVal.value.style.cursor = "pointer";
          } else {
            containerVal.value.style.cursor = "auto";
            cursor.visible = true;
            hotspotsMeshes.forEach((hotspotmesh) => {
              hotspotmesh.material.opacity = 0.5;
              hotspotmesh.material.map= hotspotTexture;
            },
            );
          }
          const intersection = intersects[0];
          cursor.position.copy(intersection.point);
          const normal = intersection.face.normal.clone();
          normal.transformDirection(intersection.object.matrixWorld);
          // cursor.position.addScaledVector(normal, 0.01);
          cursor.lookAt(cursor.position.clone().add(normal));
        }
        if (mousedown || animating) {
          needsUpdate = false;

        }
      // else {
      //     // Hide cursor if nothing is intersected
      //     cursor.visible = false;
      // }
      }
    };

    // Function to update the texture blend uniforms only
    const updateTextureBlend = function ({ blendFactor }) {
      material.uniforms.uBlendFactor.value = blendFactor;
    };

    // Animate camera position
    const animateCameraPosition = (targetPosition, duration) => {
      console.log("Inside");
      const easing=TWEEN.Easing.Sinusoidal.InOut;
      // let startTime;
      // let elapsedTime = 0;
      // model.visible=true;
      // controls.enabled = false;
      let targetTarget;
      targetPosition.y = camera.position.y;
      if (camera_position.value &&  controls_target.value){
        targetPosition = camera_position.value;
        targetTarget = controls_target.value;
      }
      const tween = new TWEEN.Tween(camera.position).to(targetPosition, duration);
      tween.easing(easing);
      tween.start();
      tween.onStart(function () {
      // startTime = performance.now();

      }.bind(this));
      tween.onUpdate(function () {
      // elapsedTime = performance.now() - startTime;
        updateCameraOrbit();
      }.bind(this));
      tween.onComplete(function () {
        updateCameraOrbit();

      }.bind(this));

      // Initial states for blending and offsetting
      const startBlend = { blendFactor: 0.0 };
      const targetBlend = { blendFactor: 1.0 };

      const startWorldOffsetBlend = { worldOffsetBlendFactor: 0.0 };
      const targetWorldOffsetBlend = { worldOffsetBlendFactor: 1.0 };

      // TWEEN.js for world offset blending (decoupled from texture blending)
      const offsetTween = new TWEEN.Tween(startWorldOffsetBlend)
        .to(targetWorldOffsetBlend, 0)  // 3-second offset transition
        .easing(easing)  // Smoother easing for world offset
        .onUpdate(updateWorldOffset)
        .onComplete(() => {
          material.uniforms.uWorldOffset1.value.copy(material.uniforms.uWorldOffset2.value);

          material.uniforms.uWorldOffsetBlendFactor.value = 0;
          if (camera_position.value && controls_target.value) {
            const ttween = new TWEEN.Tween(controls.target).to(targetTarget, duration);
            ttween.easing(easing);
            ttween.start();
            ttween.onComplete(() => {
              animating = false;
              startTransitionRef.value = false;
              timerForRaycast = setTimeout(() => {
                needsUpdate = true;
              }, 100);
            });
          } else {
            animating = false;
            startTransitionRef.value = false;
            timerForRaycast = setTimeout(() => {
              needsUpdate = true;
            }, 100);
          }
          hotspotsMeshes.forEach((hotspotmesh) => {
          // hotspotmesh.material.opacity = 0.3,
            hotspotmesh.material.map=hotspotTexture;
            hotspotmesh.visible=true;
            if (hotspotmesh.userData.targetName===imagename){
              hotspotmesh.visible=false;
            }
          });
          // setTimeout(() => {
          //    // material.uniforms.uTexture1.value = material.uniforms.uTexture2.value
          //    // material.uniforms.uBlendFactor.value = 0

          // }, 250)

        // model.visible=false;
        });

      // TWEEN.js for texture blending
      const blendTween = new TWEEN.Tween(startBlend)
        .to(targetBlend, duration)  // 5-second blend duration
        .easing(easing)
        .onUpdate(updateTextureBlend)
        .onComplete(() => {
        // Optionally start world offset tween here, or run it concurrently
          offsetTween.start();
        });

      // Start the texture blending animation
      blendTween.start();

    };

    // Helper function to combine tiles into a single texture
    const combineAndApplyTiles = function (tiles, rows, columns) {
      if (!updateTexture) {
        return null;
      }
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      const tileSize = 512; // Adjust to your tile size
      canvas.width = tileSize * columns;
      canvas.height = tileSize * rows;

      tiles.forEach((tile, i) => {
        if (!updateTexture) {
          return;
        }
        const x = (i % columns) * tileSize;
        const y = Math.floor(i / columns) * tileSize;
        if (tile) {
          context.drawImage(tile.image, x, y, tileSize, tileSize);
        }
      });

      const texture = new THREE.Texture(canvas);
      // texture.encoding = THREE.sRGBEncoding;
      texture.anisotropy = maxAnisotropy;
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.generateMipmaps = false;
      texture.minFilter = THREE.LinearFilter;
      // texture.magFilter = THREE.LinearFilter;
      texture.needsUpdate = true;
      if (!updateTexture) {
        return null;
      }
      return texture;
    };

    // Function to load tiles for an LOD in batches
    const loadTilesForLOD = function (lod) {
      if (!updateTexture) {
        return;
      }
      // const tileSize = 512; // Replace with actual size if different
      const tilesToLoad = [];
      const loader = new THREE.TextureLoader();
      if (!lod) {
        return;
      }
      // Collect tiles to load
      for (let row = 0; row < lod.rows; row++) {
        if (!updateTexture) {
          return;
        }
        for (let col = 0; col < lod.columns; col++) {
          if (!updateTexture) {
            return;
          }
          const tileIndex = (row * lod.columns) + col;
          // console.log(lod)
          if (!loadedTiles[lod.level][tileIndex]) {
            const url = `${storagePath}/${imagename}/lod_${lod.level}/tile_${row}_${col}.jpg`;
            // console.log(url)
            tilesToLoad.push({ url, index: tileIndex });
          }
        }
      // if (lod.level == 2 && row == 1) {
      //     startTransitionRef.value = true
      // }
      }

      // Helper to load a batch of tiles
      function loadBatch (batch) {
        if (!updateTexture) {
          return null;
        }
        return Promise.all(
          batch.map((tile) =>
            new Promise((resolve, reject) => {
              if ((tile.url).includes(`${imagename}/`)){
                loader.load(
                  tile.url,
                  (texture) => {
                  // console.log(tile.url)
                    if (!updateTexture) {
                      return;
                    }
                    loadedTiles[lod.level][tile.index] = texture;
                    if (lod.level === 2 && tile.index === 1) {
                      startTransitionRef.value = true;
                    }
                    resolve(texture);
                  },
                  undefined,
                  (err) => {
                    reject(err);
                  },
                );
              }
            }),
          ),
        );
      }

      // Process tiles in batches
      return new Promise((resolve) => {
        if (!updateTexture) {
          return;
        }
        let batches = [];
        // console.log(tilesToLoad)
        for (let i = 0; i < tilesToLoad.length; i += batchSize) {
          if (!updateTexture) {
            return;
          }
          batches.push(tilesToLoad.slice(i, i + batchSize));
        }

        let currentBatchIndex = 0;

        function processNextBatch () {
          if (!updateTexture) {
            loadedTiles = lods.map(() => []);
            batches=[];
            return;
          }
          if (currentBatchIndex >= batches.length) {
          // console.log(batches.length)
          // Combine and resolve once all batches are loaded
            resolve(combineAndApplyTiles(loadedTiles[lod.level], lod.rows, lod.columns));
            if (currentLODIndex >= lods.length) {
              updateTexture = false;
              // currentLODIndex = 0;
              // lastUpdateTime = 0;
              // loadedTiles = lods.map(() => []);
              // loadedTiles = lods.map(() => []);
              return;
            }

            currentLODIndex++;

            return;
          }

          loadBatch(batches[currentBatchIndex]).then(() => {
            if (showLoader.value) {
              showLoader.value = false;
            }
            // console.log(currentBatchIndex)
            if (!updateTexture) {
              loadedTiles = lods.map(() => []);
              batches=[];
              return;
            }
            currentBatchIndex++;
            setTimeout(processNextBatch, updateInterval); // Adjust delay if needed
          });
        }

        processNextBatch();
      });
    };

    // Update logic with blending
    const updateTileLoading = function  () {
      const currentTime = Date.now();
      if (!updateTexture) {
        return;
      }
      if (currentLODIndex >= lods.length) {
        currentLODIndex = 2;
      // updateTexture = false;
      // currentLODIndex = 0;
      // lastUpdateTime = 0;
      // material.uniforms.uTexture1.value = material.uniforms.uTexture2.value
      // material.uniforms.uBlendFactor.value=0;
      // console.log(loadedTiles)
      // loadedTiles = lods.map(() => []);
      // return
      }
      // console.log(currentLODIndex)

      const lod = lods[currentLODIndex];
      // console.log(lods)
      if (!lod) {
        return;
      }
      if (currentTime - lastUpdateTime > lodLoadDelay) {
        if (!updateTexture) {
          return;
        }
        loadTilesForLOD(lod).then((texture) => {
          if (!updateTexture) {
            return;
          }
          // console.log(texture)
          nextTexture = texture;
          // blendFactor = 0;
          currentTexture = nextTexture;
          material.uniforms.uTexture2.value = currentTexture;
          // if (!animating) {
          //     if (!updateTexture) return;
          //     material.uniforms.uTexture1.value = currentTexture

          // }

          material.uniforms.uTexture2update.value = nextTexture;

          //  material.needsUpdate = true;

        });

        lastUpdateTime = currentTime;

      }

    };

    const onWindowResize = function () {

      camera.aspect = containerWidth / containerHeight;
      camera.updateProjectionMatrix();

      renderer.setSize(containerWidth, containerHeight);
      renderer.setPixelRatio(window.devicePixelRatio);

    };
    window.addEventListener('resize', onWindowResize);
    const render = function () {
    // const delta = clock.getDelta();
    // requestAnimationFrame(render);
      TWEEN.update();
      renderer.render(scene, camera);
      controls.update();
      doRayCasting();
      // console.log(updateTexture)
      // console.log(updateTexture)
      if (updateTexture) {
        updateTileLoading();
      }

    };
    animate.value = () => {

      renderer.setAnimationLoop(render);

    };

    addRaycaster();
    animate.value();

    watch(startTransitionRef, (newVal) => {
      if (newVal === true) {
        animating = true;
        // setTimeout(() => {

        animateCameraPosition(imagePosition, 1000);
        // }, 0);
      }
    });
  }
};

// removeMaterial (CleanUp)
function removeMaterial (obj) {

  if (obj.material) {
    obj.material.dispose();
    if (obj.material.map) {
      obj.material.map.dispose();
    }
    if (obj.material.lightMap) {
      obj.material.lightMap.dispose();
    }
    if (obj.material.aoMap) {
      obj.material.aoMap.dispose();
    }
    if (obj.material.emissiveMap) {
      obj.material.emissiveMap.dispose();
    }
    if (obj.material.bumpMap) {
      obj.material.bumpMap.dispose();
    }
    if (obj.material.normalMap) {
      obj.material.normalMap.dispose();
    }
    if (obj.material.displacementMap) {
      obj.material.displacementMap.dispose();
    }
    if (obj.material.roughnessMap) {
      obj.material.roughnessMap.dispose();
    }
    if (obj.material.metalnessMap) {
      obj.material.metalnessMap.dispose();
    }
    if (obj.material.alphaMap) {
      obj.material.alphaMap.dispose();
    }
  }

}
// removeGeometry (CleanUp)
function removeGeometry (obj) {
  if (obj.geometry) {
    obj.geometry.dispose();
    obj.geometry.attributes.color = {};
    obj.geometry.attributes.normal = {};
    obj.geometry.attributes.position = {};
    obj.geometry.attributes.uv = {};
    obj.geometry.attributes = {};
    obj.material = {};
  }
}
// clearCache (CleanUp)
function clearCache () {
  for (const elem in Cache.files) {
    Cache.files[elem] = "";
    Cache.remove(elem);
  }
}
// CleanUp
const cleanUp = () => {
  console.log("cleanUp");
  if (projectStore.virtualtours[tourId.value]?.images && Object.keys(projectStore.virtualtours[tourId.value].images).length > 0){
    scene.traverse(function (obj) {
      removeMaterial(obj);
      removeGeometry(obj);
    });
    clearCache();
    cancelAnimationFrame(animate.value);
    TWEEN.removeAll();
    dracoLoader.dispose();
    controls.dispose();
    gltfloader.unregister();
    renderer.dispose();
    // clearMeshMemory();

    // Remove the canvas from DOM
    const container = document.getElementById("canvas-container");
    if (renderer?.domElement && container?.contains(renderer.domElement)) {
      container.removeChild(renderer.domElement);
    }

    // Reset the values
    renderer = null;
    scene = null;
    camera = null;
  }
};

/* Watch Area */
watch(rotationData, (val, old) => {
  console.log("watching rotaiton values", val, old);
  console.log(material.uniforms);
  if (val && val.x !== null && val.y !== null && val.z !== null && typeof val.x === 'number' && typeof val.y === 'number' && typeof val.z === 'number'  ){
    // Check for within the min & max range
    if ((val.x >= rotationMin && val.x <= rotationMax) && (val.y >= rotationMin && val.y <= rotationMax) && (val.z >= rotationMin && val.z <= rotationMax)){
      console.log("Watch If", val);
      material.uniforms.uRotation.value.set(val.x, val.y, val.z);
    }
  } else {
    console.log("Watch Else");
  }
}, {deep: true});

// Image id Route
watch(() => route.query.image_id, (val, old) => {
  if (val && val !== old){
    if (hotspotLabel.value.camera_name !== val){ // travel to image
      resetLabelFormValues();
      const hotspotItem = hotspots.filter((item) => item.name === val)[0];
      updateMaterial.value(hotspotItem.name, hotspotItem.position);
    }
  }
});

// Label id Route
watch(() => route.query.label_id, (val) => { // Only for multiple label flow
  console.log("Watch in openHotspot", val, route.query.label_id, props.openHotspot);
  if (val){
    if (!labelSetOn.value){
      labelSetOn.value = true; // Open the modal
    }
    prevData(projectStore.virtualtours[tourId.value]?.labels[route.query.label_id]);
  } else {
    resetLabelFormValues();
  }
});

// openHotspot (Props)
watch(() => props.openHotspot, (val) => {
  console.log("Watch in openHotspot", val, route.query.label_id);
  if (val){ // If open for  as create
    labelSetOn.value = true; // open the labelSetOn
  } else {
    resetLabelFormValues(); // reset values
  }
});

onMounted(() => {
  if (Object.keys(route.query).length > 0) { // If any query present reset
    router.replace({ path: route.path });
  }
  console.log("Component Mounted");
  init(); // Initialize
  document.addEventListener('mleCurrentLabelValues', getCurrentHotspotLabelValues); // Expose the mtd via event
});

onBeforeUnmount(() => {
  console.log("Component UnMounted");
  document.removeEventListener('mleCurrentLabelValues', getCurrentHotspotLabelValues); // Remove expose the mtd via event
  cleanUp(); // Cleanup
});

</script>
<template>
    <!-- No Tour Found -->
    <div v-if="!projectStore.virtualtours[tourId]?.images || !Object.keys(projectStore.virtualtours[tourId].images).length > 0" class="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center text-center text-black bg-white z-[4]">
              No Tour Found ! (Please upload images)
    </div>

    <!-- Viewer Loader -->
    <div v-if="projectStore.virtualtours[tourId]?.images && Object.keys(projectStore.virtualtours[tourId].images).length > 0 && viewerLoader" class="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center text-center text-white bg-[rgba(0,0,0,0.8)] z-[4]">
      <div class="loader" />
    </div>

    <!-- Viewer -->
    <div
        id="canvas-container"
        class="absolute top-0 left-0 w-full h-full"
        ref="containerVal"
    />

    <Button v-if="!isRotationSetOn && !labelSetOn" title="Set Orientation" theme="primary" class="h-3 w-fit absolute right-4 top-3 z-[3]"
                  @handleClick="handleOpenRotation">
                  <template v-slot:svg>
                      <svg class="w-3 h-3" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_308_25527)">
                          <path
                            d="M6 12C5.8446 12 5.69556 11.9383 5.58568 11.8284C5.47579 11.7185 5.41406 11.5695 5.41406 11.4141V0.585938C5.41406 0.430537 5.47579 0.281502 5.58568 0.171617C5.69556 0.0617325 5.8446 0 6 0C6.1554 0 6.30444 0.0617325 6.41432 0.171617C6.52421 0.281502 6.58594 0.430537 6.58594 0.585938V11.4141C6.58594 11.5695 6.52421 11.7185 6.41432 11.8284C6.30444 11.9383 6.1554 12 6 12Z"
                            fill="white" />
                          <path
                            d="M11.4141 6.58594H0.585938C0.430537 6.58594 0.281502 6.52421 0.171617 6.41432C0.0617325 6.30444 0 6.1554 0 6C0 5.8446 0.0617325 5.69556 0.171617 5.58568C0.281502 5.47579 0.430537 5.41406 0.585938 5.41406H11.4141C11.5695 5.41406 11.7185 5.47579 11.8284 5.58568C11.9383 5.69556 12 5.8446 12 6C12 6.1554 11.9383 6.30444 11.8284 6.41432C11.7185 6.52421 11.5695 6.58594 11.4141 6.58594Z"
                            fill="white" />
                        </g>
                        <defs>
                          <clipPath id="clip0_308_25527">
                            <rect width="12" height="12" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                    </template>
    </Button>

    <div v-if="isRotationSetOn" class="px-2.5 py-2 h-fit absolute right-3 top-3 z-[3] bg-white rounded w-fit">
            <p class="text-black text-base font-semibold capitalize"> Set Orientation </p>
            <div class="flex flex-col justify-start items-end w-full gap-3 my-2">

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-xs text-txt-50 flex flex-row gap-2 justify-start items-center">X: <input type="range" id="rot_x" :min="rotationMin" :max="rotationMax" step="0.01" v-model.number="rotationData.x" class="w-[80%]"/>  </label>
                    <input type="number" :min="rotationMin" :max="rotationMax" class="input-primary text-black" v-model.number="rotationData.x" :onchange="(e) => handleFieldRangeChanges(e,'x')"/>
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-xs text-txt-50 flex flex-row gap-2 justify-start items-center">Y: <input type="range" id="rot_y" :min="rotationMin" :max="rotationMax" step="0.01" v-model.number="rotationData.y" class="w-[80%]"/> </label>
                    <input type="number" :min="rotationMin" :max="rotationMax" class="input-primary text-black" v-model.number="rotationData.y" :onchange="(e) => handleFieldRangeChanges(e,'y')"/>
                </div>

                <div class="flex flex-col justify-start items-start w-full">
                    <label class="font-semibold text-xs text-txt-50 flex flex-row gap-2 justify-start items-center">Z: <input type="range" id="rot_z" :min="rotationMin" :max="rotationMax" step="0.01" v-model.number="rotationData.z" class="w-[80%]" /> </label>
                    <input type="number" :min="rotationMin" :max="rotationMax" class="input-primary text-black" v-model.number="rotationData.z" :onchange="(e) => handleFieldRangeChanges(e,'z')"/>
                </div>

                <div class="flex justify-end items-center gap-2 w-full h-fit mt-2">
                    <Button title="Close" type="button" theme="secondary" :disabled="rotationFormLoader" @handleClick="handleCloseRotation"> </Button>
                    <Button title="Save" type="button" theme="primary" :disabled="rotationFormLoader" @handleClick="handleSaveRotation">
                        <template v-if="rotationFormLoader" v-slot:svg>
                            <Spinner />
                        </template>
                    </Button>
                </div>
              </div>
    </div>

</template>

<style scoped>

.loader {
  border: 8px solid #343435;
  border-radius: 50%;
  border-top: 8px solid #f3f3f3;
  width: 60px;
  height: 60px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

</style>
