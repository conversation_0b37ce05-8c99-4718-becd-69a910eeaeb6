
import { defineStore } from 'pinia';
import { ListUsersInOrganization, ListProjectsFromOrganization, GetOrganization, GetNotifications } from '../api/organization/index';
import { GetLeads } from '../api/leads';
import type { Project } from '@/types/projects';
import type { Organization } from '@/types/organization';
import type { transformedMasterScene } from '@/types/masterScene';
import type { Leads } from '@/types/leads';
import type { Users } from '@/types/user';
import { getAllScenes } from '@/api/masterScene';
import type { Session } from '@/types/session';

interface Org_State {
    projects: Record<string, Project> | null;
    sessions: Record<string, Session> | null;
    users: Users[] | null;
    masterScenes:  transformedMasterScene | null;
    leads: object | null;
    role: null;
    invitation: null;
    masterSvgData: null;
    organization_data: Organization | null;
    createSession: boolean,
    notifications: object | null;
    newNotifications: object | null;
}

export const Org_Store = defineStore({
  id: 'organization_level',
  state: ():Org_State => ({
    projects: null,
    sessions: null,
    users: null,
    role: null,
    invitation: null,
    masterScenes: null,
    masterSvgData: null,
    organization_data: null,
    leads: null,
    createSession: false,
    notifications: [],
    newNotifications: [],
  }),
  actions: {
    SyncMultipleProjects (projects : Record<string, Project>) {
      this.projects = { ...this.projects, ...projects };
    },
    SyncMultipleSessions (sessions : Record<string, Session>) {
      this.sessions = { ...this.sessions, ...sessions };
    },
    RefreshProjects () {
      if (this.projects === null) {
        ListProjectsFromOrganization().then((projects) => {
          this.projects = projects;
        });
      }
    },
    ForceRefreshProjects () {
      ListProjectsFromOrganization().then((projects) => {
        this.projects = projects;
      });
    },

    SyncMultipleUsers (users: Users[]) {
      this.users = { ...this.users, ...users };
    },
    RefreshUsers () {
      if (this.users === null) {
        ListUsersInOrganization([]).then((users) => {
          this.users = users;
          console.log(this.users);
        });
      }
    },
    ForceRefreshUsers () {
      ListUsersInOrganization([]).then((users) => {
        this.users = users;
      });
    },

    // Master Scenes
    SyncMultipleMasterScenes (masterScenes: transformedMasterScene): void {
      this.masterScenes = { ...this.masterScenes, ...masterScenes };
    },
    RefreshMasterScenes ():void {
      if (this.masterScenes === null) {
        getAllScenes().then((masterScenes) => {
          this.masterScenes = masterScenes;
        });
      }
    },
    ForceRefreshMasterScenes (): void {
      getAllScenes().then((masterScenes) => {
        this.masterScenes = masterScenes;
      });
    },

    // Leads
    SyncMultiplelLeads (leads:Record<string, Leads>) {
      this.leads = { ...this.leads, ...leads };
    },
    RefreshLeads (param:string) {
      if (this.leads === null) {
        GetLeads(param).then((leads) => {
          console.log(param, leads);
          this.leads = leads;
        });
      }
    },
    ForceRefreshLeads (param:string) {
      GetLeads(param).then((leads) => {
        this.leads = leads;
      });
    },

    // Settings
    RefreshSettings () : void {
      if (!this.organization_data) {
        GetOrganization().then((settings) => {
          this.organization_data = settings;
        });
      }
    },
    ForceRefreshSettings (): void {
      GetOrganization().then((settings) => {
        this.organization_data = settings;
      });
    },
    FetchNotifications (user_id:string): void {
      GetNotifications(user_id).then((res) => {
        this.notifications = res ;
      });
    },
    FetchNewNotifications (user_id:string, viewed:boolean): void {
      GetNotifications(user_id, viewed).then((res) => {
        this.newNotifications = res ;
      });
    },
  },
});
