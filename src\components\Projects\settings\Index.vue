<script setup>
import { ClipboardDocumentCheckIcon } from '@heroicons/vue/24/outline';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { ProjectStore } from '../../../store/project.ts';
import GeneralForm from './GeneralForm.vue';
import AleForm from './AleForm.vue';
import PixelStreamForm from './PixelStreamForm.vue';
import EmbedForm from './EmbedForm.vue';
import SalesToolForm from './SalesToolForm.vue';
import ThemeForm from './ThemeForm.vue';
import HologramForm from './HologramForm.vue';
import UnitCardSelection from './UnitCardSelection.vue';
import Metadata from './Metadata.vue';

const route = useRoute();
const projectStore = ProjectStore();
const project_Id = ref(route.params.project_id); // Project id

/* Methods */

const handleClipBoardCopy = (id, copyMsgId, type) => {

  if (type === 'edit') {
    if (document.getElementById(id).value) {
      navigator.clipboard.writeText(document.getElementById(id).value);
    }
  } else {
    if (document.getElementById(id).innerHTML) {
      navigator.clipboard.writeText(document.getElementById(id).innerHTML);
    }
  }
  const messageDOM = document.getElementById(copyMsgId);
  messageDOM.style.visibility = 'visible';
  setTimeout(() => {
    messageDOM.style.visibility = 'hidden';
  }, 1000);

};

onMounted(async () => {
  project_Id.value = route.params.project_id;
  if (project_Id.value){
    projectStore.settings = null;
    projectStore.RefreshSettings(route.params.project_id);
  }
});
</script>

<template>

  <div class="relative bg-transparent h-full">

    <!-- Header -->
    <div class="mb-6 mt-2 w-full">
      <!-- Title -->
        <div class="dynamic-heading">
          <h3 class="dynamic-topic"> Project Settings </h3>
          <p class="dynamic-sub-topic"> edit project settings details </p>
        </div>
    </div>

    <!-- Body -->
    <div v-if="projectStore.settings !== null" class="mb-5">

      <!-- Normal -->
      <div class="grid grid-cols-3 gap-8 w-full mt-3">

        <div class="flex flex-col justify-start items-start gap-2 w-full">
          <label class="font-semibold text-sm text-txt-50"> ProjectId: </label>
          <div class="flex flex-row gap-2 justify-start items-center">
          <p class="font-medium text-sm text-txt-default" id="projectId"> {{ project_Id }} </p>
          <div class="relative">
                <ClipboardDocumentCheckIcon class="w-8 cursor-pointer text-zinc-900 p-[4px] border-l border-zinc-400"
                  @click="handleClipBoardCopy('projectId', 'copyMsg_View', 'view')" />
                <span id="copyMsg_View" class="copyMsgBox -left-3" style="visibility: hidden;">
                  Copied
                </span>
              </div>
               </div>
          <div>

          </div>
        </div>

        <div class="flex flex-col justify-start items-start gap-2 w-full">
          <label class="font-semibold text-sm text-txt-50"> Name: </label>
          <p class="font-medium text-sm text-txt-default"> {{ projectStore.settings.name }} </p>
        </div>

        <div class="flex flex-col justify-start items-start gap-2 w-full">
          <label class="font-semibold text-sm text-txt-50"> Description: </label>
          <p class="font-medium text-sm text-txt-default"> {{ projectStore.settings.description ?
            projectStore.settings.description : '-' }} </p>
        </div>

      </div>

      <!-- General Settings -->
      <GeneralForm/>

      <!-- Pixel Streaming Settings -->
      <PixelStreamForm/>

      <!-- Ale -->
      <AleForm/>

      <!--- UnitsCardsSelection-->
      <UnitCardSelection/>

      <!-- Embed v1 Settings -->
      <EmbedForm/>

      <!-- Sales Tool Settings -->
     <SalesToolForm/>

      <!-- Theme settings -->
     <ThemeForm/>

      <!-- Hologram -->
      <HologramForm/>

      <!-- Metadata -->
       <Metadata/>

    </div>

  </div>

</template>

<style>

/* Copy */
.copyMsgBox {
  background-color: #323232;
  color: white;
  position: absolute;
  margin-top: 10px;
  padding: 10px;
  font-size: 14px;
  border-radius: 30px;
  transition: all 900ms linear;

}

.copyMsgBox::before {
  content: '';
  position: absolute;
  left: 22px;
  top: -5px;
  display: block;
  width: 15px;
  height: 15px;
  background-color: #323232;
  border-top-left-radius: 4px;
  transform: rotate(45deg);
}

</style>
