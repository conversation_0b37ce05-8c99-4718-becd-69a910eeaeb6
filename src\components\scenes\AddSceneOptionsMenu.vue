
<script setup>
import { ref } from 'vue';
import CreateProjectScene from './CreateProjectScene.vue';

const openUploadFromPc = ref(false);

defineProps({
  category: {
    type: String,
    default: () => '',
  },
});

const emits = defineEmits(['close']);

const handleClose = () => {
  emits('close');
  openUploadFromPc.value=!openUploadFromPc.value;
};

</script>
<template>
    <div class="w-40 rounded-md shadow-lg border-[1px] border-gray-200 bg-white">
                <div>
              <div class="flex items-center gap-2 py-2 px-3 rounded-t-md cursor-pointer hover:bg-blue-50" @click="openUploadFromPc=!openUploadFromPc">
                <p class="text-sm text-gray-900">Upload from PC </p>
              </div>
                <CreateProjectScene :category="category" v-if="openUploadFromPc" class="fixed left-96 top-1/2 transform -translate-y-1/2" @close="handleClose"/>
                  </div>
                    <div class="flex items-center gap-2 py-2 px-3 cursor-pointer hover:bg-blue-50">
                   <p class="text-sm text-gray-900">Import from Files</p>
                  </div>
                    <div class="flex items-center gap-2 py-2 px-3 cursor-pointer hover:bg-blue-50">
                   <p class="text-sm text-gray-900">Upload Image Zip</p>
                  </div>
                    <div class="flex items-center gap-2 py-2 px-3 cursor-pointer rounded-b-md hover:bg-blue-50">
                   <p class="text-sm text-gray-900">Change Page Icon</p>
                  </div>
              <div></div>
            </div>
</template>
<style scoped></style>
