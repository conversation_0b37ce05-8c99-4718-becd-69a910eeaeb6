steps:

# Build the container image
- name: gcr.io/cloud-builders/docker
  args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api-frontend/$BRANCH_NAME:${COMMIT_SHA}', -f , uatDockerfile, '.']
  id: Building the container image


# Push the container image to Container Registry
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api-frontend/$BRANCH_NAME:${COMMIT_SHA}']
  id: Pushing the image to registry



# Deploy container image to Cloud Run
- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', '${_SERVICE_NAME}', '--image', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api-frontend/$BRANCH_NAME:${COMMIT_SHA}', '--region', 'asia-south1', '--platform', 'managed', "--allow-unauthenticated"]
