/* eslint-disable no-undef */
/** @type {import('tailwindcss').Config} */

/* Darker to Lighter (default considered as a 0%) */

const DarkerToLighter = {
    'default':'#000000',
    '50':"#0d0d0d",
    '100':"#1a1a1a",
    '150':"#262626",
    '200':"#333333",
    '250':"#404040",
    '300':"#4d4d4d",
    '350':"#595959",
    '400':"#666666",
    '450':"#737373",
    '500':"#808080",
    '550':"#8c8c8c",
    '600':"#999999",
    '650':"#a6a6a6",
    '700':"#b3b3b3",
    '750':"#bfbfbf",
    '800':"#cccccc",
    '850':"#d9d9d9",
    '900':"#e6e6e6",
    '950':"#f2f2f2",
    '1000':"#ffffff"
}

module.exports = {
    content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
    darkMode:"class",
    theme: {
            extend: {
                fontFamily: {
                    sans: ['Inter', 'sans-serif'],
                  },
                colors: {

                            bg:{
                                  ...DarkerToLighter,
                            },

                            txt:{
                                   ...DarkerToLighter,

                            },

                },
                borderRadius: {
                '25xl':'1.25rem'
                },
                zIndex:{
                '100':'100'
                }
            },
    },
    plugins: [
    require('@tailwindcss/aspect-ratio'),
    ],
};