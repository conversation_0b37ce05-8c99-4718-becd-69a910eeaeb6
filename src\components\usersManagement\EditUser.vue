<script setup>
import { AssignRoleToUser } from '@/api/organization';
// import { closeIcon } from '@/helpers/icons';
import { uiOperations } from '@/store/uiOperations';
import { editUserRoleInOrganization } from '@/validationSchema/user';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { ref } from 'vue';
import Spinner from '../common/Spinner.vue';
import { UserStore } from '@/store';
import { Org_Store } from '@/store/organization';

const Store = UserStore();
const uistore = uiOperations();
// const roles = ref(['Editor', 'Reader', 'Admin']);
const OrganizationStore = Org_Store();
const props = defineProps({
  userId: String,
  previousRole: String,
});

// const userId = ref();
const previousData = OrganizationStore.users[props.userId]?.role;

const emit = defineEmits(['closeEditModal']);
const loader = ref(false);
function handleForm (values){
  console.log("props", props.previousRole);
  console.log("form", values.role);

  if (props.previousRole !== values.role){
    console.log("altered role");

    loader.value = true;
    AssignRoleToUser(props.userId, values.role).then(() => {
    }).catch((err) => {
      console.log("Error in Editing Role", err);
      uistore.showToast('Error in Assigning role to user', 'error');
    }).finally(() => {
      loader.value = false;
      emit('closeEditModal');
    });
  }

}
console.log("props", props.previousRole);
</script>
<template>
        <div v-if="!Store.isMobile"  class="w-[220px] relative h-[170px] bg-white rounded-lg p-3 border border-gray-50 flex flex-col gap-3">
                <Form :validation-schema="editUserRoleInOrganization"  @submit="handleForm" class="flex flex-col gap-3">
                    <div class="flex gap-3">
                        <Field type="radio" name="role" value="admin" v-model="previousData" class="cursor-pointer" />
                        <p class="text-gray-700 text-sm font-normal">Admin</p>
                    </div>
                    <div class="flex gap-3">
                        <Field type="radio" name="role" value="editor" v-model="previousData" class="cursor-pointer" />
                         <p class="text-gray-700 text-sm font-normal">Editor</p>
                    </div>
                    <div class="flex gap-3">
                        <Field type="radio" name="role" value="reader" v-model="previousData" class="cursor-pointer"/>
                        <p class="text-gray-700 text-sm font-normal">Reader</p>
                    </div>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="role" />
                <div class="absolute bottom-5 right-5">
                    <button type="submit" class="w-fit h-[90px] hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
                        rounded-lg flex flex-row justify-center items-center gap-2">
                        <span class="text-center text-inherit whitespace-nowrap">Save</span>
                        <Spinner v-if="loader" />
                    </button>
                </div>
                </Form>
            </div>
      <div v-if="Store.isMobile" class="w-[200px] h-[170px] relative bg-white border border-gray-50 p-3 rounded-lg !z-20 ">
                <Form :validation-schema="editUserRoleInOrganization"  @submit="handleForm" v-slot="{values}" class="flex flex-col gap-3">
                    {{ console.log("edit form",values)
                     }}
                    {{ console.log("prev",previousData)
                     }}
                    <div class="flex gap-3">
                        <Field type="radio" name="role" value="admin" v-model="previousData" />
                        <p class="text-gray-700 text-sm font-normal">Admin</p>
                    </div>
                    <div class="flex gap-3">
                        <Field type="radio" name="role" value="editor" v-model="previousData" />
                         <p class="text-gray-700 text-sm font-normal">Editor</p>
                    </div>
                    <div class="flex gap-3">
                        <Field type="radio" name="role" value="reader" v-model="previousData" />
                        <p class="text-gray-700 text-sm font-normal">Reader</p>
                    </div>
                    <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="role" />
                <div class="absolute bottom-5 right-5">
                    <button type="submit" class="w-fit h-[40px] hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
                        rounded-lg flex flex-row justify-center items-center gap-2">
                        <span class="text-center text-inherit whitespace-nowrap">Save</span>
                        <Spinner v-if="loader" />
                    </button>
                </div>
                </Form>
      </div>
</template>

<style>

</style>
