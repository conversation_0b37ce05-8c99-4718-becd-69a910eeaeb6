<script setup>
import { isMasterScenePath } from '@/helpers/helpers';
import { getScene as getMasterScene } from '../../api/masterScene/index';
import { getScene } from '../../api/projects/scene/index.ts';
import { ProjectStore } from '@/store/project';
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import router from '@/router';
import { UserStore } from '@/store';
import { getCookie } from '@/helpers/domhelper';

const userStore = UserStore();
const route = useRoute();
const projectStore = ProjectStore();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const sceneDetails = ref();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const tourId = ref(null);

const preview_domain = import.meta.env.VITE_PREVIEW_DOMAIN;

const pathSegments = route.path.split('/');
const selectedOption = ref(pathSegments.indexOf('design')!==-1?'design':'dataCenter');

const openPageCategory = ref(false);
const CategoryScenes = ref([]);

if (route.params.tour_id && projectStore.virtualtours){
  if ((projectStore.virtualtours[route.params.tour_id]?.type ?? projectStore.virtualtours[route.params.tour_id]?.virtualtour_type) === 'MLE'){ // Only MLE
    tourId.value = route.params.tour_id;
  } else {
    tourId.value = null;
  }
}

function customSort (items) {
  // First, sort the items
  const sortedItems = [...items].sort((a, b) => {
    // Case 1: If both items have no order property
    if (a.order === undefined && b.order === undefined) {
      // If one is 'OTHER', it comes last
      if (a.category === 'OTHER') {
        return 1;
      }
      if (b.category === 'OTHER') {
        return -1;
      }
      return 0; // Keep original order for items with no order
    }

    // Case 2: If only one item has no order property, it goes to the end
    if (a.order === undefined) {
      return 1;
    }
    if (b.order === undefined) {
      return -1;
    }

    // Case 3: Both have order property
    // If one is 'OTHER', it goes last regardless of order
    if (a.category === 'OTHER' && b.category !== 'OTHER') {
      return 1;
    }
    if (a.category !== 'OTHER' && b.category === 'OTHER') {
      return -1;
    }

    // Case 4: Both have order and neither is 'OTHER' (or both are)
    // Sort by order
    return a.order - b.order;
  });

  // Then reassign order values sequentially
  return sortedItems.map((item, index) => {
    return { ...item, order: index + 1 };
  });
}

function createCategoryBasedSceneData (data) {
  // Create a map for categories
  const categoryMap = {};
  const DEFAULT_CATEGORY = "OTHER";
  projectStore.sidebarOptions && Object.values(projectStore.sidebarOptions).forEach((item) => {
    categoryMap[item._id] = {
      id: item._id,
      name: item.name,
      category: item._id,
      linked_scenes: [],
      draggable: true,
      isCategory: true,
      order: item.order?item.order:null,
    };
  });
  categoryMap.OTHER = {
    id: 'UNCATEGORIZED',
    name: `UNCATEGORIZED`,
    category: `UNCATEGORIZED`,
    draggable: false,
    isCategory: true,
    linked_scenes: [],
  };

  // Process each scene and add to appropriate category
  for (const sceneId in data) {
    const sceneData = data[sceneId].sceneData;
    console.log('sceneData', sceneData);

    // Assign to OTHER category if no category exists
    const category = sceneData.category || DEFAULT_CATEGORY;

    // Create simplified scene object
    const sceneObj = {
      id: sceneId,
      name: sceneData.name,
      type: sceneData.type,
      draggable: true,
      category: category,
    };

    // Initialize category in categoryMap if it doesn't exist
    if (!categoryMap[category]) {
      categoryMap[category] = {
        id: category,
        name: category,
        category: category,
        linked_scenes: [],
      };
    }

    // Add scene to its category
    categoryMap[category].linked_scenes.push(sceneObj);
  }

  return customSort(Object.values(categoryMap));
}
/* Setup Data */

// setupData (Modify based on your needs)
const setupData = (data) => {
  if (data){
    // Structuring your data's
    const convertedData = createCategoryBasedSceneData(data);

    console.log(convertedData);

    // Update the your component reference
    CategoryScenes.value = convertedData;
  }
};

// if(projectStore.scenes){
//   setupData(projectStore.scenes);
// }

watch([() => projectStore.scenes, () => projectStore.sidebarOptions], () => {
  if (projectStore.scenes && projectStore.sidebarOptions ) {
    setupData(projectStore.scenes);
  }
}, { immediate: true });

watch(() => route.params.project_id, (newVal) => {
  projectId.value = newVal;
  if (projectId.value){
    projectStore.RefreshSettings(projectId.value);
  }
});
const handleGetScene = () => {
  if (!projectId.value || !sceneId.value) {
    return;
  }
  if (isMasterScene.value){
    getMasterScene(sceneId.value).then((res) => {
      sceneDetails.value = res;
    });
  } else {
    getScene(projectId.value, sceneId.value).then((res) => {
      sceneDetails.value = res;
    });
  }
};

watch(() => route.params.scene_id, (newSceneId) => {
  if (newSceneId) {
    sceneId.value = newSceneId;
    console.log('Scene ID changed:', newSceneId);
    handleGetScene();
  } else {
    console.log('No sceneId in current route.');
  }
});

onMounted (() => {
  console.log("before getauth");
  userStore.GetAuth().then(() => {
    filteredOrganizations.value = userStore.user_data.organization_id.filter((item) => item._id === selectedOrg.value._id);
    if (filteredOrganizations.value){
      showLogo.value = true;
    }
  });
});

handleGetScene();

const handleCategoryClick = (category) => {
  console.log("category", category);
  if (category.linked_scenes.length>0){
    const sceneId = category.linked_scenes[0].id;
    router.push({ path: `/projects/${projectId.value}/design/scenes/${sceneId}` });
    openPageCategory.value = false;
  }
};

</script>
<template>
<div class="flex w-full h-11 p-1 justify-between ">
      <div class="flex h-full items-center">
      <router-link to="/projects" class="w-12 h-full flex justify-center items-center"><svg class="fill-black" width="31" height="18" viewBox="0 0 31 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.7438 17.0324C13.2952 17.4964 12.7215 17.5895 12.2009 17.624C11.1324 17.6947 10.0481 17.7269 8.98463 17.625C7.55984 17.4885 6.16456 17.1941 4.8973 16.4312C2.56698 15.0281 1.17005 13.034 0.693293 10.3387C0.431842 8.86071 0.549419 7.4402 1.05984 6.0719C2.0377 3.45036 3.92588 1.71815 6.54958 0.782845C7.52061 0.436683 8.5253 0.306157 9.56078 0.310403C13.5981 0.326971 17.6356 0.30913 21.673 0.319564C25.2524 0.328812 27.902 1.97394 29.5227 5.10984C31.1167 8.19383 30.6373 12.1795 28.0858 14.7466C26.5179 16.3241 24.7135 17.2718 22.4729 17.524C21.097 17.6789 19.7335 17.5523 18.3693 17.6533C17.9321 17.6856 17.8753 17.4993 17.8795 17.1291C17.8963 15.6472 17.8536 14.164 17.9019 12.6835C17.9225 12.0574 17.7918 11.5954 17.1915 11.357C16.8206 11.2097 16.585 10.6173 16.2067 10.7797C15.604 11.0383 14.9752 11.4114 14.5794 11.9116C14.3404 12.2137 14.5152 12.8476 14.5139 13.3325C14.5106 14.5784 14.5128 14.5784 13.3039 14.5784C12.2126 14.5784 12.2126 14.5784 12.2125 13.5214C12.2125 12.7038 12.2281 11.8856 12.2047 11.0686C12.1958 10.7556 12.2886 10.5309 12.5326 10.3534C13.655 9.53674 14.7736 8.71456 15.903 7.90751C16.0825 7.77925 16.2388 7.68195 16.5229 7.89695C17.7071 8.79291 18.9391 9.62583 20.1546 10.4803C20.3825 10.6404 20.34 10.8757 20.34 11.1013C20.3399 12.3023 20.3355 13.5033 20.3444 14.7043C20.3459 14.9018 20.2239 15.1675 20.6205 15.1976C22.7907 15.3626 24.7909 14.9347 26.2976 13.2728C28.6041 10.7288 28.649 7.22907 26.316 4.72081C25.3151 3.64477 24.0316 2.90664 22.4955 2.77043C18.6284 2.4275 14.753 2.71549 10.8828 2.61434C9.29511 2.57285 7.72212 2.63066 6.29487 3.42307C2.86499 5.32725 1.95432 9.43903 4.0305 12.3898C5.26731 14.1476 7.08409 15.2467 9.39821 15.1978C10.9052 15.166 12.4143 15.2237 13.9206 15.1776C14.6191 15.1563 14.6468 15.4896 14.4304 15.947C14.2544 16.319 14.1545 16.751 13.7438 17.0324Z"/>
</svg>
</router-link>
  <div class="flex items-center h-full gap-6">
<div class="flex items-center justify-center border border-gray-200 h-full rounded-l-md rounded-r-md">
  <router-link @click="selectedOption='dataCenter'" :to="`/projects/${projectId}/settings`" class="w-fit flex justify-center items-center h-full min-w-10 px-2 gap-2 border-r rounded-l-md border-gray-200 hover:bg-blue-50 cursor-pointer" :class="selectedOption!=='dataCenter'?'':'bg-blue-50'"><svg :class="selectedOption!=='dataCenter'?'fill-gray-500':'fill-blue-600'" class=" w-4 h-4" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_14880)">
<path d="M2.5 10.7386V12.1C2.5 13.6756 5.2665 14.5 8 14.5C10.7335 14.5 13.5 13.6756 13.5 12.1V10.7386C12.1745 11.7112 9.98306 12.1 8 12.1C6.01694 12.1 3.8255 11.7112 2.5 10.7386Z"/>
<path d="M2.5 7.1386V8.5C2.5 10.0756 5.2665 10.9 8 10.9C10.7335 10.9 13.5 10.0756 13.5 8.5V7.1386C12.1745 8.1112 9.98306 8.5 8 8.5C6.01694 8.5 3.8255 8.1112 2.5 7.1386Z"/>
<path d="M8 7.3C11.0376 7.3 13.5 6.22548 13.5 4.9C13.5 3.57452 11.0376 2.5 8 2.5C4.96243 2.5 2.5 3.57452 2.5 4.9C2.5 6.22548 4.96243 7.3 8 7.3Z"/>
</g>
<defs>
<clipPath id="clip0_723_14880">
<rect width="12" height="12" fill="white" transform="translate(2 2.5)"/>
</clipPath>
</defs>
</svg>
<p v-if="selectedOption==='dataCenter'" class="text-sm text-blue-600 font-medium">Data Center</p>
</router-link>
  <router-link @click="selectedOption='design'" :to="`/projects/${projectId}/design/scenes`" class="w-fit flex justify-center items-center h-full min-w-10 px-2 gap-2 hover:bg-blue-50 cursor-pointer" :class="selectedOption!=='design'?'':'bg-blue-50'"><svg width="16" height="17" :class="selectedOption!=='design'?'fill-gray-500':'fill-blue-600'" class=" w-4 h-4" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13 3.5H3V11.5H8V12.5H3C2.44772 12.5 2 12.0523 2 11.5V3.5C2 2.94772 2.44772 2.5 3 2.5H13C13.5523 2.5 14 2.94772 14 3.5V7.5H13V3.5ZM10 7.5V14.5L12 12.5H15L10 7.5Z"/>
<path d="M8 7.5V11.5H3V3.5H13V7.5H8Z"/>
</svg>
<p v-if="selectedOption==='design'" class="text-sm text-blue-600 font-medium">Design</p>
</router-link >

  <a :style="{ pointerEvents: sceneId || tourId ? 'auto' : 'none' }"
  :href="sceneId ?
         ( isMasterScene
             ? preview_domain + `/${getCookie('organization')}/masterscene/${sceneId}` : preview_domain + `/${getCookie('organization')}/projectscene/${projectId}/${sceneDetails?.sceneData.type==='rotatable_image_frame'?sceneDetails.sceneData.parent:sceneId}`)
          : ( tourId
             ? preview_domain + `/${getCookie('organization')}/${projectId}/tourview/${tourId}`
              : '#'
          )
          "
            target="_blank" type="button"
  class="w-fit flex justify-center min-w-10 items-center h-full border-l rounded-r-md border-gray-200 hover:bg-blue-50 cursor-pointer"><svg class="fill-gray-500 w-4 h-4" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3842 6.86054L4.88161 2.18183C4.6909 2.06364 4.47446 2.00095 4.25393 2.00001C4.0334 1.99907 3.8165 2.05993 3.62492 2.1765C3.43463 2.29049 3.27636 2.45669 3.16649 2.65789C3.05662 2.85909 2.99914 3.08797 3.00001 3.32084V12.6783C2.99914 12.9111 3.05662 13.14 3.16649 13.3412C3.27636 13.5424 3.43463 13.7086 3.62492 13.8226C3.81643 13.9397 4.0335 14.0009 4.25425 14C4.47499 13.9991 4.6916 13.936 4.88223 13.8173L12.3855 9.13854C12.5724 9.02274 12.7276 8.85657 12.8354 8.65669C12.9432 8.4568 13 8.23019 13 7.99954C13 7.76889 12.9432 7.54229 12.8354 7.3424C12.7276 7.14251 12.5724 6.97635 12.3855 6.86054H12.3842Z"/>
</svg>
</a>
</div>
<div class="flex items-center gap-2">
  <p v-if="projectStore.settings?.name" class="leading-tight text-sm font-normal text-gray-900">{{ projectStore.settings?.name }}</p>
 <div v-if="selectedOption==='design'" class="border border-gray-900 h-3 w-[1px]"></div>
  <p v-if="selectedOption==='design'" class="leading-tight text-sm font-normal text-gray-500 w-48 overflow-hidden text-ellipsis whitespace-nowrap">{{isMasterScene ? preview_domain + `/${getCookie('organization')}/masterscene/${sceneId}` : preview_domain + `/${getCookie('organization')}/projectscene/${projectId}/${sceneId}`}}</p>
</div>
</div>
</div>

<div v-if="selectedOption==='design'" class="flex justify-center items-center h-full gap-3 relative">
  <div class="flex justify-center items-center gap-2"><h class="text-sm font-semibold">{{sceneDetails?sceneDetails.sceneData.name:"Select Category"}}</h>
    <button @click="openPageCategory=!openPageCategory">
  <svg class="h4 w-4 fill-black" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>
</button>
<div v-if="openPageCategory" class="w-48 rounded-md shadow-lg border-[1px] border-gray-200 absolute  left-0 top-12 z-100 bg-white">
              <div @click="handleCategoryClick(item)" v-for="item,key in CategoryScenes" :key="key" :class="item.linked_scenes.length>0?'cursor-pointer':'cursor-default'" class="flex items-center gap-2 py-2 px-3 rounded-md cur  hover:bg-gray-100">
                <p  class="text-sm text-gray-900">{{item.name}}</p>
                  </div>
              <div></div>
            </div>
</div>
<!-- <div class="flex items-center border border-gray-200 h-full rounded-l-md rounded-r-md">
  <div class="w-fit flex justify-center items-center h-full min-w-10 border-r rounded-l-md border-gray-200 bg-blue-50"><svg class="fill-blue-600 stroke-blue-600 h-4 w-4" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_14924)">
<path d="M7.9 11H7.4H3.2C3.01435 11 2.8363 10.9263 2.70503 10.795L2.35147 11.1485L2.70502 10.795C2.57375 10.6637 2.5 10.4857 2.5 10.3V3.7C2.5 3.51673 2.57187 3.34087 2.7 3.2101V3.7V8.5V9H3.2H12.8H13.3V8.5V3.7V3.2101C13.4281 3.34087 13.5 3.51673 13.5 3.7V10.3C13.5 10.4857 13.4263 10.6637 13.295 10.795C13.1637 10.9263 12.9857 11 12.8 11H8.6H8.1V11.5V13.3V13.8H8.6H10.4C10.4265 13.8 10.452 13.8105 10.4707 13.8293C10.4895 13.848 10.5 13.8735 10.5 13.9C10.5 13.9265 10.4895 13.952 10.4707 13.9707L10.8243 14.3243L10.4707 13.9707C10.452 13.9895 10.4265 14 10.4 14H5.6C5.57348 14 5.54804 13.9895 5.52929 13.9707L5.17574 14.3243L5.52929 13.9707C5.51054 13.952 5.5 13.9265 5.5 13.9C5.5 13.8735 5.51054 13.848 5.52929 13.8293C5.54804 13.8105 5.57348 13.8 5.6 13.8H7.4H7.9V13.3V11.5V11ZM2.7101 3.2C2.84087 3.07187 3.01673 3 3.2 3H12.8C12.9833 3 13.1591 3.07187 13.2899 3.2H12.8H3.2H2.7101Z"/>
</g>
<defs>
<clipPath id="clip0_723_14924">
<rect width="12" height="12" fill="white" transform="translate(2 2.5)"/>
</clipPath>
</defs>
</svg>
</div>
  <div class="w-fit flex justify-center items-center h-full min-w-10 px-2 gap-2"><svg class="h-4 w-4 fill-gray-500 stroke-gray-500" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_14927)">
<path d="M3.72222 3H12.2778C12.6844 3 13 3.32295 13 3.7V13.3C13 13.677 12.6844 14 12.2778 14H3.72222C3.31562 14 3 13.677 3 13.3V3.7C3 3.32295 3.31562 3 3.72222 3ZM7.15178 12.9H8.69544C9.2999 12.9 9.80656 12.4169 9.80656 11.8C9.80656 11.1834 9.30084 10.7 8.69544 10.7H7.15178C6.54638 10.7 6.04067 11.1834 6.04067 11.8C6.04067 12.4166 6.54638 12.9 7.15178 12.9Z"/>
</g>
<defs>
<clipPath id="clip0_723_14927">
<rect width="12" height="12" fill="white" transform="translate(2 2.5)"/>
</clipPath>
</defs>
</svg>

</div>
  <div class="w-fit flex justify-center min-w-10 items-center h-full border-l rounded-r-md border-gray-200"><svg class="h-4 w-4 fill-gray-500 stroke-gray-500" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_14931)">
<path d="M11.2143 11.4H11.7143V10.9V4.9V4.4H11.2143H4.78571H4.28571V4.9V10.9V11.4H4.78571H11.2143ZM4.21774 13.783L3.87885 14.1461L4.21774 13.783C4.07378 13.6486 4 13.4741 4 13.3V3.7C4 3.52592 4.07378 3.35136 4.21774 3.217C4.36286 3.08155 4.56663 3 4.78571 3H11.2143C11.4334 3 11.6371 3.08155 11.7823 3.217C11.9262 3.35136 12 3.52592 12 3.7V13.3C12 13.4741 11.9262 13.6486 11.7823 13.783C11.6371 13.9184 11.4334 14 11.2143 14H4.78571C4.56662 14 4.36286 13.9184 4.21774 13.783ZM7.11013 12.9777L7.45129 12.6121L7.11013 12.9777C7.2664 13.1235 7.47142 13.2 7.67857 13.2H8.32143C8.52858 13.2 8.7336 13.1235 8.88987 12.9777L8.54871 12.6121L8.88987 12.9777C9.04731 12.8307 9.14286 12.6237 9.14286 12.4C9.14286 12.1763 9.04731 11.9693 8.88987 11.8223L8.54871 12.1879L8.88987 11.8223C8.7336 11.6765 8.52858 11.6 8.32143 11.6H7.67857C7.47142 11.6 7.2664 11.6765 7.11013 11.8223C6.95269 11.9693 6.85714 12.1763 6.85714 12.4C6.85714 12.6237 6.95269 12.8307 7.11013 12.9777Z"/>
</g>
<defs>
<clipPath id="clip0_723_14931">
<rect width="12" height="12" fill="white" transform="translate(2 2.5)"/>
</clipPath>
</defs>
</svg>

</div>
</div> -->
</div>
<div v-if="selectedOption==='design'" class="h-full flex items-center gap-4 w-72 ml-20 mr-3">
  <!-- <button  class="flex items-center h-full text-black text-xs font-medium gap-3 px-3 bg-gray-200 rounded-md cursor-pointer"><svg class="h-4 w-4 fill-green-500" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.5777 10.3333C4.4145 10.3341 4.25752 10.2574 4.14033 10.1197L1.18872 6.64789C1.12989 6.57827 1.08295 6.49528 1.05057 6.40367C1.0182 6.31205 1.00103 6.21361 1.00005 6.11395C0.998053 5.91268 1.0621 5.7187 1.1781 5.57468C1.2941 5.43065 1.45254 5.34838 1.61858 5.34597C1.78462 5.34355 1.94464 5.42119 2.06346 5.5618L4.5802 8.5209L9.9361 2.21567C10.0551 2.07506 10.2153 1.9975 10.3814 2.00006C10.5476 2.00262 10.7061 2.08508 10.8221 2.2293C10.9381 2.37353 11.0021 2.56771 11 2.76911C10.9978 2.97052 10.9298 3.16267 10.8108 3.30328L5.01507 10.1197C4.89789 10.2574 4.74091 10.3341 4.5777 10.3333Z"/>
</svg>
Save
</button>
  <button class="flex items-center h-full text-white text-xs font-medium gap-3 px-3 bg-blue-600 rounded-md cursor-pointer">
    <svg class="h-4 w-4 fill-white" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.90065 3.24475L6.64345 2.50196C7.4798 1.6656 8.57475 1.31869 9.73355 1.262C10.1843 1.23995 10.4096 1.22892 10.5903 1.40964C10.7711 1.59036 10.76 1.81572 10.738 2.26645C10.6813 3.42525 10.3344 4.52021 9.49805 5.35655L8.75525 6.09935C8.14355 6.71105 7.96965 6.885 8.09805 7.5485C8.2248 8.05535 8.34745 8.54615 7.9789 8.9147C7.53185 9.36175 7.12405 9.36175 6.677 8.9147L3.08529 5.323C2.63824 4.87594 2.63823 4.46815 3.08529 4.0211C3.45383 3.65255 3.94464 3.77522 4.45148 3.90195C5.115 4.03037 5.28895 3.85645 5.90065 3.24475Z" />
<path d="M8.49609 3.5H8.50059" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1.25 10.75L3.75 8.25" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
<path d="M4.25 10.75L5.25 9.75" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
<path d="M1.25 7.75L2.25 6.75" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
</svg>

Publish
</button> -->
</div>

    </div>
</template>

<style scoped>

</style>
