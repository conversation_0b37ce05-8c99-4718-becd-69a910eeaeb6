<script setup>
import { ref } from 'vue';
import { Form } from 'vee-validate';
import Button from '../../common/Button.vue';
import { ProjectStore } from '../../../store/project.ts';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { useRoute } from 'vue-router';
import { UpdateProjectSettings } from '@/api/projects/settings';

const metadata = ref([]);
const projectStore = ProjectStore();
const loader = ref(false);
const isEdit = ref(false);
const previousData = ref(null);
const route = useRoute();
const project_Id = ref(route.params.project_id);

function addPair () { // Adding metadata in key and value pair
  metadata.value.push({});
}

function removePair (index) {
  metadata.value.splice(index, 1);
}

const handleMetadataChange = (e, ind) => {
  console.log(metadata.value);
  metadata.value[ind][e.target.name] = e.target.value;
  console.log(metadata.value);
};

const frameParms = (sourceObj, compareObj) => {
  if (!sourceObj) {
    return compareObj;
  }

  // If both objects are empty, return false
  if (Object.keys(sourceObj).length === 0 && Object.keys(compareObj).length === 0) {
    return false;
  }

  // If the objects are exactly the same, return false
  if (JSON.stringify(sourceObj) === JSON.stringify(compareObj)) {
    return false;
  }

  // For all other cases, return the compareObj
  return compareObj;
};

const setupDataCallBack = (values) => {
  console.log(values);
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      metadata: (data.projectSettings?.metadata ? data.projectSettings?.metadata : null),
    };

    if (data.projectSettings?.metadata){
      if (Object.keys(data.projectSettings?.metadata).length){
        Object.entries(data.projectSettings?.metadata).forEach(([key, value]) => metadata.value = [{key, value}, ...metadata.value]);
      }
    }
  }
};

const handleForm = () => {  // To handle unit addition or updation
  const metadataObj = {};  // To transform metadata array into one object in key and value pair
  metadata.value.forEach((elem) => {
    if (elem.key && elem.value){
      metadataObj[elem.key] = elem.value;
    }
  });
  if (frameParms(previousData.value.metadata, metadataObj)){
    loader.value = true;
    const payload = {
      project_id: project_Id.value,
      query: {
        [projectSettingsFormTypes.METADATA]: frameParms(previousData.value.metadata, metadataObj),
      },
    };
    console.log(payload);
    UpdateProjectSettings(payload)
      .then((res) => {
        if (res){
          metadata.value = [];
          projectStore.settings.projectSettings[projectSettingsFormTypes.METADATA] = res.projectSettings[projectSettingsFormTypes.METADATA];
          setupDataCallBack(res);
          isEdit.value = false;
          loader.value = false;
        }
      })
      .catch((error) => {
        console.log(error);
        loader.value = false;
      });
  }
};

// Initialize
if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
    <div class="pb-10">
        <div class="flex justify-between items-center w-full mb-2">
            <label class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> Metadata: </label>
            <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
                @handle-click="() => isEdit = !isEdit">
                <template v-slot:svg>
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                    d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                    fill="white" />
                </svg>
                </template>
            </Button>

            <div v-if="isEdit" class="flex justify-start items-center gap-3">
                <Button title="Reset" class="!p-4 !h-10" type="button" theme="secondary" @handle-click="() => isEdit = !isEdit" :disabled="loader">
                </Button>
                <label for="addMetadata"
                :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer',(loader && `pointer-events-none opacity-50`)]">
                <div v-if="loader" class="loader"></div>
                Save </label>
            </div>
        </div>

        <!-- Table view when not in edit mode -->
        <div v-if="!isEdit" class="relative h-fit flex-col justify-start items-start inline-flex bg-inherit">
            <div style="width: 300px; background-color: #737373 !important"
                class="z-10 mt-2 top-[4.4rem] rounded-md p-1 bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <div v-if="metadata.length > 0">
                    <div class="flex items-center gap-1.5 mb-1"
                        v-for="(item, index) in metadata"
                        :key="index">
                        <div class="rounded-sm w-6 p-1 text-sm flex-1 bg-white text-black">{{ item.key }}</div>
                        <div class="rounded-sm w-7 p-1 text-sm flex-1 bg-white text-black">{{ item.value }}</div>
                    </div>
                </div>
                <div v-else class="text-white p-2 text-sm">
                    No metadata available
                </div>
            </div>
        </div>

        <!-- Edit form -->
        <Form v-if="isEdit" @submit="handleForm">
            <Menu
              as="div"
              class="relative h-fit flex-col justify-start items-start inline-flex bg-inherit"
            >
                <MenuItems
                  style="width: 300px; background-color: #737373 !important"
                  class="z-10 mt-2 top-[4.4rem] rounded-md p-1 bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                >
                  <div
                    class="flex items-center gap-1.5 mb-1"
                    v-for="(elem, index) in metadata"
                    :key="index"
                    v-bind="field"
                  >
                    <input
                      class="rounded-sm w-6 p-1 text-sm flex-1"
                      type="text"
                      name="key"
                      :value="elem.key"
                      @change="(e) => handleMetadataChange(e, index)"
                      placeholder="key"
                    />
                    <input
                      class="rounded-sm w-7 p-1 text-sm flex-1"
                      type="text"
                      name="value"
                      :value="elem.value"
                      @change="(e) => handleMetadataChange(e, index)"
                      placeholder="value"
                    />
                    <button
                      class="bg-[#1C74D0] text-white rounded-sm text-sm p-1"
                      type="button"
                      @click="removePair(index)"
                    >
                      remove
                    </button>
                  </div>
                  <button
                    class="w-full bg-[#1C74D0] text-white rounded-sm text-sm p-1 mt-0.5"
                    type="button"
                    @click="addPair(event)"
                  >
                    + Add
                  </button>
                </MenuItems>
            </Menu>
            <button
              type="submit"
              class="proceed-btn-primary !hidden"
              id="addMetadata"
            >
              Save
              <Spinner v-if="loader" />
            </button>
        </Form>
    </div>
</template>

<style lang="scss" scoped>

</style>
