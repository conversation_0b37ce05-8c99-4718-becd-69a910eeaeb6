
export enum Type {
    PROJECTSCENE = 'projectscene',
    MASTERSCENE = 'masterscene',
    GALLERY = 'gallery',
    UNITPLAN = 'unitplan',
    INVENTORY = 'inventory',
    CUSTOM = 'custom'
}
export type sidebarType = {
    _id: string,
    type:Type,
    name:string,
    scene_id?:string,
    icon_id:string,
    organization_id:string,
    link?: string
}

export type createSidebarType = {
    type:Type,
    name:string,
    scene_id?:string,
    icon_id:string,
    organization_id:string,
    link?: string
}
export type updatesidebarQuery ={
    id:string,
    type?:string,
    name?:string,
    scene_id?:string,
    icon_id?:string,
    order?:number,
    link?: string
}
export type updateSidebarType = {
    query:updatesidebarQuery[],
    project_id:string
}
export type updateSingleOption ={
    id:string,
    type?:string,
    name?:string,
    scene_id?:string,
    icon_id?:string,
    order?:number
    project_id:string,
    link?: string
}

export type sidebarTrashType = {
  sidebar_id : string[],
  timetimeStamp: string
}
