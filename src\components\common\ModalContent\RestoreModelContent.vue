<script setup>
import Spinner from '../Spinner.vue';

const emit = defineEmits(['closeModal', 'handleRestore']);

defineProps({
  dataName: String,
  loader: Boolean,
  // LoaderValue: String,
});

/* Methods */
const handleRestore = () => {
  emit('handleRestore');
};

</script>

<template>
<div  class="relative transform overflow-hidden rounded-lg bg-white sm:bg-opacity-10 backdrop-blur-xl  shadow-xl transition-all  lg:w-[20%] ">
                  <div class="p-3 sm:p-6 ">
                                        <div class="absolute top-2 right-3 cursor-pointer" @click=" () => emit('closeModal')">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g id="x" clip-path="url(#clip0_1158_6409)">
                          <path id="Vector" d="M7.0885 6.00001L11.1645 1.92399C11.2381 1.85295 11.2967 1.76798 11.3371 1.67403C11.3774 1.58007 11.3987 1.47902 11.3996 1.37677C11.4005 1.27452 11.381 1.17312 11.3423 1.07848C11.3035 0.983841 11.2464 0.897861 11.1741 0.825556C11.1018 0.753252 11.0158 0.696071 10.9211 0.657351C10.8265 0.618631 10.7251 0.599147 10.6228 0.600035C10.5206 0.600924 10.4195 0.622167 10.3256 0.662526C10.2316 0.702885 10.1467 0.761551 10.0756 0.835102L5.99961 4.91112L1.92359 0.835102C1.77835 0.694826 1.58383 0.617206 1.38192 0.618961C1.18001 0.620715 0.986864 0.701704 0.844085 0.844482C0.701307 0.987261 0.620319 1.18041 0.618564 1.38232C0.616809 1.58423 0.694429 1.77875 0.834705 1.92399L4.91072 6.00001L0.834705 10.076C0.761155 10.1471 0.702488 10.232 0.662129 10.326C0.621771 10.4199 0.600527 10.521 0.599638 10.6232C0.59875 10.7255 0.618234 10.8269 0.656954 10.9215C0.695674 11.0162 0.752855 11.1021 0.825159 11.1745C0.897464 11.2468 0.983445 11.3039 1.07808 11.3427C1.17272 11.3814 1.27413 11.4009 1.37638 11.4C1.47863 11.3991 1.57968 11.3778 1.67363 11.3375C1.76758 11.2971 1.85256 11.2385 1.92359 11.1649L5.99961 7.08889L10.0756 11.1649C10.2209 11.3052 10.4154 11.3828 10.6173 11.3811C10.8192 11.3793 11.0124 11.2983 11.1551 11.1555C11.2979 11.0127 11.3789 10.8196 11.3807 10.6177C11.3824 10.4158 11.3048 10.2213 11.1645 10.076L7.0885 6.00001Z" fill="#9CA3AF"/>
                          </g>
                          <defs>
                          <clipPath id="clip0_1158_6409">
                          <rect width="12" height="12" fill="white"/>
                          </clipPath>
                          </defs>
                        </svg>

                    </div>

                  <div>
                    <div class="flex flex-col justify-center items-center pb-0 border-b-[none]">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g id="exclamation" clip-path="url(#clip0_1158_6412)">
                          <path id="Vector" d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34873 18.9426 4.80688 17.0679 2.93215C15.1931 1.05741 12.6513 0.0029116 10 0ZM10 15C9.80222 15 9.60888 14.9413 9.44443 14.8315C9.27998 14.7216 9.15181 14.5654 9.07612 14.3827C9.00044 14.2 8.98063 13.9989 9.01922 13.8049C9.0578 13.6109 9.15304 13.4327 9.2929 13.2929C9.43275 13.153 9.61093 13.0578 9.80491 13.0192C9.99889 12.9806 10.2 13.0004 10.3827 13.0761C10.5654 13.1518 10.7216 13.28 10.8315 13.4444C10.9414 13.6089 11 13.8022 11 14C11 14.2652 10.8946 14.5196 10.7071 14.7071C10.5196 14.8946 10.2652 15 10 15ZM11 11C11 11.2652 10.8946 11.5196 10.7071 11.7071C10.5196 11.8946 10.2652 12 10 12C9.73479 12 9.48043 11.8946 9.2929 11.7071C9.10536 11.5196 9 11.2652 9 11V6C9 5.73478 9.10536 5.48043 9.2929 5.29289C9.48043 5.10536 9.73479 5 10 5C10.2652 5 10.5196 5.10536 10.7071 5.29289C10.8946 5.48043 11 5.73478 11 6V11Z" fill="#9CA3AF"/>
                          </g>
                          <defs>
                          <clipPath id="clip0_1158_6412">
                          <rect width="20" height="20" fill="white"/>
                          </clipPath>
                          </defs>
                      </svg>

                      <h5
                        class="mt-0 font-[bold] not-italic leading-[1.33] mb-0 tracking-[normal] text-base font-normal text-gray-500 text-center">
                        {{`Are you sure you want to restore this ${dataName}?`}} </h5>
                    </div>
                  </div>
                  <div class="block pt-2 pb-1 border-t-[none]">
                    <center class="flex justify-center mt-2">
                      <button @click="handleRestore()" type="button" :disabled="loader"
                        class="w-[12rem] sm:w-fit h-11 sm:h-10 rounded-xl sm:rounded  bg-[#c81e1e] m-0 px-3 text-sm  text-white font-semibold leading-6 inline-flex justify-center items-center gap-2">Restore
                        <Spinner v-if="loader" />
                        </button>
                      <button  @click=" () => emit('closeModal')" type="button"
                        class="ml-3 w-1/2 sm:w-fit h-11 sm:h-10 rounded-xl sm:rounded bg-white  m-0 px-3 text-sm  border border-gray-800 text-black font-semibold leading-6">No, cancel</button>
                    </center>
                  </div>

                  </div>
</div>
</template>

<style>

</style>
