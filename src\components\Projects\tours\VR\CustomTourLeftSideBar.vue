<script setup>
import { addImageToTour, addGroupToTour, deleteImageFromTour, duplicateTourImage, getTourById, updateGoupIdImg, updateSubGroupIdImg, updateTourImage, addSubGroup, updateTourGroup, updateSubGroup, deleteTourGroup, updateGroupOrder, deleteSubGroup, moveToUnCategory } from '@/api/projects/tours';
import Modal from '../../../../components/common/Modal/Modal.vue';
import NestedReOrder from '../../../../components/common/NestedReOrder.vue';
import DeleteModalContent from '../../../../components/common/ModalContent/DeleteModalContent.vue';
import router from '@/router';
import { ProjectStore } from '../../../../store/project';
import { nextTick, ref } from 'vue';
import { useRoute } from 'vue-router';
import CreateImageTourModal from '../CreateImageTourModal.vue';
import { getSortArrayFromObjects, resizeImage } from '@/helpers/helpers';
import { updateSubGroupOrder } from '../../../../api/projects/tours';

const route = useRoute();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const projectStore = ProjectStore();
const listOfTours = ref(null);
const nestedChildKey = ref('children');
const ungroupedIdReference = ref('Ungrouped');
const isopenDeleteGroupModal = ref(false);
const isopenDeleteSubGroupModal = ref(false);
const isopenCreateImageModal = ref(false);
const isopenDeleteImageModal = ref(false);
const deleteImageLoader = ref(false);
const createImageLoader = ref(false);
const deleteGroupLoader = ref(false);
const deleteSubGroupLoader = ref(false);
const generalUpdationIndicator = ref(null);
const currentSelectionGroupId = ref(null);
const currentSelectionSubGroupId = ref(null);
const deleteImageId = ref(null);
const replaceImageId = ref(null);
const imageClickTimer = ref(null);
const editImageNameId = ref(null);
const inputImageLabelRef = ref(null);
const editGroupNamesInfo = ref(null);
const inputGroupNameRef = ref(null);

// Get project and tour id from route
console.log(tourId.value);

// Find the possible image Object in List of Tours in ascending look through as default
const getPossibleNearestImageFromNested = (listOfItems, startImageId, isDescendingSearch) => {
  console.log("getPossibleNearestImageFromNested", listOfItems, startImageId);
  const reverseNestedItems = (items) => { // Recursively clone and reverse items in all level's (For Descending search)
    return [...items]
      .reverse()
      .map((item) => {
        const newItem = { ...item };
        if (newItem[nestedChildKey.value] && newItem[nestedChildKey.value].length > 0) {
          newItem[nestedChildKey.value] = reverseNestedItems(newItem[nestedChildKey.value]);
        }
        return newItem;
      });
  };
  let foundStart = startImageId ? false : true; // startImage Id tells from where to start the loop of search images. (closure)
  const findImage = (items) => {
    for (const item of items) {
      if (!foundStart && item.id === startImageId){ // If we haven't started yet, check if this is the start point
        foundStart = true;
        continue; // Skip processing for this item & continue to next
      }

      if (foundStart){ // If start point is found then search for imageObject
      // Image type
        if (item.type === 'image') {
          return item;
        }
        // Look through the subGroup, If it has children
        if (item[nestedChildKey.value] && item[nestedChildKey.value].length > 0) {
          const found = findImage(item[nestedChildKey.value]);
          if (found) {
            return found;
          }
        }
      } else {
        if (item[nestedChildKey.value] && item[nestedChildKey.value].length > 0) {
          const found = findImage(item[nestedChildKey.value]);
          if (found){
            return found;
          }
        }

      }
    }

    return null;
  };
  return findImage(!isDescendingSearch ? listOfItems : reverseNestedItems(listOfItems));
};

// SetUpData
const setUpData = (data) => {
  if (data){

    /* Reset */
    if (currentSelectionGroupId.value){
      currentSelectionGroupId.value = null;
    }

    if (currentSelectionSubGroupId.value){
      currentSelectionSubGroupId.value = null;
    }

    if (deleteImageId.value){
      deleteImageId.value = null;
    }

    if (replaceImageId.value){
      replaceImageId.value = null;
    }

    if (generalUpdationIndicator.value){
      generalUpdationIndicator.value = null;
    }

    /* Extact data and frame the objects, wiser (i.e, tree & node's structure) */
    console.log(data);
    let images = data?.images ? {...data.images} : undefined;
    const groupedItems = {};

    if (images){ // Update the type image to image Objects
      const modifiedImageObject = {};
      Object.keys(images).forEach((key) => {
        const cloneImageObj = {...images[key], type: 'image'};
        modifiedImageObject[key] = cloneImageObj;
      });
      images = modifiedImageObject;
    }

    // If, groups
    if (data?.groups){

      const groups = {...data.groups};

      Object.keys(groups).forEach((key) => {

        if (groups[key]){

          const group = {
            ...groups[key],
            id: groups[key]._id,
            type: 'group',
            [nestedChildKey.value]: {},
          };

          delete group._id; // Delete the _id key & value

          // Check for the Possible Subgroups, if there
          if (groups[key]?.subgroups){
            delete group.subgroups;  // Delete the subgroups in group Object

            Object.keys(groups[key].subgroups).forEach((subGroupKey) => {

              const subgroup = {
                ...groups[key].subgroups[subGroupKey],
                id: subGroupKey,
                [nestedChildKey.value]: {},
                groupId: key,
                type: 'subgroup',
              };

              delete subgroup._id; // Delete the _id key & value

              group[nestedChildKey.value] = {
                ...group[nestedChildKey.value],
                [subgroup.id]: subgroup,
              };
            });
          }

          if (images){

            Object.keys(images).forEach((imageKey) => {

              if (images[imageKey]?.groupId || images[imageKey]?.subGroupId){

                if ((images[imageKey]?.groupId && images[imageKey].groupId === group.id) && !images[imageKey]?.subGroupId){

                  // Append in groups children
                  group[nestedChildKey.value] = {
                    ... group[nestedChildKey.value],
                    [images[imageKey].id]: {...images[imageKey]},
                  };

                  delete images[imageKey];  // Delete the image Obj in images
                } else if ( (images[imageKey]?.groupId && images[imageKey]?.groupId === group.id) && (images[imageKey]?.subGroupId && group[nestedChildKey.value][images[imageKey]?.subGroupId]) ){
                  // Append in subGroups children
                  group[nestedChildKey.value][images[imageKey]?.subGroupId][nestedChildKey.value]= {
                    ... group[nestedChildKey.value][images[imageKey]?.subGroupId][nestedChildKey.value],
                    [images[imageKey].id]: {...images[imageKey]},
                  };

                  delete images[imageKey]; // Delete the image Obj in images
                }

              }
            });
          }

          groupedItems[group.id] = group; // Update the GroupedItems
        }
      });

    }

    // Left out images considered as unGroup, here..
    groupedItems[ungroupedIdReference.value] = {
      "id": ungroupedIdReference.value,
      "name": 'Ungrouped',
      "icon": null,
      "type": "group",
      "order": Object.keys(groupedItems).length + 1,
      [nestedChildKey.value]: {
        ...(images ?? {}),
      },
    };

    console.log(groupedItems);

    /* Convert to Array of Order */
    const convertedData = [...getSortArrayFromObjects(groupedItems, nestedChildKey.value, true)];
    console.log(convertedData);

    // Not draggable Item,
    if (convertedData){
      const foundIndex = convertedData.findIndex((item) => item.id === ungroupedIdReference.value);
      convertedData[foundIndex].draggable = false;
    }

    if (images){
      if (!route.query.image_id){ // If the route is not defined or undefined
        const selectImage =  getPossibleNearestImageFromNested(convertedData, null, false);
        console.log(selectImage);
        router.push({ path: route.path, query: { image_id: selectImage.id } }); // route to the last new Image Id
      }
    }
    /* Update the Reference */
    listOfTours.value = convertedData;
  }
};

// Intialize SetupData
if (projectStore.virtualtours){
  const getTourObject = projectStore.virtualtours[tourId.value];
  console.log(projectStore.virtualtours);
  console.log(getTourObject);
  console.log(getTourObject.images);
  setUpData(getTourObject);
}

// Find the base parent of targetKey in nested Objects of array
function findTargetObjectsBaseParentReference (list, targetKey, targetValue, nestedKey) {
  for (const item of list) {
    // Look for all equal child
    if (item[nestedKey]?.some((child) => child[targetKey] === targetValue)) {
      return item; // Found the parent
    }
    // Then, traverse to nested child and search again
    if (item[nestedKey]) {
      const found = findTargetObjectsBaseParentReference(item[nestedKey], targetKey, targetValue, nestedKey);
      if (found) {
        return found;
      }
    }
  }
  return null; // No parent found (means it's at the root level)
}

// Get List of AffectItems By Order for Deletion of Images, Groups, Subgroups
const getAffectedItemsOrderByDeletion = (listOfItems, deleteItemId ) => {
  const affectedItems = [];

  if (listOfItems && deleteItemId){
    console.log(listOfItems, deleteItemId);

    const findOrderOfDeleteItem = listOfItems.find((item) => item.id === deleteItemId).order; // get the order of delete Item.
    console.log(findOrderOfDeleteItem);

    if (findOrderOfDeleteItem){
      listOfItems.forEach((item) => {
        if (item.order > findOrderOfDeleteItem){
          const itemClone = {...item};
          itemClone.order -=  1;
          affectedItems.push(itemClone);
        }
      });
    }
  }

  return affectedItems.length > 0 ? affectedItems : false;
};

// Image Selection
const handleImageSelection = (e, id) => {
  console.log(id);
  console.log(route.query.image_id );
  if (e.detail === 1){ // make sure only one click
    imageClickTimer.value = setTimeout(() => {
      if (route.query.image_id !== id){
        router.push({ path: route.path, query: { image_id: id } });
      }
      imageClickTimer.value = null; // reset the value
    }, 300);
  }
};

/* --- UpdateOrder Api - Images, Groups, Subgroups --- */
const updateOrdersApiCallBack = async (listOfItems) => {
  generalUpdationIndicator.value = "Updating orders..."; // indication
  const allPromises = [];
  for (const item of listOfItems){ // Sequential Execution
    console.log(item);
    if (item.type === 'image'){ // Image
      const updateImageOrderParms = {
        project_id: projectId.value,
        tour_id: tourId.value,
        image_id: item.id,
        order: item.order,
      };
      const response = await updateTourImage(updateImageOrderParms);
      console.log(response, "image");
      allPromises.push(response); // Collect the response
    } else if (item.type === 'group'){ // Group
      const updateGroupOrderParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        group_id: item.id,
        order: item.order,
      };
      const response = await updateGroupOrder(updateGroupOrderParams);
      console.log(response, "group");

      allPromises.push(response); // Collect the response
    } else if (item.type === 'subgroup'){ // SubGroup
      const updateSubGroupOrderParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        group_id: item.groupId,
        subgroup_id: item.id,
        order: item.order,
      };
      const response = await updateSubGroupOrder(updateSubGroupOrderParams);
      console.log(response, "subgroup" );
      allPromises.push(response); // Collect the response
    }
  }
  return allPromises.length > 0 ? allPromises : false;
};

/* --- Image --- */
// Create Image
const handleCreateImage = async (listOfImagesFiles) => {
  createImageLoader.value = true; // open the loader
  console.log("Form submitted");

  const listofUploadFiles = Object.values({...listOfImagesFiles});
  let previousResponseImages = projectStore.virtualtours[tourId.value]?.images ? projectStore.virtualtours[tourId.value].images : false; // initialially From Store
  let imageId; // ImageId, Created One at each loop
  const allPromises = [];
  let currentOrderLength;

  if (currentSelectionGroupId.value || currentSelectionSubGroupId.value){
    if (currentSelectionGroupId.value && currentSelectionSubGroupId.value) {
      // Subgroup
      console.log("Create Image ~ via Subgroups");
      const findTheBaseParent = listOfTours.value.find((item) => item.id === currentSelectionGroupId.value); // find the group
      if (findTheBaseParent?.[nestedChildKey.value]){
        currentOrderLength = findTheBaseParent[nestedChildKey.value].find((item) => item.id === currentSelectionSubGroupId.value)?.[nestedChildKey.value].length;
      }
    } else {
      // Group
      console.log("Create Image ~ via Groups");
      currentOrderLength = listOfTours.value.find((item) => item.id === currentSelectionGroupId.value)?.[nestedChildKey.value].length;
    }
  } else {
    // Ungrouped
    console.log("Create Image ~ via Ungroup");
    currentOrderLength = listOfTours.value.find((item) => item.id === ungroupedIdReference.value)?.[nestedChildKey.value].length;
  }

  console.log(currentOrderLength);

  for (const value of listofUploadFiles){
    // Api call
    console.log(value);

    try {
      currentOrderLength++;
      console.log(currentOrderLength);
      const frameParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        images: [{
          ...value,
          'order': Number(currentOrderLength),
        }],
      };
      const imageResponse = await addImageToTour(frameParams); // Image Api

      if (imageResponse){
        let response = imageResponse;
        const imageResponseData = response.images; // Extract the image response
        console.log(imageResponseData);

        if (previousResponseImages && Object.keys(previousResponseImages).length > 0){ // Check for image object is exist in prev-Data, compare
          imageId = Object.keys(imageResponseData).filter((key) => !(key in previousResponseImages))[0] ?? null; // Get the Id of created Image from response with comparison of prevData
        } else {
          imageId = Object.keys(imageResponseData)[0];
        }
        previousResponseImages = {...imageResponse.images}; // Catch the previous images object from image response

        if (currentSelectionGroupId.value || currentSelectionSubGroupId.value){
          console.log(previousResponseImages);
          console.log(response);

          if (imageId){
            const frameParms = {
              project_id: projectId.value,
              tour_id: tourId.value,
              image_id: imageId,
              ...(currentSelectionGroupId.value && {group_id: currentSelectionGroupId.value}),
              ...(currentSelectionSubGroupId.value && {subgroup_id: currentSelectionSubGroupId.value}),
            };
            console.log(frameParms, "FrameParms");

            // Group
            if (currentSelectionGroupId.value && !currentSelectionSubGroupId.value){
              const groupResponse = await updateGoupIdImg(frameParms); // Add Grp Id to Img Api
              console.log("groupResponse", groupResponse);
              response = groupResponse;
            }
            // Subgroup
            if (currentSelectionGroupId.value && currentSelectionSubGroupId.value){
              const subGroupResponse = await updateSubGroupIdImg(frameParms); // Add SubGrp Id to Img Api
              console.log("subGroupResponse", subGroupResponse);
              response = subGroupResponse;
            }
          }
        }

        allPromises.push(response);
      }
    } catch (error) {
      console.log(error);
      return;
    }
  }

  if (allPromises.length > 0){
    console.log(allPromises);
    Promise.all(allPromises).then((res) => {
      console.log(res);
      const finalResponse = res[res.length - 1];
      console.log(finalResponse);
      projectStore.SyncMultipleVirtualTours({ [finalResponse._id]: finalResponse}); // Sync the Store
      router.push({ path: route.path, query: { image_id: imageId } }); // route to the last new Image Id
      setUpData(finalResponse); // Call the setUpData
    }).finally(() => {
      createImageLoader.value = false; // close the loader
      isopenCreateImageModal.value = false; // reset the modal
    });
  }

};

// Upadte Image (Replace)
const handleReplaceImage = async (file, replaceImgId) => {
  console.log("handleReplaceImage", file, replaceImgId);
  replaceImageId.value = replaceImgId;

  const frameParms = {
    project_id: projectId.value,
    tour_id: tourId.value,
    image_id: replaceImgId,
    thumbnail: await resizeImage(file, 1280, 720),
    url: file,
  };

  console.log(frameParms);

  // Form Data
  const formData = new FormData();
  for (const key in frameParms){
    console.log("key", key);
    formData.append(key, frameParms[key]);
  }

  updateTourImage(formData).then((res) => {
    console.log("updateTourImage", res);
    projectStore.SyncMultipleVirtualTours({[res._id]: res}); // Sync the store
    router.push({ path: route.path, query: { image_id: replaceImgId } }); // route to the replaced Image Id
    setUpData(res); // Call the setUpData
  });
};

// Update Image Name
const handleImageNameChanges = (id) => {
  if (imageClickTimer.value){
    clearTimeout(imageClickTimer.value); // clear it timeout
    imageClickTimer.value = null; // reset the value
  }

  editImageNameId.value =  id;

  nextTick(() => {
    if (inputImageLabelRef.value){
      inputImageLabelRef.value.focus();
    }
  });
};

const focusOutImageNameChanges = async (e, prevData) => {
  const newName = e.target.value.trim();

  const resetEditValues = () => {
    editImageNameId.value = null;
    inputImageLabelRef.value = null;
  };

  if (newName.length === 0) { // If the value is emtpy
    resetEditValues();
    return true;
  }

  // compare
  if (newName !== prevData){ // If any changes,
    console.log("Values is changed");
    generalUpdationIndicator.value = "Updating the selected image name...";

    // changes
    const frameParms = {
      project_id: projectId.value,
      tour_id: tourId.value,
      image_id: editImageNameId.value,
      name: newName,
    };

    try {
      const response = await updateTourImage(frameParms);
      projectStore.SyncMultipleVirtualTours({[response._id]: response}); // Sync the store
      setUpData(response); // Call the setUpData
    } catch (err) {
      console.log("Error", err);
    }
  }

  resetEditValues();
  return true;
};

// SetOrientation Image
const handleOrientation = (id, prevData) => {
  if (id === route.query.image_id){ // check if the image is currently selected
    console.log(id, "Yes Id");
    if (AFRAME){
      const getOrientation = {...AFRAME.scenes[0].camera.el.getAttribute('rotation')}; // get the orientation in degrees
      const previousData = prevData; // Previous data
      const convertOrientationToString = `${String(getOrientation.x)} ${String(getOrientation.y)} ${String(getOrientation.z)}`; // convert the orientation in string

      // compare Previous orientation
      if (convertOrientationToString.trim() !== previousData.trim()){
        generalUpdationIndicator.value = "Updating the selected image rotation...";
        const frameParms = {
          project_id: projectId.value,
          tour_id: tourId.value,
          image_id: id,
          rotation: convertOrientationToString.trim(),
        };
        updateTourImage(frameParms).then((res) => {
          projectStore.SyncMultipleVirtualTours({[res._id]: res}); // Sync the store
          setUpData(res); // Call the setUpData
        });
      }
    }
  }
};

// Duplicate Image
const handleDuplicateImage = (imageObject) => {
  console.log("handleDuplicateImage", imageObject);
  generalUpdationIndicator.value = "Duplicating the image...";

  let updateOrderValue;

  if (!('groupId' in imageObject) || imageObject.groupId === ''){
    // some where in ungrouped
    updateOrderValue = listOfTours.value.find((item) => item.id === ungroupedIdReference.value)?.[nestedChildKey.value].length;
  } else {
    // Some where in group or subgroup
    if (imageObject.groupId && (!('subGroupId' in imageObject) || imageObject.subGroupId === '')){
      // Inside Group
      updateOrderValue = listOfTours.value.find((item) => item.id === imageObject.groupId)[nestedChildKey.value].length;
    } else if (imageObject.groupId && imageObject.subGroupId) {
      // Inside, subgroup
      const findTheBaseParent = listOfTours.value.find((item) => item.id === imageObject.groupId); // find the group
      if (findTheBaseParent[nestedChildKey.value]){
        updateOrderValue = findTheBaseParent[nestedChildKey.value].find((item) => item.id === imageObject.subGroupId)[nestedChildKey.value].length;// Find the subgroup
      }
    }
  }

  updateOrderValue++; // Increament of the order

  const frameParms = {
    project_id: projectId.value,
    tour_id: tourId.value,
    image_id: imageObject.id,
    targetorder: updateOrderValue,
  };

  duplicateTourImage(frameParms).then( async (res) => {
    console.log("duplicateTourImage", res);
    // projectStore.SyncMultipleVirtualTours({ [res._id]: res});
    const getTheCreatedImgId = Object.keys(res.images).filter((key) => !(key in projectStore.virtualtours[tourId.value].images))[0];
    projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
    router.push({ path: route.path, query: { image_id: getTheCreatedImgId } }); // route to the new duplicated Image Id
    setUpData(res); // Call the setUpData
  });
};

/* --- Groups & SubGroups --- */
// create group
const handleAddGroup = ()  => {
  let currentGroupLength = listOfTours.value.length - 1; // slice out the ungrouped
  currentGroupLength++;
  console.log(currentGroupLength);
  const frameParams = {
    project_id: projectId.value,
    tour_id: tourId.value,
    order: currentGroupLength,
    name: `unnamedGroup${currentGroupLength}`,
  };

  console.log(frameParams);
  addGroupToTour(frameParams).then((res) => {
    console.log(res);
    getTourById( projectId.value, tourId.value).then((result) => {
      console.log(result);
      projectStore.SyncMultipleVirtualTours({ [result._id]: result}); // Sync the Store
      setUpData(result); // Call the setUpData
    });
  });
};

// create subgroup
const handleAddSubgroup = (grpId) => {
  console.log("handleAddSubgroup", grpId);
  console.log(listOfTours.value.find(((item) => item.id === grpId)));
  let currentGroupChildrenLength = listOfTours.value.find(((item) => item.id === grpId))[nestedChildKey.value].length;
  currentGroupChildrenLength++;
  console.log(currentGroupChildrenLength);

  const frameParams = {
    project_id: projectId.value,
    tour_id: tourId.value,
    group_id: grpId,
    order: currentGroupChildrenLength,
    name: `unnamedSubgroup${currentGroupChildrenLength}`,
  };

  console.log(frameParams);

  addSubGroup(frameParams).then((res) => {
    console.log(res);
    projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
    setUpData(res); // Call the setUpData
  });
};

// Edit Name (Groups & SubGroups)
const handleEditGroupNames = (item) => {
  console.log(item, "handleEditGroupNames");
  editGroupNamesInfo.value = item;

  nextTick(() => {
    if (inputGroupNameRef.value){
      inputGroupNameRef.value.focus();
    }
  });
};

const focusOutGroupNameChanges = async (e, prevData) => {
  const newName = e.target.value.trim();

  const resetEditValues = () => {
    console.log("resetEditValues is called");
    editGroupNamesInfo.value = null;
    inputGroupNameRef.value = null;
  };

  if (newName.length === 0){ // If empty value
    resetEditValues();
    return true;
  }

  // Compare
  if (newName !== prevData){ // If any changes,
    if (editGroupNamesInfo.value.type === "group"){ // Group Edit
      const frameParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        group_id: editGroupNamesInfo.value.id,
        name: newName,
      };
      try {
        const response = await updateTourGroup(frameParams);
        if (response){
          console.log("response inside try", response);
          const tourResponse = await getTourById(projectId.value, tourId.value);
          projectStore.SyncMultipleVirtualTours({ [tourResponse._id]: tourResponse}); // Sync the Store
          console.log("tourResponseresponse inside try", tourResponse);
          setUpData(tourResponse); // Call the setUpData
        }
      } catch (err) {
        console.log("Error", err);
      }
    } else { // Subgroup Edit
      const frameParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        group_id: editGroupNamesInfo.value.groupId,
        subgroup_id: editGroupNamesInfo.value.id,
        name: newName,
      };
      try {
        const response = await updateSubGroup(frameParams);
        console.log("response inside try", response);
        projectStore.SyncMultipleVirtualTours({ [response._id]: response}); // Sync the Store
        setUpData(response); // Call the setUpData
      } catch (err) {
        console.log("Error", err);
      }
    }
  }

  resetEditValues();
  return true;
};

/* Delete (Images, Groups, SubGroups) */
const handleImageSelectionOnImageDeletion = () => {
  if (route.query.image_id === deleteImageId.value){
    console.log("The Same image selected you're are trying to delete", deleteImageId.value);
    const nextAscendingImageSelection = getPossibleNearestImageFromNested(listOfTours.value, deleteImageId.value, false); // Ascending Look Through
    console.log(nextAscendingImageSelection);
    if (nextAscendingImageSelection){
      router.push({ path: route.path, query: { image_id: nextAscendingImageSelection.id } }); // route to the replaced Image Id
    } else {
      const nextDescendingImageSelection = getPossibleNearestImageFromNested(listOfTours.value, deleteImageId.value, true); // Descending look through
      console.log(nextDescendingImageSelection);
      if (nextDescendingImageSelection){
        router.push({ path: route.path, query: { image_id: nextDescendingImageSelection.id } }); // route to the replaced Image Id
      } else {
        router.push({ path: route.path, query: {} }); // route to the replaced Image Id
      }
    }
    return true;
  }
  return false;
};

const handleDelete = () => {
  console.log("handleDelete");
  if (currentSelectionGroupId.value && !currentSelectionSubGroupId.value && !deleteImageId.value) { // Group
    console.log("handleDelete ~ Group");
    deleteGroupLoader.value = true;
    const listOfItems = [...listOfTours.value].filter((item) => item.id !== ungroupedIdReference.value); // slice out the ungrouped Alone
    console.log("listOfItems", listOfItems);
    const getTheAffectedItems = getAffectedItemsOrderByDeletion(listOfItems, currentSelectionGroupId.value);
    console.log("getTheAffectedItems", getTheAffectedItems);

    const frameParams = {
      project_id: projectId.value,
      tour_id: tourId.value,
      group_id: currentSelectionGroupId.value,
    };
    // Delete Group
    deleteTourGroup(frameParams).then(async (res) => {
      console.log('deleteTourGroup', res);
      if (getTheAffectedItems){
        console.log("getTheAffectedItems", getTheAffectedItems);
        // Update Orders
        const finalResponse = await updateOrdersApiCallBack(getTheAffectedItems);
        console.log(finalResponse);

        if (finalResponse){
          const lastResponse = finalResponse[finalResponse.length -1];
          console.log(lastResponse);
          projectStore.SyncMultipleVirtualTours({ [lastResponse._id]: lastResponse}); // Sync the Store
          setUpData(lastResponse); // Call the setUpData
        }

      } else {
        projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
        setUpData(res); // Call the setUpData
      }
      deleteGroupLoader.value = false; // close the loader
      isopenDeleteGroupModal.value = false; // close the model
    });
  } else if (currentSelectionGroupId.value && currentSelectionSubGroupId.value && !deleteImageId.value) { // SubGroup
    console.log("handleDelete ~ SubGroup");
    deleteSubGroupLoader.value = true;
    const findTheBaseParent = findTargetObjectsBaseParentReference([...listOfTours.value], 'id', currentSelectionSubGroupId.value, nestedChildKey.value); // Find the Base to get the direct childs By delete SubGroup Id
    console.log("Base parent", findTheBaseParent);
    const listOfItems =  findTheBaseParent ? findTheBaseParent[nestedChildKey.value] : false;
    console.log("Children", listOfItems);
    const getTheAffectedItems = listOfItems ? getAffectedItemsOrderByDeletion(listOfItems, currentSelectionSubGroupId.value) : false;
    console.log("getTheAffectedItems", getTheAffectedItems);

    const frameParams = {
      project_id: projectId.value,
      tour_id: tourId.value,
      group_id: currentSelectionGroupId.value,
      subgroup_id: currentSelectionSubGroupId.value,
    };

    deleteSubGroup(frameParams).then(async (res) => {
      console.log(res, "DeleteSubGroupFromTour");
      if (getTheAffectedItems){
        console.log("getTheAffectedItems If");
        // Update Orders
        const finalResponse = await updateOrdersApiCallBack(getTheAffectedItems);
        console.log(finalResponse);

        if (finalResponse){
          const lastResponse = finalResponse[finalResponse.length -1];
          console.log(lastResponse);
          projectStore.SyncMultipleVirtualTours({ [lastResponse._id]: lastResponse}); // Sync the Store
          setUpData(lastResponse); // Call the setUpData
        }
      } else {
        projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
        setUpData(res); // Call the setUpData
      }
      deleteSubGroupLoader.value = false; // close the loader
      isopenDeleteSubGroupModal.value = false; // close the model
    });
  } else { // Image
    console.log("handleDelete ~ Image");
    deleteImageLoader.value = true;
    const findTheBaseParent = findTargetObjectsBaseParentReference([...listOfTours.value], 'id', deleteImageId.value, nestedChildKey.value); // Find the Base to get the direct childs By delete Image Id
    console.log("Base parent", findTheBaseParent);

    const listOfItems =  findTheBaseParent ? findTheBaseParent[nestedChildKey.value] : false;
    console.log("Children", listOfItems);

    const getTheAffectedItems = listOfItems ? getAffectedItemsOrderByDeletion(listOfItems, deleteImageId.value) : false;
    console.log("getTheAffectedItems", getTheAffectedItems);

    const frameParams = {
      project_id: projectId.value,
      tour_id: tourId.value,
      image_id: deleteImageId.value,
    };

    deleteImageFromTour(frameParams).then(async (res) => {
      console.log(res, "DeleteImageFromTour");
      if (getTheAffectedItems){
        console.log("getTheAffectedItems If");
        // Update Orders
        const finalResponse = await updateOrdersApiCallBack(getTheAffectedItems);
        console.log(finalResponse);

        if (finalResponse){
          const lastResponse = finalResponse[finalResponse.length -1];
          console.log(lastResponse);
          projectStore.SyncMultipleVirtualTours({ [lastResponse._id]: lastResponse}); // Sync the Store
          handleImageSelectionOnImageDeletion(); // Image Selection
          setUpData(lastResponse); // Call the setUpData
        }
      } else {
        projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
        handleImageSelectionOnImageDeletion(); // Image Selection
        setUpData(res); // Call the setUpData
      }
      deleteImageLoader.value = false; // close the loader
      isopenDeleteImageModal.value = false; // close the model
    });
  }
};

/* --- MoveToUnGrouped (only for subgroups) */
const moveImagesToUnGrouped = (subgroup_id) => {
  console.log("moveImagesFromSubgroupToUnGrouped", subgroup_id);
  generalUpdationIndicator.value = "Moving the images to ungrouped...";
  const frameParams = {
    project_id: projectId.value,
    tour_id: tourId.value,
    subgroup_id: subgroup_id,
  };
  moveToUnCategory(frameParams).then((res) => {
    console.log(res, "MoveToUnCategory");
    projectStore.SyncMultipleVirtualTours({ [res._id]: res}); // Sync the Store
    setUpData(res); // Call the setUpData
  });
};

/* --- ReOrder Trigger's --- */
// Child Sort
const handleChildSortEmit = async (val) => {
  console.log("Handle Child Sort Emit", val);
  const { baseParentReference, draggedItem, sortedItems } = val; // destructure the val
  const combineItems = ([draggedItem, ...sortedItems]); // make the sortItems and draggaed Item into one.

  if (!baseParentReference){
    const check = sortedItems.find((item) => item.id === ungroupedIdReference.value); // Check ungrouped is moved
    if (check){
      const resetSortDataItems = [...listOfTours.value].sort((item1, item2) => item1.order - item2.order); // Reset by 'sort' again with it's original order
      setTimeout(() => { // set timeout for animation happenings
        console.log("Set TimeOut");
        listOfTours.value = resetSortDataItems;
      }, 100);
      return true;
    }
  }
  console.log(combineItems);
  const finalResponse = await updateOrdersApiCallBack(combineItems);
  console.log(finalResponse);
  if (finalResponse){
    const lastResponse = finalResponse[finalResponse.length -1];
    console.log(lastResponse);
    projectStore.SyncMultipleVirtualTours({ [lastResponse._id]: lastResponse}); // Sync the Store
    setUpData(lastResponse); // Call the setUpData
  }
  return true;
};

// Child Reparenting Sort
const handleChildReparentingSortEmit = async (val) => {
  console.log("Handle Child Reparenting Sort Emit", val);
  const { add, draggedItem, remove } = val; // destructure the val
  const { baseParentReference: addBaseParentReference, sortedItems: addSortItems } = add; // destructure the add
  const { Item } = draggedItem; // destructure the draggedItem
  const { baseParentReference: removeBaseParentReference, sortedItems: removeSortItems } = remove; // destructure the remove

  if (Item.type === 'subgroup' && (addBaseParentReference.id === ungroupedIdReference.value || addBaseParentReference.type === 'subgroup')){ // Check Subgroup has moved to ungrouped (or) Subgroup has moved to another subgroup.
    console.log("If~Id", Item);
    console.log(addBaseParentReference);
    // Reset the data
    setTimeout(() => { // set timeout for animation happenings
      console.log("Set TimeOut");
      setUpData({...projectStore.virtualtours[tourId.value]}); // get the value from store and frame it.
    }, 100);
    return true;
  }
  console.log("Else", val);
  let response = [];
  const updateItem = {...Item};
  // Image
  if (updateItem.type === 'image'){
    console.log("Type Image");
    // Check for possibilty & update (Groups , Subgroups)
    // Group
    if (removeBaseParentReference.type === 'group' && addBaseParentReference.type === 'group'){ // Update Group
      const frameParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        image_id: updateItem.id,
        group_id: addBaseParentReference.id !== ungroupedIdReference.value ? addBaseParentReference.id : '',
      };
      const groupResponse = await updateGoupIdImg(frameParams); // Add Grp Id to Img Api
      console.log("groupResponse", groupResponse);
      response.push(groupResponse);
    }
    // Subgroup
    if (removeBaseParentReference.type === 'subgroup' || addBaseParentReference.type === 'subgroup'){ // Update SubGroup
      const frameParams = {
        project_id: projectId.value,
        tour_id: tourId.value,
        image_id: updateItem.id,
      };

      if ((removeBaseParentReference.type === 'subgroup' && addBaseParentReference.type === 'subgroup') || (removeBaseParentReference.type === 'group' && addBaseParentReference.type === 'subgroup')){
        frameParams.group_id = addBaseParentReference.groupId;
        frameParams.subgroup_id = addBaseParentReference.id;
      }

      if (removeBaseParentReference.type === 'subgroup' && addBaseParentReference.type === 'group'){
        frameParams.group_id = addBaseParentReference.id === ungroupedIdReference.value ? '' : addBaseParentReference.id;
        frameParams.subgroup_id = '';
      }

      const subGroupResponse = await updateSubGroupIdImg(frameParams); // Add SubGrp Id to Img Api
      console.log("subGroupResponse", subGroupResponse);
      response.push(subGroupResponse);
    }
  }

  // SubGroup
  if (updateItem.type === 'subgroup'){
    console.log("Type Subgroup");

    // Update the groupId
    const frameParams = {
      project_id: projectId.value,
      tour_id: tourId.value,
      group_id: addBaseParentReference.id,
      subgroup_id: updateItem.id,
    };

    const subGroupResponse = await updateSubGroup(frameParams);
    console.log("subGroupResponse", subGroupResponse);
    response.push(subGroupResponse);

    // Update the groupId of updatedItem to new one's
    updateItem.groupId = addBaseParentReference.id;

    // If, Subgroups has children
    if (updateItem[nestedChildKey.value].length > 0){
      console.log("Subgroup children", updateItem);
      console.log(updateItem[nestedChildKey.value]);
      for (const imageItem of updateItem[nestedChildKey.value]){ // Sequential Execution
        const frameParams = {
          project_id: projectId.value,
          tour_id: tourId.value,
          image_id: imageItem.id,
          group_id: addBaseParentReference.id,
        };
        const groupResponse = await updateGoupIdImg(frameParams); // Add Grp Id to Img Api
        console.log("groupResponse", groupResponse);
        response.push(groupResponse);
      }
    }
  }

  // Then, Update the Order's for Affected Items and Dragged updateItem
  console.log("Yes, Affected Items");
  const combineItems = [updateItem, ...removeSortItems, ...addSortItems];
  console.log(combineItems);

  const updateResponse = await updateOrdersApiCallBack(combineItems);
  console.log(updateResponse);

  if (response){
    response = [ ...response, ...updateResponse];
  }

  // Update the Data's
  if (response.length > 0){
    console.log(response, "Update Data");
    const lastResponse = response[response.length -1];
    console.log(lastResponse);
    projectStore.SyncMultipleVirtualTours({ [lastResponse._id]: lastResponse}); // Sync the Store
    setUpData(lastResponse); // Call the setUpData
  }

  return true;
};

/* --- Menu's (Groups & Subgroups) --- */
const handleCloseGroupsMenu = () => {
  const groupMenuElement = document.querySelector("[data-name='groupsMenu']");
  groupMenuElement.remove(); // remove directly from dom
};

const templateMenu = (left, top, item, type ) => {
  let imageId = null;
  let groupId = null;
  let subGroupId = null;
  const div = document.createElement("div");
  div.setAttribute('data-name', 'groupsMenu');
  div.classList.add(...['w-[144px]', 'absolute', 'text-xs', 'bg-white', 'z-20', 'py-1', 'rounded-lg', 'whitespace-normal', '-translate-x-[40%]', 'mt-1', 'flex', 'flex-col', 'justify-start', 'items-start', 'shadow-md', 'outline-1', 'outline-gray-200']); /* groupId !== ungroupedIdReference.value && subGroupId && checkSubGroupsChildren ? 'w-[180px]' : 'w-[120px]' */
  div.style.left = `${left + 70}px`;
  div.style.top = `${top - 20}px`;

  if (type && type !== 'image'){ // Groups & Subgroups
    if (type === 'group'){ // group
      groupId = item.id;
    } else { // subgroup
      groupId = item.groupId;
      subGroupId = item.id;
    }

    let checkSubGroupsChildren = false;
    if (groupId && subGroupId){
      const findSubGroupObject = listOfTours.value.find((item) => item.id === groupId)?.[nestedChildKey.value].find((item) => item.id === subGroupId);
      if (findSubGroupObject[nestedChildKey.value].length > 0){
        checkSubGroupsChildren = true;
      } else {
        checkSubGroupsChildren = false;
      }
      console.log(checkSubGroupsChildren);
    }
    div.innerHTML =  `  
                <button class='text-gray-700 text-sm font-normal px-4 py-2 w-full text-left cursor-pointer' id="createImage_ByPopMenu">Upload</button> 
                ${ groupId !== ungroupedIdReference.value && !subGroupId ? '<button class="text-gray-700 text-sm font-normal px-4 py-2 w-full text-left cursor-pointer" id="createSubGroup_ByPopMenu">SubGroup</button> ' : '' }
                ${ groupId !== ungroupedIdReference.value && subGroupId && checkSubGroupsChildren ? '<button class="text-gray-700 text-sm font-normal px-4 py-2 w-full text-left cursor-pointer" id="moveToUnGrouped_ByPopMenu">Ungroup</button> ' : '' }            
                ${ groupId !== ungroupedIdReference.value ? '<button class="text-red-600 text-sm font-normal  px-4 py-2 w-full text-left cursor-pointer" id="deleteGroup&SubGroup_ByPopMenu">Delete</button>' : '' }
    `;
  }  else if (type && type === 'image') { // Image
    imageId = item.id;
    if (item?.groupId && !item?.subGroupId){ // group image
      groupId = item.groupId;
    } else if (item?.groupId && item?.subGroupId){ // subgroup image
      groupId = item.groupId;
      subGroupId = item.subGroupId;
    } else { // Ungrouped Image
      groupId=null;
      subGroupId=null;
    }
    div.innerHTML =  `
              <button class='text-gray-700 text-sm font-normal px-4 py-2 w-full text-left cursor-pointer' id="duplicateImage_ByPopMenu">Duplicate</button> 
              <label for="customImageReplace" class="block mb-0 text-gray-700 text-sm font-normal cursor-pointer px-4 py-2 w-full text-left" id="replaceImage_ByPopMenu">
                Replace
                <input id="customImageReplace" type="file" class="hidden"/>  
              </label> 
              <button class="text-red-600 text-sm font-normal  px-4 py-2 w-full text-left cursor-pointer" id="deleteImage_ByPopMenu">Delete</button> 
  `;
  } else { // null type (i.e for create group & ungrouped images creation)
    div.innerHTML =  `
              <button class='text-gray-700 text-sm font-normal px-4 py-2 w-full text-left cursor-pointer' id="createUngroupedImages_ByPopMenu">Upload</button> 
              <button class='text-gray-700 text-sm font-normal px-4 py-2 w-full text-left cursor-pointer' id="createGroup_ByPopMenu">Group</button> 
  `;
  }

  nextTick(() => {
    // Events for menu item's
    if (type && type !== 'image'){
      const addImage = document.getElementById('createImage_ByPopMenu');
      const addSubGroup = document.getElementById('createSubGroup_ByPopMenu');
      const deleteGroups_SubGroups = document.getElementById('deleteGroup&SubGroup_ByPopMenu');
      const moveToUnGrouped = document.getElementById('moveToUnGrouped_ByPopMenu');

      // Delete (Groups & Subgroups)
      if (deleteGroups_SubGroups){
        deleteGroups_SubGroups.addEventListener("click", () => {
          if (groupId && !subGroupId){
          // Group
            currentSelectionGroupId.value = groupId; // set delete groupId
            isopenDeleteGroupModal.value = true; // open the modal
          } else if (groupId && subGroupId){
          // SubGroup
            currentSelectionGroupId.value = groupId; // set delete currrenGroupId
            currentSelectionSubGroupId.value = subGroupId; // set delete currrenSubGroupId
            isopenDeleteSubGroupModal.value = true; // Open the modal
          }
        });
      }

      // Add SubGroup
      if (addSubGroup){
        addSubGroup.addEventListener("click", () => {
          handleAddSubgroup(groupId); // set currrenGroupId as parms to create subgroup
          handleCloseGroupsMenu(); // close immediately
        });
      }

      // Add Image
      if (addImage){
        addImage.addEventListener("click", () => {
          isopenCreateImageModal.value = true;
          if (groupId){
            if (subGroupId){
              currentSelectionSubGroupId.value = subGroupId;
            }
            currentSelectionGroupId.value = groupId;
          }
        });
      }

      // Move subgroup images to ungrouped
      if (moveToUnGrouped){
        moveToUnGrouped.addEventListener("click", () => {
          moveImagesToUnGrouped(subGroupId);
        });
      }
    } else if (type && type === 'image') {
      const duplicateImage = document.getElementById('duplicateImage_ByPopMenu');
      const replaceImage = document.getElementById('replaceImage_ByPopMenu');
      const deleteImage = document.getElementById('deleteImage_ByPopMenu');
      const fileUploadInp = document.getElementById("customImageReplace");

      // Duplicate Image
      if (duplicateImage){
        duplicateImage.addEventListener("click", () => {
          handleDuplicateImage(item);
          handleCloseGroupsMenu(); // close immediately
        });
      }

      // Replace Image
      if (replaceImage){
        fileUploadInp.addEventListener("change", (val) => {
          const file = val.target.files[0]; // file
          handleReplaceImage(file, imageId);
        });
      }

      // DeleteImage Image
      if (deleteImage){
        deleteImage.addEventListener("click", () => {
          isopenDeleteImageModal.value = true;
          deleteImageId.value = imageId;
        });
      }
    } else {
      const createUngroupedImages = document.getElementById('createUngroupedImages_ByPopMenu');
      const createGroup = document.getElementById('createGroup_ByPopMenu');

      if (createUngroupedImages){
        createUngroupedImages.addEventListener("click", () => {
          isopenCreateImageModal.value = true;
        });
      }

      if (createGroup){
        createGroup.addEventListener('click', () => {
          handleAddGroup();
          handleCloseGroupsMenu(); // close immediately
        });
      }
    }
  });
  div.addEventListener("mouseleave", () => handleCloseGroupsMenu()); // Close
  return div;
};

const handleOpenMenu = (e, item, type) => {
  document.body.appendChild(templateMenu(e.clientX, e.clientY + 15, item, type));
};

const handleMenus = (e, item, type) => {
  console.log(e, item, type);// Parms(event, item, type)
  console.log(document);
  if (document.querySelector('[data-name="groupsMenu"]')){
    console.log("Close");
    handleCloseGroupsMenu(); // Close
  } else {
    handleOpenMenu(e, item, type);
    /* if (id && groupId ){ // It can image or subgroup
      // Subgroups, Images
      handleOpenMenu(e, groupId, id, type);
    } else if(id && !groupId) { // It can image or group
      // Groups, Images
      handleOpenMenu(e, id, null, type);
    } else {
      // Ungrouped Images
       handleOpenMenu(e, null, null, type);
    } */
  }
};

/* --- Tooltip popUp --- */
/* const templateElementForTooltip = (left, top, msg) => {
  const div = document.createElement("div");
  div.setAttribute('data-name', 'toursImagesTooltip');
  div.classList.add(...['block', 'absolute', 'text-xs', 'bg-[#111111]', 'text-white', 'text-center', 'z-20', 'px-2', 'py-[3px]', 'rounded-[30px]', 'whitespace-normal', '-translate-x-[50%]', 'min-w-[auto]', 'max-w-[90px]', 'mt-2', 'custom_shadow']);
  div.style.left = `${left}px`;
  div.style.top = `${top}px`;
  div.innerText = msg; // message
  return div;
};

const handleOpenTooltip = (e, msg) => {
  document.body.appendChild(templateElementForTooltip(e.clientX, e.clientY + 15, msg));
};

const handleCloseTooltip = () => {
  const toolTipElement = document.querySelector("[data-name='toursImagesTooltip']");
  toolTipElement.remove(); // remove directly from dom
}; */

</script>

<template>
   <div
    :class="['h-full w-full flex flex-col justify-start items-start gap-3 bg-white p-2 relative py-3 rounded-t-lg']">

  <!--   <div class="absolute -right-10 top-12 bg-black w-[35px] h-10 rounded-r-full z-10 flex justify-end items-center pe-1">
      <svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 transition-all cursor-pointer " :class="[isVisibile ? 'rotate-90' : '-rotate-90']" viewBox="0 0 24 24" fill="none" @click="() => isVisibile = !isVisibile">
             <path d="M5.70711 9.71069C5.31658 10.1012 5.31658 10.7344 5.70711 11.1249L10.5993 16.0123C11.3805 16.7927 12.6463 16.7924 13.4271 16.0117L18.3174 11.1213C18.708 10.7308 18.708 10.0976 18.3174 9.70708C17.9269 9.31655 17.2937 9.31655 16.9032 9.70708L12.7176 13.8927C12.3271 14.2833 11.6939 14.2832 11.3034 13.8927L7.12132 9.71069C6.7308 9.32016 6.09763 9.32016 5.70711 9.71069Z" fill="white"/>
      </svg>
    </div> -->

    <div class="shrink-0 w-full flex items-center px-[6.1px] border-b border-gray-200 gap-1 pb-[7px]">

   <!--      <button class="w-10 h-full bg-black rounded flex justify-start items-center"
          @click="() => router.push(`/projects/${projectId}/tours`)">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 fill-white" viewBox="0 0 24 24">
            <g data-name="Layer 2">
              <g data-name="arrow-back">
                <rect width="24" height="24" transform="rotate(90 12 12)" opacity="0" />
                <path
                  d="M19 11H7.14l3.63-4.36a1 1 0 1 0-1.54-1.28l-5 6a1.19 1.19 0 0 0-.09.15c0 .05 0 .08-.07.13A1 1 0 0 0 4 12a1 1 0 0 0 .07.36c0 .05 0 .08.07.13a1.19 1.19 0 0 0 .09.15l5 6A1 1 0 0 0 10 19a1 1 0 0 0 .64-.23 1 1 0 0 0 .13-1.41L7.14 13H19a1 1 0 0 0 0-2z" />
              </g>
            </g>
          </svg>
        </button> -->

        <div class="w-full flex flex-row justify-between items-center">
          <div class="flex justify-start items-center gap-2 w-fit">
              <svg class="w-8 h-8 cursor-pointer" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" @click="() => router.push(`/projects/${projectId}/design/tours`)">
              <rect width="32" height="32" rx="8" fill="#F3F4F6"/>
              <path d="M14.042 16.0002L19.6829 10.4805C20.0859 10.0855 20.1079 9.4224 19.7309 8.99918C19.3549 8.57595 18.7199 8.55395 18.3179 8.94889L12.617 14.5283C12.219 14.918 12 15.4397 12 16.0002C12 16.5607 12.219 17.0823 12.616 17.4721L18.3169 23.0515C18.5099 23.2401 18.7549 23.3333 18.9999 23.3333C19.2679 23.3333 19.5339 23.2222 19.7299 23.0012C20.1069 22.578 20.0849 21.9159 19.6819 21.5199L14.042 16.0002Z" fill="#4B5563"/>
              </svg>
               <h2 class="!text-gray-600 !text-lg font-medium w-24 block text-ellipsis whitespace-nowrap overflow-hidden select-none"> {{ projectStore.virtualtours[tourId].name ?? projectStore.virtualtours[tourId].tour_name }}</h2>
          </div>

<!--           <Button title="Group" theme="secondary" class="!py-1 !px-3 w-fit" @handleClick="() => {isopenGroupModal = true; groupModalType = modalTypes.CREATE;}">
            <template v-slot:svg>
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_308_25527)">
                  <path
                    d="M6 12C5.8446 12 5.69556 11.9383 5.58568 11.8284C5.47579 11.7185 5.41406 11.5695 5.41406 11.4141V0.585938C5.41406 0.430537 5.47579 0.281502 5.58568 0.171617C5.69556 0.0617325 5.8446 0 6 0C6.1554 0 6.30444 0.0617325 6.41432 0.171617C6.52421 0.281502 6.58594 0.430537 6.58594 0.585938V11.4141C6.58594 11.5695 6.52421 11.7185 6.41432 11.8284C6.30444 11.9383 6.1554 12 6 12Z"
                    fill="black" />
                  <path
                    d="M11.4141 6.58594H0.585938C0.430537 6.58594 0.281502 6.52421 0.171617 6.41432C0.0617325 6.30444 0 6.1554 0 6C0 5.8446 0.0617325 5.69556 0.171617 5.58568C0.281502 5.47579 0.430537 5.41406 0.585938 5.41406H11.4141C11.5695 5.41406 11.7185 5.47579 11.8284 5.58568C11.9383 5.69556 12 5.8446 12 6C12 6.1554 11.9383 6.30444 11.8284 6.41432C11.7185 6.52421 11.5695 6.58594 11.4141 6.58594Z"
                    fill="black" />
                </g>
                <defs>
                  <clipPath id="clip0_308_25527">
                    <rect width="12" height="12" fill="black" />
                  </clipPath>
                </defs>
              </svg>
            </template>
        </Button> -->

        <svg @click.stop="(e) =>  handleMenus(e, null, null)" class="w-4 h-4 fill-[#4B5563] cursor-pointer"  viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg">
          <path d="M13.689 7.78886H8.71121V2.81109C8.71121 2.62249 8.63629 2.44161 8.50293 2.30826C8.36957 2.1749 8.1887 2.09998 8.0001 2.09998C7.8115 2.09998 7.63063 2.1749 7.49727 2.30826C7.36391 2.44161 7.28899 2.62249 7.28899 2.81109V7.78886H2.31121C2.12261 7.78886 1.94174 7.86378 1.80838 7.99714C1.67502 8.1305 1.6001 8.31138 1.6001 8.49998C1.6001 8.68857 1.67502 8.86945 1.80838 9.00281C1.94174 9.13617 2.12261 9.21109 2.31121 9.21109H7.28899V14.1889C7.28899 14.3775 7.36391 14.5583 7.49727 14.6917C7.63063 14.8251 7.8115 14.9 8.0001 14.9C8.1887 14.9 8.36957 14.8251 8.50293 14.6917C8.63629 14.5583 8.71121 14.3775 8.71121 14.1889V9.21109H13.689C13.8776 9.21109 14.0585 9.13617 14.1918 9.00281C14.3252 8.86945 14.4001 8.68857 14.4001 8.49998C14.4001 8.31138 14.3252 8.1305 14.1918 7.99714C14.0585 7.86378 13.8776 7.78886 13.689 7.78886Z" />
        </svg>
        </div>
    </div>

    <!-- Note:  Ungrouped can't be moved or Dragged (&) Subgroup can't be moved to ungrouped. (&) Subgroups can't be moved to another subgroup -->
   <!--  <p class="italic text-xs text-gray-300 font-normal mb-1.5"> <b> Note: </b> Ungrouped can't be moved or Dragged (&) Subgroup can't be moved to ungrouped. (&) Subgroups can't be moved to another subgroup </p> -->

    <div class="flex-1 h-full flex flex-col justify-start items-start overflow-hidden gap-2 w-full">
       <p class="italic text-xs text-gray-600 font-normal"> {{ generalUpdationIndicator ? generalUpdationIndicator : '' }} </p>
      <p v-if="projectStore.virtualtours === null || !projectStore.virtualtours[tourId] || !listOfTours" class="text-white w-full px-2"> No Data ! </p>
      <div v-else class="bg-transparent h-full w-full py-1.5 overflow-x-auto overflow-y-auto hide-scroll-bar">
            <div class="w-full bg-tranparent pb-1.5 px-2">
               <NestedReOrder commonClasses="!pl-0" v-model="listOfTours" groupName="tours" :allowChildReparenting="true" :allowChildSort="true" uniqueKey="id" :nestedChildKey="nestedChildKey" ghostClass="sampe_ghost" animationMilliSec="450" sortReferenceKey="id" @handleChildSort="(val) => handleChildSortEmit(val) " @handleChildReparentingSort="(val) => handleChildReparentingSortEmit(val)">
               <template #default="{item}">
                    <!-- Card (Structure) -->
                     <div v-if="item.type === 'image'" :class="['w-fit pr-2 !mb-3',!item?.groupId  ? 'pl-0' : !item?.subGroupId ? 'pl-4' : 'pl-9']">
                        <div @click="(e) => { handleImageSelection(e,item.id);}" @dblclick="() => handleImageNameChanges(item.id)" :class="['w-[176px] h-auto bg-transparent']">
                          <div :class="[' relative rounded-lg w-full bg-white flex flex-col justify-start items-start gap-0 shrink-0 overflow-hidden border-none shadow-md', route.query.image_id === item.id ? 'outline outline-2 outline-offset-0 outline-blue-600' : 'outline-none']">
                          <!-- <p class="position-absolute top-1 right-1 bg-black opacity-90 text-xs rounded-lg px-2 py-1 text-white">{{ item.order }}</p>  -->
                              <div class="h-28 w-full mb-0 relative">
                                <img :src="item.thumbnail" alt="" class="w-full h-full object-cover"/>
                                <div v-if="replaceImageId === item.id" class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-[rgba(0,0,0,0.6)]">
                                    <span class="loader"></span>
                                </div>
                              </div>
                          <div class="w-full h-6 flex justify-between items-center bg-transparent gap-1.5 !ps-2">
                                  <p v-if="editImageNameId !== item.id" class="text-black text-xs font-medium select-none overflow-hidden whitespace-nowrap text-ellipsis w-[60%] ps-[6px]"> {{ item.name }} </p>
                                  <input v-else ref="inputImageLabelRef" @click.stop @blur="(e) => {e.stopPropagation(); focusOutImageNameChanges(e,item.name);}" type="text" name="tourImageName" :value="item.name" :class="[`border-none rounded-lg  text-black text-xs font-medium bg-gray-100 flex items-center justify-start h-5 outline-none w-[60%] ps-[6px]`]"/>
                                  <div class="flex gap-1 w-fit shrink-0 justify-center items-center">
                                        <svg @click.stop="() => handleOrientation(item.id,item.rotation)" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" :class="['!w-4 !h-4' , item.id === route.query.image_id ? '' : ' opacity-30'  ]" fill="#313131" >
                                              <g data-name="Layer 2"><g data-name="compass"><rect width="24" height="24" opacity="0"></rect><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"></path><path d="M15.68 8.32a1 1 0 0 0-1.1-.25l-4.21 1.7a1 1 0 0 0-.55.55l-1.75 4.26a1 1 0 0 0 .18 1h.05A1 1 0 0 0 9 16a1 1 0 0 0 .38-.07l4.21-1.7a1 1 0 0 0 .55-.55l1.75-4.26a1 1 0 0 0-.21-1.1zm-4.88 4.89l.71-1.74 1.69-.68-.71 1.74z"></path>
                                              </g></g>
                                        </svg>
                                        <svg @click.stop="(e) =>  handleMenus(e, item, item.type)" class="w-6 h-6 cursor-pointer" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12.5 16C12.7761 16 13 16.2239 13 16.5C13 16.7761 12.7761 17 12.5 17C12.2239 17 12 16.7761 12 16.5C12 16.2239 12.2239 16 12.5 16ZM12.5 12C12.7761 12 13 12.2239 13 12.5C13 12.7761 12.7761 13 12.5 13C12.2239 13 12 12.7761 12 12.5C12 12.2239 12.2239 12 12.5 12ZM12.5 8C12.7761 8 13 8.22386 13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8Z" fill="#6B7280" stroke="#6B7280"/>
                                        </svg>
                                  </div>
                          </div>
                          </div>
                        </div>
                     </div>
                    <!-- Label (Structure) -->
                     <div v-else :class="['w-fit pr-2', item.name === ungroupedIdReference ? 'invisible !h-0' :'visible !h-8 !mb-3', item.type === 'subgroup' ? 'pl-9' : 'pl-4' ]" @dblclick="() => handleEditGroupNames(item)">
                      <div :class="['w-[176px] flex justify-between items-center  rounded-lg px-2 bg-blue-50 py-[6px]']" >
                        <div class="flex justify-start items-center gap-[10px] bg-transparent">
                          <svg class="w-4 h-4 shrink-0" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0.992769 5.80021L7.21283 11.6912C7.42164 11.8889 7.70482 12 8.00008 12C8.29534 12 8.57852 11.8889 8.78733 11.6912L15.0074 5.80021C15.1631 5.65272 15.2691 5.46483 15.312 5.26028C15.355 5.05574 15.3329 4.84373 15.2487 4.65106C15.1644 4.45838 15.0217 4.29369 14.8387 4.1778C14.6556 4.06192 14.4403 4.00004 14.2201 4H1.78002C1.55982 4.00004 1.34458 4.06192 1.16151 4.1778C0.97844 4.29369 0.835756 4.45838 0.751496 4.65106C0.667237 4.84373 0.645186 5.05574 0.688131 5.26028C0.731077 5.46483 0.83709 5.65272 0.992769 5.80021Z" fill="#6B7280"/>
                          </svg>
                          <div class="w-[100px] bg-transparent">
                               <input v-if="editGroupNamesInfo && (editGroupNamesInfo.id === item.id && editGroupNamesInfo.type === item.type)"  ref="inputGroupNameRef" type="text" name="tourGroupNames" :value="item.name" @click.stop @blur="(e) => {e.stopPropagation(); focusOutGroupNameChanges(e,item.name);}" :class="[`border-none text-gray-900 text-sm font-medium leading-tight bg-transparent flex items-center justify-start outline-none w-full h-fit`]">
                               <p v-else class="text-gray-900 text-sm font-medium leading-tight overflow-hidden whitespace-nowrap text-ellipsis w-full"> {{ item.name }} </p>
                          </div>
                        </div>
                        <svg class="w-4 h-4 shrink-0 cursor-pointer" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" @click.stop="(e) => handleMenus(e, item, item.type)">
                              <path d="M13.689 7.78886H8.71121V2.81109C8.71121 2.62249 8.63629 2.44161 8.50293 2.30826C8.36957 2.1749 8.1887 2.09998 8.0001 2.09998C7.8115 2.09998 7.63063 2.1749 7.49727 2.30826C7.36391 2.44161 7.28899 2.62249 7.28899 2.81109V7.78886H2.31121C2.12261 7.78886 1.94174 7.86378 1.80838 7.99714C1.67502 8.1305 1.6001 8.31138 1.6001 8.49998C1.6001 8.68857 1.67502 8.86945 1.80838 9.00281C1.94174 9.13617 2.12261 9.21109 2.31121 9.21109H7.28899V14.1889C7.28899 14.3775 7.36391 14.5583 7.49727 14.6917C7.63063 14.8251 7.8115 14.9 8.0001 14.9C8.1887 14.9 8.36957 14.8251 8.50293 14.6917C8.63629 14.5583 8.71121 14.3775 8.71121 14.1889V9.21109H13.689C13.8776 9.21109 14.0585 9.13617 14.1918 9.00281C14.3252 8.86945 14.4001 8.68857 14.4001 8.49998C14.4001 8.31138 14.3252 8.1305 14.1918 7.99714C14.0585 7.86378 13.8776 7.78886 13.689 7.78886Z" fill="#6B7280"/>
                        </svg>
                      </div>
                    </div>
               </template>
               </NestedReOrder>
            </div>
      </div>

    </div>

    <!-- Image -->
    <Modal :open="isopenCreateImageModal">
            <CreateImageTourModal
             :loader="createImageLoader"
             @closeModal="(e) => {isopenCreateImageModal = false; if(currentSelectionGroupId) { currentSelectionGroupId = null;} if(currentSelectionSubGroupId) { currentSelectionSubGroupId = null; }}"
             @handleCreate="handleCreateImage"
            />
    </Modal>

    <Modal :open="isopenDeleteImageModal">
            <DeleteModalContent
                :trash="false"
                :loader="deleteImageLoader"
                @closeModal="(e) => {isopenDeleteImageModal = false; deleteImageId = null;}"
                @handleDelete="handleDelete"
                dataName="Tour Image"
            />
    </Modal>

    <!-- Group -->
    <Modal :open="isopenDeleteGroupModal">
            <DeleteModalContent
                :trash="false"
                :loader="deleteGroupLoader"
                @closeModal="(e) => {isopenDeleteGroupModal = false; currentSelectionGroupId = null;}"
                @handleDelete="handleDelete"
                dataName="Group"
            />
    </Modal>

    <!-- SubGroup -->
    <Modal :open="isopenDeleteSubGroupModal">
            <DeleteModalContent
                :trash="false"
                :loader="deleteSubGroupLoader"
                @closeModal="(e) => {isopenDeleteSubGroupModal = false; currentSelectionGroupId = null; currentSelectionSubGroupId = null;}"
                @handleDelete="handleDelete"
                dataName="Sub Group"
            />
    </Modal>

  </div>
</template>

<style scoped>
.custom_shadow{
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}

.loader {
  border: 2px solid #2d2d2e;
  border-radius: 50%;
  border-top: 2px solid white;
  width: 40px;
  height: 40px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
