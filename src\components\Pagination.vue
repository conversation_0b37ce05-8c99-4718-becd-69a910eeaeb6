<script setup>
import { computed} from 'vue';
const props = defineProps({
  totalPages: {
    type: Number,
    required: true,
  },
  currentPage: {
    type: Number,
    required: true,
  },
});
const emits = defineEmits(['currentPageSync']);
const  pagesToShow = computed(() => {
  console.log("totoalPages", props.totalPages);
  console.log("currr", props.currentPage);
  const pages = [];
  const delta = 4; // Range of pages to show around the current page
  const left = Math.max(1, props.currentPage - delta);
  const right = Math.min(props.totalPages, props.currentPage + delta);
  console.log("left", left);
  console.log("right", right);
  // Add ellipsis and first/last page if necessary
  for (let i = left; i <= right; i++) {
    pages.push(i);
  }
  if (left > 1) {
    pages.unshift('...');
    pages.unshift(1);
  }
  if (right < props.totalPages) {
    pages.push('...');
    pages.push(props.totalPages);
  }
  return pages;
});

function goToPage (page) {
  if (page !== "..." && page > 0) {
    console.log("page", page);
    emits('currentPageSync', page);
  }
}
</script>
<template>
    <div class="flex h-[50%] rounded-tl-md rounded-bl-md rounded-tr-md rounded-br-md justify-center items-center relative right-8">
        <!-- Previous Button -->
        <div
            class="h-full w-[40px]  rounded-tl-md rounded-bl-md flex justify-center items-center px-1 py-1 border cursor-pointer"
            :class="{ 'opacity-50': currentPage === 1 }"
            @click="goToPage(currentPage - 1)"
        >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="chevron-left">
                    <path id="Vector" d="M7.98974 11.4C7.72508 11.3999 7.47128 11.2862 7.28416 11.0839L3.2922 6.76923C3.1051 6.56694 3 6.29263 3 6.0066C3 5.72058 3.1051 5.44626 3.2922 5.24398L7.28416 0.929282C7.37623 0.826258 7.48635 0.744082 7.60811 0.68755C7.72987 0.631018 7.86082 0.601261 7.99334 0.600016C8.12585 0.598772 8.25726 0.626064 8.37991 0.6803C8.50256 0.734537 8.61399 0.814632 8.70769 0.915912C8.8014 1.01719 8.8755 1.13763 8.92568 1.27019C8.97586 1.40276 9.00111 1.5448 8.99996 1.68802C8.99881 1.83125 8.97128 1.97279 8.91898 2.10439C8.86667 2.236 8.79064 2.35502 8.69532 2.45453L5.40894 6.0066L8.69532 9.55868C8.83485 9.70953 8.92987 9.90171 8.96836 10.1109C9.00685 10.3201 8.98709 10.537 8.91157 10.7341C8.83605 10.9311 8.70817 11.0996 8.54409 11.2181C8.38001 11.3366 8.1871 11.3999 7.98974 11.4Z" fill="#6B7280"/>
                </g>
            </svg>
        </div>
        <!-- Page Numbers -->
        {{ console.log("pagesToShow",pagesToShow) }}
        <div
            v-for="page in pagesToShow"
            :key="page"
            class="h-full w-[40px] flex items-center justify-center pagination-number px-1 border cursor-pointer text-black"
            :class="{ 'bg-[#e1effe] text-[#1a56db] ': currentPage === page }"
            @click="goToPage(page)"
        >
            {{ page }}
        </div>
        <!-- Next Button -->
        <div
            class="h-full w-[40px] rounded-tr-md rounded-br-md flex justify-center items-center px-1 py-1 border cursor-pointer"
            :class="{ 'opacity-50 ': currentPage === totalPages }"
            @click="goToPage(currentPage + 1)"
        >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="chevron-right">
                    <path id="Vector" d="M4.01026 11.4C3.8129 11.3999 3.61999 11.3366 3.45591 11.2181C3.29183 11.0996 3.16395 10.9311 3.08843 10.7341C3.01291 10.537 2.99315 10.3201 3.03164 10.1109C3.07013 9.90171 3.16515 9.70953 3.30468 9.55868L6.59106 6.0066L3.30468 2.45453C3.20936 2.35502 3.13333 2.236 3.08102 2.10439C3.02872 1.97279 3.00119 1.83125 3.00004 1.68802C2.99889 1.5448 3.02414 1.40276 3.07432 1.27019C3.1245 1.13763 3.1986 1.01719 3.29231 0.915912C3.38601 0.814632 3.49744 0.734537 3.62009 0.6803C3.74274 0.626064 3.87415 0.598772 4.00666 0.600016C4.13918 0.601261 4.27013 0.631018 4.39189 0.68755C4.51365 0.744082 4.62377 0.826258 4.71584 0.929282L8.7078 5.24398C8.8949 5.44626 9 5.72058 9 6.0066C9 6.29263 8.8949 6.56694 8.7078 6.76923L4.71584 11.0839C4.52872 11.2862 4.27492 11.3999 4.01026 11.4Z" fill="#6B7280"/>
                </g>
            </svg>
        </div>
    </div>
</template>
<style>
</style>
