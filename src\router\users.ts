import type { RouteRecordRaw } from "vue-router";

export default [
  {
    path: '/users',
    name: 'users',
    component: () => import('../views/users/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'create_user',
        components: {
          modal: () =>
            import('../components/usersManagement/AddMembers.vue'),
        },
      },
    ],
  },
] as Array<RouteRecordRaw>;
