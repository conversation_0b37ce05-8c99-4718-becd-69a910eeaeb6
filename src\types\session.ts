
// import { Leads } from './leads';
export enum SessionStatus {
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  ONGOING = 'on-going',
  ENDED = 'ended',
  ONHOLD = 'on-hold',
  CANCELLED = 'cancelled'
}
export enum SessionSource {
  API = 'api',
  DASHBOARD = 'dashboard'
}

export enum SessionType {
  DEFAULT = 'default',
  PIXELSTREAMING = 'pixel_streaming',
  ALE = 'ale'
}
export type CheckAvailableSessionSlotsResponse={
  activeSessions:number,
  availableSessions?:number
}
export type FetchSessionSlotsResponse={
  schedule_time:string,
  end_time:string,
  activeSessions:number,
  availableSession?:number
}
export type Session = {
  _id: string;
  duration_minutes: number;
  organization_id: string; //
  user_id: string; //
  project_id: string; //
  status: SessionStatus;
  code: string;
  start: string;
  type: string;
  invite_link: string;
  source: SessionSource;
  last_interaction_time:string,
  config:object,
  is_scheduled: boolean,
  schedule_time: string|null,
  end_time: string,
  scheduled_end_time:string,
  description:string,
  // participants:Leads[],
  thread_id?: string,
  tag?:string,
  pixel_streaming_link?:string,
  is_pixelstreaming_active:boolean,
};

export type Anonymous_Session = {
  _id: string;
  duration_minutes: number;
  organization_id: string; //
  project_id: string; //
  status: SessionStatus;
  code: string;
  start: string;
  type: string;
  invite_link: string;
  source: SessionSource;
  last_interaction_time:string,
  config:object,
  is_scheduled: boolean,
  schedule_time: string|null,
  end_time: string,
  description:string,
  participants:object[]
};

export type UpdateSessionInput = {
  session_id: string,
  lead_id?: string
  duration_minutes: number,
  is_pixelstreaming_active?: boolean
};
export type ExportSessionInput = {
  session_id:string,
  duration:number
}
export type createSessionInput = {
  project_id: string,
  type: string,
  source: SessionSource,
  is_scheduled: boolean,
  description: string,
  schedule_time: string|null,
  config:object,
  referrer:string | undefined,
  tag?:string
  timeSlot?:string,
}
export type CheckAvailableSessionSlotsType ={
  start_time:string,
  end_time:string
}
export type FetchSessionSlotsType ={
  selected_date:string,
}
export type GetAvailableSloteType = {
  project_id : string,
  date:string
}

export type SessionControl ={
  session_id:string,
}

export type getSlotsInput = {
  project_id: string,
  date: string,
  organization_id: string,
  zone:string
}
export type getMonthlySlotsInput = {
  project_id: string,
  from_date: string,
  to_date: string,
  organization_id: string | string[] | undefined,
  zone:string
}

export type sessionAnalyticsQuery = {
  organization_id? : string,
  user_id?: string,
  project_id?: string,
  min_duration? : string,
  max_duration? : string,
  status?: SessionStatus,
  type?: string,
  start_date?: string,
  end_date?: string,
  is_scheduled?: boolean,
  tag?:string,
  source?: SessionSource
}

export type GetAnalyticsType = {
  organization_id? : string,
  userId?: string,
  projectId?: string,
  min_duration? : string,
  max_duration? : string,
  status?: SessionStatus,
  type?: string,
  startDate?: string,
  endDate?: string,
  is_scheduled?: boolean,
  tag?:string,
  source?: SessionSource
}
export type bookSessionInput = {
  project_id: string,
  type: string,
  source: SessionSource,
  is_scheduled: boolean,
  description: string,
  organization_id: string,
  schedule_time: string|null,
  config:object,
  referrer:string | undefined
}
