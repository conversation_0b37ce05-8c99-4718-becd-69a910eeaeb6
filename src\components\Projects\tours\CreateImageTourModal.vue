<script setup>
import { ref } from 'vue';
import { resizeImage } from '../../../helpers/helpers';
import { useRoute } from 'vue-router';
import { getCookie } from '@/helpers/domhelper';
import { deleteObject, getDownloadURL, getStorage, ref as reference, uploadBytes } from 'firebase/storage';
import Spinner from '@/components/common/Spinner.vue';

const route = useRoute();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const filesProgess = ref(false);
const listOfUploadedFiles = ref({});
const localImagesStore = ref({});
const errorMsg = ref(null);
const emit = defineEmits(['closeModal', 'handleCreate']);
const storage = getStorage();
defineProps({
  loader: {
    type: Boolean,
    default: false,
  },
});

/* Methods */
// Local image read Url
const localImageReadUrl = (file) => URL.createObjectURL(file);

// Delete fireBase Item
const deleteFirebaseFile = async (obj) => {
  console.log("deleteFirebaseFile", obj);

  const results = Object.keys(obj).map(async (key) => {
    const fileRef = reference(storage, obj[key]);
    const result = await deleteObject(fileRef);
    console.log('File deleted successfully:' + obj[key], result);
    return result;
  });

  await Promise.all(results);

  console.log("yes", results);

  return results;
};

// Storage Urls
const convertFireBaseUrls = async (file) => {
  console.log(file, "convertFireBaseUrls", file.name);
  // Run Parallelly
  const timestamp = Date.now();
  const extension = (file) => file.name.substring(file.name.lastIndexOf('.'));

  const [thumbnailFile, originalFile] = await Promise.all([
    resizeImage(file, 1280, 720),
    Promise.resolve(file),
  ]);

  const thumbnailDest =  `CreationtoolAssets/${getCookie('organization')}/projects/${projectId.value}/tours/${tourId.value}/${thumbnailFile.name}`;
  const updatedThumbnailDest = thumbnailDest.substring(0, thumbnailDest.lastIndexOf('.')) + '_' + timestamp + extension(thumbnailFile);
  console.log("updatedThumbDestination", updatedThumbnailDest);
  const originalDest =  `CreationtoolAssets/${getCookie('organization')}/projects/${projectId.value}/tours/${tourId.value}/${originalFile.name}`;
  const updatedOriginalDest = originalDest.substring(0, originalDest.lastIndexOf('.')) + '_' + timestamp + extension(originalFile);
  console.log("updatedOrgDestination", updatedOriginalDest);

  const [originalSnap, thumbSnap] = await Promise.all([
    uploadBytes(reference(storage, updatedOriginalDest), originalFile),
    uploadBytes(reference(storage, updatedThumbnailDest), thumbnailFile),
  ]);

  const [originalURL, thumbURL] = await Promise.all([
    getDownloadURL(originalSnap.ref),
    getDownloadURL(thumbSnap.ref),
  ]);

  return {
    url: originalURL,
    thumbnail: thumbURL,
  };
};

// Error handler
const errorHandler = (files) => {
  const listOfFiles = files;
  console.log(listOfFiles);
  console.log(typeof listOfFiles);
  console.log(Array.isArray(listOfFiles));
  const formats = ['image/jpeg', 'image/jpg']; // only formats

  listOfFiles.some((item) => {
    // check size and formats
    if (!formats.includes(item.type)){
      errorMsg.value = 'Please check file format should be JPEG or JPG';
      return true;
    }
    errorMsg.value = null;
    return false;
  });
};

// File Change
const handleFileChange = async (e) => {
  console.log("handleFileChange");
  console.log(e);
  const files = Object.values(e.target.files);
  errorHandler(files); // error handler

  // Further,
  if (!errorMsg.value && files.length > 0){
    filesProgess.value = true;

    // push files
    await Promise.all(files.map(async (item, index) => {
      console.log("finalData");
      if (item){
        const urlResult = await convertFireBaseUrls(item);
        console.log(urlResult, "Result in " + index);
        const Obj = {
          name: item.name,
          rotation: '0 0 0',
          ...urlResult,
        };
        const key = `${index}_${Date.now()}`; // generate a id
        localImagesStore.value[key] = item; // local images
        listOfUploadedFiles.value[key] = Obj;
      }
      return true;
    }),
    );

    console.log("Yes Here Buddy !");

    filesProgess.value = false;
  }
  e.target.value = ''; // reset the files value in input file
};

const handleDeleteFile = async (id) => {
  console.log("handleDeleteFile", id);
  await deleteFirebaseFile({'url': listOfUploadedFiles.value[id].url, 'thumbnail': listOfUploadedFiles.value[id].thumbnail});
  console.log(id, "After deleteFirebaseFile");
  delete listOfUploadedFiles.value[id]; // delete the Obj for respect media
};

</script>

<template>

    <div class="modal-content-primary">
      <div class="p-3 sm:p-6 flex flex-col justify-start items-start gap-2 w-full">
        <div class="flex justify-between items-center w-full">
          <div>
            <h1 class="modal-heading-primary">Create Images</h1>
          <p class="modal-subheading-primary">
            Upload the images to create tour images.
          </p>
          </div>

          <div class="w-fit flex flex-col items-center justify-center mb-0">
                  <label for="uploadimages" class="bg-black p-3 rounded text-white text-center mb-0">
                      +  Upload Images
                  </label>

                   <input @change="handleFileChange" name="uploadimages" id="uploadimages" type="file" class="hidden" :disabled="loader" multiple/>
          </div>
        </div>

        <span class="text-xs font-semibold text-black">Please Note: <span class="italic font-normal text-gray-500"> Only jpeg/jpg are accepted.</span> </span>
        <p v-if="filesProgess && Object.keys(listOfUploadedFiles).length > 0" class="text-xs text-black text-center font-bold">  Please be patience, Uploading... </p>

        <p v-if="errorMsg" class="text-red-700 font-bold text-xs text-left w-full "> {{ errorMsg }}! </p>
        <!-- Images Uploaded -->
        <div class="w-full h-52 overflow-y-auto bg-transparent">

          <div v-if="Object.keys(listOfUploadedFiles).length > 0" class="grid grid-cols-4 gap-2 px-1">
                <div v-for="item,id in listOfUploadedFiles" :key="id" class="w-full h-fit bg-gray-200 rounded overflow-hidden flex flex-col justify-start items-start gap-2">
                    <img :src='localImageReadUrl(localImagesStore[id])' class="w-full h-28 object-cover"/>
                    <div class="flex justify-between items-center w-full gap-2 px-2 pb-2">
                      <p class="block w-full text-ellipsis whitespace-nowrap overflow-hidden ">{{item.name}}</p>
                      <svg @click="handleDeleteFile(id)" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 fill-red-600 cursor-pointer" viewBox="0 0 24 24" >
                            <path d="M20.25 4.5H16.5V3.75C16.5 3.15326 16.2629 2.58097 15.841 2.15901C15.419 1.73705 14.8467 1.5 14.25 1.5H9.75C9.15326 1.5 8.58097 1.73705 8.15901 2.15901C7.73705 2.58097 7.5 3.15326 7.5 3.75V4.5H3.75C3.55109 4.5 3.36032 4.57902 3.21967 4.71967C3.07902 4.86032 3 5.05109 3 5.25C3 5.44891 3.07902 5.63968 3.21967 5.78033C3.36032 5.92098 3.55109 6 3.75 6H4.5V19.5C4.5 19.8978 4.65804 20.2794 4.93934 20.5607C5.22064 20.842 5.60218 21 6 21H18C18.3978 21 18.7794 20.842 19.0607 20.5607C19.342 20.2794 19.5 19.8978 19.5 19.5V6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5ZM9 3.75C9 3.55109 9.07902 3.36032 9.21967 3.21967C9.36032 3.07902 9.55109 3 9.75 3H14.25C14.4489 3 14.6397 3.07902 14.7803 3.21967C14.921 3.36032 15 3.55109 15 3.75V4.5H9V3.75ZM18 19.5H6V6H18V19.5ZM10.5 9.75V15.75C10.5 15.9489 10.421 16.1397 10.2803 16.2803C10.1397 16.421 9.94891 16.5 9.75 16.5C9.55109 16.5 9.36032 16.421 9.21967 16.2803C9.07902 16.1397 9 15.9489 9 15.75V9.75C9 9.55109 9.07902 9.36032 9.21967 9.21967C9.36032 9.07902 9.55109 9 9.75 9C9.94891 9 10.1397 9.07902 10.2803 9.21967C10.421 9.36032 10.5 9.55109 10.5 9.75ZM15 9.75V15.75C15 15.9489 14.921 16.1397 14.7803 16.2803C14.6397 16.421 14.4489 16.5 14.25 16.5C14.0511 16.5 13.8603 16.421 13.7197 16.2803C13.579 16.1397 13.5 15.9489 13.5 15.75V9.75C13.5 9.55109 13.579 9.36032 13.7197 9.21967C13.8603 9.07902 14.0511 9 14.25 9C14.4489 9 14.6397 9.07902 14.7803 9.21967C14.921 9.36032 15 9.55109 15 9.75Z"/>
                            </svg>
                    </div>
                </div>
          </div>

          <div v-else class="h-full w-full flex justify-center items-center">
              <p v-if="!filesProgess" class="text-sm text-black text-center"> No Images Uploaded ! </p>
              <p v-else class="text-sm text-black text-center">  Please be patience, Uploading... </p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end items-center w-full gap-2">
          <button
              type="button"
              class="cancel-btn-primary"
              :disabled="loader"
              @click=" () => emit('closeModal')"
            >
              Cancel
            </button>
            <button
              type="button"
              class="proceed-btn-primary disabled:bg-gray-600"
              :disabled="(Object.keys(listOfUploadedFiles).length > 0 ? true : !errorMsg) && !filesProgess && Object.keys(listOfUploadedFiles).length > 0 && !loader  ? false : true"
              @click="() => emit('handleCreate',listOfUploadedFiles)"
            >
              Save
              <Spinner v-if="loader" />
            </button>
        </div>

      </div>
    </div>

</template>
