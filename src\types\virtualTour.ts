export enum projectType {
    AMENITIES = 'amenities',
    UNIT_PLAN = 'unit_plan',
    VIEWS = 'views',
    CUSTOM = 'custom'
  }
export enum SceneType {
    EQUIRECTANGULAR = 'equirectangular',
    CUBE_MAP = 'cube_map',

    MULTIRES = 'multires'
  }
export enum HotSpotType {
    SCENE = 'scene',
    INFO = 'info',
}
export enum tourCategory {
    INTERIOR = 'interior',
    EXTERIOR = 'exterior'
}
export enum tourType {
    CUSTOM = 'custom',
    EXTERNAL = 'external',
    MATTERPORT = 'matterport',
    MLE = 'MLE'
}
export type coordinate = {
    x: string,
    y: string,
    z: string
}
export type Link = {
    project_id?: string;
    tour_id?: string;
    image_id?: string;
    link_id?: string,
    position?: coordinate,
    text?: string,
    destination_img_id?: string
}

export type Image = {
    project_id?: string;
    tour_id?: string;
    image_id?:string;
    name: string,
    thumbnail: string,
    url: string,
    rotation?: string,
    links?: Link[],
    groupId?: string,
    subGroupId?: string,
    order?: number,
    group_id?:string,
    subgroup_id?:string
}

export type Label = {
    project_id?: string;
    tour_id?: string;
    label_id?:string;
    name?: string,
    camera_name?: string,
    camera_position?: coordinate,
    controls_target?: coordinate,
}

export type SubGroup= {
    project_id?: string,
    tour_id?: string,
    group_id?:string,
    subgroup_id?:string,
    _id: string,
    name: string,
    icon: string,
    order: number
}
export type Group = {
    project_id?: string,
    tour_id?: string,
    group_id?:string,
    _id: string,
    name: string,
    icon: string,
    order?: number,
    subgroups: SubGroup[]
}
export type VirtualTour = {
    _id: string;
    name: string ;
    description: string;
    organization: string;
    project_id: string;
    category: tourCategory;
    type: tourType;
    images: Image[];
    groups: Group[];
    created_at: string;
    updated_at: string;
    unitplan_id: string;
    space_id: string | null;
    link: string | null;
}

export type createVirtualTourData = {
    name: string ;
    description: string;
    organization: string;
    project_id: string;
    category: tourCategory;
    type: tourType;
    created_at: string;
    updated_at: string;
    unitplan_id: string;
    space_id: string | null;
    link: string | null;
}

export type updateVirtualTourData = {
    tour_id:string;
    name: string ;
    description: string;
    project_id: string;
    category: tourCategory;
    type: tourType;
    unitplan_id: string;
    space_id: string | null;
    link: string | null;
    initial_rotation?: coordinate;
}

export type createSceneData = {
    title: string;
    hfov: number;
    pitch: number;
    yaw: number;
    scene_type: SceneType;
    panorama: string;
    hot_spots: object;
    tour_id: string;
}

export type createHotspotData = {
    pitch: number;
    yaw: number;
    hotspot_type: HotSpotType;
    text: string;
    scene_id: string;
    target_yaw?: number;
    target_pitch?: number;
    tour_id: string;
}

export type responseVirtualTourData = {
    _id: string;
    project_id: string ;
    tour_name: string;
    description: string;
    created_at: string;
    updated_at: string;
    scenes: {
        [scene_id: string]:{
            _id: string;
            title: string;
            hfov: number;
            pitch: number;
            yaw: number;
            scene_type: SceneType,
            panorama: string;
            hot_spots: {
                [hotspot_id: string]:{
                    _id: string;
                    pitch: number;
                    yaw: number;
                    hotspot_type: HotSpotType;
                    text: string;
                    scene_id: string;
                    target_yaw: number; // Need to check this
                    target_pitch: number; // Need to check this
                }
            }
        }
    };
    unitplan_id: string;
    virtualtour_type: tourType;
    space_id: string | null;
    link: string | null;
}

export type DeleteVirtualTour = {
    tour_id: string;
    project_id: string;
}

export type ListSceneInTourType = {
    tour_id: string,
    project_id: string,
}

export type HotSpots = {
    _id: string;
    pitch: number;
    yaw: number;
    hotspot_type: HotSpotType;
    text: string;
    scene_id: string;
    target_yaw?: number; // Need to check this
    target_pitch?: number; // Need to check this
}

export type UpdateHotSpots = {
    hotspot_id: string;
    pitch: number;
    tour_id:string;
    project_id:string;
    scene_id: string;
}
