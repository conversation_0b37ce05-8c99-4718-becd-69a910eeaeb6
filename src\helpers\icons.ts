export const sidebar_icons = {
  '66026a782f7e24c644a3a3ee': {
    id: '66026a782f7e24c644a3a3ee',
    name: 'Home',
    active: `<svg width="21" height="21" viewBox="0 0 21 21" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M1.28539 11.7911L1.28541 11.7913L1.56169 13.7538L1.5617 13.7539C2.02096 17.0164 2.2506 18.6477 3.40614 19.6338C4.56169 20.6199 6.26898 20.6414 9.68355 20.6846L11.961 20.7133C15.3756 20.7565 17.0829 20.778 18.2629 19.8215C19.443 18.8649 19.7139 17.2399 20.2553 13.99L20.5811 12.035C20.9632 9.74235 21.1541 8.5961 20.7327 7.57245C20.3114 6.54883 19.3567 5.84004 17.4475 4.42257L17.4473 4.42238L16.0206 3.36322L16.0203 3.363C13.6453 1.59967 12.4578 0.718005 11.0749 0.700536C9.69198 0.683068 8.48244 1.53453 6.06343 3.23744L4.61055 4.26023C2.666 5.62915 1.69371 6.31362 1.24659 7.32631C0.799459 8.33897 0.961432 9.48965 1.28539 11.7911ZM8.35379 15.0755C8.03017 14.8169 7.55824 14.8697 7.29969 15.1933C7.04114 15.5169 7.09389 15.9889 7.4175 16.2474C8.34186 16.9859 9.54935 17.4326 10.8633 17.4492C12.1773 17.4658 13.3957 17.0498 14.3383 16.3348C14.6684 16.0845 14.733 15.6141 14.4827 15.284C14.2324 14.954 13.762 14.8893 13.4319 15.1396C12.76 15.6492 11.8686 15.9618 10.8822 15.9493C9.89594 15.9369 9.01264 15.6019 8.35379 15.0755Z"/>
    </svg>
      `,
    inactive: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 25 25" fill="none">
    <path d="M3.1338 12.7913C2.80983 10.4897 2.64784 9.33899 3.09497 8.32631C3.5421 7.31362 4.51438 6.62915 6.45894 5.26023L7.91182 4.23744C10.3308 2.53453 11.5404 1.68307 12.9233 1.70054C14.3062 1.71801 15.4938 2.59974 17.869 4.36322L19.2957 5.42238C21.205 6.83998 22.1597 7.54878 22.5811 8.57245C23.0025 9.5961 22.8115 10.7424 22.4295 13.035L22.1037 14.99C21.5622 18.2399 21.2914 19.8649 20.1113 20.8215C18.9312 21.778 17.224 21.7565 13.8094 21.7133L11.5319 21.6846C8.11737 21.6414 6.41008 21.6199 5.25453 20.6338C4.09898 19.6477 3.86935 18.0164 3.41008 14.7538L3.1338 12.7913Z" stroke="white" stroke-opacity="0.7" stroke-linejoin="round"/>
    <path d="M9.79645 16.5833L9.48435 16.974L9.79645 16.5833ZM9.6559 16.599L9.26527 16.2869L9.6559 16.599ZM15.6731 16.6576L15.371 16.2592L15.371 16.2592L15.6731 16.6576ZM15.8132 16.6768L15.4148 16.9789L15.4148 16.9789L15.8132 16.6768ZM15.794 16.8169L16.0961 17.2153L16.0961 17.2153L15.794 16.8169ZM9.67161 16.7396L9.98371 16.3489L9.98371 16.3489L9.67161 16.7396ZM10.1085 16.1927C9.84966 15.9858 9.47211 16.028 9.26527 16.2869L10.0465 16.9111C9.90865 17.0837 9.65695 17.1119 9.48435 16.974L10.1085 16.1927ZM12.7287 17.0993C11.7097 17.0864 10.7939 16.7403 10.1085 16.1927L9.48435 16.974C10.3468 17.663 11.4786 18.0836 12.7161 18.0992L12.7287 17.0993ZM15.371 16.2592C14.672 16.7893 13.7479 17.1122 12.7287 17.0993L12.7161 18.0992C13.9537 18.1149 15.0957 17.723 15.9752 17.0559L15.371 16.2592ZM16.2116 16.3747C16.0114 16.1106 15.635 16.0589 15.371 16.2592L15.9752 17.0559C15.7992 17.1894 15.5483 17.155 15.4148 16.9789L16.2116 16.3747ZM16.0961 17.2153C16.3601 17.0151 16.4118 16.6387 16.2116 16.3747L15.4148 16.9789C15.2813 16.8029 15.3158 16.552 15.4918 16.4185L16.0961 17.2153ZM12.7136 18.2992C13.9948 18.3154 15.1805 17.9097 16.0961 17.2153L15.4918 16.4185C14.7567 16.976 13.789 17.3127 12.7262 17.2993L12.7136 18.2992ZM9.35952 17.1302C10.2573 17.8475 11.4324 18.283 12.7136 18.2992L12.7262 17.2993C11.6635 17.2859 10.7045 16.9248 9.98371 16.3489L9.35952 17.1302ZM9.26527 16.2869C9.05843 16.5458 9.10063 16.9234 9.35952 17.1302L9.98371 16.3489C10.1563 16.4868 10.1844 16.7385 10.0465 16.9111L9.26527 16.2869Z" fill="white" fill-opacity="0.7"/>
    </svg>
      `,
  },
  '660294c6d2dcb300f89325e8': {
    id: '660294c6d2dcb300f89325e8',
    name: 'Location',
    active: `<svg xmlns="http://www.w3.org/2000/svg"  width="24" height="24" viewBox="0 0 24 24">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0011 22C12.6044 22 13.1841 21.773 13.6177 21.367C13.9205 21.0834 14.231 20.7972 14.5451 20.5075C18.3609 16.9893 22.711 12.9784 20.316 7.37966C18.9126 4.09916 15.5439 2 12.0011 2C8.45834 2 5.08963 4.09916 3.68627 7.37966C1.30092 12.9556 5.60302 16.9368 9.39811 20.4489C9.73249 20.7583 10.0629 21.0641 10.3845 21.367C10.8182 21.773 11.3978 22 12.0011 22ZM12 14.5C13.933 14.5 15.5 12.933 15.5 11C15.5 9.067 13.933 7.5 12 7.5C10.067 7.5 8.5 9.067 8.5 11C8.5 12.933 10.067 14.5 12 14.5Z"/>
    </svg>
    `,
    inactive: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.6177 21.367C13.1841 21.773 12.6044 22 12.0011 22C11.3978 22 10.8182 21.773 10.3845 21.367C6.41302 17.626 1.09076 13.4469 3.68627 7.37966C5.08963 4.09916 8.45834 2 12.0011 2C15.5439 2 18.9126 4.09916 20.316 7.37966C22.9082 13.4393 17.599 17.6389 13.6177 21.367Z" stroke="white" stroke-width="1.5"/>
    <path d="M15.5 11C15.5 12.933 13.933 14.5 12 14.5C10.067 14.5 8.5 12.933 8.5 11C8.5 9.067 10.067 7.5 12 7.5C13.933 7.5 15.5 9.067 15.5 11Z" stroke="white" stroke-width="1.5"/>
    </svg>
    `,
  },
  '660294cd04f5d80bc07059ce': {
    id: '660294cd04f5d80bc07059ce',
    name: 'Amenities',
    active: `<svg width="25" height="25" viewBox="0 0 25 25"  xmlns="http://www.w3.org/2000/svg">
    <path d="M20.0654 4.21785C19.8159 3.24231 18.4365 3.22489 18.1625 4.19382L17.7736 5.56861C17.6781 5.90656 17.4098 6.1681 17.0696 6.25512L15.6854 6.60912C14.7099 6.85861 14.6924 8.23801 15.6614 8.51206L17.0362 8.90091C17.3741 8.9965 17.6356 9.26473 17.7227 9.60498L18.0767 10.9892C18.3261 11.9647 19.7056 11.9821 19.9796 11.0132L20.3685 9.6384C20.464 9.30045 20.7323 9.03892 21.0725 8.9519L22.4567 8.5979C23.4322 8.3484 23.4497 6.969 22.4807 6.69495L21.1059 6.3061C20.768 6.21052 20.5065 5.94229 20.4194 5.60203L20.0654 4.21785Z"/>
    <path d="M8.73327 8.15273C8.48378 7.17719 7.10437 7.15976 6.83032 8.12869L5.80761 11.7445C5.71203 12.0825 5.4438 12.344 5.10354 12.431L1.46303 13.3621C0.487493 13.6116 0.470067 14.991 1.43899 15.265L5.05483 16.2877C5.39278 16.3833 5.65431 16.6515 5.74133 16.9918L6.67238 20.6323C6.92188 21.6079 8.30128 21.6253 8.57533 20.6564L9.59804 17.0405C9.69362 16.7026 9.96185 16.441 10.3021 16.354L13.9426 15.423C14.9182 15.1735 14.9356 13.7941 13.9667 13.52L10.3508 12.4973C10.0129 12.4017 9.75134 12.1335 9.66432 11.7932L8.73327 8.15273Z"/>
    </svg>
    `,
    inactive: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" width="25" height="25" viewBox="0 0 25 25">
    <g clip-path="url(#clip0_1907_181)">
    <path d="M19.492 4.3299L19.1032 5.70469C18.9591 6.21412 18.5547 6.60835 18.0418 6.73953L16.6577 7.09353C16.1771 7.21643 16.1685 7.89594 16.6458 8.03094L18.0206 8.41978C18.53 8.56387 18.9243 8.9682 19.0555 9.4811L19.4095 10.8653C19.5324 11.3458 20.2119 11.3544 20.3469 10.8771L20.7357 9.50232C20.8798 8.9929 21.2841 8.59866 21.797 8.46749L23.1812 8.11349C23.6618 7.99059 23.6703 7.31108 23.193 7.17608L21.8183 6.78723C21.3088 6.64314 20.9146 6.23882 20.7834 5.72592L20.4294 4.34174C20.3065 3.86118 19.627 3.85259 19.492 4.3299Z" stroke="white" stroke-opacity="0.7"/>
    <path d="M8.15984 8.26477L7.13713 11.8806C6.99304 12.39 6.58872 12.7843 6.07582 12.9154L2.43531 13.8465C1.95474 13.9694 1.94616 14.6489 2.42347 14.7839L6.0393 15.8066C6.54872 15.9507 6.94296 16.355 7.07413 16.8679L8.00518 20.5084C8.12808 20.989 8.80759 20.9976 8.94259 20.5203L9.9653 16.9044C10.1094 16.395 10.5137 16.0008 11.0266 15.8696L14.6671 14.9386C15.1477 14.8157 15.1563 14.1361 14.679 14.0011L11.0631 12.9784C10.5537 12.8343 10.1595 12.43 10.0283 11.9171L9.09725 8.27661C8.97435 7.79605 8.29484 7.78747 8.15984 8.26477Z" stroke="white" stroke-opacity="0.7"/>
    </g>
    <defs>
    <clipPath id="clip0_1907_181">
    <rect width="24" height="24" fill="white" transform="translate(1) rotate(0.361844)"/>
    </clipPath>
    </defs>
    </svg>
    `,
  },
  '660294f57e2049d8e6fce0a0': {
    id: '660294f57e2049d8e6fce0a0',
    name: 'Gallery',
    active: `<svg width="24" height="24" viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg">
    <path d="M7 8C7 5.64298 7 4.46447 7.73223 3.73223C8.46447 3 9.64298 3 12 3C14.357 3 15.5355 3 16.2678 3.73223C17 4.46447 17 5.64298 17 8L17 16C17 18.357 17 19.5355 16.2678 20.2678C15.5355 21 14.357 21 12 21C9.64298 21 8.46447 21 7.73223 20.2678C7 19.5355 7 18.357 7 16L7 8Z"/>
    <path d="M2 7C2.54697 7.10449 2.94952 7.28931 3.26777 7.61621C4 8.36835 4 9.5789 4 12C4 14.4211 4 15.6316 3.26777 16.3838C2.94952 16.7107 2.54697 16.8955 2 17" />
    <path d="M22 7C21.453 7.10449 21.0505 7.28931 20.7322 7.61621C20 8.36835 20 9.5789 20 12C20 14.4211 20 15.6316 20.7322 16.3838C21.0505 16.7107 21.453 16.8955 22 17" />
    </svg>
  `,
    inactive: `<svg width="25" height="25" viewBox="0 0 25 25"  fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.8984 8.08778C7.92817 5.73095 7.94305 4.55253 8.68447 3.8296C9.4259 3.10668 10.6043 3.12156 12.9611 3.15133C15.318 3.1811 16.4964 3.19599 17.2194 3.93741C17.9423 4.67884 17.9274 5.85725 17.8976 8.21409L17.7966 16.2134C17.7668 18.5703 17.7519 19.7487 17.0105 20.4717C16.269 21.1946 15.0906 21.1797 12.7338 21.1499C10.377 21.1201 9.19855 21.1052 8.47562 20.3639C7.7527 19.6224 7.76758 18.444 7.79735 16.0871L7.8984 8.08778Z" stroke="white" stroke-opacity="0.7" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2.91138 7.0247C3.45698 7.13609 3.85717 7.32598 4.17126 7.65688C4.89393 8.41821 4.87864 9.62866 4.84807 12.0496C4.81749 14.4705 4.8022 15.6809 4.06052 16.4238C3.73817 16.7466 3.33332 16.9263 2.78507 17.0239" stroke="white" stroke-opacity="0.7" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M22.9099 7.27731C22.3616 7.37488 21.9568 7.5546 21.6344 7.87745C20.8928 8.62029 20.8775 9.83074 20.8469 12.2516C20.8163 14.6726 20.801 15.883 21.5237 16.6443C21.8378 16.9752 22.2379 17.1651 22.7836 17.2765" stroke="white" stroke-opacity="0.7" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  `,
  },
  '660294fc73af1e94826ee3c6': {
    id: '660294fc73af1e94826ee3c6',
    name: 'Info',
    active: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M22.2014 12.8057C22.2014 7.28281 17.7242 2.80566 12.2014 2.80566C6.67857 2.80566 2.20142 7.28281 2.20142 12.8057C2.20142 18.3285 6.67857 22.8057 12.2014 22.8057C17.7242 22.8057 22.2014 18.3285 22.2014 12.8057ZM12.0921 11.0806C12.3114 11.1101 12.5916 11.1859 12.8274 11.4217C13.0632 11.6575 13.1391 11.9377 13.1686 12.1571C13.1938 12.3443 13.1937 12.5661 13.1936 12.7712V12.7712L13.1936 12.8057V17.8057C13.1936 18.2199 12.8578 18.5557 12.4436 18.5557C12.0294 18.5557 11.6936 18.2199 11.6936 17.8057V12.8057C11.6936 12.7066 11.6936 12.6263 11.6924 12.5568C11.623 12.5557 11.5427 12.5557 11.4436 12.5557C11.0294 12.5557 10.6936 12.2199 10.6936 11.8057C10.6936 11.3915 11.0294 11.0557 11.4436 11.0557L11.478 11.0557C11.6832 11.0556 11.9049 11.0555 12.0921 11.0806ZM12.1934 7.80566C11.6411 7.80566 11.1934 8.25338 11.1934 8.80566C11.1934 9.35795 11.6411 9.80566 12.1934 9.80566H12.2024C12.7546 9.80566 13.2024 9.35795 13.2024 8.80566C13.2024 8.25338 12.7546 7.80566 12.2024 7.80566H12.1934Z" fill="black"/>
  <defs>
  <linearGradient id="paint0_linear_93_16949" x1="2.20142" y1="-5.45521" x2="26.2381" y2="-2.75118" gradientUnits="userSpaceOnUse">
  <stop stop-color="#E4D5B4"/>
  <stop offset="1" stop-color="#E0BA67"/>
  </linearGradient>
  </defs>
  </svg>
  `,
    inactive: `<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M22.2014 12.2085C22.2014 6.68565 17.7242 2.2085 12.2014 2.2085C6.67857 2.2085 2.20142 6.68565 2.20142 12.2085C2.20142 17.7313 6.67857 22.2085 12.2014 22.2085C17.7242 22.2085 22.2014 17.7313 22.2014 12.2085Z" stroke="white" stroke-opacity="0.7" stroke-width="1.5"/>
  <path d="M12.4436 17.2085V12.2085C12.4436 11.7371 12.4436 11.5014 12.2971 11.3549C12.1507 11.2085 11.915 11.2085 11.4436 11.2085" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M12.1934 8.2085H12.2024" stroke="white" stroke-opacity="0.7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
  `,
  },
  '661f9cbe67526186d8abcd20': {
    id: '661f9cbe67526186d8abcd20',
    name: 'Unit plan',
    active: `<svg width="21" height="21" viewBox="0 0 21 21" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.52979 0.0831635L6.52979 3.03946C6.52979 3.45367 6.86557 3.78946 7.27979 3.78946C7.694 3.78946 8.02979 3.45367 8.02979 3.03946L8.02979 0.0471251C8.71384 0.0394592 9.46128 0.0394592 10.2798 0.0394592C14.9938 0.0394592 17.3509 0.0394592 18.8153 1.50393C19.8255 2.51414 20.1389 3.94908 20.2361 6.28946L16.2798 6.28946C15.8656 6.28946 15.5298 6.62525 15.5298 7.03946C15.5298 7.45367 15.8656 7.78946 16.2798 7.78946L20.2721 7.78946C20.2798 8.47351 20.2798 9.22096 20.2798 10.0395C20.2798 14.7535 20.2798 17.1106 18.8153 18.575C17.9989 19.3915 16.9049 19.7528 15.2798 19.9126L11.0298 20.0204C11.0401 17.682 12.9389 15.7895 15.2798 15.7895C15.694 15.7895 16.0298 15.4537 16.0298 15.0395C16.0298 14.6252 15.694 14.2895 15.2798 14.2895C12.1043 14.2895 9.52991 16.8637 9.52979 20.0392C8.99829 20.0387 8.49912 20.0371 8.02979 20.0318L8.02979 7.78946L12.2798 7.78946C12.694 7.78946 13.0298 7.45367 13.0298 7.03946C13.0298 6.62525 12.694 6.28946 12.2798 6.28946L7.27979 6.28946L0.323489 6.28946C0.420685 3.94908 0.734035 2.51414 1.74426 1.50393C2.75447 0.493709 4.18941 0.180359 6.52979 0.0831635ZM0.287451 7.78946C0.279785 8.47351 0.279785 9.22096 0.279785 10.0395C0.279785 14.7535 0.279785 17.1106 1.74426 18.575C2.75447 19.5852 4.18941 19.8986 6.52979 19.9958L6.52979 7.78946L0.287451 7.78946Z"/>
    </svg>
  `,
    inactive: `
    <svg width="25" height="26" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.0244 22.9992C7.31075 22.9397 4.95392 22.9099 3.50807 21.427C2.06222 19.9442 2.09199 17.5873 2.15153 12.8737C2.21107 8.16004 2.24084 5.80321 3.72369 4.35736C5.20653 2.91151 7.56336 2.94128 12.277 3.00082C16.9907 3.06036 19.3476 3.09013 20.7934 4.57298C22.2392 6.05582 22.2095 8.41265 22.1499 13.1263C22.0904 17.8399 22.0606 20.1969 20.5777 21.6426C19.7511 22.4488 18.6526 22.7962 17.0256 22.9355" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M12.0245 22.9992C12.0594 20.238 14.3261 18.0279 17.0872 18.0628" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M2.18945 9.87393L14.1885 10.0255" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M18.1882 10.076L22.1879 10.1265" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M9.02453 22.9613L9.18872 9.96234" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M9.23945 5.96268L9.27734 2.96292" stroke="white" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round"/>
    </svg>
  `,
  },
  '6645fc5d71fe460dd1011e6a': {
    id: '6645fc5d71fe460dd1011e6a',
    name: 'Inventory',
    active: `<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.31779 14.2364C4.2753 15 5.68353 15 8.5 15C8.77614 15 9 15.2239 9 15.5C9 18.3165 9 19.7247 9.76359 20.6822C9.92699 20.8871 10.1129 21.073 10.3178 21.2364C11.2753 22 12.6835 22 15.5 22C18.3165 22 19.7247 22 20.6822 21.2364C20.8871 21.073 21.073 20.8871 21.2364 20.6822C22 19.7247 22 18.3165 22 15.5C22 12.6835 22 11.2753 21.2364 10.3178C21.073 10.1129 20.8871 9.92699 20.6822 9.76359C19.7247 9 18.3165 9 15.5 9C15.2239 9 15 8.77614 15 8.5C15 5.68353 15 4.2753 14.2364 3.31779C14.073 3.11289 13.8871 2.92699 13.6822 2.76359C12.7247 2 11.3165 2 8.5 2C5.68353 2 4.2753 2 3.31779 2.76359C3.11289 2.92699 2.92699 3.11289 2.76359 3.31779C2 4.2753 2 5.68353 2 8.5C2 11.3165 2 12.7247 2.76359 13.6822C2.92699 13.8871 3.11289 14.073 3.31779 14.2364Z"/>
    </svg>
  `,
    inactive: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.31779 14.2364C4.2753 15 5.68353 15 8.5 15C8.77614 15 9 15.2239 9 15.5C9 18.3165 9 19.7247 9.76359 20.6822C9.92699 20.8871 10.1129 21.073 10.3178 21.2364C11.2753 22 12.6835 22 15.5 22C18.3165 22 19.7247 22 20.6822 21.2364C20.8871 21.073 21.073 20.8871 21.2364 20.6822C22 19.7247 22 18.3165 22 15.5C22 12.6835 22 11.2753 21.2364 10.3178C21.073 10.1129 20.8871 9.92699 20.6822 9.76359C19.7247 9 18.3165 9 15.5 9C15.2239 9 15 8.77614 15 8.5C15 5.68353 15 4.2753 14.2364 3.31779C14.073 3.11289 13.8871 2.92699 13.6822 2.76359C12.7247 2 11.3165 2 8.5 2C5.68353 2 4.2753 2 3.31779 2.76359C3.11289 2.92699 2.92699 3.11289 2.76359 3.31779C2 4.2753 2 5.68353 2 8.5C2 11.3165 2 12.7247 2.76359 13.6822C2.92699 13.8871 3.11289 14.073 3.31779 14.2364Z" stroke="white" stroke-width="1.5"/>
    </svg>
  `,
  },
};

export const searchIcon = `<svg width="16" height="17" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="search-outline" clip-path="url(#clip0_1989_192755)">
<g id="Vector">
<path d="M8.0001 16C6.41783 16 4.87109 15.5308 3.55548 14.6518C2.23987 13.7727 1.21448 12.5233 0.608974 11.0615C0.00346629 9.59966 -0.154962 7.99112 0.153723 6.43928C0.462409 4.88743 1.22434 3.46197 2.34318 2.34315C3.46201 1.22433 4.88749 0.462403 6.43936 0.153721C7.99122 -0.15496 9.59977 0.00346625 11.0616 0.608967C12.5234 1.21447 13.7729 2.23985 14.6519 3.55544C15.531 4.87103 16.0002 6.41775 16.0002 8C15.9978 10.121 15.1542 12.1544 13.6544 13.6542C12.1546 15.154 10.1211 15.9976 8.0001 16ZM8.0001 2C6.8134 2 5.65334 2.3519 4.66664 3.01118C3.67993 3.67047 2.91089 4.60754 2.45675 5.7039C2.00262 6.80026 1.8838 8.00665 2.11532 9.17054C2.34683 10.3344 2.91828 11.4035 3.75741 12.2426C4.59653 13.0818 5.66564 13.6532 6.82954 13.8847C7.99344 14.1162 9.19985 13.9974 10.2962 13.5433C11.3926 13.0891 12.3297 12.3201 12.989 11.3334C13.6483 10.3467 14.0002 9.18669 14.0002 8C13.9986 6.40919 13.3659 4.88399 12.241 3.75912C11.1161 2.63424 9.59093 2.00159 8.0001 2Z" fill="#6B7280"/>
<path d="M19.0002 20C18.735 19.9999 18.4807 19.8946 18.2932 19.707L14.2931 15.707C14.111 15.5184 14.0102 15.2658 14.0125 15.0036C14.0147 14.7414 14.1199 14.4906 14.3053 14.3052C14.4907 14.1198 14.7416 14.0146 15.0038 14.0123C15.266 14.01 15.5186 14.1108 15.7072 14.293L19.7072 18.293C19.847 18.4329 19.9422 18.611 19.9808 18.805C20.0194 18.9989 19.9996 19.2 19.9239 19.3827C19.8482 19.5654 19.7201 19.7215 19.5557 19.8314C19.3913 19.9413 19.198 20 19.0002 20Z" fill="#6B7280"/>
</g>
</g>
<defs>
<clipPath id="clip0_1989_192755">
<rect width="20" height="20" fill="#6B7280" />
</clipPath>
</defs>
</svg>`;

export const editIcon = `<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path id="Vector" d="M11.158 0.945285C10.6163 0.361226 9.88312 0.0333252 9.11878 0.0333252C8.35445 0.0333252 7.62124 0.361226 7.07956 0.945285L0.963336 7.57075C0.828963 7.71602 0.737318 7.90113 0.699962 8.10275L0.0194206 11.784C-0.00910617 11.9354 -0.0061736 12.0917 0.0280051 12.2417C0.0621838 12.3917 0.126752 12.5316 0.217027 12.6512C0.307303 12.7708 0.421023 12.8671 0.549941 12.9332C0.678858 12.9993 0.819741 13.0335 0.962374 13.0333C1.02688 13.0332 1.09124 13.0266 1.15462 13.0135L4.55348 12.2765C4.73963 12.236 4.91054 12.1367 5.04466 11.9912L5.71944 11.251L11.158 5.36261C11.6973 4.77593 12 3.98179 12 3.15395C12 2.32611 11.6973 1.53197 11.158 0.945285Z" fill="#6B7280"/>
</svg>
`;
export const deleteIcon = `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trash" clip-path="url(#clip0_1071_15083)">
<path id="Vector" d="M14.5185 3.90175H11.2593V2.21754C11.2593 1.77086 11.0876 1.34247 10.782 1.02662C10.4764 0.710768 10.0619 0.533325 9.62965 0.533325H6.37039C5.93819 0.533325 5.52368 0.710768 5.21807 1.02662C4.91245 1.34247 4.74076 1.77086 4.74076 2.21754V3.90175H1.4815C1.2654 3.90175 1.05815 3.99047 0.905341 4.14839C0.752533 4.30632 0.666687 4.52051 0.666687 4.74385C0.666687 4.96719 0.752533 5.18138 0.905341 5.33931C1.05815 5.49724 1.2654 5.58596 1.4815 5.58596H2.29632V14.8491C2.29632 15.2958 2.46801 15.7242 2.77362 16.04C3.07924 16.3559 3.49374 16.5333 3.92595 16.5333H12.0741C12.5063 16.5333 12.9208 16.3559 13.2264 16.04C13.532 15.7242 13.7037 15.2958 13.7037 14.8491V5.58596H14.5185C14.7346 5.58596 14.9419 5.49724 15.0947 5.33931C15.2475 5.18138 15.3334 4.96719 15.3334 4.74385C15.3334 4.52051 15.2475 4.30632 15.0947 4.14839C14.9419 3.99047 14.7346 3.90175 14.5185 3.90175ZM6.37039 2.21754H9.62965V3.90175H6.37039V2.21754ZM7.18521 13.1649C7.18521 13.3882 7.09936 13.6024 6.94655 13.7604C6.79374 13.9183 6.58649 14.007 6.37039 14.007C6.15429 14.007 5.94704 13.9183 5.79423 13.7604C5.64142 13.6024 5.55558 13.3882 5.55558 13.1649V7.27017C5.55558 7.04683 5.64142 6.83263 5.79423 6.67471C5.94704 6.51678 6.15429 6.42806 6.37039 6.42806C6.58649 6.42806 6.79374 6.51678 6.94655 6.67471C7.09936 6.83263 7.18521 7.04683 7.18521 7.27017V13.1649ZM10.4445 13.1649C10.4445 13.3882 10.3586 13.6024 10.2058 13.7604C10.053 13.9183 9.84575 14.007 9.62965 14.007C9.41355 14.007 9.2063 13.9183 9.05349 13.7604C8.90068 13.6024 8.81483 13.3882 8.81483 13.1649V7.27017C8.81483 7.04683 8.90068 6.83263 9.05349 6.67471C9.2063 6.51678 9.41355 6.42806 9.62965 6.42806C9.84575 6.42806 10.053 6.51678 10.2058 6.67471C10.3586 6.83263 10.4445 7.04683 10.4445 7.27017V13.1649Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_1071_15083">
<rect width="16" height="16" fill="white" transform="translate(0 0.533325)"/>
</clipPath>
</defs>
</svg>
`;

export const uploadFileIcon = `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="file-export">
<g id="Vector">
<path d="M4 6H0.104C0.211983 5.59337 0.41829 5.2224 0.7032 4.92253L2.9656 2.56594C3.25348 2.26917 3.60963 2.05427 4 1.94179V6Z" fill="#4B5563"/>
<path d="M12.38 13.3064C12.5257 13.1493 12.6064 12.9388 12.6045 12.7203C12.6027 12.5018 12.5186 12.2928 12.3703 12.1383C12.2219 11.9838 12.0213 11.8961 11.8115 11.8943C11.6018 11.8924 11.3997 11.9763 11.2488 12.1281L10.4 13.0114L10.4 7.66661C10.4 7.4456 10.3157 7.23365 10.1657 7.07737C10.0157 6.9211 9.81217 6.8333 9.6 6.8333C9.38783 6.8333 9.18434 6.9211 9.03432 7.07737C8.88429 7.23365 8.8 7.4456 8.8 7.66661L8.8 13.0114L7.9512 12.1281C7.8774 12.0485 7.78913 11.9851 7.69152 11.9414C7.59392 11.8977 7.48894 11.8747 7.38272 11.8738C7.2765 11.8728 7.17115 11.8939 7.07284 11.9358C6.97452 11.9777 6.8852 12.0396 6.81008 12.1178C6.73497 12.1961 6.67557 12.2891 6.63534 12.3915C6.59512 12.4939 6.57488 12.6036 6.5758 12.7143C6.57672 12.8249 6.59879 12.9343 6.64072 13.0359C6.68265 13.1376 6.74359 13.2296 6.82 13.3064L8.6056 15.1664H1.6C1.1829 15.1733 0.780194 15.0076 0.480239 14.7056C0.180284 14.4036 0.00757422 13.99 0 13.5556L0 7.66661H4C4.42435 7.66661 4.83131 7.49102 5.13137 7.17847C5.43143 6.86592 5.6 6.44201 5.6 6V1.83346L14.4 1.83346C14.8171 1.82655 15.2198 1.99226 15.5198 2.29423C15.8197 2.59621 15.9924 3.00979 16 3.44424V13.4998C16 13.9418 15.8314 14.3657 15.5314 14.6782C15.2313 14.9908 14.8243 15.1664 14.4 15.1664H10.5944L12.38 13.3064Z" fill="#4B5563"/>
</g>
</g>
</svg>`;

export const closeIcon = `<svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="x" clip-path="url(#clip0_1071_15928)">
<path id="Vector" d="M5.90741 5L9.30409 1.60332C9.36538 1.54412 9.41427 1.47331 9.4479 1.39502C9.48153 1.31672 9.49924 1.23252 9.49998 1.14731C9.50072 1.0621 9.48448 0.977595 9.45221 0.898729C9.41995 0.819863 9.3723 0.748212 9.31204 0.687958C9.25179 0.627705 9.18014 0.580054 9.10127 0.547787C9.0224 0.515521 8.9379 0.499284 8.85269 0.500024C8.76748 0.500765 8.68328 0.518468 8.60498 0.5521C8.52669 0.585733 8.45588 0.634621 8.39668 0.695913L5 4.09259L1.60332 0.695913C1.48229 0.579017 1.32019 0.514333 1.15193 0.515796C0.983666 0.517258 0.822712 0.584748 0.70373 0.70373C0.584748 0.822712 0.517258 0.983666 0.515796 1.15193C0.514333 1.32019 0.579017 1.48229 0.695913 1.60332L4.09259 5L0.695913 8.39668C0.634621 8.45588 0.585733 8.52669 0.5521 8.60498C0.518468 8.68328 0.500765 8.76748 0.500024 8.85269C0.499284 8.9379 0.515521 9.0224 0.547787 9.10127C0.580054 9.18014 0.627705 9.25179 0.687958 9.31204C0.748212 9.3723 0.819863 9.41995 0.898729 9.45221C0.977595 9.48448 1.0621 9.50072 1.14731 9.49998C1.23252 9.49924 1.31672 9.48153 1.39502 9.4479C1.47331 9.41427 1.54412 9.36538 1.60332 9.30409L5 5.90741L8.39668 9.30409C8.51771 9.42098 8.67981 9.48567 8.84807 9.4842C9.01633 9.48274 9.17729 9.41525 9.29627 9.29627C9.41525 9.17729 9.48274 9.01633 9.4842 8.84807C9.48567 8.67981 9.42098 8.51771 9.30409 8.39668L5.90741 5Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_1071_15928">
<rect width="10" height="10" fill="white"/>
</clipPath>
</defs>
</svg>
`;

export const repeatAgainIcon = `<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="arrows-repeat" clip-path="url(#clip0_1440_18527)">
<g id="Vector">
<path d="M12.4006 7.96304C12.0142 7.96304 11.7005 8.28244 11.7005 8.67599V10.1019H2.88922L3.79444 9.18005C4.06817 8.90128 4.06817 8.4507 3.79444 8.17193C3.5207 7.89317 3.07825 7.89317 2.80451 8.17193L0.704251 10.3108C0.639843 10.3764 0.588736 10.4555 0.553032 10.5425C0.482323 10.7172 0.482323 10.9132 0.553032 11.0872C0.588736 11.1742 0.639843 11.2533 0.704251 11.3189L2.80451 13.4578C2.94103 13.5968 3.12025 13.6667 3.29947 13.6667C3.4787 13.6667 3.65792 13.5968 3.79444 13.4578C4.06817 13.179 4.06817 12.7284 3.79444 12.4497L2.88922 11.5278H12.4006C12.7871 11.5278 13.1007 11.2084 13.1007 10.8148V8.67599C13.1007 8.28244 12.7871 7.96304 12.4006 7.96304Z" fill="#1C64F2"/>
<path d="M14.447 3.41297C14.4113 3.32599 14.3602 3.24685 14.2957 3.18126L12.1955 1.0424C11.9218 0.763637 11.4793 0.763637 11.2056 1.0424C10.9318 1.32117 10.9318 1.77175 11.2056 2.05052L12.1108 2.97237H2.59939C2.21294 2.97237 1.8993 3.29177 1.8993 3.68532V5.82418C1.8993 6.21773 2.21294 6.53713 2.59939 6.53713C2.98584 6.53713 3.29947 6.21773 3.29947 5.82418V4.39827H12.1108L11.2056 5.32012C10.9318 5.59889 10.9318 6.04947 11.2056 6.32824C11.3421 6.46726 11.5213 6.53713 11.7005 6.53713C11.8797 6.53713 12.059 6.46726 12.1955 6.32824L14.2957 4.18938C14.3602 4.12378 14.4113 4.04465 14.447 3.95767C14.5177 3.78371 14.5177 3.58693 14.447 3.41297Z" fill="#1C64F2"/>
</g>
</g>
<defs>
<clipPath id="clip0_1440_18527">
<rect width="14" height="14" fill="white" transform="translate(0.5 0.25)"/>
</clipPath>
</defs>
</svg>
`;
export const activeRepeatAgainIcon=`<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3587_16276)">
<path d="M11.9006 7.71296C11.5142 7.71296 11.2005 8.03236 11.2005 8.42591V9.85182H2.38922L3.29444 8.92997C3.56817 8.65121 3.56817 8.20062 3.29444 7.92186C3.0207 7.64309 2.57825 7.64309 2.30451 7.92186L0.204251 10.0607C0.139843 10.1263 0.0887361 10.2054 0.0530316 10.2924C-0.0176772 10.4671 -0.0176772 10.6632 0.0530316 10.8371C0.0887361 10.9241 0.139843 11.0032 0.204251 11.0688L2.30451 13.2077C2.44103 13.3467 2.62025 13.4166 2.79947 13.4166C2.9787 13.4166 3.15792 13.3467 3.29444 13.2077C3.56817 12.9289 3.56817 12.4783 3.29444 12.1996L2.38922 11.2777H11.9006C12.2871 11.2777 12.6007 10.9583 12.6007 10.5648V8.42591C12.6007 8.03236 12.2871 7.71296 11.9006 7.71296Z" fill="white"/>
<path d="M13.947 3.16289C13.9113 3.07591 13.8602 2.99678 13.7958 2.93118L11.6955 0.792325C11.4218 0.513561 10.9793 0.513561 10.7056 0.792325C10.4318 1.07109 10.4318 1.52168 10.7056 1.80044L11.6108 2.72229H2.09939C1.71294 2.72229 1.3993 3.04169 1.3993 3.43524V5.5741C1.3993 5.96765 1.71294 6.28705 2.09939 6.28705C2.48584 6.28705 2.79947 5.96765 2.79947 5.5741V4.1482H11.6108L10.7056 5.07004C10.4318 5.34881 10.4318 5.79939 10.7056 6.07816C10.8421 6.21719 11.0213 6.28705 11.2005 6.28705C11.3797 6.28705 11.559 6.21719 11.6955 6.07816L13.7958 3.9393C13.8602 3.87371 13.9113 3.79457 13.947 3.70759C14.0177 3.53363 14.0177 3.33686 13.947 3.16289Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_3587_16276">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>

`;
export const filterIconInActive =`<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="filter" clip-path="url(#clip0_1440_20385)">
<path id="Vector" d="M0.698345 2.05152L6.5057 9.71212V13.5909L10.4943 16.5V9.71212L16.3017 2.05152C16.4124 1.90719 16.4797 1.73552 16.4961 1.55582C16.5125 1.37612 16.4773 1.19551 16.3944 1.0343C16.3116 0.873096 16.1844 0.737685 16.0271 0.643297C15.8699 0.548908 15.6888 0.499285 15.5043 0.500008H1.49567C1.31116 0.499285 1.13011 0.548908 0.972865 0.643297C0.815623 0.737685 0.688428 0.873096 0.605584 1.0343C0.52274 1.19551 0.487532 1.37612 0.50392 1.55582C0.520307 1.73552 0.587639 1.90719 0.698345 2.05152Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_1440_20385">
<rect width="16" height="16" fill="white" transform="translate(0.5 0.5)"/>
</clipPath>
</defs>
</svg>
`;
export const filterIconActive =`<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="filter" clip-path="url(#clip0_1316_58864)">
<path id="Vector" d="M0.698345 2.05152L6.5057 9.71212V13.5909L10.4943 16.5V9.71212L16.3017 2.05152C16.4124 1.90719 16.4797 1.73552 16.4961 1.55582C16.5125 1.37612 16.4773 1.19551 16.3944 1.0343C16.3116 0.873096 16.1844 0.737685 16.0271 0.643297C15.8699 0.548908 15.6888 0.499285 15.5043 0.500008H1.49567C1.31116 0.499285 1.13011 0.548908 0.972865 0.643297C0.815623 0.737685 0.688428 0.873096 0.605584 1.0343C0.52274 1.19551 0.487532 1.37612 0.50392 1.55582C0.520307 1.73552 0.587639 1.90719 0.698345 2.05152Z" fill="#1C64F2"/>
</g>
<defs>
<clipPath id="clip0_1316_58864">
<rect width="16" height="16" fill="white" transform="translate(0.5 0.5)"/>
</clipPath>
</defs>
</svg>
`;

export const plusIcon = `<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="plus">
<path id="Vector" d="M14.1885 7.78886H9.21072V2.81109C9.21072 2.62249 9.1358 2.44161 9.00244 2.30826C8.86908 2.1749 8.68821 2.09998 8.49961 2.09998C8.31101 2.09998 8.13014 2.1749 7.99678 2.30826C7.86342 2.44161 7.7885 2.62249 7.7885 2.81109V7.78886H2.81072C2.62212 7.78886 2.44125 7.86378 2.30789 7.99714C2.17453 8.1305 2.09961 8.31138 2.09961 8.49998C2.09961 8.68857 2.17453 8.86945 2.30789 9.00281C2.44125 9.13617 2.62212 9.21109 2.81072 9.21109H7.7885V14.1889C7.7885 14.3775 7.86342 14.5583 7.99678 14.6917C8.13014 14.8251 8.31101 14.9 8.49961 14.9C8.68821 14.9 8.86908 14.8251 9.00244 14.6917C9.1358 14.5583 9.21072 14.3775 9.21072 14.1889V9.21109H14.1885C14.3771 9.21109 14.558 9.13617 14.6913 9.00281C14.8247 8.86945 14.8996 8.68857 14.8996 8.49998C14.8996 8.31138 14.8247 8.1305 14.6913 7.99714C14.558 7.86378 14.3771 7.78886 14.1885 7.78886Z" fill="white"/>
</g>
</svg>
`;

export const infoIcon = {
  active: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="info" clip-path="url(#clip0_1313_41358)">
<path id="Vector" d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM7.6 3.2C7.83734 3.2 8.06935 3.27038 8.26669 3.40224C8.46402 3.53409 8.61783 3.72151 8.70866 3.94078C8.79948 4.16005 8.82325 4.40133 8.77694 4.63411C8.73064 4.86688 8.61635 5.0807 8.44853 5.24853C8.28071 5.41635 8.06689 5.53064 7.83411 5.57694C7.60133 5.62324 7.36005 5.59948 7.14078 5.50865C6.92151 5.41783 6.7341 5.26402 6.60224 5.06668C6.47038 4.86934 6.4 4.63734 6.4 4.4C6.4 4.08174 6.52643 3.77651 6.75147 3.55147C6.97652 3.32643 7.28174 3.2 7.6 3.2ZM9.6 12H6.4C6.18783 12 5.98435 11.9157 5.83432 11.7657C5.68429 11.6157 5.6 11.4122 5.6 11.2C5.6 10.9878 5.68429 10.7843 5.83432 10.6343C5.98435 10.4843 6.18783 10.4 6.4 10.4H7.2V8H6.4C6.18783 8 5.98435 7.91571 5.83432 7.76568C5.68429 7.61565 5.6 7.41217 5.6 7.2C5.6 6.98782 5.68429 6.78434 5.83432 6.63431C5.98435 6.48428 6.18783 6.4 6.4 6.4H8C8.21218 6.4 8.41566 6.48428 8.56569 6.63431C8.71572 6.78434 8.8 6.98782 8.8 7.2V10.4H9.6C9.81217 10.4 10.0157 10.4843 10.1657 10.6343C10.3157 10.7843 10.4 10.9878 10.4 11.2C10.4 11.4122 10.3157 11.6157 10.1657 11.7657C10.0157 11.9157 9.81217 12 9.6 12Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_1313_41358">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  inActive: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="info" clip-path="url(#clip0_1313_41370)">
<path id="Vector" d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM7.6 3.2C7.83734 3.2 8.06935 3.27038 8.26669 3.40224C8.46402 3.53409 8.61783 3.72151 8.70866 3.94078C8.79948 4.16005 8.82325 4.40133 8.77694 4.63411C8.73064 4.86688 8.61635 5.0807 8.44853 5.24853C8.28071 5.41635 8.06689 5.53064 7.83411 5.57694C7.60133 5.62324 7.36005 5.59948 7.14078 5.50865C6.92151 5.41783 6.7341 5.26402 6.60224 5.06668C6.47038 4.86934 6.4 4.63734 6.4 4.4C6.4 4.08174 6.52643 3.77651 6.75147 3.55147C6.97652 3.32643 7.28174 3.2 7.6 3.2ZM9.6 12H6.4C6.18783 12 5.98435 11.9157 5.83432 11.7657C5.68429 11.6157 5.6 11.4122 5.6 11.2C5.6 10.9878 5.68429 10.7843 5.83432 10.6343C5.98435 10.4843 6.18783 10.4 6.4 10.4H7.2V8H6.4C6.18783 8 5.98435 7.91571 5.83432 7.76568C5.68429 7.61565 5.6 7.41217 5.6 7.2C5.6 6.98782 5.68429 6.78434 5.83432 6.63431C5.98435 6.48428 6.18783 6.4 6.4 6.4H8C8.21218 6.4 8.41566 6.48428 8.56569 6.63431C8.71572 6.78434 8.8 6.98782 8.8 7.2V10.4H9.6C9.81217 10.4 10.0157 10.4843 10.1657 10.6343C10.3157 10.7843 10.4 10.9878 10.4 11.2C10.4 11.4122 10.3157 11.6157 10.1657 11.7657C10.0157 11.9157 9.81217 12 9.6 12Z" fill="#1C64F2"/>
</g>
<defs>
<clipPath id="clip0_1313_41370">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>`,
};
