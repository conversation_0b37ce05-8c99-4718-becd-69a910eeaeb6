<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ProjectStore } from '../../../store/project.ts';
import ImgPreviewModal from '@/components/common/Modal/imgPreviewModal.vue';
import AssetTableRow from './AssetTableRow.vue';
import { getListofAssets } from '@/api/projects/assets';

/* States */
const route = useRoute();
const projectId = ref(route.params.project_id);
const headers = ['S.NO', 'FILE NAME', 'SELECT FILE TYPE', 'MEDIA TYPE', 'UPLOAD FILE', 'MODIFIED', ''];
const projectStore = ProjectStore();
const loader = ref(false);
const isModalOpen = ref({ status: false, url: '' });
const newFileRows = ref({});

const addNewRow = () => {
  const id = crypto.randomUUID();
  const newFile = {
    _id: id,
    file_name: "",
    file_type: "",
    file_url: "",
    media_type: "",
    updated_at: null,
    isNew: true,
  };
  newFileRows.value[id] = newFile;
};

const handleListOfAssets = async () => {
  if (projectStore.assets === null) {
    await getListofAssets(projectId.value).then((response) => {
      if (Object.values(response.assets.items).length !== 0) {
        projectStore.SyncMultipleAssets(response.assets.items);
      }
    });
  }
};

// Preview Checker (checks for previewing only Image Files)
const allowedFileTypes = ['JPEG', 'WEBP', 'PNG'];

function openPreviewModal (status, url, previewFiletype) {
  if (allowedFileTypes.includes(previewFiletype)) {
    isModalOpen.value = { status: status, url: url };
  } else {
    window.open(url, '_blank');
  }
}

function deleteNewRow (id) {
  delete newFileRows.value[id];
}
// Initialize
handleListOfAssets();
</script>

<template>
  <div class="">
    <div class=" dynamic-header">
      <div class="dynamic-heading ">
        <p class="dynamic-topic">Enter File Details</p>
        <p class="dynamic-sub-topic">These files are used to
          create renders
          and serve as references for building the experience.</p>
      </div>
      <div class="flex gap-2">
        <Button :disabled="loader" title="Add File" class="!text-[#1c64f2] bg-white border border-[#1c64f2] px-2"
          @click="addNewRow">
          <template v-slot:svg>
            <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M10.2659 5.96686H6.53255V2.23353C6.53255 2.09208 6.47636 1.95642 6.37634 1.85641C6.27632 1.75639 6.14067 1.7002 5.99922 1.7002C5.85777 1.7002 5.72211 1.75639 5.6221 1.85641C5.52208 1.95642 5.46589 2.09208 5.46589 2.23353V5.96686H1.73255C1.5911 5.96686 1.45545 6.02305 1.35543 6.12307C1.25541 6.22309 1.19922 6.35875 1.19922 6.5002C1.19922 6.64164 1.25541 6.7773 1.35543 6.87732C1.45545 6.97734 1.5911 7.03353 1.73255 7.03353H5.46589V10.7669C5.46589 10.9083 5.52208 11.044 5.6221 11.144C5.72211 11.244 5.85777 11.3002 5.99922 11.3002C6.14067 11.3002 6.27632 11.244 6.37634 11.144C6.47636 11.044 6.53255 10.9083 6.53255 10.7669V7.03353H10.2659C10.4073 7.03353 10.543 6.97734 10.643 6.87732C10.743 6.7773 10.7992 6.64164 10.7992 6.5002C10.7992 6.35875 10.743 6.22309 10.643 6.12307C10.543 6.02305 10.4073 5.96686 10.2659 5.96686Z"
                fill="#1C64F2" />
            </svg>
          </template>
        </Button>
        <Button title="Save" type="submit" class="!bg-[#1c64f2] px-2">
        </Button>

      </div>

    </div>
    <div class="">
      <div class="rounded-md border border-[#1c64f2]">
        <div class="w-full bg-bg-1000">
          <div class="grid bg-gray-50 w-full gap-6 grid-cols-7 p-3">
            <div v-for="(header, index) in headers" :key="index"
              class="rounded-lg text-gray-500 text-sm font-semibold text-left uppercase leading-[18px] text-nowrap flex justify-start items-center">
              {{ header }}
            </div>
          </div>

          <div v-if="(projectStore.assets === null || Object.keys(projectStore.assets).length === 0)  && Object.keys(newFileRows).length === 0"
            class="flex flex-col items-center justify-center p-4 text-gray-500 text-lg">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
              <g clip-path="url(#clip0_4982_18313)">
                <path
                  d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z"
                  fill="#6B7280" />
              </g>
              <defs>
                <clipPath id="clip0_4982_18313">
                  <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                </clipPath>
              </defs>
            </svg>
            <p class="text-black text-xs font-normal pt-1">Empty</p>
          </div>

          <!-- Existing Rows -->
          <AssetTableRow v-for="(item, id, index) in projectStore.assets" :key="index" :data="item" :index="index"
            @openPreviewModal="openPreviewModal" @addNewRow="(val) => { projectStore.assets[val._id] = val }"
            @deleteAssetModal="handleListOfAssets" />

          <!-- NewRows -->
          <AssetTableRow v-for="(newFileRow, id, index) in newFileRows" :key="id"
            :index="Object.values(projectStore.assets ? projectStore.assets : {}).length + index" :data="newFileRow"
            @deleteRow="deleteNewRow" @openPreviewModal="openPreviewModal"
            @addNewFileRow="(val, id) => { (projectStore.assets && (projectStore.assets[val._id] = val)); delete newFileRows[id] }" />
        </div>
      </div>
    </div>
  </div>

  <!-- Image Preview Modal -->
  <ImgPreviewModal v-if="isModalOpen.url" :isOpen="isModalOpen.status" :imageUrl="isModalOpen.url"
    @close="isModalOpen.status = false" />
</template>

<style></style>
