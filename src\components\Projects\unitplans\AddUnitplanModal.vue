<script setup>
// Import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
// Import { ChevronDownIcon } from '@heroicons/vue/20/solid';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { unitplanSchema } from '../../../validationSchema/unitplan';
import { useRouter } from 'vue-router';
import Modal from '../../common/Modal/Modal.vue';
import { ProjectStore } from '../../../store/project';
import { createUnitplan } from '../../../api/projects/unitplans/index';
import { getStyles, getTypes } from '../../../api/projects/unitplans';
import { resizeImage } from '../../../helpers/helpers';
import { unitplanTypeList, measurementTypeList } from '../../../helpers/constants';
import Multiselect from 'vue-multiselect';
import Spinner from '../../common/Spinner.vue';

const router = useRouter();
const projectStore = ProjectStore();
const route = useRoute();
const loader = ref(false);
const projectId = ref(route.params.project_id);
const unitplanId = ref(route.params.unitplan_id);
const unitTypeList = ["flat", "villa", "villa_floor"];
const exteriorType = ["gallery", "scene"];
const selectedUnitType = ref(route.params.unitplan_id?"villa_floor":'');
const selected_exterior_type = ref();
const selectedGalleryItems = ref();
const galleryItems = ref([]);
const styleRef = ref();
const styleList = ref([]);
const unitplanTypeRef = ref();
const unitplanTypeListRef = ref([]);

projectStore.RefreshVirtualTours(projectId.value);
projectStore.RefreshGalleryItems(projectId.value);
projectStore.RefreshScenes(projectId.value);

watch(selected_exterior_type, () => {
  if (selected_exterior_type.value==='gallery') {
    galleryItems.value = Object.values(projectStore.galleryItems)
      .map((item) => ({ '_id': item._id, 'name': item.name }));
  }
});

// Const measurementType = ref('sqft');

getStyles(projectId.value).then((res) => { // Get list of styles
  styleList.value = res.map((elem) => {
    return {name: elem};
  });
}).catch((err) => {
  console.log('output->err', err);
});
getTypes(projectId.value).then((res) => {
  unitplanTypeListRef.value = res.map((elem) => {
    return {name: elem};
  });
}).catch((err) => {
  console.log('output->err', err);
});

const handleAddUnitplan = (data) => {
  loader.value = true;
  createUnitplan(data).then(() => {
    //  ProjectStore.SyncMultipleUnitplans({res})
    loader.value = false;
    document.dispatchEvent(new Event('getListOfUnitplan'));
    document.dispatchEvent(new Event('getListOfVillaUnitplan'));
    router.go(-1);
  }).catch(() => {
  }).finally(() => {
    loader.value = false;
  });
};

const addTag = (newTag) => {
  const tag = {
    name: newTag,
  };
  styleList.value.push(tag);
  styleRef.value = tag;

};

const addTagUnitPlanType = (newTag) => {
  const tag = {
    name: newTag,
  };
  unitplanTypeListRef.value.push(tag);
  unitplanTypeRef.value = tag;
};

const handleForm = async (values) => {
  if (values.isFurnished === undefined || values.isFurnished === null) {
    values.isFurnished = false;
  }
  if (values.is_commercial === undefined || values.is_commercial === null) {
    values.is_commercial = false;
  }
  let galleryList = null;
  if (values.gallery_id && values.gallery_id.length !== 0) {
    galleryList = values.gallery_id.map((elem) => elem._id);
  }
  console.log("gallery list", galleryList);
  const formData = new FormData();
  formData.append('project_id', projectId.value);
  formData.append('name', values.unitplanName);
  values.unitplanType && values.unitplanType.name && formData.append('type', values.unitplanType.name);
  formData.append('measurement', values.measurement);
  formData.append('measurement_type', values.measurement_type);
  formData.append('bedrooms', values.bedrooms);
  formData.append('bathrooms', values.bathrooms);
  formData.append('is_furnished', values.isFurnished);
  formData.append('unit_type', values.unit_type);
  formData.append('is_commercial', values.is_commercial);
  values.balcony_measurement && formData.append('balcony_measurement', values.balcony_measurement);
  values.balcony_measurement_type && formData.append('balcony_measurement_type', values.balcony_measurement_type);
  values.suite_area_measurement && formData.append('suite_area', values.suite_area_measurement);
  values.suite_area_measurement_type && formData.append('suite_area_type', values.suite_area_measurement_type);
  values.exterior_type && formData.append('exterior_type', values.exterior_type);
  values.style && values.style.name && formData.append('style', values.style.name);
  values.scene_id && formData.append('scene_id', values.scene_id);
  galleryList && formData.append('gallery_id', JSON.stringify(galleryList));
  values.tour_id && formData.append('tour_id', values.tour_id);
  values.unit_type==="villa_floor" && formData.append('parent_unitplan', unitplanId.value);
  if (values.unit_type!=='villa'){
    const resizedThumbnail =await resizeImage(values.file, 1280, 720);
    formData.append('lowRes', resizedThumbnail);
    formData.append('highRes', values.file);
  }
  handleAddUnitplan(formData);
  console.log('output->values', values, galleryList);
};
</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary">
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Add Unitplan</h1>
          <p class="modal-subheading-primary">Fill All Details of Unit Plan</p>
        </div>
        <Form
          @submit="handleForm"
          :validation-schema="unitplanSchema"
          class="flex flex-col justify-center"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label for="unitplanName" class="label-primary"
                >unitplan Name</label
              >
              <div class="mt-2">
                <Field
                  type="text"
                  name="unitplanName"
                  id="unitplanName"
                  class="input-primary"
                  placeholder="Enter Unitplan Name"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="unitplanName"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="unitplanType" class="label-primary">
                Unitplan Type
              </label>
              <div class="mt-2">
                <Field  name="unitplanType" :model-value="unitplanTypeRef" v-slot="{ unitplanType }">
                  <Multiselect
                    v-bind="unitplanType"
                    v-model="unitplanTypeRef"
                    tag-placeholder="Add this as new unitplanType"
                    placeholder="Search or add unitplanType"
                    label="name"
                    track-by="name"
                    :multiple="false"
                    :taggable="true"
                    @tag="addTagUnitPlanType"
                    :options="unitplanTypeListRef" maxHeight="250" >
                  </Multiselect>
                </Field>
                <!-- <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="unitplanType" /> -->
              </div>
            </div>
            <div class="col-span-auto">
              <label for="unit_type" class="label-primary"
                >Unit Type</label
              >
              <div class="mt-2">
                <Field
                :disabled="unitplanId"
                  as="select"
                  v-model="selectedUnitType"
                  type="text"
                  name="unit_type"
                  id="unit_type"
                  autocomplete="unit_type"
                  class="select-primary"
                  :placeholder="`Enter Unit Type`"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="dataName == !unitTypeList">
                    No Type found !
                  </option>
                  <option
                    :disabled="!unitplanId && option==='villa_floor'"
                    v-else
                    :value="option"
                    v-for="(option, index) in unitTypeList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="unit_type"
                />
              </div>
            </div>
            <div class="col-span-auto" v-if="!unitplanId">
              <label for="exterior_type" class="label-primary"
                >Exterior Type</label
              >
              <div class="mt-2">
                <Field
                 v-model="selected_exterior_type"
                  as="select"
                  type="text"
                  name="exterior_type"
                  id="exterior_type"
                  autocomplete="exterior_type"
                  class="select-primary"
                  :placeholder="`Enter Exterior Type`"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="dataName == !exteriorType">
                    No Type found !
                  </option>
                  <option
                    v-else
                    :value="option"
                    v-for="(option, index) in exteriorType"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="exterior_type"
                />
              </div>
            </div>

            <div class="col-span-auto" v-if="selected_exterior_type==='scene'">
              <label for="scene_id" class="label-primary"
                >Scene Id</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="scene_id"
                  id="scene_id"
                  autocomplete="scene_id"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="!projectStore.scenes">
                    No Scene found !
                  </option>
                  <option
                    v-else
                    :value="option.sceneData._id"
                    v-for="(option, index) in projectStore.scenes"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.sceneData.name }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="scene_id"
                />
              </div>
            </div>

            <div v-if="selected_exterior_type === 'gallery'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label for="gallery_id"
                                class="label-primary">select
                                gallery Items</label>
                            <Field as="input"
                                v-model="selectedGalleryItems"
                                class="sr-only"
                                name="gallery_id">
                            </Field>
                            <Multiselect
                                v-model="selectedGalleryItems"
                                :options="galleryItems"
                                :searchable="false"
                                :multiple="true"
                                :taggable="true"
                                placeholder="gallery_id"
                                :close-on-select="false"
                                label="name" track-by="_id"
                                open-direction="top"
                                :max-height="150">
                            </Multiselect>
                            <ErrorMessage name="gallery_id"
                                class="text-sm text-rose-500 mt-1"
                                as="p" />
                        </div>

            <div class="col-span-auto" v-if="selectedUnitType !=='villa'">
              <label for="file" class="label-primary"
                >Upload High Resolution File</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  name="file"
                  id="file"
                  autocomplete="file"
                  class="input-primary"
                  placeholder="Upload High Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="file"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="measurement" class="label-primary">
                Measurement</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  name="measurement"
                  id="measurement"
                  autocomplete="measurement"
                  class="h-11 input-primary"
                  placeholder="Measurement"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="measurement"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="measurement_type" class="label-primary"
                >Measurement Type</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="measurement_type"
                  id="measurement_type"
                  autocomplete="measurement_type"
                  class="select-primary"
                  :placeholder="`Enter Measurement Type`"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="dataName == !measurementTypeList">
                    No Type found !
                  </option>
                  <option
                    :value="option"
                    v-for="(option, index) in measurementTypeList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="measurement_type"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="measurement" class="label-primary">
                Balcony Measurement</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  name="balcony_measurement"
                  id="balcony_measurement"
                  autocomplete="balcony_measurement"
                  class="h-11 input-primary"
                  placeholder="Balcony Measurement"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="balcony_measurement"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="measurement_type" class="label-primary"
                >Balcony Measurement Type</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="balcony_measurement_type"
                  id="balcony_measurement_type"
                  autocomplete="balcony_measurement_type"
                  class="select-primary"
                  :placeholder="`Enter Balcony Measurement Type`"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="dataName == !measurementTypeList">
                    No Type found !
                  </option>
                  <option
                    :value="option"
                    v-for="(option, index) in measurementTypeList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="balcony_measurement_type"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="measurement" class="label-primary">
                Suite Area Measurement</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  name="suite_area_measurement"
                  id="suite_area_measurement"
                  autocomplete="suite_area_measurement"
                  class="h-11 input-primary"
                  placeholder="Suite Area Measurement"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="suite_area_measurement"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="measurement_type" class="label-primary"
                >Suite Area Measurement Type</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="suite_area_measurement_type"
                  id="suite_area_measurement_type"
                  autocomplete="suite_area_measurement_type"
                  class="select-primary"
                  :placeholder="`Enter Suite Area Measurement Type`"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="dataName == !measurementTypeList">
                    No Type found !
                  </option>
                  <option
                    :value="option"
                    v-for="(option, index) in measurementTypeList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="suite_area_measurement_type"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="bedrooms" class="label-primary"
                >Bedrooms</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="bedrooms"
                  id="bedrooms"
                  autocomplete="bedrooms"
                  class="select-primary"
                  :placeholder="`Bedrooms`"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="dataName == !unitplanTypeList">
                    No Type found !
                  </option>
                  <option
                    v-else
                    :value="option"
                    v-for="(option, index) in unitplanTypeList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="bedrooms"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="bathrooms" class="label-primary"> Bathrooms</label>
              <div class="mt-2">
                <Field
                  type="number"
                  name="bathrooms"
                  id="bathrooms"
                  autocomplete="bathrooms"
                  class="h-11 input-primary"
                  placeholder="Bathrooms"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="bathrooms"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="tour_id" class="label-primary"
                >Tour</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="tour_id"
                  id="tour_id"
                  autocomplete="tour_id"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="!projectStore.virtualtours">
                    No tours found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in projectStore.virtualtours"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.tour_name }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="tour_id"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="style" class="label-primary">
                Style
              </label>
              <div class="mt-2">
                <Field  name="style" :model-value="styleRef" v-slot="{ style }">
                  <Multiselect
                    v-bind="style"
                    v-model="styleRef"
                    tag-placeholder="Add this as new style"
                    placeholder="Search or add Style"
                    label="name"
                    track-by="name"
                    :multiple="false"
                    :taggable="true"
                    @tag="addTag"
                    :options="styleList" maxHeight="250" >
                  </Multiselect>
                </Field>
                <!-- <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="style" /> -->
              </div>
            </div>

            <div class="col-span-full mt-2 flex flex-row gap-4">
              <div class="col-span-full">
                <div class="flex justify-start items-start gap-2 w-auto">
                  <label for="isFurnished" class="label-primary">
                    isFurnished</label
                  >
                  <div
                    class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                  >
                    <div class="relative mb-0 p-0">
                      <Field
                        id="isFurnished"
                        class="sr-only peer"
                        name="isFurnished"
                        type="checkbox"
                        :value="true"
                      />
                      <label
                        for="isFurnished"
                        class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                      >
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-span-full">
                <div class="flex justify-start items-start gap-2 w-auto">
                  <label for="is_commercial" class="label-primary">
                    isCommercial</label
                  >
                  <div
                    class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                  >
                    <div class="relative mb-0 p-0">
                      <Field
                        id="is_commercial"
                        class="sr-only peer"
                        name="is_commercial"
                        type="checkbox"
                        :value="true"
                      />
                      <label
                        for="is_commercial"
                        class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                      >
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="() => router.go(-1)"
              ref="cancelButtonRef"
            >
              Cancel
            </button>
            <button type="submit" :disabled="loader" class="proceed-btn-primary">Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
