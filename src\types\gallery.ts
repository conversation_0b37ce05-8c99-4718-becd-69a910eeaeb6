import { MediaType } from './amenity';
export type galleryItem = {
    _id: string,
    name: string,
    category: string,
    type: MediaType,
    url?:string,
    thumbnail?: string,
    link?: string
    tour_id?: string
}
export type createGalleryItem = {
    name: string,
    category: string,
    type: MediaType,
    url?:string,
    thumbnail?: string,
    link?: string,
    tour_id?: string
}
export type updateGalleryItemType = {
    category?: string,
    name?: string,
    type?: MediaType,
    file?: Blob | File,
    thumbnail?:  Blob | File,
    link?: string | null
    tour_id?: string | null
}
export type bulkUpdateQuery = {
    id:string,
    order?:number,
    category?: string
}
export type bulkUpdateType = {
    query:bulkUpdateQuery[],
    project_id:string
}
