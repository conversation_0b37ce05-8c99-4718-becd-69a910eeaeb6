type EventName = "unit_clicked" | "amenity_clicked" | "floor_clicked" | "landmark_clicked" | "menu_clicked" | "tour_clicked";

export const eventConfig = {
  unit_clicked: ["unit_id", "building_id", "project_id", "floor_id", "type", "building_name", "unitplan_id"],
  amenity_clicked: ["project_id", "amenity_id", "amenity_category", "amenity_timestart", "amenity_timespent"],
  floor_clicked: ["project_id", "floor_id", "building_id", "building_name", "floor_timespent", "floor_timestart"],
  landmark_clicked: ["project_id", "landmark_id", "landmark_category", "landmark_timespent", "landmark_timestart"],
  menu_clicked: ["menu_timestart", "menu_timespent"],
  tour_clicked: ["tour_id", "project_id", "unit_id", "type", "unitplan_id", "tour_timespent", "tour_timestart"],
  // session_start: ['session_id']
};

export const getEventNames = () => Object.keys(eventConfig);

export const getCustomFields = (eventName:EventName) => eventConfig[eventName] || [];

export default eventConfig;
