
<script setup>
import { ref } from 'vue';

const activeButton = ref(false);
const handleClick = () => {
  activeButton.value = !activeButton.value;
};
</script>

<template>
    <button @click="handleClick"
        class="w-full h-10 px-2.5 py-2 bg-gray-600 bg-opacity-30 rounded justify-start items-start gap-2.5 inline-flex items-center">
        <div class="w-fit"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-5 h-5 fill-white">
                <path
                    d="M11.644 1.59a.75.75 0 01.712 0l9.75 5.25a.75.75 0 010 1.32l-9.75 5.25a.75.75 0 01-.712 0l-9.75-5.25a.75.75 0 010-1.32l9.75-5.25z" />
                <path
                    d="M3.265 10.602l7.668 4.129a2.25 2.25 0 002.134 0l7.668-4.13 1.37.739a.75.75 0 010 1.32l-9.75 5.25a.75.75 0 01-.71 0l-9.75-5.25a.75.75 0 010-1.32l1.37-.738z" />
                <path
                    d="M10.933 19.231l-7.668-4.13-1.37.739a.75.75 0 000 1.32l9.75 5.25c.221.12.489.12.71 0l9.75-5.25a.75.75 0 000-1.32l-1.37-.738-7.668 4.13a2.25 2.25 0 01-2.134-.001z" />
            </svg>
        </div>
        <div class="grow flex justify-start items-center text-sm">
            <h>Dropdown of Scenes</h>
        </div>
        <div class="w-fit">

            <svg v-if="activeButton" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-5 h-5 fill-white">
                <g data-name="Layer 2">
                    <g data-name="arrow-up">
                        <rect transform="rotate(90 12 12)" opacity="0" />
                        <path
                            d="M16.21 16H7.79a1.76 1.76 0 0 1-1.59-1 2.1 2.1 0 0 1 .26-2.21l4.21-5.1a1.76 1.76 0 0 1 2.66 0l4.21 5.1A2.1 2.1 0 0 1 17.8 15a1.76 1.76 0 0 1-1.59 1z" />
                    </g>
                </g>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-5 h-5 fill-white">
                <g data-name="Layer 2">
                    <g data-name="arrow-downward">
                        <rect transform="rotate(-90 12 12)" opacity="0" />
                        <path
                        d="M12 17a1.72 1.72 0 0 1-1.33-.64l-4.21-5.1a2.1 2.1 0 0 1-.26-2.21A1.76 1.76 0 0 1 7.79 8h8.42a1.76 1.76 0 0 1 1.59 1.05 2.1 2.1 0 0 1-.26 2.21l-4.21 5.1A1.72 1.72 0 0 1 12 17z" />
                </g>
            </g>
        </svg>
    </div>
</button></template>

<style scoped></style>
