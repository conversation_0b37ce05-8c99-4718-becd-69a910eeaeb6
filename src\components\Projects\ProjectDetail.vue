<script setup>
// Import CountShowCaseCard from '../../components/common/ShowCaseCards/CountShowCaseCard.vue';
// Import TimeShowCaseCard from '../../components/common/ShowCaseCards/TimeShowCaseCard.vue';
// Import Button from '../../components/common/Button.vue';
// Import TableView from '../../components/common/TableView.vue';
// Import Multiselect from 'vue-multiselect';

// /* Dump Table View Data */
// Const SampleData = {
//   1: {
//     'id': 1,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Phone': '0987654321',
//     'Interested Unit': ['1234', '456', '567', '12', '2'],
//     'status': 'new',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',

//   },
//   2: {
//     'id': 2,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Phone': '0987654321',
//     'Interested Unit': ['1234', '12', '2'],
//     'status': 'cold',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
//   },
//   3: {
//     'id': 3,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',

//     'Phone': '0987654321',
//     'Interested Unit': ['1234', '456', '567', '12', '2'],
//     'status': 'new',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',

//   },
//   4: {
//     'id': 4,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Phone': '0987654321',
//     'Interested Unit': 'null',
//     'status': 'warm',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',

//   },
//   5: {
//     'id': 5,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',

//     'Phone': '0987654321',
//     'Interested Unit': ['1234', '456', '567', '12', '2'],
//     'status': 'hot',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',

//   },
//   6: {
//     'id': 6,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Phone': '0987654321',
//     'Interested Unit': ['1234', '456', '567', '12', '2'],
//     'status': 'warm',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
//   },
//   7: {
//     'id': 7,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Phone': '0987654321',
//     'Interested Unit': ['1234'],
//     'status': 'new',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
//   },
//   8: {
//     'id': 8,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Phone': '0987654321',
//     'Interested Unit': ['1234', '456', '567', '12', '2'],
//     'status': 'hot',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
//   },
//   9: {
//     'id': 9,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Phone': '0987654321',
//     'Interested Unit': ['1234', '456', '567', '12', '2'],
//     'status': 'warm',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
//   },
//   10: {
//     'id': 10,
//     'Date': '20/07/2022',
//     'Time': '13:45',
//     'Sales Executive': 'Prop VR',
//     'Username': 'Thomas Magnum',
//     'Email': '<EMAIL>',
//     'Interested Unit': ['1234'],
//     'Phone': '0987654321',
//     'status': 'cold',
//     'Feedback': 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
//   },
// };

// Const options = ['One', 'Two', 'Three', 'Four'];

</script>

<template>
    <div>
    <!-- <div
        class=" relative bg-transparent mb-5 h-full">

        <div class="mb-6">
            <div class="mb-4 px-8 pt-6">
                <div
                    class="flex flex-col justify-start items-start gap-3">
                    <h3
                        class="text-txt-50 dark:text-txt-1000 text text-2xl font-semibold">
                        Damac </h3>
                    <p
                        class="text-gray-600 dark:text-txt-650 text-base font-normal mb-0">
                        Create session or view analytics </p>
                </div>
            </div>
        </div>

        <div class="px-8">

            <div
                class="p-4 rounded-2xl border-[1px] border-gray-300 dark:border-bg-400 flex justify-start items-start gap-5">
                <img src="https://images.pexels.com/photos/271816/pexels-photo-271816.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                    alt=""
                    class="w-[420px] h-[200px] bg-opacity-10 rounded-lg">
                <div
                    class="flex flex-col justify-start items-start w-full">
                    <div
                        class="w-full flex justify-between items-center">
                        <h5
                            class="text-txt-50 dark:text-txt-1000 text-2xl font-semibold">
                            Damac </h5>
                        <div
                            class="flex justify-start items-start gap-6">
                            <span
                                class="w-10 h-10 bg-gray-200 rounded-lg justify-center items-center flex cursor-pointer">
                                <svg class="w-6 h-6"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M21.5303 6.93757L17.0624 2.46874C16.9139 2.32013 16.7375 2.20225 16.5433 2.12182C16.3492 2.0414 16.1411 2 15.931 2C15.7208 2 15.5128 2.0414 15.3186 2.12182C15.1245 2.20225 14.9481 2.32013 14.7995 2.46874L2.46899 14.8003C2.31978 14.9483 2.20147 15.1245 2.12096 15.3187C2.04045 15.5128 1.99934 15.721 2.00001 15.9312V20.4001C2.00001 20.8244 2.16857 21.2313 2.46862 21.5314C2.76867 21.8314 3.17562 22 3.59995 22H8.06878C8.27896 22.0007 8.48718 21.9595 8.68134 21.879C8.87549 21.7985 9.0517 21.6802 9.19973 21.531L21.5303 9.20048C21.6789 9.05191 21.7968 8.87552 21.8772 8.68138C21.9576 8.48724 21.999 8.27916 21.999 8.06903C21.999 7.85889 21.9576 7.65081 21.8772 7.45667C21.7968 7.26253 21.6789 7.08614 21.5303 6.93757ZM8.06878 20.4001H3.59995V15.9312L12.3996 7.13156L16.8684 11.6004L8.06878 20.4001ZM17.9994 10.4684L13.5306 6.00061L15.9305 3.6007L20.3993 8.06853L17.9994 10.4684Z"
                                        fill="black" />
                                </svg>
                            </span>

                            <span
                                class="w-10 h-10 bg-gray-200 rounded-lg justify-center items-center flex cursor-pointer">
                                <svg class="w-5 h-5"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M11.9904 3.50077C11.1614 3.50077 10.4894 4.17267 10.4894 5.00149L10.4894 5.00812H13.4914L13.4914 5.00149C13.4914 4.17267 12.8194 3.50077 11.9904 3.50077ZM8.98836 5.00149L8.98837 5.00812H3.75369C3.33744 5.00812 3 5.34556 3 5.76181C3 6.17806 3.33744 6.5155 3.75369 6.5155H5.00399V19.0001C5.00399 20.6569 6.34713 22.0001 8.00399 22.0001H16.0079C17.6648 22.0001 19.0079 20.6569 19.0079 19.0001V6.5155H20.2363C20.6526 6.5155 20.99 6.17806 20.99 5.76181C20.99 5.34556 20.6526 5.00812 20.2363 5.00812H14.9924L14.9925 5.00149C14.9925 3.34381 13.6484 2 11.9904 2C10.3324 2 8.98836 3.34381 8.98836 5.00149ZM13.4912 9.75192C13.4912 9.3371 13.828 9.00083 14.2428 9.00083C14.6576 9.00083 14.9944 9.33711 14.9944 9.75192V17.2979C14.9944 17.7127 14.6576 18.049 14.2428 18.049C13.828 18.049 13.4912 17.7127 13.4912 17.2979V9.75192ZM9.00057 9.75194C9.00057 9.33712 9.33737 9.00085 9.75218 9.00085C10.167 9.00085 10.5038 9.33713 10.5038 9.75194V17.298C10.5038 17.7128 10.167 18.049 9.75218 18.049C9.33737 18.049 9.00057 17.7128 9.00057 17.298V9.75194Z"
                                        fill="#FF1E46" />
                                </svg>
                            </span>

                        </div>
                    </div>
                    <div
                        class="flex flex-col justify-start items-start gap-3">
                        <p
                            class="text-txt-400 text-lg font-normal">
                            About the project </p>
                        <p
                            class="text-txt-default font-Roboto dark:text-txt-950 text-lg font-medium w-full">
                            Located in the heart of Dubai's
                            bustling cityscape, this remarkable
                            real estate property offers a unique
                            and sophisticated living experience.
                            Boasting breath-taking views of the
                            glittering city skyline and the
                            tranquil waters of the Arabian
                            Gulf,this property seamlessly blends
                            luxury and comfort More.
                        </p>
                        <div class="w-[172px] h-[40px] bg-bg-800 rounded-lg mt-3">
                            <multiselect v-model="value" :options="options" :close-on-select="true" :show-labels="false" class="flex justify-center items-center"></multiselect>
                        </div>
                    </div>
                </div>
            </div>

            <div class="">

                <div class="mt-6 mb-7">
                    <div
                        class="border-b-[1px]  border-gray-300 dark:border-bg-400 flex justify-start items-center gap-9 ">
                        <p
                            class="text-txt-50 dark:text-txt-1000 text-xl relative font-semibold analyticsTitleActiveBorder pb-2 after:bg-bg-50 after:dark:bg-bg-1000 cursor-pointer">
                            Session Analytics </p>
                        <p
                            class="text-txt-500 text-xl relative font-semibold pb-2 cursor-pointer">
                            Project Analytics </p>
                    </div>
                </div>

                <div
                    class="flex flex-col justify-start items-start gap-8 mb-6">
                    <div
                        class="flex justify-between items-center w-full ">
                        <div
                            class="flex justify-start items-center gap-2">
                            <h5
                                class="text-txt-50 dark:text-txt-1000 text-lg font-medium">
                                Analytics Overview
                            </h5>
                            <svg class="w-6 h-6"
                                viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M18.4166 3H5.58332C3.60833 3 2 4.35736 2 6.03121V13.1838C2 14.8506 3.60833 16.2079 5.58332 16.2079L8.40767 16.2334C8.5893 16.235 8.7569 16.3162 8.85028 16.4477L10.1583 18.2897C10.475 18.7327 11.0333 19 11.65 19C12.2666 19 12.8167 18.7327 13.1333 18.2897L14.5052 16.417C14.6004 16.287 14.7686 16.2079 14.9496 16.2079H18.4167C20.3957 16.2079 22 14.854 22 13.1838V6.03121C22 4.35736 20.3916 3 18.4166 3ZM11.9471 5.73086C12.4353 5.73086 12.7804 6.06781 12.7804 6.43415C12.7804 6.81539 12.4407 7.12843 11.9512 7.12843C11.4542 7.12844 11.1138 6.79946 11.1138 6.43415C11.1138 6.06525 11.4747 5.73086 11.9471 5.73086ZM12.7821 12.7897C12.7821 13.1781 12.4093 13.493 11.9487 13.493C11.4881 13.493 11.1154 13.1781 11.1154 12.7897V9.18149C11.1154 8.7931 11.4881 8.47819 11.9487 8.47819C12.4093 8.47819 12.7821 8.7931 12.7821 9.18149V12.7897Z"
                                    fill="#5B616E" />
                            </svg>
                        </div>
                        <div
                            class="gap-8 flex justify-start items-center">
                            <div
                                class="rounded-lg flex justify-start items-center  border border-gray-300 overflow-hidden">
                                <span
                                    class="px-3 py-[10px] bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-50 text-center border-r border-gray-300 flex justify-center items-center gap-2">
                                    <svg class="fill-bg-1000 dark:fill-bg-50"
                                        width="19" height="18"
                                        viewBox="0 0 19 18"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M7.65002 12.1275L4.52252 9L3.45752 10.0575L7.65002 14.25L16.65 5.25L15.5925 4.1925L7.65002 12.1275Z"
                                            fill="" />
                                    </svg>

                                    Table
                                </span>
                                <span
                                    class="px-3 py-[10px] text-center text-txt-400 text-sm font-medium  border-r border-gray-300">
                                    This month
                                </span>
                                <span
                                    class="px-3 py-[10px] text-center text-txt-400 text-sm font-medium  border-r border-gray-300">
                                    6 month
                                </span>
                                <span
                                    class="px-3 py-[10px] text-center text-txt-400 text-sm font-medium  border-r border-gray-300">
                                    1 year
                                </span>
                                <span
                                    class="px-3 py-[10px] text-center text-txt-400 text-sm font-medium">
                                    All time
                                </span>
                            </div>
                            <span
                                class="w-10 h-10 bg-transparent border border-gray-200 rounded-lg justify-center items-center flex cursor-pointer">
                                <svg class="w-6 h-6"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M3.09277 9.4043H20.9167"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M16.4424 13.3097H16.4516"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M12.0044 13.3097H12.0137"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M7.55811 13.3097H7.56737"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M16.4424 17.1962H16.4516"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M12.0044 17.1962H12.0137"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M7.55811 17.1962H7.56737"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path d="M16.0439 2V5.29078"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path d="M7.96533 2V5.29078"
                                        stroke="#5B616E"
                                        stroke-width="1.5"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                <path fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M16.2383 3.57922H7.77096C4.83427 3.57922 3 5.21516 3 8.22225V17.2719C3 20.3263 4.83427 22 7.77096 22H16.229C19.175 22 21 20.3546 21 17.3475V8.22225C21.0092 5.21516 19.1842 3.57922 16.2383 3.57922Z"
                                    stroke="#5B616E"
                                    stroke-width="1.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </span>

                    </div>
                </div>

                <div
                    class="p-8 bg-gray-50 dark:bg-bg-100 w-full grid grid-cols-4  gap-4 rounded-2xl overflow-x-auto flex-nowrap noScrollApperance">

                    <CountShowCaseCard class=""
                        style="grid-column: 1/1;"
                        title="No. of Sessions Hosted"
                        count="20"
                        description="1.10% Since yesterday">
                        <template v-slot:svg>
                            <svg width="40" height="40"
                                viewBox="0 0 40 40"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink">
                                <rect width="40" height="40"
                                    rx="8" fill="#E9FAEB" />
                                <mask id="mask0_307_22872"
                                    style="mask-type:alpha"
                                    maskUnits="userSpaceOnUse"
                                    x="8" y="8" width="24"
                                    height="24">
                                    <rect x="8" y="8"
                                        width="24"
                                        height="24"
                                        fill="url(#pattern0)" />
                                </mask>
                                <g
                                    mask="url(#mask0_307_22872)">
                                    <rect x="8" y="8"
                                        width="24"
                                        height="24"
                                        fill="#28A745" />
                                </g>
                                <defs>
                                    <pattern id="pattern0"
                                        patternContentUnits="objectBoundingBox"
                                        width="1"
                                        height="1">
                                        <use xlink:href="#image0_307_22872"
                                            transform="scale(0.00195312)" />
                                    </pattern>
                                    <image
                                        id="image0_307_22872"
                                        width="512"
                                        height="512"
                                        xlink:href="data:image/png;base64,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" />
                                </defs>
                            </svg>

                        </template>
                    </CountShowCaseCard>
                    <CountShowCaseCard
                        title="Leads generated" count="34"
                        description="1.10% Since yesterday">
                        <template v-slot:svg>
                            <svg width="40" height="40"
                                viewBox="0 0 40 40"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40"
                                    rx="8" fill="#FEDDEB" />
                                <path
                                    d="M29.2225 11.7952C28.3187 10.9111 27.24 10.2086 26.0496 9.72867C24.8592 9.24874 23.581 9.00103 22.2895 9.00003H22.2529C20.9673 8.99686 19.6939 9.23814 18.5063 9.70989C17.3187 10.1816 16.2406 10.8745 15.3344 11.7483L8.51361 18.3704C8.1834 18.6905 7.99876 19.1228 8.00001 19.5729C8.00126 20.023 8.1883 20.4543 8.52028 20.7727L11.7045 23.8244C11.8694 23.9832 12.0654 24.109 12.2813 24.1948C12.4971 24.2805 12.7286 24.3243 12.9623 24.3238H12.9723C13.2076 24.3226 13.4404 24.2766 13.657 24.1885C13.8737 24.1004 14.07 23.972 14.2344 23.8106L21.0007 17.1661C21.3311 16.8526 21.776 16.6749 22.2409 16.6708C22.7058 16.6666 23.1541 16.8363 23.4906 17.1438C23.6569 17.2986 23.7891 17.4838 23.8793 17.6885C23.9696 17.8932 24.0161 18.1131 24.0161 18.3353C24.0183 18.5718 23.9719 18.8064 23.8794 19.0256C23.787 19.2449 23.6503 19.4445 23.4772 19.6131L16.5565 26.0244C16.3875 26.1817 16.2528 26.3696 16.1602 26.5773C16.0676 26.7849 16.019 27.0081 16.0173 27.2339C16.0155 27.4597 16.0606 27.6836 16.1499 27.8926C16.2393 28.1015 16.371 28.2914 16.5376 28.4511L19.7219 31.5029C20.0539 31.8191 20.5022 31.9976 20.9702 32C21.4382 32.0024 21.8885 31.8284 22.224 31.5156L29.0903 25.0788C32.9178 21.4052 32.9767 15.4486 29.2225 11.7952ZM12.9623 22.618L9.7791 19.5673L12.4234 16.999L15.5921 20.0358L12.9623 22.618ZM20.9774 30.2964L17.7909 27.2457L20.4996 24.7359L23.6672 27.7749L20.9774 30.2964ZM27.8392 23.8606L24.9383 26.5802L21.7784 23.554L24.7161 20.8312L24.7272 20.8217C25.0679 20.4924 25.3373 20.1018 25.5198 19.6723C25.7023 19.2429 25.7943 18.783 25.7904 18.3193C25.7884 17.8713 25.6926 17.4282 25.5086 17.0163C25.3246 16.6043 25.0563 16.232 24.7194 15.9214C24.0443 15.3059 23.1454 14.9672 22.2138 14.9771C21.2822 14.9871 20.3914 15.3449 19.7308 15.9746L16.8332 18.8155L13.6678 15.7872L16.5954 12.9494C17.3365 12.2351 18.2182 11.6687 19.1893 11.2832C20.1604 10.8978 21.2017 10.7008 22.2529 10.7037H22.2829C23.3394 10.7046 24.3852 10.9073 25.3591 11.3001C26.333 11.6928 27.2154 12.2676 27.9548 12.991C31.0279 15.9799 30.9724 20.86 27.8392 23.8628V23.8606Z"
                                    fill="#FF74AE" />
                            </svg>
                        </template>
                    </CountShowCaseCard>
                    <CountShowCaseCard
                        title="Customers visited virtually"
                        count="56"
                        description="1.10% Since yesterday">
                        <template v-slot:svg>
                            <svg width="40" height="40"
                                viewBox="0 0 40 40"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40"
                                    rx="8" fill="#EDE9FA" />
                                <path
                                    d="M31.931 19.676C31.896 19.597 31.049 17.718 29.166 15.835C26.657 13.326 23.488 12 20 12C16.512 12 13.343 13.326 10.834 15.835C8.95099 17.718 8.09999 19.6 8.06899 19.676C8.0235 19.7783 8 19.889 8 20.001C8 20.113 8.0235 20.2237 8.06899 20.326C8.10399 20.405 8.95099 22.283 10.834 24.166C13.343 26.674 16.512 28 20 28C23.488 28 26.657 26.674 29.166 24.166C31.049 22.283 31.896 20.405 31.931 20.326C31.9765 20.2237 32 20.113 32 20.001C32 19.889 31.9765 19.7783 31.931 19.676ZM20 26.4C16.922 26.4 14.233 25.281 12.007 23.075C11.0936 22.1667 10.3166 21.131 9.69999 20C10.3164 18.8689 11.0935 17.8332 12.007 16.925C14.233 14.719 16.922 13.6 20 13.6C23.078 13.6 25.767 14.719 27.993 16.925C28.9081 17.833 29.6869 18.8687 30.305 20C29.584 21.346 26.443 26.4 20 26.4ZM20 15.2C19.0506 15.2 18.1226 15.4815 17.3333 16.0089C16.5439 16.5364 15.9287 17.286 15.5654 18.1631C15.2021 19.0402 15.107 20.0053 15.2922 20.9364C15.4774 21.8675 15.9346 22.7228 16.6059 23.3941C17.2772 24.0654 18.1325 24.5226 19.0636 24.7078C19.9947 24.893 20.9598 24.7979 21.8369 24.4346C22.714 24.0713 23.4636 23.4561 23.9911 22.6667C24.5185 21.8774 24.8 20.9494 24.8 20C24.7987 18.7274 24.2925 17.5072 23.3927 16.6074C22.4928 15.7075 21.2726 15.2013 20 15.2ZM20 23.2C19.3671 23.2 18.7484 23.0123 18.2222 22.6607C17.6959 22.3091 17.2858 21.8093 17.0436 21.2246C16.8014 20.6399 16.738 19.9965 16.8615 19.3757C16.985 18.755 17.2897 18.1848 17.7373 17.7373C18.1848 17.2897 18.755 16.985 19.3757 16.8615C19.9965 16.738 20.6399 16.8014 21.2246 17.0436C21.8093 17.2858 22.3091 17.6959 22.6607 18.2222C23.0123 18.7484 23.2 19.3671 23.2 20C23.2 20.8487 22.8629 21.6626 22.2627 22.2627C21.6626 22.8629 20.8487 23.2 20 23.2Z"
                                    fill="#874CD1" />
                            </svg>

                        </template>
                    </CountShowCaseCard>
                    <TimeShowCaseCard title="Usage Time"
                        hour="03" minute="57" second="05" />

                </div>
            </div>

            <div
                class="flex flex-col justify-start items-start gap-8 mb-6">
                <div
                    class="flex justify-between items-center w-full ">
                    <div
                        class="flex justify-start items-center gap-2">
                        <h5
                            class="text-txt-50 dark:text-txt-1000 text-lg font-medium">
                            Data Collected
                        </h5>
                        <svg class="w-6 h-6"
                            viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M18.4166 3H5.58332C3.60833 3 2 4.35736 2 6.03121V13.1838C2 14.8506 3.60833 16.2079 5.58332 16.2079L8.40767 16.2334C8.5893 16.235 8.7569 16.3162 8.85028 16.4477L10.1583 18.2897C10.475 18.7327 11.0333 19 11.65 19C12.2666 19 12.8167 18.7327 13.1333 18.2897L14.5052 16.417C14.6004 16.287 14.7686 16.2079 14.9496 16.2079H18.4167C20.3957 16.2079 22 14.854 22 13.1838V6.03121C22 4.35736 20.3916 3 18.4166 3ZM11.9471 5.73086C12.4353 5.73086 12.7804 6.06781 12.7804 6.43415C12.7804 6.81539 12.4407 7.12843 11.9512 7.12843C11.4542 7.12844 11.1138 6.79946 11.1138 6.43415C11.1138 6.06525 11.4747 5.73086 11.9471 5.73086ZM12.7821 12.7897C12.7821 13.1781 12.4093 13.493 11.9487 13.493C11.4881 13.493 11.1154 13.1781 11.1154 12.7897V9.18149C11.1154 8.7931 11.4881 8.47819 11.9487 8.47819C12.4093 8.47819 12.7821 8.7931 12.7821 9.18149V12.7897Z"
                                fill="#5B616E" />
                        </svg>
                    </div>
                    <Button title="Download.csv"
                        theme="secondary"  >

                        <template v-slot:svg>
                            <svg width="18" height="18"
                                viewBox="0 0 18 18"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
                                    fill="#0F0F0F" />
                            </svg>

                        </template>

                    </Button>

                </div>

                <TableView :data="SampleData"
                    class="mb-4" />

            </div>

        </div>

    </div>

</div> -->
</div>
</template>

<style>.analyticsTitleActiveBorder::after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    bottom: -1px;
    height: 1px;
    width: 100%;
}</style>
