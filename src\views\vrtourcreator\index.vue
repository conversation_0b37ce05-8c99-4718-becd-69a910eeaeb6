<script setup>
import TourSideBar from '../../components/tourcreator/TourSideBar.vue';
import Projector from '../../components/tourcreator/Projector.vue';
import { onMounted, ref } from 'vue';
import { getImages } from '../../api/projects/customtour';
import { useRoute} from 'vue-router';
import { ProjectStore } from '../../store/project';
import Navbar from '../../components/common/Navbar.vue';

const projectStore = ProjectStore();
const route = useRoute();
const imgUrl = ref('');
const project_id = ref(route.params.project_id);
const tour_id = ref(route.params.tour_id);
const Id = ref();
const orientationValue = ref(null);

const emittedImgUrl = (Url) => {
  imgUrl.value = Url;
};

const changeOrientationId = (id, activateOrientation) => {
  Id.value = id,
  orientationValue.value =activateOrientation.value;
};

onMounted(() => {

  getImages(project_id.value, tour_id.value).then((res) => {
    projectStore.tourImages= res;

  })
    .catch((err) => {
      console.log(err);
    });
});
</script>

<template>

  <div class="flex flex-col w-screen h-screen ">
    <div class="w-full z-[2] !p-0 !m-0 dark:bg-bg-50 flex flex-col navbar">
      <Navbar />
    </div>
    <div class="relative h-full ">
      <div class="flex z-[2] w-full absolute h-full left-0 sidebar">
        <TourSideBar @selectedImage="emittedImgUrl" @changeOrientationId="changeOrientationId" />
      </div>

      <div class="flex w-full ">
        <Projector :url="imgUrl" :id="Id" :activateOrientation="orientationValue" />
        <router-view name="addMedia"></router-view>
      </div>
    </div>
  </div>

</template>

<style scoped>
</style>
