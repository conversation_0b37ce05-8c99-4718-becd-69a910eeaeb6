import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../helpers/apihelper';
import type {invitation, Invitation} from '@/types/invitations';
const api_url = import.meta.env.VITE_API_URL;

export async function GetInvitations (): Promise<Record<string, Invitation>>{
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({url: `${api_url}/invitations/GetInvitations`}).then((res) => {
      resolve(res as Record<string, Invitation>);
    }).catch((error: unknown) => {
      reject(error);
    });
  });
}

export async function MoveInvitationToTrash (id:string, obj: invitation):Promise<void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/invitations/moveToTrash/${id}`, body: {
      ...obj,
    }}).then(() => {
      resolve();
    }).catch((error: unknown) => {
      console.log("error", error);
      reject();
    });
  });
}
export async function DeleteInvitation (id:string):Promise<void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/invitations/DeleteInvitation`, body: {
      invitation_id: id,
    }}).then(() => {
      resolve();
    }).catch((error: unknown) => {
      console.log("error", error);
      reject();
    });
  });
}
