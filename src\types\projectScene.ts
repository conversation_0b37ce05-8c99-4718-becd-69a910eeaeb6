import type { transformedSVG } from './projectSVG';
export enum projectSceneType {
  IMAGE = 'image',
  IDENTICAL_UNITPLAN = 'identical_unitplan',
  GSPLAT = 'gsplat',
  CAESIUM = 'caesium',
  NEIGHBOURHOOD = 'neighbourhood',
  ROTATABLE_IMAGE = 'rotatable_image',
  ROTATABLE_IMAGE_FRAME = 'rotatable_image_frame',
  DEEP_ZOOM = 'deep_zoom',
}
export enum  deep_zoom_status{
  NOT_STARTED = 'not_started',
  PROCESSING = 'processing',
  PROCESS_SUCCESS = 'process_success',
  PROCESS_FAILED = 'process_failed',
}
export type projectScene = {
  _id: string;
  organization_id: string;
  type: projectSceneType;
  name: string;
  background: object;
  active: boolean;
  info_icon: string;
  parent: string;
  info_text: string;
  root: boolean;
  building_id: string;
  floor_ids: string[];
  clouds: boolean;
  video: string;
  gsplat_link: string;
  order: number;
  deep_zoom_status:deep_zoom_status;
  deep_zoom_failed_info:string;
};

export type updateProjectSceneObj = {
  type?: string;
  name?: string;
  lowRes?: string;
  highRes?: string;
  file_url?:string;
  active?: boolean;
  info_icon?: string;
  parent?: number;
  info_text?: string;
  root?: boolean;
  building_id?: string;
  clouds?: boolean;
  video?: string;
  gsplat_link?: string;
  category?: string;
  frame_id?: string;
  order?: number;
  _id?: string;
  position?: object;
  polar_angle?: object;
  distance?: object;
  auto_rotate?: boolean;
  deep_zoom_status?:{
    type:string,
    enum:deep_zoom_status
  }
  deep_zoom_failed_info?:string;
};

export type updateProjectSceneFrame = {
  order: number;
  id: string;
};

export type updateBulkSceneType = {
  query: updateProjectSceneFrame[];
  project_id: string;
};

export type background = {
  low_resolution: string;
  high_resolution: string;
};

export type createSceneInputType = {
  organization_id: string;
  type: string;
  name: string;
  active: boolean;
  parent: string;
  info_text: string;
  project_id: string;
  building_id: string;
  root: boolean;
  clouds: boolean;
  gsplat_link: string;
  category?: string;
  position?: string;
  polar_angle?: string;
  distance?: string;
  auto_rotate?: boolean;
  path?: string;
  file_url?:string;
  deep_zoom_status?:string;
}

export type transformedProjectScene = {
  [key: string]: {
    sceneData: projectScene;
    svgData: transformedSVG;
  };
};

export type convertDeepZoom = {
    scene_id: string,
    toType: string,
    fromType: string,
    project_id:string,
    high_resolution: string,
    action:string
}
