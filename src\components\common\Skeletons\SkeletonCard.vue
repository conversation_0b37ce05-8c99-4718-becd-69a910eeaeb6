<script setup>

</script>

<template>
      <div class="bg-[#262626] max-sm:rounded-md rounded-[2px] overflow-hidden cursor-pointer">
                                    <div class="w-full skeleton-loader" style="height: 190px; object-fit: cover;" ></div>
     </div>
</template>

<style scoped>

    .skeleton-loader {
        background-color: #262626;
        background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, .5) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #262626;
        background-size: 200% 100%;
        background-position-x: 180%;
        animation: 1s loading ease-in-out infinite;
    }

    @keyframes loading {
        to {
          background-position-x: -20%;
        }
    }

</style>
