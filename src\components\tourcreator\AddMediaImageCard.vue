<script setup>
import { ref, defineProps, defineEmits } from 'vue';

defineProps({
  data: Object,
});

const imgCheckBox = ref(false);
const emits = defineEmits(['checked']);
const toggleChecked = (event) => {
  if (event.target.checked === true) {
    emits('checked');
  } else {
    emits('uncheck');
  }
};
</script>

<template>

    <label class="custom-checkbox relative max-w-[11.875rem]  cursor-pointer w-full h-[6rem] mt-[1.25rem] mb-[1.25rem] m-2 hover:relative hover:border-[solid] hover:scale-110 hover:z-[3] hover:shadow-[0_0_7px_1px_black]" >
        <img class="object-fill w-full h-full " :src="data.thumbnailUrl"/>
        <input class="w-4 h-4 absolute right-1.5 top-3" type="checkbox" ref="imgCheckBox"  @change="toggleChecked">
        <span class="absolute right-1.5 top-3"></span>
    </label>

</template>
<style scoped>
.custom-checkbox input {
  display: none;
}
.custom-checkbox span {
  border: 2px solid white;
  float: right;
  height: 20px;
  width: 20px;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-checkbox input:checked + span:before {
  content: "✅";
}
</style>
