<script setup>
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ProjectStore } from '../../../store/project';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import Button from '../../common/Button.vue';
import NotfoundImage from '../../common/NotfoundImage.vue';
import { amenityDataSyncUp, getListOfAmenities, moveAmenityToTrash } from '../../../api/projects/amenties/index';
import DeleteModalContent from '../../common/ModalContent/DeleteModalContent.vue';
import Modal from '../../common/Modal/Modal.vue';
import Spinner from '@/components/common/Spinner.vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import Pagination from '../../Pagination.vue';

/* States */
const route = useRoute();
const router = useRouter();
const projectId = ref(route.params.project_id);
const category = ref(route.params.category_id);
const projectStore = ProjectStore();
const AmenityItems = ref([]);
const openDeleteModal = ref(false);
const amenityToDelete = ref();
const deleteLoader = ref(false);
const showTrash = ref(false), trashData = ref([]), load = ref(null), searchQuery = ref('');
const currentPage = ref(1);
const totalPages = ref(1);

/* Methods */
const handleListOfAmenities = () => {
  AmenityItems.value = [];
  getListOfAmenities(projectId.value, category.value).then((response) => {
    AmenityItems.value = response;
    projectStore.SyncMultipleAmenities(response);
  });
};

handleListOfAmenities();

document.addEventListener('refreshAmenitiyListByCategory', () => {
  handleListOfAmenities();
});

async function fetchTrashData () {
  await GetAllTrash(projectId.value, 'amenitys', currentPage.value, 5).then((res) => {
    trashData.value = res.items;
    totalPages.value = res.total;
  });
}
fetchTrashData();

const handleMoveToTrash = () => {
  deleteLoader.value = true;
  if ( projectId.value && amenityToDelete.value ){
    const newObj = {
      amenity_id: [amenityToDelete.value],
      timeStamp: Date.now(),
    };
    moveAmenityToTrash(newObj, projectId.value).then(async () => {
      deleteLoader.value = false;
      handleListOfAmenities();
      fetchTrashData();
      await amenityDataSyncUp(projectId.value);
      openDeleteModal.value = false;
      amenityToDelete.value = null;
    });
  }
};

projectStore.RefreshCommunities(projectId.value);

async function restoreFunc (item) {
  const payload = {
    trash_id: item._id,
  };
  load.value = item._id;
  try {
    await RestoreTrash(payload, projectId.value, 'amenity', 'restoreAmenity');
    await fetchTrashData();
    if (Object.keys(trashData.value).length === 0 && currentPage.value > 1) {
      currentPage.value = currentPage.value - 1;
      await fetchTrashData();
    }
    handleListOfAmenities();
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

const filteredTrashData = computed(() => {
  if (!searchQuery.value) {
    return trashData.value;
  }
  return Object.entries(trashData.value).reduce((result, [key, item]) => {
    const filteredUnitPlans = Object.entries(item.data).filter(([_, unitplan]) => {
      console.log(_);
      const queryLower = searchQuery.value.toLowerCase();
      console.log(unitplan.tour_name);
      return (
        (unitplan.name && unitplan.name.toLowerCase().includes(queryLower)) ||
                (unitplan.tour_name && unitplan.tour_name.toLowerCase().includes(queryLower))
      );
    });
    if (filteredUnitPlans.length > 0) {
      result[key] = { ...item, data: Object.fromEntries(filteredUnitPlans) };
    }
    return result;
  }, {});
});

const totalPagesProps = computed(() => {
  return Math.ceil(totalPages.value / 5);
});

const updateCurrentPage = (page) => {
  currentPage.value = page;
  fetchTrashData();
};

watch(() => trashData.value, (newVal) => {
  trashData.value = newVal;
});
watch(() => currentPage.value, (newVal) => {
  currentPage.value = newVal;
});

</script>

<template>
    <div class="relative bg-transparent pb-5">
        <!-- Header -->
        <div class="mb-6">
            <!-- Title -->
            <div class="mb-4 px-8 pt-6 flex justify-between">
                <div class="flex flex-col justify-start items-start gap-3">
                    <h3 class="text-txt-50 dark:text-txt-1000 text text-2xl font-semibold">Amenities</h3>
                    <p class="text-gray-600 dark:text-txt-650 text-base font-normal mb-0"> Lorem, ipsum dolor sit amet
                        consectetur adipisicing elit. </p>
                </div>
                <div class="flex items-end gap-4">

                        <Button v-if="AmenityItems && Object.keys(AmenityItems).length > 1" title="ReOrder Items"
                            theme="secondary" class="h-10"
                            @click="router.push(`/projects/${projectId}/amenities/${category}/reorder`)">

                            <template v-slot:svg>
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.85746 12.5061C6.36901 10.6456 4.59564 8.59915 3.62734 7.44867C3.3276 7.09253 3.22938 6.8319 3.17033 6.3728C2.96811 4.8008 2.86701 4.0148 3.32795 3.5074C3.7889 3 4.60404 3 6.23433 3H17.7657C19.396 3 20.2111 3 20.672 3.5074C21.133 4.0148 21.0319 4.8008 20.8297 6.37281C20.7706 6.83191 20.6724 7.09254 20.3726 7.44867C19.403 8.60062 17.6261 10.6507 15.1326 12.5135C14.907 12.6821 14.7583 12.9567 14.7307 13.2614C14.4837 15.992 14.2559 17.4876 14.1141 18.2442C13.8853 19.4657 12.1532 20.2006 11.226 20.8563C10.6741 21.2466 10.0043 20.782 9.93278 20.1778C9.79643 19.0261 9.53961 16.6864 9.25927 13.2614C9.23409 12.9539 9.08486 12.6761 8.85746 12.5061Z"
                                        stroke="black" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>

                            </template>

                        </Button>

                        <div>
                            <Button title="Create Amenities" theme="primary"
                                @click="router.push(`/projects/${projectId}/amenities/create`)">
                                <template v-slot:svg>
                                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0_306_21007)">
                                            <path class="fill-txt-1000 dark:fill-txt-default"
                                                d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                                            <path class="fill-txt-1000 dark:fill-txt-default"
                                                d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_306_21007">
                                                <rect width="12" height="12" fill="white"
                                                    transform="translate(0.00390625 0.5)" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </template>
                            </Button>
                        </div>
                        <Button title="Trashed Amenities" @click="showTrash = !showTrash" theme="primary"></Button>
                </div>
            </div>
        </div>
        <div class="mt-3 text-white px-8" v-if="!showTrash">
            <div v-if="AmenityItems && Object.keys(AmenityItems).length !== 0" class="h-fit">
                <div
                    :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ']">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Name</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Categories</th>
                                <!-- <th class="p-3 text-left text-sm font-semibold text-gray-900">media</th> -->
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Community</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Preview</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900"></th>

                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="items, index in AmenityItems" :key="index"
                                class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">

                                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                                    {{ items.name }}
                                </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                                    {{ items.category ?
                                    items.category :
                                    '-' }}
                                </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ items?.community_id ?
                                    projectStore.communities?.[items.community_id]?.name
                                    : '-' }}
                                </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <div v-if="items.media_type !== 'embed_link'">
                                        <img v-if="items.thumbnail" class="w-10 rounded-sm" :src="items.thumbnail" alt="" />
                                        <p v-else class="text-left"> - </p>
                                    </div>
                                    <div v-else>
                                        <a :href="items.embed_link">Link</a>
                                    </div>

                                </td>
                                <td class="p-3 flex justify-center">
                                    <Menu as="div" class="relative whitespace-nowrap flex justify-center items-center">
                                        <div>
                                            <MenuButton as="div"
                                                class="inline-flex w-full mr-1.5 rounded-md bg-inherit py-0 text-xs text ring-gray-300 cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                    fill="currentColor" class="w-6 h-6 fill-black">
                                                    <path fillRule="evenodd"
                                                        d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                                                        clipRule="evenodd" />
                                                </svg>
                                            </MenuButton>
                                        </div>

                                        <transition enter-active-class="transition ease-out duration-100"
                                            enter-from-class="transform opacity-0 scale-95"
                                            enter-to-class="transform opacity-100 scale-100"
                                            leave-active-class="transition ease-in duration-75"
                                            leave-from-class="transform opacity-100 scale-100"
                                            leave-to-class="transform opacity-0 scale-95">
                                            <MenuItems
                                                class="absolute -top-2 right-6 z-80 mt-2 w-fit origin-top-right rounded-md bg-neutral-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                                <div class="py-2 flex flex-col">
                                                    <MenuItem>
                                                    <router-link
                                                        :to="{ path: `/projects/${projectId}/amenities/${items._id}/edit` }"
                                                        class="text-gray-300 block px-3 py-1 text-xs hover:text-white">
                                                        Edit
                                                    </router-link>
                                                    </MenuItem>
                                                    <MenuItem>
                                                    <div
                                                    @click="()=>{openDeleteModal=true;amenityToDelete=items._id}"
                                                        class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Delete
                                                    </div>
                                                    </MenuItem>
                                                </div>
                                            </MenuItems>
                                        </transition>
                                    </Menu>
                                </td>

                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div v-else class="w-full">
                <div class="w-fit m-auto">
                    <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink">
                        <rect width="300" height="286" fill="url(#pattern0)" />
                        <NotfoundImage />
                    </svg>
                </div>
                <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Amenities Created</div>
                <Button class="mt-8 mx-auto" title="Create Amenities"
                    @click="router.push(`/projects/${projectId}/amenities/create`)" theme="primary">
                    <template v-slot:svg>
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_306_21007)">
                                <path class="fill-txt-1000 dark:fill-txt-default"
                                    d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                                <path class="fill-txt-1000 dark:fill-txt-default"
                                    d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                            </g>
                            <defs>
                                <clipPath id="clip0_306_21007">
                                    <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                                </clipPath>
                            </defs>
                        </svg>
                    </template>
                </Button>
            </div>
        </div>
        <div class="mt-3 text-white px-8" v-if="showTrash">
            <span class="flex pb-4 font-bold text-black">Trashed Buildings</span>
            <input
                type="text"
                v-model="searchQuery"
                placeholder="Search"
                class="border px-2 py-1 mb-3 rounded text-black"
            />
            <div v-if="filteredTrashData && Object.keys(filteredTrashData).length !== 0" class="h-fit">
                <div class="overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Name</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Categories</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="(item, index) in filteredTrashData" :key="index">
                                <tr v-for="amenity, amenityId in item.data"
                                :key="amenityId"
                                class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                                    <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                                        {{ amenity.name }}
                                    </td>
                                    <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                                        {{ amenity.category ?
                                            amenity.category :
                                        '-' }}
                                    </td>
                                    <td class="flex items-center gap-2 p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap cursor-pointer" @click="restoreFunc(item)">
                                        Restore
                                        <Spinner v-if="load === item._id"/>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <Pagination v-if="totalPagesProps > 1" :totalPages="totalPagesProps" :currentPage="currentPage" @currentPageSync="updateCurrentPage"/>
            </div>
            <div v-else class="w-full">
                <div class="w-fit m-auto">
                    <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <rect width="300" height="286" fill="url(#pattern0)"/>
                        <NotfoundImage/>
                    </svg>
                </div>
                <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Trashed Amenities Available</div>
            </div>
        </div>
        <Modal :open="openDeleteModal">
          <DeleteModalContent
          :trash="true"
            @closeModal="(e) => openDeleteModal = false"
            :loader="deleteLoader"
            @handleDelete="handleMoveToTrash"
            :dataName="'Amenity'" />
        </Modal>
    </div>

</template>
