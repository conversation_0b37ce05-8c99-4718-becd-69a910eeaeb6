<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import SvgOverlay from '../SvgOverlay.vue';
import { Org_Store } from '../../store/organization';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import Modal from '../common/Modal/Modal.vue';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import CreateCoordinates from '../common/ModalContent/CreateCoordinates.vue';

const route = useRoute();

const sceneId = ref(route.params.scene_id);

const newOrganizationStore = Org_Store();
const openDeleteCoordinateModal = ref(false);
const loader = ref([]);
const selectedCoordinate = ref();
const openUpdateCoordinateModal = ref(false);

newOrganizationStore.RefreshMasterScenes();

// Const handleDeleteCoordinate = () => {
//     If (selectedCoordinate.value) {
//         Const data = {
//             MasterSceneId: sceneId.value,
//             CoordinateId: selectedCoordinate.value._id,
//         };
//         Loader.value.push('deleteCoordinate');
//         OrganizationStore.deleteCoordinates(data).then(() => {
//             Loader.value.splice(loader.value.indexOf('deleteCoordinate'), 1);
//             OpenDeleteCoordinateModal.value = false;
//             SelectedCoordinate.value = null;
//         }).catch(() => {
//             Loader.value.splice(loader.value.indexOf('deleteCoordinate'), 1);
//         });
//     }
// };
// Const handleUpdateCoordinates = (data) => {
//     If (selectedCoordinate.value) {
//         Data.masterSceneId = sceneId.value;
//         Data.coordinateId = selectedCoordinate.value._id;

//         Loader.value.push('updateCoordinates');
//         OrganizationStore.updateCoordinates(data).then(() => {
//             Loader.value.splice(loader.value.indexOf('updateCoordinate'), 1);
//             OpenUpdateCoordinateModal.value = false;
//             SelectedCoordinate.value = null;
//         }).catch(() => {
//             Loader.value.splice(loader.value.indexOf('updateCoordinate'), 1);
//         });
//     }
// };
</script>

<template>
    <div class="bg-neutral-900 h-full w-full text-white">
        <div class="h-full flex justify-center items-center text-white"
            v-if="newOrganizationStore.masterScenes?.[sceneId] && newOrganizationStore.masterScenes?.[sceneId].sceneData.type !== 'earth'">
            <SvgOverlay ref="containerRef"
                style="height:100%;width:100%"
                :Data="newOrganizationStore.masterScenes[sceneId]"
                bucketURL="propvr-in-31420.appspot.com"
                replaceURL="storagecdn.propvr.ai" />
        </div>

        <div
            v-else-if="newOrganizationStore.masterScenes?.[sceneId]">
            <div style="height: inherit;"
                class="mx-auto max-w-7xl hidden sm:block  ">
                <div style="height: inherit;"
                    class="bg-neutral-900">
                    <div style="height: inherit;"
                        class="p-3 sm:px-6 flex flex-col lg:px-8">
                        <div
                            class="sm:flex sm:items-center sm:justify-between mb-3">
                            <div class="min-w-0 flex-1">
                                <h2
                                    class="text-2xl font-bold -tracking-2 leading-7 text-white sm:truncate sm:text-3xl sm:tracking-tight">
                                    Coordinates</h2>
                            </div>
                        </div>
                        <div style="height: inherit;"
                            class="pt-2 flow-root">
                            <div style="height: inherit; overflow-y: scroll;"
                                class="-mx-4 -my-2 min-h-full sm:-mx-6 lg:-mx-8 h-full">
                                <div v-if="false"
                                    class=" flex w-full m-auto justify-center p-4">
                                    <div class="w-full">
                                        <img class="w-72 m-auto"
                                            :src="noDataFound"
                                            alt="">
                                        <p
                                            class="text-xs text-center text-neutral-100 mt-2">
                                            Oops! No Data Found,
                                            Contact
                                            admin
                                            to Coordinates</p>
                                    </div>
                                </div>
                                <div v-else
                                    style="height: inherit;"
                                    class="inline-block min-w-full align-middle sm:px-6 lg:px-8 ">
                                    <table
                                        class="min-w-full pb-40 overflow-y-auto ">
                                        <thead>
                                            <tr>
                                                <th scope="col"
                                                    class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                                                    name
                                                </th>
                                                <th scope="col"
                                                    class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                                                    latitude
                                                </th>
                                                <th scope="col"
                                                    class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                                                    longitude
                                                </th>
                                                <th scope="col"
                                                    class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                                                    link
                                                </th>

                                                <th scope="col"
                                                    class="relative  w-8">

                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody
                                            class="divide-y divide-[#56565680]">
                                            <tr v-for="elem, elemId in newOrganizationStore.masterScenes?.[sceneId].sceneData.coordinates"
                                                :key="elemId">
                                                <td
                                                    class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                                                    {{ elem.name
                                                    }} </td>
                                                <td
                                                    class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                                                    {{ elem.lat
                                                    }}
                                                </td>
                                                <td
                                                    class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                                                    {{ elem.lng
                                                    }}
                                                </td>
                                                <td
                                                    class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                                                    {{
                                                        newOrganizationStore.masterScenes[elem.link].sceneData.name
                                                    }}
                                                </td>

                                                <td
                                                    class="relative whitespace-nowrap text-right text-sm font-medium sm:pr-0 h-full">
                                                    <Menu
                                                        as="div"
                                                        class="relative inline-block text-left">
                                                        <div>
                                                            <MenuButton
                                                                as="div"
                                                                class="inline-flex w-full mr-1.5 rounded-md bg-inherit py-0 text-xs text-white ring-gray-300 cursor-pointer">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    viewBox="0 0 24 24"
                                                                    fill="currentColor"
                                                                    class="w-6 h-6 fill-white">
                                                                    <path
                                                                        fillRule="evenodd"
                                                                        d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                                                                        clipRule="evenodd" />
                                                                </svg>
                                                            </MenuButton>
                                                        </div>

                                                        <transition
                                                            enter-active-class="transition ease-out duration-100"
                                                            enter-from-class="transform opacity-0 scale-95"
                                                            enter-to-class="transform opacity-100 scale-100"
                                                            leave-active-class="transition ease-in duration-75"
                                                            leave-from-class="transform opacity-100 scale-100"
                                                            leave-to-class="transform opacity-0 scale-95">
                                                            <MenuItems
                                                                class="absolute -top-2 right-6 z-50 mt-2 w-fit origin-top-right rounded-md
                                                                bg-neutral-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                                                <div
                                                                    class="py-2 flex flex-col">
                                                                    <MenuItem>
                                                                    <a href="#"
                                                                        @click="() => { openUpdateCoordinateModal = true, selectedCoordinate = elem }"
                                                                        class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Update
                                                                        Coordinate</a>
                                                                    </MenuItem>
                                                                    <MenuItem>
                                                                    <a href="#"
                                                                        @click="() => { openDeleteCoordinateModal = true, selectedCoordinate = elem }"
                                                                        class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Delete
                                                                        Coordinate</a>
                                                                    </MenuItem>
                                                                    <MenuItem>
                                                                    <a href="#"
                                                                        @click="{ }"
                                                                        class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Update
                                                                        Co-Setting</a>
                                                                    </MenuItem>
                                                                </div>
                                                            </MenuItems>
                                                        </transition>
                                                    </Menu>

                                                </td>
                                            </tr>
                                            <div
                                                class=" border-0 h-36">
                                            </div>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <Modal :open="openDeleteCoordinateModal">
            <DeleteModalContent
                @closeModal="(e) => openDeleteCoordinateModal = false"
                @handleDelete="handleDeleteCoordinate"
                :dataName="'Coordinate'" :loader="loader"
                :loaderValue="'deleteCoordinate'" />
        </Modal>

        <Modal :open="openUpdateCoordinateModal">
            <CreateCoordinates :sceneId="sceneId"
                :dataName="'Update Coordinates'"
                @handleSubmit="handleUpdateCoordinates"
                @closeModal="() => openUpdateCoordinateModal = !openUpdateCoordinateModal"
                :coordinateValues="selectedCoordinate"
                :loader="loader"
                :loaderValue="'updateCoordinates'" />
        </Modal>

    </div>
</template>
