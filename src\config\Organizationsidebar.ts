export const menuListTop = [{
  name: 'project', route: '/projects', roles: ['admin'], icon: `<svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
	<title>projects</title>

	<g>
		<path class="s0" d="m8.9 2.1h-4.7c-1.2 0-2.1 0.9-2.1 2.1v4.7c0 1.1 0.9 2 2.1 2h4.7c1.1 0 2-0.9 2-2v-4.7c0-1.2-0.9-2.1-2-2.1z"/>
		<path class="s0" d="m19.8 2.1h-4.7c-1.1 0-2 0.9-2 2.1v4.7c0 1.1 0.9 2 2 2h4.7c1.2 0 2.1-0.9 2.1-2v-4.7c0-1.2-0.9-2.1-2.1-2.1z"/>
		<path class="s0" d="m8.9 13.1h-4.7c-1.2 0-2.1 0.9-2.1 2v4.7c0 1.2 0.9 2.1 2.1 2.1h4.7c1.1 0 2-0.9 2-2.1v-4.7c0-1.1-0.9-2-2-2z"/>
		<path class="s0" d="m19.8 13.1h-4.7c-1.1 0-2 0.9-2 2v4.7c0 1.2 0.9 2.1 2 2.1h4.7c1.2 0 2.1-0.9 2.1-2.1v-4.7c0-1.1-0.9-2-2.1-2z"/>
	</g>
</svg>
`}, {
  name: 'session', route: '/sessions', roles: ['admin', 'editor', 'reader'], icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-6 w-6">
                  <g data-name="Layer 2">
                      <g data-name="calendar">
                          <rect width="24" height="24" opacity="0" />
                          <path
                              d="M18 4h-1V3a1 1 0 0 0-2 0v1H9V3a1 1 0 0 0-2 0v1H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3zM6 6h1v1a1 1 0 0 0 2 0V6h6v1a1 1 0 0 0 2 0V6h1a1 1 0 0 1 1 1v4H5V7a1 1 0 0 1 1-1zm12 14H6a1 1 0 0 1-1-1v-6h14v6a1 1 0 0 1-1 1z" />
                          <circle cx="8" cy="16" r="1" />
                          <path d="M16 15h-4a1 1 0 0 0 0 2h4a1 1 0 0 0 0-2z" />
                      </g>
                  </g>
              </svg>`}, {
  name: 'leads', route: '/leads', roles: ['admin'], icon: `<svg width="24" height="24" viewBox="0 0 24 24" class="h-6 w-6"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                              d="M19.4062 4.71096C18.6436 3.93261 17.7334 3.31408 16.729 2.89154C15.7245 2.46899 14.6459 2.25091 13.5562 2.25003H13.5253C12.4406 2.24724 11.366 2.45967 10.364 2.87501C9.36189 3.29036 8.45217 3.90036 7.68748 4.66971L1.93216 10.5C1.65353 10.7818 1.49773 11.1624 1.49878 11.5587C1.49984 11.955 1.65766 12.3347 1.93779 12.615L4.62466 15.3019C4.76375 15.4417 4.92914 15.5525 5.11129 15.6279C5.29345 15.7034 5.48875 15.742 5.68591 15.7416H5.69435C5.89294 15.7405 6.08935 15.7 6.27217 15.6225C6.45499 15.5449 6.62058 15.4318 6.75935 15.2897L12.4687 9.43971C12.7475 9.16371 13.1229 9.00726 13.5152 9.00358C13.9075 8.99991 14.2858 9.1493 14.5697 9.42003C14.71 9.55634 14.8215 9.71942 14.8977 9.89962C14.9739 10.0798 15.0131 10.2735 15.0131 10.4691C15.015 10.6773 14.9758 10.8838 14.8978 11.0769C14.8198 11.2699 14.7044 11.4457 14.5584 11.5941L8.71873 17.2388C8.57609 17.3773 8.46242 17.5427 8.38431 17.7256C8.30619 17.9084 8.26521 18.1049 8.26372 18.3037C8.26224 18.5025 8.30029 18.6996 8.37567 18.8836C8.45104 19.0676 8.56224 19.2347 8.70279 19.3753L11.3897 22.0622C11.6698 22.3406 12.0481 22.4978 12.443 22.4999C12.8379 22.502 13.2178 22.3489 13.5009 22.0735L19.2947 16.4063C22.5243 13.1719 22.574 7.92753 19.4062 4.71096ZM5.68591 14.2397L2.99998 11.5538L5.23123 9.29253L7.90498 11.9663L5.68591 14.2397ZM12.449 21L9.76029 18.3141L12.0459 16.1044L14.7187 18.78L12.449 21ZM18.239 15.3338L15.7912 17.7282L13.125 15.0638L15.6037 12.6666L15.6131 12.6582C15.9006 12.3683 16.1279 12.0244 16.2819 11.6463C16.4359 11.2681 16.5135 10.8633 16.5103 10.455C16.5086 10.0605 16.4277 9.67042 16.2725 9.30776C16.1173 8.94511 15.8908 8.6173 15.6065 8.34378C15.0369 7.80195 14.2784 7.50369 13.4923 7.51245C12.7062 7.5212 11.9546 7.83627 11.3972 8.39065L8.95216 10.8919L6.28123 8.22565L8.75154 5.72721C9.3769 5.09825 10.1208 4.59963 10.9403 4.26024C11.7597 3.92085 12.6384 3.74743 13.5253 3.75003H13.5506C14.4421 3.7508 15.3246 3.92928 16.1463 4.27504C16.9681 4.62081 17.7127 5.12691 18.3365 5.76378C20.9297 8.39534 20.8828 12.6919 18.239 15.3357V15.3338Z" />
                      </svg>`},
{
  name: 'notification', route: '/notification', roles: ['admin'],
  icon: `<svg id="svg" width="16" height="17" viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg">
                       <path d="M14.4231 12.1284H2.18618C2.20218 12.1119 2.21854 12.0957 2.23523 12.0797L2.24147 12.0738L2.24753 12.0676C3.38013 10.9172 4.02322 9.39465 4.04176 7.80264L4.0418 7.80264V7.79612L4.0418 5.78007L4.0418 5.77908C4.03984 4.67701 4.4782 3.61188 5.27357 2.80431C6.06302 2.00274 7.14473 1.516 8.30038 1.44863C9.45677 1.51502 10.5395 2.00134 11.3299 2.80302C12.1261 3.6107 12.565 4.67639 12.5631 5.77911V5.78007V7.23164H12.5566L12.5632 7.79816C12.5817 9.39017 13.2248 10.9127 14.3574 12.0632L14.3635 12.0693L14.3697 12.0753C14.3879 12.0927 14.4057 12.1104 14.4231 12.1284ZM8.30013 15.6005C8.05144 15.6001 7.80567 15.5619 7.57179 15.4884H9.02847C8.79459 15.5619 8.54883 15.6001 8.30013 15.6005Z" stroke="#666666" fill-rule="evenodd" stroke-width="1.12"/>
                     </svg>
                    `,
},
// {
//   name: 'Landmarks', route: '/landmarks', roles: ['admin'], icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//   <path d="M14.5 9C14.5 10.3807 13.3807 11.5 12 11.5C10.6193 11.5 9.5 10.3807 9.5 9C9.5 7.61929 10.6193 6.5 12 6.5C13.3807 6.5 14.5 7.61929 14.5 9Z" stroke="#666666" stroke-width="1.5"/>
//   <path d="M18.2222 17C19.6167 18.9885 20.2838 20.0475 19.8865 20.8999C19.8466 20.9854 19.7999 21.0679 19.7469 21.1467C19.1724 22 17.6875 22 14.7178 22H9.28223C6.31251 22 4.82765 22 4.25311 21.1467C4.20005 21.0679 4.15339 20.9854 4.11355 20.8999C3.71619 20.0475 4.38326 18.9885 5.77778 17" stroke="#666666" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
//   <path d="M13.2574 17.4936C12.9201 17.8184 12.4693 18 12.0002 18C11.531 18 11.0802 17.8184 10.7429 17.4936C7.6543 14.5008 3.51519 11.1575 5.53371 6.30373C6.6251 3.67932 9.24494 2 12.0002 2C14.7554 2 17.3752 3.67933 18.4666 6.30373C20.4826 11.1514 16.3536 14.5111 13.2574 17.4936Z" stroke="#666666" stroke-width="1.5"/>
//   </svg>
//   `},
{
  name: 'Scene', route: '/masterscenes', roles: ['admin'], icon: `<svg width="24" height="24" viewBox="0 0 24 24" width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M6 17.9745C6.1287 19.2829 6.41956 20.1636 7.07691 20.8209C8.25596 22 10.1536 22 13.9489 22C17.7442 22 19.6419 22 20.8209 20.8209C22 19.6419 22 17.7442 22 13.9489C22 10.1536 22 8.25596 20.8209 7.07691C20.1636 6.41956 19.2829 6.1287 17.9745 6" stroke="#666666" stroke-width="1.5"/>
  <path d="M2 10C2 6.22876 2 4.34315 3.17157 3.17157C4.34315 2 6.22876 2 10 2C13.7712 2 15.6569 2 16.8284 3.17157C18 4.34315 18 6.22876 18 10C18 13.7712 18 15.6569 16.8284 16.8284C15.6569 18 13.7712 18 10 18C6.22876 18 4.34315 18 3.17157 16.8284C2 15.6569 2 13.7712 2 10Z" stroke="#666666" stroke-width="1.5"/>
  <path d="M5 18C8.42061 13.2487 12.2647 6.94746 18 11.6734"/>
  </svg>`},
{
  name: 'user', route: '/users', roles: ['admin'], icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-6 w-6">
              <g data-name="Layer 2">
                  <g data-name="people">
                      <rect width="24" height="24" opacity="0" />
                      <path d="M9 11a4 4 0 1 0-4-4 4 4 0 0 0 4 4zm0-6a2 2 0 1 1-2 2 2 2 0 0 1 2-2z" />
                      <path d="M17 13a3 3 0 1 0-3-3 3 3 0 0 0 3 3zm0-4a1 1 0 1 1-1 1 1 1 0 0 1 1-1z" />
                      <path
                          d="M17 14a5 5 0 0 0-3.06 1.05A7 7 0 0 0 2 20a1 1 0 0 0 2 0 5 5 0 0 1 10 0 1 1 0 0 0 2 0 6.9 6.9 0 0 0-.86-3.35A3 3 0 0 1 20 19a1 1 0 0 0 2 0 5 5 0 0 0-5-5z" />
                  </g>
              </g>
          </svg>`},
{
  name: 'analytics', route: '/analytics', roles: ['admin'], icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 17V13" stroke="#666666" stroke-width="1.5" stroke-linecap="round"/>
            <path d="M12 17V7" stroke="#666666" stroke-width="1.5" stroke-linecap="round"/>
            <path d="M17 17V11" stroke="#666666" stroke-width="1.5" stroke-linecap="round"/>
            <path d="M2.5 12C2.5 7.52166 2.5 5.28249 3.89124 3.89124C5.28249 2.5 7.52166 2.5 12 2.5C16.4783 2.5 18.7175 2.5 20.1088 3.89124C21.5 5.28249 21.5 7.52166 21.5 12C21.5 16.4783 21.5 18.7175 20.1088 20.1088C18.7175 21.5 16.4783 21.5 12 21.5C7.52166 21.5 5.28249 21.5 3.89124 20.1088C2.5 18.7175 2.5 16.4783 2.5 12Z" stroke="#666666" stroke-width="1.5" stroke-linejoin="round"/>
            </svg>
            `},
{
  name: 'translate', route: '/translate', roles: ['admin'],
  icon: `<svg id="svg" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="h-6" viewBox="0, 0, 400,400"><g id="svgg"><path id="path0" d="M23.553 1.554 C 14.250 4.915,7.354 11.162,2.902 20.258 L 0.391 25.391 0.391 115.234 L 0.391 205.078 2.902 210.210 C 10.103 224.925,20.338 230.469,40.304 230.469 L 51.563 230.469 51.565 249.805 C 51.568 267.721,51.682 269.328,53.121 271.687 C 62.918 287.755,107.584 266.924,125.332 238.010 L 129.962 230.469 165.680 230.469 C 199.084 230.469,201.723 230.368,206.412 228.920 C 216.587 225.777,225.768 216.616,228.898 206.484 C 231.352 198.537,231.357 30.951,228.903 23.818 C 225.637 14.327,219.482 7.440,210.210 2.902 L 205.078 0.391 116.406 0.217 C 30.465 0.048,27.606 0.089,23.553 1.554 M288.672 0.898 C 286.234 2.224,260.966 27.481,259.713 29.844 C 258.246 32.609,258.288 37.792,259.803 40.984 C 262.059 45.738,286.667 69.247,290.455 70.267 C 303.018 73.650,311.114 59.103,302.111 49.325 L 300.391 47.456 304.297 48.002 C 343.710 53.516,372.787 84.958,375.696 125.208 C 376.549 137.013,379.700 141.016,388.143 141.016 C 413.505 141.016,396.671 77.387,363.897 49.367 C 346.722 34.684,319.902 23.437,302.060 23.437 L 299.708 23.437 301.766 21.289 C 311.630 10.991,300.883 -5.746,288.672 0.898 M121.174 58.805 C 124.899 61.294,126.560 65.021,126.561 70.898 L 126.563 75.781 145.508 75.782 C 166.706 75.784,168.283 76.108,171.664 81.170 C 177.629 90.100,170.344 98.438,156.576 98.438 L 149.421 98.438 148.903 103.011 C 147.622 114.334,142.251 128.984,136.013 138.170 C 134.253 140.762,132.813 143.045,132.813 143.243 C 132.813 144.885,154.735 150.781,160.841 150.781 C 173.029 150.781,177.933 165.689,167.781 171.879 C 161.277 175.844,140.208 171.770,123.024 163.224 L 115.188 159.327 107.742 163.118 C 91.306 171.486,69.120 175.800,62.688 171.879 C 52.494 165.664,57.389 150.781,69.628 150.781 C 75.582 150.781,97.656 144.897,97.656 143.310 C 97.656 143.149,95.937 140.384,93.836 137.165 C 87.601 127.614,82.859 114.447,81.579 103.127 L 81.048 98.438 73.532 98.425 C 60.057 98.402,53.082 90.472,58.590 81.438 C 61.888 76.028,62.995 75.787,84.570 75.784 L 103.906 75.781 103.907 70.898 C 103.910 62.435,108.323 57.031,115.234 57.031 C 117.349 57.031,119.465 57.663,121.174 58.805 M103.906 100.243 C 103.906 109.060,113.218 129.651,116.126 127.264 C 118.248 125.523,123.948 112.631,125.313 106.486 C 127.264 97.707,128.178 98.438,115.234 98.438 C 103.918 98.438,103.906 98.439,103.906 100.243 M326.172 124.186 C 304.740 129.844,285.966 143.630,274.668 162.003 L 270.038 169.531 261.582 169.531 L 253.125 169.531 253.125 186.203 C 253.125 212.319,249.711 222.221,235.966 235.966 C 222.218 249.713,212.321 253.125,186.189 253.125 L 169.503 253.125 169.712 313.477 L 169.922 373.828 171.674 378.125 C 175.209 386.794,182.738 394.322,191.406 397.857 L 195.703 399.609 285.156 399.609 L 374.609 399.609 380.078 396.937 C 387.234 393.441,393.441 387.234,396.937 380.078 L 399.609 374.609 399.609 284.766 L 399.609 194.922 397.098 189.790 C 389.897 175.075,379.662 169.531,359.696 169.531 L 348.438 169.531 348.435 150.195 C 348.430 122.733,345.342 119.125,326.172 124.186 M290.705 228.320 C 294.318 230.713,342.971 327.094,342.966 331.850 C 342.957 340.270,333.179 345.737,326.031 341.319 C 323.928 340.019,322.291 337.533,318.316 329.600 L 313.270 319.531 284.955 319.535 L 256.641 319.538 251.703 329.310 C 244.969 342.634,239.647 345.939,232.219 341.410 C 222.990 335.783,222.791 336.424,251.367 279.494 C 278.599 225.242,277.766 226.563,284.766 226.563 C 286.864 226.563,289.008 227.197,290.705 228.320 M5.859 260.047 C -0.127 263.745,-1.602 272.446,1.101 288.114 C 9.290 335.564,45.731 369.576,95.431 376.154 L 99.064 376.635 96.407 380.584 C 89.537 390.792,96.955 401.918,109.110 399.637 C 111.573 399.175,114.224 396.944,125.240 386.066 C 146.818 364.756,146.818 364.070,125.264 342.803 C 118.398 336.029,112.434 330.816,110.739 330.108 C 97.797 324.701,88.157 339.939,97.824 350.522 L 99.371 352.215 95.974 351.786 C 57.452 346.919,24.372 311.760,24.200 275.500 C 24.136 262.049,15.034 254.380,5.859 260.047 M276.361 280.078 L 267.973 296.875 284.768 296.875 C 294.005 296.875,301.563 296.696,301.563 296.477 C 301.563 295.956,285.210 263.281,284.949 263.281 C 284.839 263.281,280.974 270.840,276.361 280.078 " stroke="none" fill-rule="evenodd"></path></g></svg>
            `,
},
{
  name: 'trash', route: '/trash', roles: ['admin'],
  icon: `<svg width="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M8 3a2 2 0 012-2h4a2 2 0 012 2v1h4a1 1 0 110 2H4a1 1 0 110-2h4V3zm2 0v1h4V3h-4zM6 22a2 2 0 01-2-1.85L3 7h18l-1 13.15A2 2 0 0118 22H6zm3-11a1 1 0 00-1 1v7a1 1 0 002 0v-7a1 1 0 00-1-1zm5 0a1 1 0 00-1 1v7a1 1 0 002 0v-7a1 1 0 00-1-1z"/>
</svg>
`,
},

{
  name: 'Settings',
  route: '/settings',
  roles: ['admin'],
  icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0  -960 960 960" width="24"><path d="m370-80-16-128q-13-5-24.5-12T307-235l-119 50L78-375l103-78q-1-7-1-13.5v-27q0-6.5 1-13.5L78-585l110-190 119 50q11-8 23-15t24-12l16-128h220l16 128q13 5 24.5 12t22.5 15l119-50 110 190-103 78q1 7 1 13.5v27q0 6.5-2 13.5l103 78-110 190-118-50q-11 8-23 15t-24 12L590-80H370Zm70-80h79l14-106q31-8 57.5-23.5T639-327l99 41 39-68-86-65q5-14 7-29.5t2-31.5q0-16-2-31.5t-7-29.5l86-65-39-68-99 42q-22-23-48.5-38.5T533-694l-13-106h-79l-14 106q-31 8-57.5 23.5T321-633l-99-41-39 68 86 64q-5 15-7 30t-2 32q0 16 2 31t7 30l-86 65 39 68 99-42q22 23 48.5 38.5T427-266l13 106Zm42-180q58 0 99-41t41-99q0-58-41-99t-99-41q-59 0-99.5 41T342-480q0 58 40.5 99t99.5 41Zm-2-140Z"/></svg>',
},
];
