<script setup>
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { UserStore } from '../../../store';
import { uiOperations } from '../../../store/uiOperations';
import { OrganizationStore } from '../../../store/archiveprojects';
import { notAuthorized } from '../../../enum';

/* Emits */
const emit = defineEmits(['closeModal', 'handleModalSaveChanges']);

/* Props and State */
const auth =  UserStore();
const uiStore = uiOperations();
const organizationStore = OrganizationStore();
const props = defineProps({
  sessionType: String,
  projectId: String,
});
const route = useRoute();
const router = useRouter();
const email = ref(null);
const name = ref(null);
const description = ref(null);
const timeSlots = ref(null);
const timeSlotSelection = ref(null);
const dateSlot = ref(null);
const projects = ref(null);
const phonenumber = ref(null);
const erroMsg = ref({
  isShow: false,
  message: null,
});
const presentDateRef = ref(new Date().toISOString().split('T')[0]);
const sessionDTO = {
  name: null,
  email: null,
  timeSlot: null,
  phonenumber: null,
  description: null,
};

/* Methods */
const datePattern = (value) => {
  var regex = /^\d{4}-\d{2}-\d{2}$/; // Pattern
  if (regex.test(value)){
    erroMsg.value.isShow = false;
    erroMsg.value.message = null;
    return true;
  }
  erroMsg.value.isShow = true;
  erroMsg.value.message = 'invalid date';
  return false;

};

const dateValidation = (value) => {
  if (value) {
    if (datePattern(value) ) {
      if (value < presentDateRef.value) {
        erroMsg.value.isShow = true;
        erroMsg.value.message = 'Please enter the future Dates';
        return false;
      }
      erroMsg.value.isShow = false;
      erroMsg.value.message = null;
      return true;
    }
    return false;

  }
  erroMsg.value.isShow = true;
  erroMsg.value.message = 'Please fill the date';
  return false;

};

const validationCheck = (value, fieldName) => {
  if (value) {
    erroMsg.value.isShow = false;
    erroMsg.value.message = null;
    return true;
  }
  erroMsg.value.isShow = true;
  erroMsg.value.message = `Please fill the ${fieldName}`;
  return false;
};

const reFrameTheSelectionTimeSlot = (value) => {
  // HH:MM:SS
  if (value.length === 5){
    // If the time value has HH:MM then ,
    return `${value}:00`;
  }
  // If the time value has H:MM then ,
  const newValue = value.split('');
  newValue.unshift('0');
  return `${newValue.join('')}:00`;

};

const showOnlyValidateSlots = (currentTime, arrayOfSlots) => {
  console.log(currentTime);
  const newArray = arrayOfSlots.filter((item) => {
    const reFramerTheCurrentDateTime = String(currentTime).split(' ');
    reFramerTheCurrentDateTime[4] = reFrameTheSelectionTimeSlot(item.slot); // Update the time in array  // 08:00:00
    console.log(new Date(reFramerTheCurrentDateTime.join(' ')));
    if (new Date(reFramerTheCurrentDateTime.join(' ')) >= currentTime) {
      console.log(item);
      return true;
    }
    return false;

  });
  return newArray;
};

const handleTimeSlotApi = (date, project_Id) => {
  const newObject = {
    project_id: null,
    date: null,
  };

  if ( (props.projectId === null || props.projectId === undefined) && props.sessionType !== 'create'){
    // Project Id is not available then get the project_Id
    if (dateValidation(date) && validationCheck(project_Id)) {
      newObject.date = new Date(date).toISOString();
      newObject.project_id = project_Id;
      console.log(newObject);
      // Api Call
      organizationStore.GetAvailableSlots(newObject).then((res) => {
        if (newObject.date.split('T')[0] === new Date().toISOString().split('T')[0]){
          timeSlots.value =   showOnlyValidateSlots(new Date(), res);// Based on LocalZones
        } else {
          timeSlots.value = res;
        }
      }).catch((err) => {
        console.log(err);
      });

    }
  } else {
    // Project Id is Available then
    if (dateValidation(date) && props.projectId){
      newObject.date = new Date(date).toISOString();
      newObject.project_id = props.projectId;
      // Api Call
      organizationStore.GetAvailableSlots(newObject).then((res) => {
        // ShowOnlyValidateSlots(new Date(),res)
        if (newObject.date.split('T')[0] === new Date().toISOString().split('T')[0]){
          timeSlots.value =  showOnlyValidateSlots(new Date(), res); // Based on LocalZones
        } else {
          timeSlots.value = res;
        }
      }).catch((err) => {
        console.log(err);
      });
    }
  }

};

// DateSlot
watch(dateSlot, () => {
  handleTimeSlotApi(dateSlot.value, projects.value);
});

// Projects
watch(projects, () => {
  if ((props.projectId === null || props.projectId === undefined) && props.sessionType !== 'create'){
    handleTimeSlotApi(dateSlot.value, projects.value);
  }
});

const emailValidation = (value) => {
  if (value) {
    const regex = new RegExp(`[a-z0-9]+@[a-z]+\\.[a-z]{2,3}`); // Pattern
    if (regex.test(value)) {
      erroMsg.value.isShow = false;
      erroMsg.value.message = null;
      return true;
    }
    erroMsg.value.isShow = true;
    erroMsg.value.message = 'Invalid email Id';
    return false;
  }
  erroMsg.value.isShow = true;
  erroMsg.value.message = 'Please fill the email';
  return false;

};

const handleCreateSessionApi = (DTO, project_Id) => {
  if (project_Id && props.sessionType && DTO){
    organizationStore
      .createSession(DTO, props.sessionType, project_Id)
      .then((res) => {
        erroMsg.value.isShow = false;
        erroMsg.value.message = null;
        if (route.path === '/myschedule'){
          console.log(route.path);
          emit('handleModalSaveChanges');
        }
        emit('closeModal');
        window.location = `/salestool/preview/${res._id}`;
        //  Window.location = `/salestool/preview/${res._id}`
        /*   If(organizationStore.lisfOfSchedule === null){
                  organizationStore.listOfLeads = [];
                  organizationStore.lisfOfSchedule.push(res);
            } else {
                   organizationStore.lisfOfSchedule.push(res);
            }  */

      })
      .catch((error) => {
        if (auth.verifyAuth()) {
          router.push('/login');
        } else {
          erroMsg.value.isShow = true;
          if (error.message){
            erroMsg.value.message = error.message;
          } else {
            if (notAuthorized.toLowerCase() !== error.error.toLowerCase()){
              erroMsg.value.message = error.error;
            }
          }

        }
      });
  }
};

const HandleCreateSession = () => {
  if (props.sessionType !== 'create') {
    // Schedule
    if (
      validationCheck(name.value, 'name') &&
      emailValidation(email.value) &&
      dateValidation(dateSlot.value) &&
      validationCheck(timeSlotSelection.value, 'timeslot')
    ) {
      const selectionDate = reFrameTheSelectionTimeSlot(timeSlotSelection.value);
      const isoString = new Date(
        dateSlot.value + 'T' + selectionDate,
      ).toISOString();
      sessionDTO.email = email.value; // Email
      sessionDTO.name = name.value; // Name
      sessionDTO.timeSlot = isoString;  // Iso timeslot
      sessionDTO.description = description.value; // Description
      sessionDTO.phonenumber = phonenumber.value; // Phonenumber
      if ( props.projectId === null || props.projectId === undefined ){
        // Not Available Project Id
        handleCreateSessionApi(sessionDTO, projects.value);
      } else {
        // Available Project Id
        handleCreateSessionApi(sessionDTO, props.projectId);
      }
    }
  } else {
    // Create
    if (
      validationCheck(name.value, 'name') &&
      validationCheck(email.value, 'email') && ( props.projectId === null || props.projectId === undefined  ?  validationCheck(projects.value, 'Projects')  : true )
    ) {
      sessionDTO.email = email.value;
      sessionDTO.name = name.value;
      sessionDTO.timeSlot = null;
      sessionDTO.description = description.value;
      sessionDTO.phonenumber = phonenumber.value;
      if ( props.projectId === null || props.projectId === undefined ) {
        handleCreateSessionApi(sessionDTO, projects.value);
      } else {
        handleCreateSessionApi(sessionDTO, props.projectId);
      }
    }
  }
};

/* PhoneNumber */
const handlePhoneNumberRestriction = (event) => {
  var inputValue = event.target.value.replace(/[^0-9]/g, '');
  // Update the input field with the cleaned value
  event.target.value = inputValue;
};

// For Cross Browser Issue Fix
const handlePhoneNumberRestrictiononKeyPress = (event) => {
  // Allow only numeric characters
  if (!/^\d$/.test(event.key)) {
    event.preventDefault();
  }
};

/* Hooks */

onMounted(() => {
  if ( props.projectId === null || props.projectId === undefined ){
    if (organizationStore.projects === null ){
      organizationStore.GetProjects();
    }
  }
});

</script>

<template>
  <div class="w-[100%] sm:w-auto">
    <div
      class="relative transform overflow-hidden rounded-t-2xl rounded-b-none sm:rounded-t-lg sm:rounded-b-lg bg-neutral-800 text-left shadow-xl transition-all sm:my-8 w-full sm:max-w-md"
    >
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="text-base text-white font-semibold">
            {{
              props.sessionType.toLowerCase() !== "create"
                ? "Schedule Session"
                : "Create Session"
            }}
          </h1>
          <p class="text-sm text-white">Lorem ipsum dolor sit amet elit.</p>
        </div>
        <form @submit.prevent="register" class="sm:w-[400px] mb-1">
          <div class="">
            <div class="flex flex-col">
              <div
                class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
              >
                <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                  Name
                </label>
                <input
                  v-model="name"
                  style="border: 1px solid #737373"
                  type="text"
                  id="Username"
                  name="Username"
                  class="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs focus:outline-none"
                  placeholder="name"
                />
              </div>

              <div
                class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
              >
                <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                  Email Id
                </label>
                <input
                  v-model="email"
                  style="border: 1px solid #737373"
                  type="text"
                  id="email"
                  name="email"
                  class="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs focus:outline-none"
                  placeholder="Email Id"
                />
              </div>

              <div
                class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
              >
                <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                  Phone Number
                </label>
                <input
                  v-model="phonenumber"
                  style="border: 1px solid #737373"
                  type="text"
                  name="phoneNo"
                  id="phoneNo"
                  maxlength="10"
                  @input="handlePhoneNumberRestriction"
                  @keypress="handlePhoneNumberRestrictiononKeyPress"
                  class="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs focus:outline-none"
                  placeholder="Phone Number"
                />
              </div>

              <div
                class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
              >
                <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                  Description
                </label>
                <textarea
                  v-model="description"
                  style="border: 1px solid #737373"
                  id="description"
                  name="description"
                  class="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs focus:outline-none"
                  placeholder="description"
                >
                </textarea>
              </div>

                <div
                v-if=" props.projectId === null || props.projectId === undefined "
                class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                  <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                    Projects
                  </label>
                  <select name="projects" id="projects"  v-model="projects"  style="border: 1px solid #737373" class="w-full h-11 p-2 text-white bg-neutral-800 rounded-lg justify-start items-center inline-flex bg-inherit placeholder:text-left placeholder:text-[#F5F5F5] placeholder:text-xs focus:outline-none" >
                    <option disabled class="p-4" v-if="organizationStore.projects === null || organizationStore.projects.length < 0" > No data Found ! </option>
                    <option :key="project._id" :value="project._id" class="p-4 " v-else  v-for="project, in organizationStore.projects"  :v-if="project.is_public">{{ project.name ? project.name : 'nan' }}</option>
                  </select>
              </div>

              <div
                v-if="sessionType !== 'create'"
                class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
              >
                <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                  Date
                </label>
                <input
                  v-model="dateSlot"
                  style="border: 1px solid #737373"
                  type="date"
                  name="dateSlot"
                  id="dateSlot"
                  :min="presentDateRef"
                  class="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs focus:outline-none"
                />
              </div>

              <div
                v-if="sessionType !== 'create'"
                class="w-full flex-grow h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
              >
                <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                  Time Slot
                </label>

                <div v-if="!uiStore.loader">
                  <div class="flex gap-3 justify-start items-center flex-wrap px-2 " v-if="timeSlots === null || timeSlots.length === 0" >
                    <p class="text-white">

                      {{(props.projectId === null || props.projectId === undefined) && sessionType !== 'create' ? " *Kindly Select Date and Project for Available Slots" : "*Kindly select the Date for Available Slots"  }}

                    </p>
                </div>

                <div v-else class="flex gap-3 justify-start items-center flex-wrap px-2 ">
                    <span :key="index" v-for="item,index in timeSlots"
                    :class="['border-[1px] rounded-md py-2 px-3 cursor-pointer', ( timeSlotSelection === item.slot
                        ? 'bg-blue-600 text-blue-100'
                        : 'border-blue-300 text-blue-200') ,  (item.availableSessions > 0 ?  'bg-tranparent': 'bg-gray-600 select-none') ]"
                    @click="item.availableSessions > 0 ? timeSlotSelection = item.slot : false" :id="index">
                          {{  formatTime(item.slot) }}
                    </span>
                </div>
                </div>

                <p v-else class="text-white ms-2 text-lg">  Loading available slots .... </p>

              </div>
            </div>

            <!-- Error -->
            <p
              v-if="erroMsg.isShow"
              class="text-base text-bold not-italic tracking-normal text-left mt-1 text-[#ff4070]"
            >
              <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
              {{ erroMsg.message }}
            </p>

            <div class="text-center mt-3 flex justify-center items-center">
              <button
                @click="() => emit('closeModal')"
                class="w-1/2 sm:w-fit h-11 sm:h-10 rounded-full sm:rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent m-0 px-3 text-xs text-white font-semibold leading-6"
              >
                Discard
              </button>
              <button
                @click="HandleCreateSession()"
                id="submit"
                type="submit"
                class="ml-3 w-1/2 sm:w-fit h-11 sm:h-10 rounded-full sm:rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-3 text-xs text-white font-semibold leading-6"
              >
                Save Changes
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
