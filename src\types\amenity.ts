export enum MediaType {
    IMAGE = 'image',
    VIDEO = 'video',
    IMAGE_360 = '360_image',
    VIDEO_360 = '360_video',
    VIRTUAL_TOUR = 'virtual_tour',
    PDF = 'pdf',
    LINK = 'embed_link',
    IFRAME = 'iframe',
    MULTIRES = 'multires'
  }

export type Media = {
    _id: string
    media_type: MediaType,
    link?: string,
    media_file?: string
    tour_id?: string
}

export type Link = {
    _id: string,
    position: coordinate,
    text: string,
    destination_img_id: string
}

export type Amenity = {
    _id: string;
    project_id: string,
    name: string,
    community_id?:string,
    category: string,
    thumbnail: string,
    description:string,
    file?:string,
    media_type:string,
    embed_link?:string,
    tour_id?:string,
    links?: { [key: string]: Link },
    rotation?: string,
}

export type CreateAmenityInput = {
    project_id: string,
    name: string,
    community_id?:string,
    category: string,
    thumbnail: string,
    description:string,
    file?:string,
    media_type:string,
    embed_link?:string,
    tour_id?:string,

}

export type UpdateAmenityInput = {
    project_id: string,
    name?: string,
    category?: string,
    media_type?: MediaType,
    file?:string
    embed_link?: string,
    tour_id?: string,
    thumbnail?: string,
    description?: string,
    community_id?:string,

}

export type CreateMediaObj = {
    media_type: string,
    link?: string,
    media_file?: string
    tour_id?: string
}
export type getCategories ={
    category:string,
    count:number
}

export type bulkUpdateQuery = {
    id:string,
    order?:number,
    category?: string
}

export type bulkUpdateType = {
    query:bulkUpdateQuery[],
    project_id:string
}

export type moveAmenityToTrash = {
    amenity_id:string,
    timeStamp:string,
}

export type DeleteMedia = {
    project_id: string,
    amenity_id: string,
    media_id: string,
}

export type MoveGalleryItemToTrash = {
    gallery_id: string,
    timeStamp: string,
}

export type createAmenityFormSubmit = {
    project_id: string,
    name: string,
    communityId?:string,
    category: string,
    thumbnail?: string,
    description:string,
    file?:string,
    mediaType:string,
    link?:string,
    tour_id?:string,
}

export type CategoryList = {
    name : string;
}

export type coordinate = {
    x: string,
    y: string,
    z: string
}

export type hotspotLink = {
    project_id?: string;
    amenity_id?: string;
    link_id?: string,
    position?: coordinate,
    text?: string,
    destination_img_id?: string
}
