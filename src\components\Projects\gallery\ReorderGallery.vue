<script setup>
import { ref } from 'vue';
import NestedReOrder from '@/components/common/NestedReOrder.vue';
import { GetProject } from '@/api/projects/settings';
import { getCategories, updateGallerySettings, getListofGalleryItems, updateBulkGalleryItems, updateGalleryItem } from '@/api/projects/gallery';
import { useRoute } from 'vue-router';
import { ProjectStore } from '@/store/project';
import PDFViewer from '@/components/common/PDFViewer.vue';
import Button from '@/components/common/Button.vue';
import CreateModal from './CreateModal.vue';
import VideoPlayer from '@/components/common/VideoPlayer.vue';
import AFrameComp from '@/components/common/AFrameComp.vue';
import AFrameVideo360 from '@/components/common/AFrameVideo360.vue';

const route = useRoute();
const nestedGallery = ref([]);
const projectId = ref(route.params.project_id);
const originalListOfItems = ref([]);
const listOfItems = ref({});
const hasGallerySettings = ref(false);
const projectStore = ProjectStore();
const openCreateCategory = ref(false);
const originalGalleryItems = ref({}); // { [categoryName]: [originalItemsArray] }
const galleryItems = ref({});
const loading = ref(true);        // { [categoryName]: [currentItemsArray] }
const newCategoryName = ref('');
const selectedItem = ref(null); // Track the selected item
const openCreateGallery = ref(false);

/* Methods */
const handleGetListofGalleryItems = async () => {
  await getListofGalleryItems(projectId.value).then((res) => {
    listOfItems.value = res;
    projectStore.SyncMultipleGalleryItems(res);
    // Convert object of objects to array
    const itemsArray = Object.values(res);
    // Group items by category_key
    const itemsByCategory = {};
    itemsArray.forEach((item) => {
      if (!itemsByCategory[item.category]) {
        itemsByCategory[item.category] = [];
      }
      itemsByCategory[item.category].push(item);
    });
    // Map children to nestedGallery by matching category id to item.category_key
    nestedGallery.value = nestedGallery.value.map((cat) => {
      const children = (itemsByCategory[cat.name] || []).map((item, idx) => {
        return ({
          id: item._id,
          name: item.name,
          order: item.order || idx + 1,
        // ...other fields if needed
        });
      }).sort((a, b) => {
        // Handle cases where order might be undefined or null
        const orderA = a.order || 0;
        const orderB = b.order || 0;
        return orderA - orderB;
      });
      // Initialize tracking objects
      originalGalleryItems.value[cat.name] = children.map((item) => ({ ...item }));
      galleryItems.value[cat.name] = children.map((item) => ({ ...item }));

      return {
        ...cat,
        children,
      };
    });
    // Set selectedItem to the first item of the first category if available
    if (nestedGallery.value.length > 0 && nestedGallery.value[0].children && nestedGallery.value[0].children.length > 0) {
      selectedItem.value = nestedGallery.value[0].children[0];
      selectedItem.value =  listOfItems.value[selectedItem.value.id];
    } else {
      selectedItem.value = null;
    }
    loading.value = false;
  })
    .catch((err) => {
      loading.value = false;
      console.log(err);
    });
};

// Call this after setting nestedGallery in frameTheArrayOfItems
const frameTheArrayOfItems = async () => {
  loading.value =true;
  await GetProject(projectId.value).then(async (projectDetails) => {
    const gallerySettings = projectDetails.projectSettings?.gallery;
    hasGallerySettings.value = !!gallerySettings;
    await getCategories(projectId.value).then((res) => {
      const completeGalleryCategory = res.map((elem) => {
        const objectToFind = gallerySettings
          ? Object.values(gallerySettings).find((item) => item.name === elem)
          : null;
        return objectToFind ? objectToFind : { name: elem };
      });
      completeGalleryCategory.sort((a, b) => {
        // Handle cases where order might be undefined or null
        const orderA = a.order || 0;
        const orderB = b.order || 0;
        return orderA - orderB;
      });

      // For now, just set categories without children
      const nested = completeGalleryCategory.map((cat, index) => ({
        id: cat.id || index + 1,
        name: cat.name,
        order: cat.order || index + 1,
        ...(!cat.id && { type: 'new'}),
      }));
      nestedGallery.value = nested;
      originalListOfItems.value = nested.map((item) => ({ ...item }));
      // Fetch and map gallery items as children
      handleGetListofGalleryItems();
    }).catch((err) => {
      loading.value = false;
      console.log('output->err', err);
    });
  });
};

frameTheArrayOfItems(); // Initialize

function compareTheOriginalListOfItems () {
  if (
    originalListOfItems.value.length !== nestedGallery.value.length ||
    originalListOfItems.value.some((item, idx) => item.id !== nestedGallery.value[idx].id)
  ) {
    const isNew = nestedGallery.value.some((item) => item.type === 'new');

    const listToReturn = {
      isNew,
      // Always return an array of {id, order, name}, but omit id if type is 'new'
      items: nestedGallery.value.map((item, index) => {
        const base = {
          name: item.name,
          order: index + 1,
        };
        if (item.type === 'new') {
          return base;
        }
        return {
          id: item.id,
          ...base,
        };

      })};
    return listToReturn;
  }
  return false;
}

async function handleCategorySort () {
  const changed = compareTheOriginalListOfItems();
  if (changed) {
    const reqBody = {
      query: changed.items,
      project_id: projectId.value,
    };
    await updateGallerySettings(reqBody).then(async () => {
      document.dispatchEvent(new Event('refreshGalleryItemsList'));
      if (changed.isNew) {
        await frameTheArrayOfItems();
        return;
      }
      originalListOfItems.value = changed.items.map((item) => ({ ...item }));
    });
  }
}

async function handleGalleryItemDrop (categoryName, { sortedItems, draggedItem }) {
  const categoryList = galleryItems.value[categoryName] || [];

  // Step 1: Update order for items in sortedItems
  for (const sorted of sortedItems) {
    const item = categoryList.find((i) => i.id === sorted.id);
    if (item) {
      item.order = sorted.order;
    }
  }

  // Step 2: Update order for draggedItem
  const draggedInList = categoryList.find((i) => i.id === draggedItem.id);
  if (draggedInList) {
    draggedInList.order = draggedItem.order;
  }

  // Step 3: Normalize to remove duplicate order values (optional but recommended)
  const newList = [...categoryList]
    .sort((a, b) => {
      // Handle cases where order might be undefined or null
      const orderA = a.order || 0;
      const orderB = b.order || 0;
      return orderA - orderB;
    })
    .map((item, idx) => ({
      ...item,
      order: idx + 1,
    }));

  // Save updated list
  galleryItems.value[categoryName] = newList;

  // Update nestedGallery for UI
  const catIdx = nestedGallery.value.findIndex((cat) => cat.name === categoryName);
  if (catIdx !== -1) {
    nestedGallery.value[catIdx].children = newList;
  }

  // Prepare payload and call API
  const changed = newList.map((item) => ({
    id: item.id,
    order: item.order,
  }));
  const reqBody = {
    query: changed,
    project_id: projectId.value,
  };
  await updateBulkGalleryItems(reqBody);

  // Optionally sync original list
  originalGalleryItems.value[categoryName] = newList.map((item) => ({ ...item }));

  document.dispatchEvent(new Event('refreshGalleryItemsListByCategory'));
}

// 3. Handle sort events
function handleChildSort ({ baseParentReference, sortedItems, draggedItem }) {
  // 1. If baseParentReference is undefined/null, it's a category reorder
  if (!baseParentReference) {
    handleCategorySort();
    return;
  }

  // 2. If baseParentReference is defined, check if the dragged item changed category (reparenting)
  // If draggedItem has a parent and its parent is not baseParentReference, it's a reparent
  const prevCategory = draggedItem?.parent?.name;
  const newCategory = baseParentReference?.name;
  if (prevCategory && newCategory && prevCategory !== newCategory) {
    // Reparenting: update item's category and order in new category
    // Update the gallery item with new category
    const formData = new FormData();
    formData.append('item_id', draggedItem.Item.id);
    formData.append('project_id', projectId.value);
    formData.append('category', newCategory);

    updateGalleryItem(formData).then(() => {
      // Now update the order of all items in the new category
      const newOrder = sortedItems.map((item, idx) => {
        return ({
          id: item.id,
          order: idx + 1,
        });
      });
      const reqBody = {
        query: newOrder,
        project_id: projectId.value,
      };
      updateBulkGalleryItems(reqBody).then(() => {
        document.dispatchEvent(new Event('refreshGalleryItemsListByCategory'));
      });
    });
    return;
  }

  // 3. Otherwise, it's a reorder within the same category
  if (newCategory) {
    handleGalleryItemDrop(newCategory, { sortedItems, draggedItem });
  }
}

function handleChildReparentingSort ({add, draggedItem, remove}) {
  // Call handleCategorySort for category updates
  console.log(loading.value);
  const updateList = [...add.sortedItems, ...remove.sortedItems, draggedItem.Item];
  if (draggedItem && add) {
    loading.value = true;
    const formData = new FormData();
    formData.append('item_id', draggedItem.Item.id);
    formData.append('project_id', projectId.value);
    formData.append('category', add.baseParentReference.name); // New category name
    updateGalleryItem(formData).then(async () => {

      const changed = updateList.map((item) => ({
        id: item.id,
        order: item.order,
      }));
      const reqBody = {
        query: changed,
        project_id: projectId.value,
      };
      await updateBulkGalleryItems(reqBody);
      await handleCategorySort();
      loading.value = false;
      // Optionally refresh the gallery items list
      document.dispatchEvent(new Event('refreshGalleryItemsListByCategory'));
    }).catch((err) => {
      console.error('Error updating gallery item:', err);
    });
  }
}

function addCategory () {
  if (!newCategoryName.value.trim()) {
    return;
  }
  // Add the new category to nestedGallery and originalListOfItems
  const newId = Date.now(); // Or use a better unique ID
  const newCategory = {
    id: newId,
    name: newCategoryName.value,
    order: nestedGallery.value.length + 1,
    children: [],
    type: 'new',
  };
  nestedGallery.value.push(newCategory);
  galleryItems.value[newCategoryName.value] = [];
  newCategoryName.value = '';
  openCreateCategory.value = false;
  // Optionally, call your API to persist the new category
}

</script>

<template>
    <div class="flex w-full h-full">
        <div class="w-[250px] h-full bg-white p-2 flex flex-col border-r-2 border-black">
          <div class="flex-1 flex flex-col h-full w-full">
          <div class="flex flex-col gap-2">
            <div class="flex justify-between items-center px-3 w-full">
              <h2 class=" text-gray-500 text-lg font-semibold">
                Gallery
              </h2>
              <button @click="openCreateCategory=true" :class="`${openCreateCategory?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'}`" class="flex justify-center items-center w-6 h-6 rounded-md cursor-pointer">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
              </button>
            </div>
            <div class="px-3">
                <Button @click="openCreateGallery=true" title="Create Gallery" class="!rounded-lg m-auto !px-3 !py-2 !bg-bg-1000 border border-gray-200 !text-[#111928]">
                  <template v-slot:svg>
                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                  </template>
                </Button>
            </div>
          </div>
          <div class="flex-1 mt-3 w-full" v-if="loading">
                      <div v-for="i in 3" :key="i"  class="mb-3">

                        <div class="my-1.5 h-6 w-full flex items-center gap-1.5">
              <div class="h-4 w-4 rounded-full scene-skeleton-loader ">

                </div>
                <div class="flex-1 w-full h-full scene-skeleton-loader">
                </div>
                  <div class="h-full w-10 rounded-sm  scene-skeleton-loader">

                  </div>

            </div>

              <div class="flex justify-between align-center p-1.5 ml-4  my-1 gap-2  rounded-md"
            >
                  <div class="w-full flex-1 scene-skeleton-loader"></div>
                  <div class="px-1 h-6 w-4 rounded-sm bg-[#e5e7eb]"></div>
            </div>
              <div class="flex justify-between align-center p-1.5 ml-4  my-1 gap-2  rounded-md"
            >
                  <div class="w-full flex-1 scene-skeleton-loader"></div>
                  <div class="px-1 h-6 w-4 rounded-sm bg-[#e5e7eb]"></div>
            </div>
              <div class="flex justify-between align-center p-1.5 ml-4  my-1 gap-2  rounded-md"
            >
                  <div class="w-full flex-1 scene-skeleton-loader"></div>
                  <div class="px-1 h-6 w-4 rounded-sm bg-[#e5e7eb]"></div>
            </div>

                      </div>
          </div>

              <div class=" flex-1 mt-3 relative z-10 overflow-y-auto " v-else>
              <div class="flex-1 mt-3 relative z-10 overflow-y-auto">
                  <NestedReOrder
                              v-model="nestedGallery"
                              :uniqueKey="'id'"
                              :nestedChildKey="'children'"
                              @handleChildSort="handleChildSort"
                              @handleChildReparentingSort="handleChildReparentingSort"
                              >
                              <template #default="{ item }">
                                  <div v-if="item.children">
                                      <div class="flex gap-1.5">
                                          <div class="flex justify-center items-center">
                                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                  <path d="M8 1C6.61553 1 5.26215 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32121C1.00303 6.6003 0.86441 8.00776 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C14.998 6.14411 14.2598 4.36482 12.9475 3.0525C11.6352 1.74019 9.85589 1.00204 8 1ZM7.65 3.8C7.85767 3.8 8.06068 3.86158 8.23335 3.97696C8.40602 4.09233 8.5406 4.25632 8.62008 4.44818C8.69955 4.64004 8.72034 4.85116 8.67983 5.05484C8.63931 5.25852 8.53931 5.44562 8.39246 5.59246C8.24562 5.73931 8.05853 5.83931 7.85485 5.87982C7.65117 5.92034 7.44005 5.89954 7.24818 5.82007C7.05632 5.7406 6.89233 5.60602 6.77696 5.43335C6.66158 5.26068 6.6 5.05767 6.6 4.85C6.6 4.57152 6.71063 4.30445 6.90754 4.10754C7.10445 3.91062 7.37152 3.8 7.65 3.8ZM9.4 11.5H6.6C6.41435 11.5 6.2363 11.4262 6.10503 11.295C5.97375 11.1637 5.9 10.9856 5.9 10.8C5.9 10.6143 5.97375 10.4363 6.10503 10.305C6.2363 10.1737 6.41435 10.1 6.6 10.1H7.3V8H6.6C6.41435 8 6.2363 7.92625 6.10503 7.79497C5.97375 7.6637 5.9 7.48565 5.9 7.3C5.9 7.11435 5.97375 6.9363 6.10503 6.80502C6.2363 6.67375 6.41435 6.6 6.6 6.6H8C8.18565 6.6 8.3637 6.67375 8.49498 6.80502C8.62625 6.9363 8.7 7.11435 8.7 7.3V10.1H9.4C9.58565 10.1 9.7637 10.1737 9.89498 10.305C10.0263 10.4363 10.1 10.6143 10.1 10.8C10.1 10.9856 10.0263 11.1637 9.89498 11.295C9.7637 11.4262 9.58565 11.5 9.4 11.5Z" fill="#6B7280"/>
                                              </svg>
                                          </div>
                                          <div class="flex items-center max-w-20">
                                              <h2 class="text-xs leading-4 text-gray-500 font-semibold ml-1 overflow-hidden text-ellipsis whitespace-nowrap">{{item.name }}</h2>
                                          </div>
                                      </div>
                                  </div>
                                  <div v-else>
                                      <div @click="()=>{selectedItem = null; selectedItem = listOfItems[item.id]}" :class="selectedItem._id == item.id && 'bg-blue-50'" class="!w-full">
                                          <div data-v-4c984519="" id="scene-673db7b12fa83b3e755e5dc4" class="scene-scroll-target flex justify-between align-center p-1.5 my-2 rounded-md relative">
                                            <div data-v-4c984519="" class="max-w-32 cursor-pointer text-gray-900 hover:text-gray-900">
                                                <p data-v-4c984519="" class="overflow-hidden text-ellipsis whitespace-nowrap">{{item.name}}</p>
                                            </div>
                                        </div>
                                    </div>
                                  </div>
                                  <div v-if="(item?.children?.length === 0)" class="ml-8 my-2 text-xs text-gray-400 cursor-">
                                    No Items present
                                  </div>
                              </template>
                              </NestedReOrder>
              </div>
              </div>
          </div>
        </div>

        <div v-if="selectedItem" class="h-full w-full p-2">
            <!-- Image -->
            <img v-if="selectedItem.type === 'image'" :src="selectedItem.url" class="h-full w-full">

            <!-- embed-link -->
            <div v-if="selectedItem.type === 'embed_link'" class="h-full w-full">
                <iframe
                  id="showcase_frame"
                  class="w-full h-full"
                  :src="selectedItem.link"
                  frameborder="0"
                  allowfullscreen
                  allow="xr-spatial-tracking"
                />
            </div>

            <!-- PDF -->
            <div v-if="selectedItem.type==='pdf'" class="h-full w-full flex bg-black">
              <PDFViewer :url="selectedItem.url" />
            </div>

            <!-- Video -->
            <div v-if="selectedItem.type==='video'" class="h-full w-full flex bg-black">
              <VideoPlayer :url="selectedItem.url" />
            </div>

            <!-- 360_image -->
            <div v-if="selectedItem.type==='360_image'" class="h-full w-full flex bg-black">
              <AFrameComp
              :thumbnail="selectedItem.thumbnail"
              :highRes="selectedItem.url" />
            </div>

            <!-- 360_video -->
            <div v-if="selectedItem.type==='360_video'" class="h-full w-full flex bg-black">
              <AFrameVideo360 :videoSrc="selectedItem.url" />
            </div>
        </div>
    </div>
    <div v-if="openCreateCategory" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded shadow-lg w-80">
        <h3 class="text-lg font-semibold mb-4">Add New Category</h3>
        <input v-model="newCategoryName" placeholder="Category Name" class="border p-2 w-full mb-4" />
        <div class="flex justify-end gap-2">
          <button @click="addCategory" class="bg-blue-600 text-white px-4 py-2 rounded">Add</button>
          <button @click="openCreateCategory = false" class="bg-gray-200 px-4 py-2 rounded">Cancel</button>
        </div>
      </div>
    </div>
    <div v-if="openCreateGallery" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <CreateModal @close="(save)=>{ openCreateGallery = false; if(save) frameTheArrayOfItems()}"/>
    </div>
</template>

<style scoped>
.category-item {
  background: #f0f0f0;
  padding: 8px;
  margin-bottom: 4px;
  border-radius: 4px;
}
.gallery-item {
  background: #fff;
  padding: 6px 16px;
  margin: 2px 0 2px 16px;
  border-radius: 4px;
  border: 1px solid #eee;
}
.save-btn {
  margin-top: 16px;
  padding: 8px 24px;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.scene-skeleton-loader{
  background-color: #5a5757;
        background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, 0.468) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #e5e7eb;
        background-size: 200% 100%;
        background-position-x: 180%;
        animation: 1s loading ease-in-out infinite;
}

@keyframes loading {
        to {
          background-position-x: -20%;
        }
    }
</style>
