<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
// import SideBar from '../../../components/Projects/SideBar.vue';
import ProjectUnitplans from '../../../components/Projects/unitplans/Index.vue';
import { UserStore } from '../../../store/index';
import SideNavBar from '@/components/common/SideNavBar.vue';
const userStore = UserStore();
</script>

<template>
    <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
         <DatacenterNavBar/>
        <div class="dynamic-viewbox">
            <!-- <SideBar /> -->
            <SideNavBar/>
            <div v-if="userStore.user_data" class="dynamic-container">
                <ProjectUnitplans/>
                <router-view></router-view>
            </div>
        </div>
    </div>
</template>
