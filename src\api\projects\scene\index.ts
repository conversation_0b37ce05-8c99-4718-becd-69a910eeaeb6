import type { createSceneInputType, projectScene, transformedProjectScene, updateBulkSceneType, updateProjectSceneObj, convertDeepZoom} from '@/types/projectScene';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';
import { getCookie } from '../../../helpers/domhelper';

const api_url = import.meta.env.VITE_API_URL;

export async function createScene (payload: createSceneInputType): Promise<projectScene> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectScene/createScene`, body: payload }).then((res) => {
      resolve(res as projectScene);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function getListofScenes (project_id: string): Promise<Record<string, transformedProjectScene> | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/projectScene/getAllScenes/${project_id}` }).then((res) => {
      resolve(res as Record<string, transformedProjectScene>);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function getAllScenesFrames (project_id: string, parent_scene_id: string): Promise<transformedProjectScene | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/projectScene/getAllScenes/${project_id}?type=rotatable_image_frame&parent=${parent_scene_id}` }).then((res) => {
      resolve(res as transformedProjectScene);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function getScene (project_id: string, scene_id: string): Promise<transformedProjectScene> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/projectScene/${project_id}/getScene/${scene_id}` }).then((res) => {
      resolve(res as transformedProjectScene);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function updateFloors (project_id: string, payload: object): Promise<projectScene> {
  return new Promise((resolve, reject) => {
    const current_organization = getCookie('organization');

    PostRequestWithHeaders({ url: `${api_url}/projectScene/updateFloors?project_id=${project_id}&organization_id=${current_organization}`, body: payload }).then((res) => {
      resolve(res as projectScene);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function updateProjectSceneFloors (project_id: string, payload: object): Promise<projectScene> {
  return new Promise((resolve, reject) => {
    const current_organization = getCookie('organization');
    PostRequestWithHeaders({ url: `${api_url}/projectScene/updateFloors?project_id=${project_id}&organization_id=${current_organization}`, body: payload }).then((res) => {
      resolve(res as projectScene);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateScene (payload: updateProjectSceneObj): Promise<projectScene> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectScene/updateScene`, body: payload }).then((res) => {
      resolve(res as projectScene);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function moveSceneToTrash (project_id: string, payload: object): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectScene/moveToTrash/${project_id}`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateBulkSceneFrames (payload: updateBulkSceneType): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectScene/updateBulkSceneFrames`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateSceneFiles (payload: updateProjectSceneObj): Promise<projectScene> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectScene/updateSceneFiles`, body: payload }).then((res) => {
      resolve(res as projectScene);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function convertDeepZoom (payload: convertDeepZoom): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectScene/convertSceneType`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function restoreScenes (project_id:string, trash_id:string): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectScene/restoreScenes/${project_id}`, body: {trash_id: trash_id} }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}
