export const userManagement = ['users', 'invitations']; // User Management

export const mySchedule = ['upcoming', 'previous'];  // My Schedule

export const apiMediaTypes = ['image', 'video', '360_image', '360_video', 'virtual_tour', 'pdf', 'embed_link', 'multires'];

export const assetsType = ['hologram', 'video'];

export const amenitiesCategories = {
  'indoor_amenities': {
    label: 'Indoor',
    value: 'Indoor',
  },
  'outdoor_amenities': {
    label: 'Outdoor',
    value: 'Outdoor',
  },
  'security_convenience': {
    label: 'Security Convenience',
    value: 'Security Convenience',
  },
  'additional_services': {
    label: 'Additional Services',
    value: 'Additional Services',
  },
  'technology_connectiviy': {
    label: 'Technology Connectivity',
    value: 'Technology Connectivity',
  },
};
export const projectCardsMenuItems = {
  'Edit': {
    label: 'Edit',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="edit" clip-path="url(#clip0_977_23343)">
      <g id="Vector">
      <path d="M10.8952 1.04356L9.77765 2.2239L12.3503 4.94117L13.4671 3.76082C13.636 3.58242 13.7701 3.37061 13.8615 3.13749C13.9529 2.90438 14 2.65452 14 2.40219C14 2.14986 13.9529 1.9 13.8615 1.66688C13.7701 1.43376 13.636 1.22196 13.4671 1.04356C13.1204 0.694636 12.66 0.5 12.1811 0.5C11.7023 0.5 11.2418 0.694636 10.8952 1.04356Z" fill="#6B7280"/>
      <path d="M11.3215 6.02776L8.74888 3.3105L5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776Z" fill="#6B7280"/>
      <path d="M5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776L8.74888 3.3105L5.31117 6.93838Z" fill="#6B7280"/>
      <path d="M8.58008 10.5532C8.37679 10.7676 8.11814 10.9138 7.83652 10.9736L5.26388 11.5169C5.02903 11.5664 4.78623 11.554 4.55702 11.4806C4.32781 11.4073 4.11929 11.2753 3.94994 11.0965C3.7806 10.9176 3.65567 10.6974 3.58624 10.4553C3.51681 10.2132 3.50503 9.95673 3.55193 9.70867L4.06632 6.99141C4.12291 6.69396 4.26135 6.42077 4.46429 6.20605L8.04387 2.42524C8.02396 2.3536 8.01368 2.27936 8.01332 2.2047H1.57662C1.15859 2.2051 0.757795 2.38068 0.462206 2.69288C0.166616 3.00509 0.000385278 3.42842 0 3.86994L0 12.8348C0.000385278 13.2763 0.166616 13.6996 0.462206 14.0118C0.757795 14.324 1.15859 14.4996 1.57662 14.5H10.0643C10.4823 14.4996 10.8831 14.324 11.1787 14.0118C11.4743 13.6996 11.6405 13.2763 11.6409 12.8348V7.32031L8.58008 10.5532Z" fill="#6B7280"/>
      </g>
      </g>
      <defs>
      <clipPath id="clip0_977_23343">
      <rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
      </clipPath>
      </defs>
    </svg>

      `,
  },
  'Delete': {
    label: 'Delete',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trash" clip-path="url(#clip0_977_23346)">
<path id="Vector" d="M12.7044 3.44737H9.8525V1.97368C9.8525 1.58284 9.70227 1.208 9.43486 0.931632C9.16745 0.655263 8.80476 0.5 8.42658 0.5H5.57473C5.19655 0.5 4.83386 0.655263 4.56644 0.931632C4.29903 1.208 4.1488 1.58284 4.1488 1.97368V3.44737H1.29695C1.10786 3.44737 0.926513 3.525 0.792806 3.66318C0.6591 3.80137 0.583984 3.98879 0.583984 4.18421C0.583984 4.37963 0.6591 4.56705 0.792806 4.70524C0.926513 4.84342 1.10786 4.92105 1.29695 4.92105H2.00991V13.0263C2.00991 13.4172 2.16014 13.792 2.42755 14.0684C2.69497 14.3447 3.05766 14.5 3.43584 14.5H10.5655C10.9436 14.5 11.3063 14.3447 11.5737 14.0684C11.8412 13.792 11.9914 13.4172 11.9914 13.0263V4.92105H12.7044C12.8934 4.92105 13.0748 4.84342 13.2085 4.70524C13.3422 4.56705 13.4173 4.37963 13.4173 4.18421C13.4173 3.98879 13.3422 3.80137 13.2085 3.66318C13.0748 3.525 12.8934 3.44737 12.7044 3.44737ZM5.57473 1.97368H8.42658V3.44737H5.57473V1.97368ZM6.28769 11.5526C6.28769 11.7481 6.21257 11.9355 6.07887 12.0737C5.94516 12.2118 5.76381 12.2895 5.57473 12.2895C5.38564 12.2895 5.20429 12.2118 5.07058 12.0737C4.93688 11.9355 4.86176 11.7481 4.86176 11.5526V6.39474C4.86176 6.19931 4.93688 6.0119 5.07058 5.87371C5.20429 5.73553 5.38564 5.65789 5.57473 5.65789C5.76381 5.65789 5.94516 5.73553 6.07887 5.87371C6.21257 6.0119 6.28769 6.19931 6.28769 6.39474V11.5526ZM9.13954 11.5526C9.13954 11.7481 9.06442 11.9355 8.93072 12.0737C8.79701 12.2118 8.61567 12.2895 8.42658 12.2895C8.23749 12.2895 8.05614 12.2118 7.92244 12.0737C7.78873 11.9355 7.71361 11.7481 7.71361 11.5526V6.39474C7.71361 6.19931 7.78873 6.0119 7.92244 5.87371C8.05614 5.73553 8.23749 5.65789 8.42658 5.65789C8.61567 5.65789 8.79701 5.73553 8.93072 5.87371C9.06442 6.0119 9.13954 6.19931 9.13954 6.39474V11.5526Z" fill="#C81E1E"/>
</g>
<defs>
<clipPath id="clip0_977_23346">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

    `,
  },

};
export const CancelSessionSessionListMenuItems = {
  'Cancel Session': {
    label: 'Cancel Session',
    svg: `
<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="edit" clip-path="url(#clip0_6014_22956)">
<g id="Vector">
<path d="M10.8952 1.44358L9.77765 2.62393L12.3503 5.34119L13.4671 4.16084C13.636 3.98244 13.7701 3.77063 13.8615 3.53752C13.9529 3.3044 14 3.05454 14 2.80221C14 2.54988 13.9529 2.30002 13.8615 2.0669C13.7701 1.83379 13.636 1.62198 13.4671 1.44358C13.1204 1.09466 12.66 0.900024 12.1811 0.900024C11.7023 0.900024 11.2418 1.09466 10.8952 1.44358Z" fill="#6B7280"/>
<path d="M11.3215 6.42779L8.74888 3.71053L5.31117 7.33841C5.20946 7.44564 5.1401 7.58227 5.11182 7.73109L4.72912 9.76904C4.70558 9.89313 4.71142 10.0214 4.74612 10.1426C4.78082 10.2637 4.84331 10.3739 4.92804 10.4634C5.01277 10.5529 5.11711 10.6189 5.2318 10.6556C5.34649 10.6922 5.46797 10.6984 5.58546 10.6735L7.51494 10.2662C7.65558 10.2362 7.78467 10.163 7.886 10.0557L11.3215 6.42779Z" fill="#6B7280"/>
<path d="M5.31117 7.33841C5.20946 7.44564 5.1401 7.58227 5.11182 7.73109L4.72912 9.76904C4.70558 9.89313 4.71142 10.0214 4.74612 10.1426C4.78082 10.2637 4.84331 10.3739 4.92804 10.4634C5.01277 10.5529 5.11711 10.6189 5.2318 10.6556C5.34649 10.6922 5.46797 10.6984 5.58546 10.6735L7.51494 10.2662C7.65558 10.2362 7.78467 10.163 7.886 10.0557L11.3215 6.42779L8.74888 3.71053L5.31117 7.33841Z" fill="#6B7280"/>
<path d="M8.58008 10.9532C8.37679 11.1676 8.11814 11.3138 7.83652 11.3736L5.26388 11.9169C5.02903 11.9664 4.78623 11.954 4.55702 11.8806C4.32781 11.8073 4.11929 11.6754 3.94994 11.4965C3.7806 11.3176 3.65567 11.0974 3.58624 10.8553C3.51681 10.6132 3.50503 10.3568 3.55193 10.1087L4.06632 7.39144C4.12291 7.09399 4.26135 6.82079 4.46429 6.60607L8.04387 2.82527C8.02396 2.75362 8.01368 2.67938 8.01332 2.60472H1.57662C1.15859 2.60513 0.757795 2.7807 0.462206 3.09291C0.166616 3.40511 0.000385278 3.82844 0 4.26997L0 13.2348C0.000385278 13.6763 0.166616 14.0996 0.462206 14.4118C0.757795 14.724 1.15859 14.8996 1.57662 14.9H10.0643C10.4823 14.8996 10.8831 14.724 11.1787 14.4118C11.4743 14.0996 11.6405 13.6763 11.6409 13.2348V7.72034L8.58008 10.9532Z" fill="#6B7280"/>
</g>
</g>
<defs>
<clipPath id="clip0_6014_22956">
<rect width="14" height="14" fill="white" transform="translate(0 0.900024)"/>
</clipPath>
</defs>
</svg>

    `,
  },
};
export const EndSessionSessionListMenuItems = {
  'End Session': {
    label: 'End Session',
    svg: `
<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="edit" clip-path="url(#clip0_6014_22956)">
<g id="Vector">
<path d="M10.8952 1.44358L9.77765 2.62393L12.3503 5.34119L13.4671 4.16084C13.636 3.98244 13.7701 3.77063 13.8615 3.53752C13.9529 3.3044 14 3.05454 14 2.80221C14 2.54988 13.9529 2.30002 13.8615 2.0669C13.7701 1.83379 13.636 1.62198 13.4671 1.44358C13.1204 1.09466 12.66 0.900024 12.1811 0.900024C11.7023 0.900024 11.2418 1.09466 10.8952 1.44358Z" fill="#6B7280"/>
<path d="M11.3215 6.42779L8.74888 3.71053L5.31117 7.33841C5.20946 7.44564 5.1401 7.58227 5.11182 7.73109L4.72912 9.76904C4.70558 9.89313 4.71142 10.0214 4.74612 10.1426C4.78082 10.2637 4.84331 10.3739 4.92804 10.4634C5.01277 10.5529 5.11711 10.6189 5.2318 10.6556C5.34649 10.6922 5.46797 10.6984 5.58546 10.6735L7.51494 10.2662C7.65558 10.2362 7.78467 10.163 7.886 10.0557L11.3215 6.42779Z" fill="#6B7280"/>
<path d="M5.31117 7.33841C5.20946 7.44564 5.1401 7.58227 5.11182 7.73109L4.72912 9.76904C4.70558 9.89313 4.71142 10.0214 4.74612 10.1426C4.78082 10.2637 4.84331 10.3739 4.92804 10.4634C5.01277 10.5529 5.11711 10.6189 5.2318 10.6556C5.34649 10.6922 5.46797 10.6984 5.58546 10.6735L7.51494 10.2662C7.65558 10.2362 7.78467 10.163 7.886 10.0557L11.3215 6.42779L8.74888 3.71053L5.31117 7.33841Z" fill="#6B7280"/>
<path d="M8.58008 10.9532C8.37679 11.1676 8.11814 11.3138 7.83652 11.3736L5.26388 11.9169C5.02903 11.9664 4.78623 11.954 4.55702 11.8806C4.32781 11.8073 4.11929 11.6754 3.94994 11.4965C3.7806 11.3176 3.65567 11.0974 3.58624 10.8553C3.51681 10.6132 3.50503 10.3568 3.55193 10.1087L4.06632 7.39144C4.12291 7.09399 4.26135 6.82079 4.46429 6.60607L8.04387 2.82527C8.02396 2.75362 8.01368 2.67938 8.01332 2.60472H1.57662C1.15859 2.60513 0.757795 2.7807 0.462206 3.09291C0.166616 3.40511 0.000385278 3.82844 0 4.26997L0 13.2348C0.000385278 13.6763 0.166616 14.0996 0.462206 14.4118C0.757795 14.724 1.15859 14.8996 1.57662 14.9H10.0643C10.4823 14.8996 10.8831 14.724 11.1787 14.4118C11.4743 14.0996 11.6405 13.6763 11.6409 13.2348V7.72034L8.58008 10.9532Z" fill="#6B7280"/>
</g>
</g>
<defs>
<clipPath id="clip0_6014_22956">
<rect width="14" height="14" fill="white" transform="translate(0 0.900024)"/>
</clipPath>
</defs>
</svg>

    `,
  },
};
export const meetingsListMenuItems = {
  'Edit': {
    label: 'Edit',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="edit" clip-path="url(#clip0_977_23343)">
      <g id="Vector">
      <path d="M10.8952 1.04356L9.77765 2.2239L12.3503 4.94117L13.4671 3.76082C13.636 3.58242 13.7701 3.37061 13.8615 3.13749C13.9529 2.90438 14 2.65452 14 2.40219C14 2.14986 13.9529 1.9 13.8615 1.66688C13.7701 1.43376 13.636 1.22196 13.4671 1.04356C13.1204 0.694636 12.66 0.5 12.1811 0.5C11.7023 0.5 11.2418 0.694636 10.8952 1.04356Z" fill="#6B7280"/>
      <path d="M11.3215 6.02776L8.74888 3.3105L5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776Z" fill="#6B7280"/>
      <path d="M5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776L8.74888 3.3105L5.31117 6.93838Z" fill="#6B7280"/>
      <path d="M8.58008 10.5532C8.37679 10.7676 8.11814 10.9138 7.83652 10.9736L5.26388 11.5169C5.02903 11.5664 4.78623 11.554 4.55702 11.4806C4.32781 11.4073 4.11929 11.2753 3.94994 11.0965C3.7806 10.9176 3.65567 10.6974 3.58624 10.4553C3.51681 10.2132 3.50503 9.95673 3.55193 9.70867L4.06632 6.99141C4.12291 6.69396 4.26135 6.42077 4.46429 6.20605L8.04387 2.42524C8.02396 2.3536 8.01368 2.27936 8.01332 2.2047H1.57662C1.15859 2.2051 0.757795 2.38068 0.462206 2.69288C0.166616 3.00509 0.000385278 3.42842 0 3.86994L0 12.8348C0.000385278 13.2763 0.166616 13.6996 0.462206 14.0118C0.757795 14.324 1.15859 14.4996 1.57662 14.5H10.0643C10.4823 14.4996 10.8831 14.324 11.1787 14.0118C11.4743 13.6996 11.6405 13.2763 11.6409 12.8348V7.32031L8.58008 10.5532Z" fill="#6B7280"/>
      </g>
      </g>
      <defs>
      <clipPath id="clip0_977_23343">
      <rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
      </clipPath>
      </defs>
    </svg>

      `,
  },
  'Cancel': {
    label: 'Cancel',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trash" clip-path="url(#clip0_977_23346)">
<path id="Vector" d="M12.7044 3.44737H9.8525V1.97368C9.8525 1.58284 9.70227 1.208 9.43486 0.931632C9.16745 0.655263 8.80476 0.5 8.42658 0.5H5.57473C5.19655 0.5 4.83386 0.655263 4.56644 0.931632C4.29903 1.208 4.1488 1.58284 4.1488 1.97368V3.44737H1.29695C1.10786 3.44737 0.926513 3.525 0.792806 3.66318C0.6591 3.80137 0.583984 3.98879 0.583984 4.18421C0.583984 4.37963 0.6591 4.56705 0.792806 4.70524C0.926513 4.84342 1.10786 4.92105 1.29695 4.92105H2.00991V13.0263C2.00991 13.4172 2.16014 13.792 2.42755 14.0684C2.69497 14.3447 3.05766 14.5 3.43584 14.5H10.5655C10.9436 14.5 11.3063 14.3447 11.5737 14.0684C11.8412 13.792 11.9914 13.4172 11.9914 13.0263V4.92105H12.7044C12.8934 4.92105 13.0748 4.84342 13.2085 4.70524C13.3422 4.56705 13.4173 4.37963 13.4173 4.18421C13.4173 3.98879 13.3422 3.80137 13.2085 3.66318C13.0748 3.525 12.8934 3.44737 12.7044 3.44737ZM5.57473 1.97368H8.42658V3.44737H5.57473V1.97368ZM6.28769 11.5526C6.28769 11.7481 6.21257 11.9355 6.07887 12.0737C5.94516 12.2118 5.76381 12.2895 5.57473 12.2895C5.38564 12.2895 5.20429 12.2118 5.07058 12.0737C4.93688 11.9355 4.86176 11.7481 4.86176 11.5526V6.39474C4.86176 6.19931 4.93688 6.0119 5.07058 5.87371C5.20429 5.73553 5.38564 5.65789 5.57473 5.65789C5.76381 5.65789 5.94516 5.73553 6.07887 5.87371C6.21257 6.0119 6.28769 6.19931 6.28769 6.39474V11.5526ZM9.13954 11.5526C9.13954 11.7481 9.06442 11.9355 8.93072 12.0737C8.79701 12.2118 8.61567 12.2895 8.42658 12.2895C8.23749 12.2895 8.05614 12.2118 7.92244 12.0737C7.78873 11.9355 7.71361 11.7481 7.71361 11.5526V6.39474C7.71361 6.19931 7.78873 6.0119 7.92244 5.87371C8.05614 5.73553 8.23749 5.65789 8.42658 5.65789C8.61567 5.65789 8.79701 5.73553 8.93072 5.87371C9.06442 6.0119 9.13954 6.19931 9.13954 6.39474V11.5526Z" fill="#C81E1E"/>
</g>
<defs>
<clipPath id="clip0_977_23346">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

    `,
  },

};
export const projectCardsMenuItemsMobileView = {
  'Edit': {
    label: 'Edit',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="edit" clip-path="url(#clip0_977_23343)">
      <g id="Vector">
      <path d="M10.8952 1.04356L9.77765 2.2239L12.3503 4.94117L13.4671 3.76082C13.636 3.58242 13.7701 3.37061 13.8615 3.13749C13.9529 2.90438 14 2.65452 14 2.40219C14 2.14986 13.9529 1.9 13.8615 1.66688C13.7701 1.43376 13.636 1.22196 13.4671 1.04356C13.1204 0.694636 12.66 0.5 12.1811 0.5C11.7023 0.5 11.2418 0.694636 10.8952 1.04356Z" fill="#6B7280"/>
      <path d="M11.3215 6.02776L8.74888 3.3105L5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776Z" fill="#6B7280"/>
      <path d="M5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776L8.74888 3.3105L5.31117 6.93838Z" fill="#6B7280"/>
      <path d="M8.58008 10.5532C8.37679 10.7676 8.11814 10.9138 7.83652 10.9736L5.26388 11.5169C5.02903 11.5664 4.78623 11.554 4.55702 11.4806C4.32781 11.4073 4.11929 11.2753 3.94994 11.0965C3.7806 10.9176 3.65567 10.6974 3.58624 10.4553C3.51681 10.2132 3.50503 9.95673 3.55193 9.70867L4.06632 6.99141C4.12291 6.69396 4.26135 6.42077 4.46429 6.20605L8.04387 2.42524C8.02396 2.3536 8.01368 2.27936 8.01332 2.2047H1.57662C1.15859 2.2051 0.757795 2.38068 0.462206 2.69288C0.166616 3.00509 0.000385278 3.42842 0 3.86994L0 12.8348C0.000385278 13.2763 0.166616 13.6996 0.462206 14.0118C0.757795 14.324 1.15859 14.4996 1.57662 14.5H10.0643C10.4823 14.4996 10.8831 14.324 11.1787 14.0118C11.4743 13.6996 11.6405 13.2763 11.6409 12.8348V7.32031L8.58008 10.5532Z" fill="#6B7280"/>
      </g>
      </g>
      <defs>
      <clipPath id="clip0_977_23343">
      <rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
      </clipPath>
      </defs>
    </svg>

      `,
  },
  'Creator': {
    label: 'Creator',
    svg: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="window">
<path id="Vector" d="M12.6 1.08325H1.4C0.6279 1.08325 0 1.72278 0 2.50918L0 12.4907C0 13.2771 0.6279 13.9166 1.4 13.9166H12.6C13.3721 13.9166 14 13.2771 14 12.4907V2.50918C14 1.72278 13.3721 1.08325 12.6 1.08325ZM7.35 3.22214C7.7364 3.22214 8.05 3.54155 8.05 3.9351C8.05 4.32866 7.7364 4.64807 7.35 4.64807C6.9636 4.64807 6.65 4.32866 6.65 3.9351C6.65 3.54155 6.9636 3.22214 7.35 3.22214ZM5.25 3.22214C5.6364 3.22214 5.95 3.54155 5.95 3.9351C5.95 4.32866 5.6364 4.64807 5.25 4.64807C4.8636 4.64807 4.55 4.32866 4.55 3.9351C4.55 3.54155 4.8636 3.22214 5.25 3.22214ZM3.15 3.22214C3.5364 3.22214 3.85 3.54155 3.85 3.9351C3.85 4.32866 3.5364 4.64807 3.15 4.64807C2.7636 4.64807 2.45 4.32866 2.45 3.9351C2.45 3.54155 2.7636 3.22214 3.15 3.22214ZM1.4 12.4907V6.78696H12.6007L12.6014 12.4907H1.4Z" fill="#6B7280"/>
</g>
</svg>
`,
  },
  'Delete': {
    label: 'Delete',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trash" clip-path="url(#clip0_977_23346)">
<path id="Vector" d="M12.7044 3.44737H9.8525V1.97368C9.8525 1.58284 9.70227 1.208 9.43486 0.931632C9.16745 0.655263 8.80476 0.5 8.42658 0.5H5.57473C5.19655 0.5 4.83386 0.655263 4.56644 0.931632C4.29903 1.208 4.1488 1.58284 4.1488 1.97368V3.44737H1.29695C1.10786 3.44737 0.926513 3.525 0.792806 3.66318C0.6591 3.80137 0.583984 3.98879 0.583984 4.18421C0.583984 4.37963 0.6591 4.56705 0.792806 4.70524C0.926513 4.84342 1.10786 4.92105 1.29695 4.92105H2.00991V13.0263C2.00991 13.4172 2.16014 13.792 2.42755 14.0684C2.69497 14.3447 3.05766 14.5 3.43584 14.5H10.5655C10.9436 14.5 11.3063 14.3447 11.5737 14.0684C11.8412 13.792 11.9914 13.4172 11.9914 13.0263V4.92105H12.7044C12.8934 4.92105 13.0748 4.84342 13.2085 4.70524C13.3422 4.56705 13.4173 4.37963 13.4173 4.18421C13.4173 3.98879 13.3422 3.80137 13.2085 3.66318C13.0748 3.525 12.8934 3.44737 12.7044 3.44737ZM5.57473 1.97368H8.42658V3.44737H5.57473V1.97368ZM6.28769 11.5526C6.28769 11.7481 6.21257 11.9355 6.07887 12.0737C5.94516 12.2118 5.76381 12.2895 5.57473 12.2895C5.38564 12.2895 5.20429 12.2118 5.07058 12.0737C4.93688 11.9355 4.86176 11.7481 4.86176 11.5526V6.39474C4.86176 6.19931 4.93688 6.0119 5.07058 5.87371C5.20429 5.73553 5.38564 5.65789 5.57473 5.65789C5.76381 5.65789 5.94516 5.73553 6.07887 5.87371C6.21257 6.0119 6.28769 6.19931 6.28769 6.39474V11.5526ZM9.13954 11.5526C9.13954 11.7481 9.06442 11.9355 8.93072 12.0737C8.79701 12.2118 8.61567 12.2895 8.42658 12.2895C8.23749 12.2895 8.05614 12.2118 7.92244 12.0737C7.78873 11.9355 7.71361 11.7481 7.71361 11.5526V6.39474C7.71361 6.19931 7.78873 6.0119 7.92244 5.87371C8.05614 5.73553 8.23749 5.65789 8.42658 5.65789C8.61567 5.65789 8.79701 5.73553 8.93072 5.87371C9.06442 6.0119 9.13954 6.19931 9.13954 6.39474V11.5526Z" fill="#C81E1E"/>
</g>
<defs>
<clipPath id="clip0_977_23346">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

    `,
  },

};
export const projectCardsMenuItemsArchivePage = {
  'Unarchive': {
    label: 'Unarchive',
    svg: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="window" clip-path="url(#clip0_2988_22406)">
<path id="Vector" d="M12.6 1.17432H1.4C0.6279 1.17432 0 1.81384 0 2.60024L0 12.5817C0 13.3681 0.6279 14.0077 1.4 14.0077H12.6C13.3721 14.0077 14 13.3681 14 12.5817V2.60024C14 1.81384 13.3721 1.17432 12.6 1.17432ZM7.35 3.31321C7.7364 3.31321 8.05 3.63261 8.05 4.02617C8.05 4.41972 7.7364 4.73913 7.35 4.73913C6.9636 4.73913 6.65 4.41972 6.65 4.02617C6.65 3.63261 6.9636 3.31321 7.35 3.31321ZM5.25 3.31321C5.6364 3.31321 5.95 3.63261 5.95 4.02617C5.95 4.41972 5.6364 4.73913 5.25 4.73913C4.8636 4.73913 4.55 4.41972 4.55 4.02617C4.55 3.63261 4.8636 3.31321 5.25 3.31321ZM3.15 3.31321C3.5364 3.31321 3.85 3.63261 3.85 4.02617C3.85 4.41972 3.5364 4.73913 3.15 4.73913C2.7636 4.73913 2.45 4.41972 2.45 4.02617C2.45 3.63261 2.7636 3.31321 3.15 3.31321ZM1.4 12.5817V6.87802H12.6007L12.6014 12.5817H1.4Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_2988_22406">
<rect width="14" height="14" fill="white" transform="translate(0 0.590942)"/>
</clipPath>
</defs>
</svg>
`,
  },
  'Delete': {
    label: 'Delete',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trash" clip-path="url(#clip0_977_23346)">
<path id="Vector" d="M12.7044 3.44737H9.8525V1.97368C9.8525 1.58284 9.70227 1.208 9.43486 0.931632C9.16745 0.655263 8.80476 0.5 8.42658 0.5H5.57473C5.19655 0.5 4.83386 0.655263 4.56644 0.931632C4.29903 1.208 4.1488 1.58284 4.1488 1.97368V3.44737H1.29695C1.10786 3.44737 0.926513 3.525 0.792806 3.66318C0.6591 3.80137 0.583984 3.98879 0.583984 4.18421C0.583984 4.37963 0.6591 4.56705 0.792806 4.70524C0.926513 4.84342 1.10786 4.92105 1.29695 4.92105H2.00991V13.0263C2.00991 13.4172 2.16014 13.792 2.42755 14.0684C2.69497 14.3447 3.05766 14.5 3.43584 14.5H10.5655C10.9436 14.5 11.3063 14.3447 11.5737 14.0684C11.8412 13.792 11.9914 13.4172 11.9914 13.0263V4.92105H12.7044C12.8934 4.92105 13.0748 4.84342 13.2085 4.70524C13.3422 4.56705 13.4173 4.37963 13.4173 4.18421C13.4173 3.98879 13.3422 3.80137 13.2085 3.66318C13.0748 3.525 12.8934 3.44737 12.7044 3.44737ZM5.57473 1.97368H8.42658V3.44737H5.57473V1.97368ZM6.28769 11.5526C6.28769 11.7481 6.21257 11.9355 6.07887 12.0737C5.94516 12.2118 5.76381 12.2895 5.57473 12.2895C5.38564 12.2895 5.20429 12.2118 5.07058 12.0737C4.93688 11.9355 4.86176 11.7481 4.86176 11.5526V6.39474C4.86176 6.19931 4.93688 6.0119 5.07058 5.87371C5.20429 5.73553 5.38564 5.65789 5.57473 5.65789C5.76381 5.65789 5.94516 5.73553 6.07887 5.87371C6.21257 6.0119 6.28769 6.19931 6.28769 6.39474V11.5526ZM9.13954 11.5526C9.13954 11.7481 9.06442 11.9355 8.93072 12.0737C8.79701 12.2118 8.61567 12.2895 8.42658 12.2895C8.23749 12.2895 8.05614 12.2118 7.92244 12.0737C7.78873 11.9355 7.71361 11.7481 7.71361 11.5526V6.39474C7.71361 6.19931 7.78873 6.0119 7.92244 5.87371C8.05614 5.73553 8.23749 5.65789 8.42658 5.65789C8.61567 5.65789 8.79701 5.73553 8.93072 5.87371C9.06442 6.0119 9.13954 6.19931 9.13954 6.39474V11.5526Z" fill="#C81E1E"/>
</g>
<defs>
<clipPath id="clip0_977_23346">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

    `,
  },

};
export const projectCardsMenuItemsMobileArchivePage = {
  'Creator': {
    label: 'Creator',
    svg: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="window">
<path id="Vector" d="M12.6 1.08325H1.4C0.6279 1.08325 0 1.72278 0 2.50918L0 12.4907C0 13.2771 0.6279 13.9166 1.4 13.9166H12.6C13.3721 13.9166 14 13.2771 14 12.4907V2.50918C14 1.72278 13.3721 1.08325 12.6 1.08325ZM7.35 3.22214C7.7364 3.22214 8.05 3.54155 8.05 3.9351C8.05 4.32866 7.7364 4.64807 7.35 4.64807C6.9636 4.64807 6.65 4.32866 6.65 3.9351C6.65 3.54155 6.9636 3.22214 7.35 3.22214ZM5.25 3.22214C5.6364 3.22214 5.95 3.54155 5.95 3.9351C5.95 4.32866 5.6364 4.64807 5.25 4.64807C4.8636 4.64807 4.55 4.32866 4.55 3.9351C4.55 3.54155 4.8636 3.22214 5.25 3.22214ZM3.15 3.22214C3.5364 3.22214 3.85 3.54155 3.85 3.9351C3.85 4.32866 3.5364 4.64807 3.15 4.64807C2.7636 4.64807 2.45 4.32866 2.45 3.9351C2.45 3.54155 2.7636 3.22214 3.15 3.22214ZM1.4 12.4907V6.78696H12.6007L12.6014 12.4907H1.4Z" fill="#6B7280"/>
</g>
</svg>
`,
  },
  'Unarchive': {
    label: 'Unarchive',
    svg: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="archive">
<g id="Vector">
<path d="M13.2222 1.08325H0.777778C0.348223 1.08325 0 1.42123 0 1.83815L0 3.34796C0 3.76488 0.348223 4.10286 0.777778 4.10286H13.2222C13.6518 4.10286 14 3.76488 14 3.34796V1.83815C14 1.42123 13.6518 1.08325 13.2222 1.08325Z" fill="#6B7280"/>
<path d="M0.777778 5.61266V12.4068C0.777778 12.8072 0.941666 13.1912 1.23339 13.4744C1.52511 13.7575 1.92077 13.9166 2.33333 13.9166H11.6667C12.0792 13.9166 12.4749 13.7575 12.7666 13.4744C13.0583 13.1912 13.2222 12.8072 13.2222 12.4068V5.61266H0.777778ZM9.33333 7.87737C9.33333 8.07758 9.25139 8.26959 9.10553 8.41117C8.95967 8.55274 8.76184 8.63227 8.55556 8.63227H5.44444C5.23817 8.63227 5.04033 8.55274 4.89447 8.41117C4.74861 8.26959 4.66667 8.07758 4.66667 7.87737V7.12247C4.66667 6.92226 4.74861 6.73024 4.89447 6.58867C5.04033 6.4471 5.23817 6.36757 5.44444 6.36757C5.65072 6.36757 5.84855 6.4471 5.99442 6.58867C6.14028 6.73024 6.22222 6.92226 6.22222 7.12247H7.77778C7.77778 6.92226 7.85972 6.73024 8.00558 6.58867C8.15145 6.4471 8.34928 6.36757 8.55556 6.36757C8.76184 6.36757 8.95967 6.4471 9.10553 6.58867C9.25139 6.73024 9.33333 6.92226 9.33333 7.12247V7.87737Z" fill="#6B7280"/>
</g>
</g>
</svg>

`,
  },
  'Delete': {
    label: 'Delete',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trash" clip-path="url(#clip0_977_23346)">
<path id="Vector" d="M12.7044 3.44737H9.8525V1.97368C9.8525 1.58284 9.70227 1.208 9.43486 0.931632C9.16745 0.655263 8.80476 0.5 8.42658 0.5H5.57473C5.19655 0.5 4.83386 0.655263 4.56644 0.931632C4.29903 1.208 4.1488 1.58284 4.1488 1.97368V3.44737H1.29695C1.10786 3.44737 0.926513 3.525 0.792806 3.66318C0.6591 3.80137 0.583984 3.98879 0.583984 4.18421C0.583984 4.37963 0.6591 4.56705 0.792806 4.70524C0.926513 4.84342 1.10786 4.92105 1.29695 4.92105H2.00991V13.0263C2.00991 13.4172 2.16014 13.792 2.42755 14.0684C2.69497 14.3447 3.05766 14.5 3.43584 14.5H10.5655C10.9436 14.5 11.3063 14.3447 11.5737 14.0684C11.8412 13.792 11.9914 13.4172 11.9914 13.0263V4.92105H12.7044C12.8934 4.92105 13.0748 4.84342 13.2085 4.70524C13.3422 4.56705 13.4173 4.37963 13.4173 4.18421C13.4173 3.98879 13.3422 3.80137 13.2085 3.66318C13.0748 3.525 12.8934 3.44737 12.7044 3.44737ZM5.57473 1.97368H8.42658V3.44737H5.57473V1.97368ZM6.28769 11.5526C6.28769 11.7481 6.21257 11.9355 6.07887 12.0737C5.94516 12.2118 5.76381 12.2895 5.57473 12.2895C5.38564 12.2895 5.20429 12.2118 5.07058 12.0737C4.93688 11.9355 4.86176 11.7481 4.86176 11.5526V6.39474C4.86176 6.19931 4.93688 6.0119 5.07058 5.87371C5.20429 5.73553 5.38564 5.65789 5.57473 5.65789C5.76381 5.65789 5.94516 5.73553 6.07887 5.87371C6.21257 6.0119 6.28769 6.19931 6.28769 6.39474V11.5526ZM9.13954 11.5526C9.13954 11.7481 9.06442 11.9355 8.93072 12.0737C8.79701 12.2118 8.61567 12.2895 8.42658 12.2895C8.23749 12.2895 8.05614 12.2118 7.92244 12.0737C7.78873 11.9355 7.71361 11.7481 7.71361 11.5526V6.39474C7.71361 6.19931 7.78873 6.0119 7.92244 5.87371C8.05614 5.73553 8.23749 5.65789 8.42658 5.65789C8.61567 5.65789 8.79701 5.73553 8.93072 5.87371C9.06442 6.0119 9.13954 6.19931 9.13954 6.39474V11.5526Z" fill="#C81E1E"/>
</g>
<defs>
<clipPath id="clip0_977_23346">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

    `,
  },

};
export const projectCardsMobileMenuItems = {
  'Edit': {
    label: 'Edit',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="edit" clip-path="url(#clip0_977_23343)">
      <g id="Vector">
      <path d="M10.8952 1.04356L9.77765 2.2239L12.3503 4.94117L13.4671 3.76082C13.636 3.58242 13.7701 3.37061 13.8615 3.13749C13.9529 2.90438 14 2.65452 14 2.40219C14 2.14986 13.9529 1.9 13.8615 1.66688C13.7701 1.43376 13.636 1.22196 13.4671 1.04356C13.1204 0.694636 12.66 0.5 12.1811 0.5C11.7023 0.5 11.2418 0.694636 10.8952 1.04356Z" fill="#6B7280"/>
      <path d="M11.3215 6.02776L8.74888 3.3105L5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776Z" fill="#6B7280"/>
      <path d="M5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776L8.74888 3.3105L5.31117 6.93838Z" fill="#6B7280"/>
      <path d="M8.58008 10.5532C8.37679 10.7676 8.11814 10.9138 7.83652 10.9736L5.26388 11.5169C5.02903 11.5664 4.78623 11.554 4.55702 11.4806C4.32781 11.4073 4.11929 11.2753 3.94994 11.0965C3.7806 10.9176 3.65567 10.6974 3.58624 10.4553C3.51681 10.2132 3.50503 9.95673 3.55193 9.70867L4.06632 6.99141C4.12291 6.69396 4.26135 6.42077 4.46429 6.20605L8.04387 2.42524C8.02396 2.3536 8.01368 2.27936 8.01332 2.2047H1.57662C1.15859 2.2051 0.757795 2.38068 0.462206 2.69288C0.166616 3.00509 0.000385278 3.42842 0 3.86994L0 12.8348C0.000385278 13.2763 0.166616 13.6996 0.462206 14.0118C0.757795 14.324 1.15859 14.4996 1.57662 14.5H10.0643C10.4823 14.4996 10.8831 14.324 11.1787 14.0118C11.4743 13.6996 11.6405 13.2763 11.6409 12.8348V7.32031L8.58008 10.5532Z" fill="#6B7280"/>
      </g>
      </g>
      <defs>
      <clipPath id="clip0_977_23343">
      <rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
      </clipPath>
      </defs>
    </svg>

      `,
  },
  'Creator': {
    label: 'Creator',
    svg: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="window" clip-path="url(#clip0_2988_22406)">
<path id="Vector" d="M12.6 1.17432H1.4C0.6279 1.17432 0 1.81384 0 2.60024L0 12.5817C0 13.3681 0.6279 14.0077 1.4 14.0077H12.6C13.3721 14.0077 14 13.3681 14 12.5817V2.60024C14 1.81384 13.3721 1.17432 12.6 1.17432ZM7.35 3.31321C7.7364 3.31321 8.05 3.63261 8.05 4.02617C8.05 4.41972 7.7364 4.73913 7.35 4.73913C6.9636 4.73913 6.65 4.41972 6.65 4.02617C6.65 3.63261 6.9636 3.31321 7.35 3.31321ZM5.25 3.31321C5.6364 3.31321 5.95 3.63261 5.95 4.02617C5.95 4.41972 5.6364 4.73913 5.25 4.73913C4.8636 4.73913 4.55 4.41972 4.55 4.02617C4.55 3.63261 4.8636 3.31321 5.25 3.31321ZM3.15 3.31321C3.5364 3.31321 3.85 3.63261 3.85 4.02617C3.85 4.41972 3.5364 4.73913 3.15 4.73913C2.7636 4.73913 2.45 4.41972 2.45 4.02617C2.45 3.63261 2.7636 3.31321 3.15 3.31321ZM1.4 12.5817V6.87802H12.6007L12.6014 12.5817H1.4Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_2988_22406">
<rect width="14" height="14" fill="white" transform="translate(0 0.590942)"/>
</clipPath>
</defs>
</svg>
`,
  },
  'Delete': {
    label: 'Delete',
    svg: `
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="trash" clip-path="url(#clip0_977_23346)">
<path id="Vector" d="M12.7044 3.44737H9.8525V1.97368C9.8525 1.58284 9.70227 1.208 9.43486 0.931632C9.16745 0.655263 8.80476 0.5 8.42658 0.5H5.57473C5.19655 0.5 4.83386 0.655263 4.56644 0.931632C4.29903 1.208 4.1488 1.58284 4.1488 1.97368V3.44737H1.29695C1.10786 3.44737 0.926513 3.525 0.792806 3.66318C0.6591 3.80137 0.583984 3.98879 0.583984 4.18421C0.583984 4.37963 0.6591 4.56705 0.792806 4.70524C0.926513 4.84342 1.10786 4.92105 1.29695 4.92105H2.00991V13.0263C2.00991 13.4172 2.16014 13.792 2.42755 14.0684C2.69497 14.3447 3.05766 14.5 3.43584 14.5H10.5655C10.9436 14.5 11.3063 14.3447 11.5737 14.0684C11.8412 13.792 11.9914 13.4172 11.9914 13.0263V4.92105H12.7044C12.8934 4.92105 13.0748 4.84342 13.2085 4.70524C13.3422 4.56705 13.4173 4.37963 13.4173 4.18421C13.4173 3.98879 13.3422 3.80137 13.2085 3.66318C13.0748 3.525 12.8934 3.44737 12.7044 3.44737ZM5.57473 1.97368H8.42658V3.44737H5.57473V1.97368ZM6.28769 11.5526C6.28769 11.7481 6.21257 11.9355 6.07887 12.0737C5.94516 12.2118 5.76381 12.2895 5.57473 12.2895C5.38564 12.2895 5.20429 12.2118 5.07058 12.0737C4.93688 11.9355 4.86176 11.7481 4.86176 11.5526V6.39474C4.86176 6.19931 4.93688 6.0119 5.07058 5.87371C5.20429 5.73553 5.38564 5.65789 5.57473 5.65789C5.76381 5.65789 5.94516 5.73553 6.07887 5.87371C6.21257 6.0119 6.28769 6.19931 6.28769 6.39474V11.5526ZM9.13954 11.5526C9.13954 11.7481 9.06442 11.9355 8.93072 12.0737C8.79701 12.2118 8.61567 12.2895 8.42658 12.2895C8.23749 12.2895 8.05614 12.2118 7.92244 12.0737C7.78873 11.9355 7.71361 11.7481 7.71361 11.5526V6.39474C7.71361 6.19931 7.78873 6.0119 7.92244 5.87371C8.05614 5.73553 8.23749 5.65789 8.42658 5.65789C8.61567 5.65789 8.79701 5.73553 8.93072 5.87371C9.06442 6.0119 9.13954 6.19931 9.13954 6.39474V11.5526Z" fill="#C81E1E"/>
</g>
<defs>
<clipPath id="clip0_977_23346">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

    `,
  },

};
export const sceneCardsMenuItems = {
  'Convert To Deep Zoom': {
    label: 'Convert To Deep Zoom',
    svg: `
     <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
                            <path d="M15.1667 1L15.7646 2.11777C16.1689 2.87346 16.371 3.25131 16.2374 3.41313C16.1037 3.57495 15.6635 3.44426 14.7831 3.18288C13.9029 2.92155 12.9684 2.78095 12 2.78095C6.75329 2.78095 2.5 6.90846 2.5 12C2.5 13.6791 2.96262 15.2535 3.77093 16.6095M8.83333 23L8.23536 21.8822C7.83108 21.1265 7.62894 20.7486 7.7626 20.5868C7.89627 20.425 8.33649 20.5557 9.21689 20.8171C10.0971 21.0784 11.0316 21.219 12 21.219C17.2467 21.219 21.5 17.0915 21.5 12C21.5 10.3208 21.0374 8.74647 20.2291 7.39047" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg> 
      `,
  },
  'Delete': {
    label: 'Delete',
    svg: `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
                            <path d="M20.25 4.5H16.5V3.75C16.5 3.15326 16.2629 2.58097 15.841 2.15901C15.419 1.73705 14.8467 1.5 14.25 1.5H9.75C9.15326 1.5 8.58097 1.73705 8.15901 2.15901C7.73705 2.58097 7.5 3.15326 7.5 3.75V4.5H3.75C3.55109 4.5 3.36032 4.57902 3.21967 4.71967C3.07902 4.86032 3 5.05109 3 5.25C3 5.44891 3.07902 5.63968 3.21967 5.78033C3.36032 5.92098 3.55109 6 3.75 6H4.5V19.5C4.5 19.8978 4.65804 20.2794 4.93934 20.5607C5.22064 20.842 5.60218 21 6 21H18C18.3978 21 18.7794 20.842 19.0607 20.5607C19.342 20.2794 19.5 19.8978 19.5 19.5V6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5ZM9 3.75C9 3.55109 9.07902 3.36032 9.21967 3.21967C9.36032 3.07902 9.55109 3 9.75 3H14.25C14.4489 3 14.6397 3.07902 14.7803 3.21967C14.921 3.36032 15 3.55109 15 3.75V4.5H9V3.75ZM18 19.5H6V6H18V19.5ZM10.5 9.75V15.75C10.5 15.9489 10.421 16.1397 10.2803 16.2803C10.1397 16.421 9.94891 16.5 9.75 16.5C9.55109 16.5 9.36032 16.421 9.21967 16.2803C9.07902 16.1397 9 15.9489 9 15.75V9.75C9 9.55109 9.07902 9.36032 9.21967 9.21967C9.36032 9.07902 9.55109 9 9.75 9C9.94891 9 10.1397 9.07902 10.2803 9.21967C10.421 9.36032 10.5 9.55109 10.5 9.75ZM15 9.75V15.75C15 15.9489 14.921 16.1397 14.7803 16.2803C14.6397 16.421 14.4489 16.5 14.25 16.5C14.0511 16.5 13.8603 16.421 13.7197 16.2803C13.579 16.1397 13.5 15.9489 13.5 15.75V9.75C13.5 9.55109 13.579 9.36032 13.7197 9.21967C13.8603 9.07902 14.0511 9 14.25 9C14.4489 9 14.6397 9.07902 14.7803 9.21967C14.921 9.36032 15 9.55109 15 9.75Z" fill="#5B616E"/>
                            </svg>
    `,
  },

};

export const notAuthorized = 'Not authorized';

export const tableViewColumns = ['email', 'phone', 'interested unit', 'status', 'time', 'date', 'tag'];

export const tableViewStatusTypes = ['new', 'hot', 'cold', 'warm', 'not_interested'];

export const sessionTypes = ['ale', 'pixel_streaming'];

export const unitplanHotspotPills = ['Balcony', 'Bathroom1', 'Bathroom2', 'Bedroom1', 'Bedroom2', 'BedroomBalcony', 'Dining', 'Kitchen', 'Laundry', 'LivingBalcony', 'MaidRoom', 'Maidbathroom', 'MasterBathroom', 'MasterBedroom', 'PowderRoom', 'Store', 'Study', 'Wardrobe'];

export const unitplanListTypes = {
  DEFAULT: 'default',
  IMAGE: 'image',
  LABEL: 'label',
  GROUP: 'group',
  SUBGROUP: 'subgroup',
};

export const unitplanScaleTypes = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
};

export const unitplanDescTypes = {
  TEXT: 'text',
  PILL: 'pill',
};

export const projectSettingsFormTypes = {
  GENERAL: 'general',
  ALE: 'ale',
  PIXELSTREAMING: 'pixelstreaming',
  EMBED: 'embed',
  SALESTOOL: 'salestool',
  THEME: 'theme',
  HOLOGRAM: 'hologram',
  METADATA: 'metadata',
};

export const deepZoomConversionActionTypes = {
  COPY: 'copy',
  REPLACE: 'replace',
};

export const deepZoomConversionStatusTypes = {
  INITIAL: 'Initial',
  CONVERSION: 'Conversion',
  COMPLETED: 'Completed',
};

export const masterSceneCoordinateLinkType = {
  PROJECT: 'project',
  MASTER: 'master',
  EXTERNAL: 'external',
};

export const sceneTypeRoutes = {
  MASTERSCENES: 'masterscenes',
  PROJECTS: 'projects',
};

export const tourCardsMenuItems = {
  'Edit': {
    label: 'Edit',
    svg: `
     <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g>
        <path d="M10.8952 1.04356L9.77765 2.2239L12.3503 4.94117L13.4671 3.76082C13.636 3.58242 13.7701 3.37061 13.8615 3.13749C13.9529 2.90438 14 2.65452 14 2.40219C14 2.14986 13.9529 1.9 13.8615 1.66688C13.7701 1.43376 13.636 1.22196 13.4671 1.04356C13.1204 0.694636 12.66 0.5 12.1811 0.5C11.7023 0.5 11.2418 0.694636 10.8952 1.04356Z" fill="#6B7280"/>
        <path d="M11.3215 6.02776L8.74888 3.3105L5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776Z" fill="#6B7280"/>
        <path d="M5.31117 6.93838C5.20946 7.04561 5.1401 7.18225 5.11182 7.33107L4.72912 9.36901C4.70558 9.4931 4.71142 9.62142 4.74612 9.74255C4.78082 9.86369 4.84331 9.9739 4.92804 10.0634C5.01277 10.1529 5.11711 10.2189 5.2318 10.2555C5.34649 10.2922 5.46797 10.2984 5.58546 10.2735L7.51494 9.8662C7.65558 9.8362 7.78467 9.76294 7.886 9.65565L11.3215 6.02776L8.74888 3.3105L5.31117 6.93838Z" fill="#6B7280"/>
        <path d="M8.58008 10.5532C8.37679 10.7676 8.11814 10.9138 7.83652 10.9736L5.26388 11.5169C5.02903 11.5664 4.78623 11.554 4.55702 11.4806C4.32781 11.4073 4.11929 11.2753 3.94994 11.0965C3.7806 10.9176 3.65567 10.6974 3.58624 10.4553C3.51681 10.2132 3.50503 9.95673 3.55193 9.70867L4.06632 6.99141C4.12291 6.69396 4.26135 6.42077 4.46429 6.20605L8.04387 2.42524C8.02396 2.3536 8.01368 2.27936 8.01332 2.2047H1.57662C1.15859 2.2051 0.757795 2.38068 0.462206 2.69288C0.166616 3.00509 0.000385278 3.42842 0 3.86994L0 12.8348C0.000385278 13.2763 0.166616 13.6996 0.462206 14.0118C0.757795 14.324 1.15859 14.4996 1.57662 14.5H10.0643C10.4823 14.4996 10.8831 14.324 11.1787 14.0118C11.4743 13.6996 11.6405 13.2763 11.6409 12.8348V7.32031L8.58008 10.5532Z" fill="#6B7280"/>
        </g>
      </svg>
      `,
  },
  'Delete': {
    label: 'Delete',
    svg: `
      <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g>
        <path d="M12.7037 3.44737H9.85189V1.97368C9.85189 1.58284 9.70166 1.208 9.43425 0.931632C9.16684 0.655263 8.80415 0.5 8.42597 0.5H5.57411C5.19594 0.5 4.83325 0.655263 4.56583 0.931632C4.29842 1.208 4.14819 1.58284 4.14819 1.97368V3.44737H1.29634C1.10725 3.44737 0.925903 3.525 0.792196 3.66318C0.65849 3.80137 0.583374 3.98879 0.583374 4.18421C0.583374 4.37963 0.65849 4.56705 0.792196 4.70524C0.925903 4.84342 1.10725 4.92105 1.29634 4.92105H2.0093V13.0263C2.0093 13.4172 2.15953 13.792 2.42694 14.0684C2.69436 14.3447 3.05705 14.5 3.43523 14.5H10.5649C10.943 14.5 11.3057 14.3447 11.5731 14.0684C11.8406 13.792 11.9908 13.4172 11.9908 13.0263V4.92105H12.7037C12.8928 4.92105 13.0742 4.84342 13.2079 4.70524C13.3416 4.56705 13.4167 4.37963 13.4167 4.18421C13.4167 3.98879 13.3416 3.80137 13.2079 3.66318C13.0742 3.525 12.8928 3.44737 12.7037 3.44737ZM5.57411 1.97368H8.42597V3.44737H5.57411V1.97368ZM6.28708 11.5526C6.28708 11.7481 6.21196 11.9355 6.07826 12.0737C5.94455 12.2118 5.7632 12.2895 5.57411 12.2895C5.38503 12.2895 5.20368 12.2118 5.06997 12.0737C4.93627 11.9355 4.86115 11.7481 4.86115 11.5526V6.39474C4.86115 6.19931 4.93627 6.0119 5.06997 5.87371C5.20368 5.73553 5.38503 5.65789 5.57411 5.65789C5.7632 5.65789 5.94455 5.73553 6.07826 5.87371C6.21196 6.0119 6.28708 6.19931 6.28708 6.39474V11.5526ZM9.13893 11.5526C9.13893 11.7481 9.06381 11.9355 8.93011 12.0737C8.7964 12.2118 8.61506 12.2895 8.42597 12.2895C8.23688 12.2895 8.05553 12.2118 7.92183 12.0737C7.78812 11.9355 7.713 11.7481 7.713 11.5526V6.39474C7.713 6.19931 7.78812 6.0119 7.92183 5.87371C8.05553 5.73553 8.23688 5.65789 8.42597 5.65789C8.61506 5.65789 8.7964 5.73553 8.93011 5.87371C9.06381 6.0119 9.13893 6.19931 9.13893 6.39474V11.5526Z" fill="#C81E1E"/>
        </g>
      </svg>
    `,
  },

};

export const AssetType = {
  MASTERPLAN: 'master_plan',
  ARCHITECTURAL_DRAWING: 'architectural_drawing',
  INTERIOR_RENDER: 'interior_render',
  EXTERIOR_RENDER: 'exterior_render',
  FLOOR_PLAN: 'floor_plan',
};

export const unitType = {
  VILLA: 'villa',
  FLAT: 'flat',
};

export const unitplan_type = {
  STUDIO: 'studio',
  PENTHOUSE: 'penthouse',
  TOWNHOUSE: 'townhouse',
  SUITE: 'suite',
  DUPLEX: 'duplex',
  ONEBHK: '1BHK',
  TWOBHK: '2BHK',
  THREEBHK: '3BHK',
  FOURBHK: '4BHK',
  FIVEBHK: '5BHK',
  SIXBHK: '6BHK',
  SEVENBHK: '7BHK',
  EIGHTBHK: '8BHK',
  NINEBHK: '9BHK',
  TENBHK: '10BHK',
  ONEPOINTFIVEBHK: '1.5BHK',
  TWOPOINTFIVEBHK: '2.5BHK',
  THREEPOINTFIVEBHK: '3.5BHK',
  FOURPOINTFIVEBHK: '4.5BHK',
  FIVEPOINTFIVEBHK: '5.5BHK',
  SIXPOINTFIVEBHK: '6.5BHK',
  SEVENPOINTFIVEBHK: '7.5BHK',
  EIGHTPOINTFIVEBHK: '8.5BHK',
  NINEPOINTFIVEBHK: '9.5BHK',
  TENPOINTFIVEBHK: '10.5BHK',
};

export const measurementType = {
  SQFT: 'sqft',
  SQMT: 'sqmt',
};

export const compressImageQualityFormats = ['image/jpeg', 'image/png', 'image/webp'];

export const formTypes = {
  CREATE: 'create',
  EDIT: 'edit',
};
