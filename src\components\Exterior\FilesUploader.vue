<script setup>
import { ref, defineProps, defineEmits, computed, watch } from 'vue';
import ImgPreviewModal from '../common/Modal/imgPreviewModal.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['closeModal', 'fileUploaded']);
const selectedData = ref(null);
const previewImgUrl = ref();
const showFileViewer = ref(false);
const fileInput = ref(null);
const triggerFileInput = () => fileInput.value?.click();
const selectedFileId = ref(null);
const searchQuery = ref("");
const isLoading = ref(true);
const assestData = computed(() => {
  const values = Object.values(props.data);
  return values.length ? values : [];
});

const filteredFiles = computed(() => {
  if (!searchQuery.value) {
    return assestData.value;
  }
  return assestData.value.filter((file) =>
    file.file_name.toLowerCase().includes(searchQuery.value.toLowerCase()),
  );
});

const handleSelection = async (file) => {
  console.log('got file', file);
  selectedFileId.value = file._id;
  try {
    // Fetch the file as a Blob
    const response = await fetch(file.file_url);
    const blob = await response.blob();

    // Create a File-like object
    selectedData.value = new File([blob], file.file_name, {
      type: `image/${file.media_type}`,
      lastModified: new Date(file.updated_at).getTime() || Date.now(),
    });
    console.log("Transformed selected file:", selectedData.value);
  } catch (error) {
    console.error("Error converting URL to file:", error);
  }
};

const handleConfirm = () => {
  emit('fileUploaded', selectedData.value);
  emit('closeModal');
};

const extractFileName = (url) => {
  try {
    const decodedUrl = decodeURIComponent(url); // Decode URL in case of encoded characters
    const match = decodedUrl.match(/\/([^/?]+)\?alt=media/); // Removed unnecessary escapes
    return match ? match[1] : null;
  } catch (error) {
    console.error("Error extracting filename:", error);
    return null;
  }
};

const handlePreview = (url) => {
  showFileViewer.value = true;
  previewImgUrl.value = url;
};

const handleClose = () => {
  showFileViewer.value = false;
};

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  emit("fileUploaded", file);
  emit('closeModal');
};

const formatDate = (isoString) => {
  const date = new Date(isoString);
  return date.toLocaleString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
    month: "short",
    day: "numeric",
  }).replace(',', '').toUpperCase();
};

watch(
  () => props.data,
  (newVal, oldVal) => {
    // If the parent sets data to anything (even empty), loading is done
    if (newVal !== oldVal) {
      isLoading.value = false;
    }
  },
  { immediate: true, deep: true },
);

</script>

<template>
  <div class="bg-white rounded-lg right-2 w-[90%] h-[80%] flex flex-col mt-14">
    <div class="h-[90%] w-[webkit-fill-available] mt-4 px-4">
      <div class="flex justify-between">
        <div class="flex flex-col gap-2">
          <p class="text-xl font-bold">Upload</p>
          <p class="text-xs text-gray-500">PNG, JPG or GIF (2MB)</p>
        </div>
        <button title="Browse From Computer" class="!bg-blue-600 rounded-lg py-1 px-4 !h-[40px] text-white text-sm" @click="triggerFileInput">
          Browse From Computer
        </button>
        <input
          type="file"
          ref="fileInput"
          class="hidden"
          accept="image/png, image/jpeg, image/gif, image/webp"
          @change="handleFileSelect"
        />
      </div>
      <div class="pt-2">
        <p>Available Files In Data Center</p>
        <div class="relative pt-2">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search Files"
            class="bg-gray-100 rounded-lg !pl-[2.5rem] p-1 placeholder:text-left"
          />
          <span class="absolute left-3 top-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="16" viewBox="0 0 16 16" fill="none">
              <g clip-path="url(#clip0_5245_18851)">
                <path d="M15.2002 16L15.2003 15.5C15.1207 15.5 15.0444 15.4684 14.9882 15.4121L14.9881 15.412L11.7919 12.2159C11.7387 12.1595 11.7093 12.0848 11.71 12.0072C11.7106 11.9286 11.7422 11.8533 11.7978 11.7977L11.4443 11.4441L11.7978 11.7977C11.8534 11.7421 11.9287 11.7105 12.0073 11.7098C12.0849 11.7092 12.1597 11.7386 12.216 11.7918L15.4122 14.9879C15.4122 14.9879 15.4122 14.9879 15.4122 14.988C15.4541 15.0299 15.4827 15.0833 15.4942 15.1415C15.5058 15.1997 15.4999 15.26 15.4772 15.3148C15.4545 15.3696 15.416 15.4165 15.3667 15.4494C15.3174 15.4824 15.2594 15.5 15.2001 15.5L15.2002 16ZM15.2002 16C14.988 16 14.7846 15.9156 14.6346 15.7656L11.4345 12.5656L15.7658 14.6344C15.8776 14.7463 15.9538 14.8888 15.9846 15.044C16.0155 15.1991 15.9996 15.36 15.9391 15.5061C15.8786 15.6523 15.7761 15.7772 15.6445 15.8651C15.513 15.953 15.3584 16 15.2002 16ZM6.40058 1.1H6.40008C5.35183 1.1 4.32712 1.41084 3.45553 1.99321C2.58394 2.57558 1.90461 3.40333 1.50347 4.37178C1.10232 5.34022 0.997356 6.40588 1.20186 7.43398C1.40637 8.46208 1.91115 9.40645 2.65237 10.1477C3.3936 10.8889 4.33798 11.3937 5.36609 11.5982C6.3942 11.8027 7.45986 11.6977 8.42832 11.2966C9.39678 10.8954 10.2245 10.2161 10.8069 9.34452C11.3893 8.47294 11.7001 7.44824 11.7001 6.4V6.3995C11.6987 4.99441 11.1399 3.64728 10.1464 2.65374C9.15282 1.66019 7.80567 1.10141 6.40058 1.1ZM6.39952 12.3C5.23279 12.2999 4.09228 11.9539 3.12217 11.3057C2.15191 10.6574 1.39568 9.73591 0.949118 8.65783C0.502557 7.57975 0.385716 6.39345 0.613371 5.24897C0.841026 4.10448 1.40295 3.0532 2.22809 2.22807C3.05323 1.40294 4.10453 0.841023 5.24903 0.61337C6.39353 0.385717 7.57984 0.502557 8.65794 0.949114C9.73604 1.39567 10.6575 2.15189 11.3058 3.12214C11.954 4.09223 12.3 5.23273 12.3002 6.39944C12.2984 7.96382 11.6762 9.46363 10.57 10.5698C9.46375 11.676 7.96392 12.2982 6.39952 12.3Z" fill="#6B7280" stroke="#6B7280"/>
              </g>
              <defs>
                <clipPath id="clip0_5245_18851">
                  <rect width="16" height="16" fill="white"/>
                </clipPath>
              </defs>
            </svg>
          </span>
        </div>
      </div>
      <!-- Table View -->
      <div class="rounded-lg border border-blue-600" :class="['overflow-x-auto w-full mt-3 bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden']">
        <table class="table-auto w-full">
          <!-- Table Header -->
          <thead>
            <tr>
              <th class=" px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 text-xs font-semibold">S.No
              </th>
              <th class="px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 text-xs font-semibold">File
                Name</th>
              <th class="px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 text-xs font-semibold">Select
                File Type</th>
              <th class="px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 text-xs font-semibold whitespace-nowrap">Media
                Type</th>
              <th class="px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 text-xs font-semibold">Upload
                File</th>
              <th class="px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 text-xs font-semibold">File Link </th>
              <th class="px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 text-xs font-semibold">
                Modified</th>
              <th class="px-3 py-2 bg-gray-50 border-b border-gray-200 text-gray-500 font-semibold"></th>
            </tr>
          </thead>
          <!-- Table Body -->
          <tbody>
            <template v-if="isLoading">
              <tr>
                <td colspan="100%">
                  <div class="flex flex-col items-center justify-center p-3">
                    <div class="loader !h-8 !w-8 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
                    <p class="text-black text-xs font-normal pt-1">Loading...</p>
                  </div>
                </td>
              </tr>
            </template>
            <template v-else-if="filteredFiles.length === 0">
              <tr>
                <td colspan="100%" class="text-center text-gray-500">
                  <div class="flex flex-col items-center justify-center p-3 text-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                      <g clip-path="url(#clip0_4982_18313)">
                        <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z" fill="#6B7280"/>
                      </g>
                      <defs>
                        <clipPath id="clip0_4982_18313">
                          <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                        </clipPath>
                      </defs>
                    </svg>
                    <p class="text-black text-xs font-normal pt-1">Empty</p>
                  </div>
                </td>
              </tr>
            </template>
            <tr v-else v-for="(file, index) in filteredFiles" :key="file._id" :class="{'bg-blue-50': selectedFileId === file._id}">
              <td class="px-3 py-2 text-sm font-normal text-gray-500">{{ index + 1 }}</td>
              <td class="px-3 py-2">
                <p class="text-sm font-normal">{{file.file_name}}</p>
              </td>
              <td class="px-3 py-2">
                <p class="text-sm font-normal">{{file.file_type}}</p>
              </td>
              <td class="px-3 py-2">
                <p class="text-sm font-normal">{{file.media_type}}</p>
              </td>
              <td class="px-3 py-2">
                <div class="flex justify-between w-full items-center">
                  <p class="text-sm font-normal w-[80%] h-5 whitespace-nowrap !overflow-hidden !text-ellipsis">{{ extractFileName(file.file_url) }}</p>
                  <div class="w-7 h-7 bg-gray-100 flex justify-center items-center rounded-md cursor-pointer" @click="handlePreview(file.file_url)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M8 2C3.6896 2 0 6.57371 0 8C0 9.49314 2.8368 14 8 14C13.1632 14 16 9.49314 16 8C16 6.57371 12.3104 2 8 2ZM8 10.5714C7.52532 10.5714 7.06131 10.4206 6.66663 10.1381C6.27195 9.85551 5.96434 9.45391 5.78269 8.98404C5.60104 8.51418 5.55351 7.99715 5.64612 7.49834C5.73872 6.99953 5.9673 6.54135 6.30294 6.18173C6.63859 5.8221 7.06623 5.5772 7.53178 5.47798C7.99734 5.37876 8.4799 5.42968 8.91844 5.62431C9.35698 5.81893 9.73181 6.14852 9.99553 6.57139C10.2592 6.99426 10.4 7.49142 10.4 8C10.4 8.68199 10.1471 9.33604 9.69706 9.81827C9.24697 10.3005 8.63652 10.5714 8 10.5714Z" fill="#6B7280"/>
                    </svg>
                  </div>
                </div>
              </td>
              <td class="px-3 py-2">
                <p class="text-sm font-normal w-40 h-5 overflow-hidden text-ellipsis">
                  <a
                    :href="file.file_url"
                    target="_blank"
                    class="text-blue-500 hover:underline hover:text-blue-500 text-sm font-normal w-40 h-5 overflow-hidden text-ellipsis block"
                  >
                    {{ file.file_url }}
                  </a>
                </p>
              </td>
              <td class="px-3 py-2 text-sm font-normal text-gray-500">
                {{ file.updated_at ? formatDate(file.updated_at) : 'Not Modified' }}
              </td>

              <td class="px-3 py-2 text-sm font-normal text-center">
                <input
                  :id="file._id"
                  :name="'file_selection'"
                  type="radio"
                  :value="file._id"
                  @change="handleSelection(file)"
                  class="w-4 h-4 bg-gray-100 border-gray-300 focus:ring-blue-00 dark:focus:ring-blue-00 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                >
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="h-[10%] flex justify-end gap-3 mr-3 mb-1">
      <button @click="emit('closeModal')" title="Cancel" class="!bg-white !border !border-gray-300 !text-black rounded-lg px-5 !h-[40px]">
        Cancel
      </button>
      <button title="Confirm" class="!bg-blue-600 rounded-lg py-1 px-5 !h-[40px] text-white" :disabled="!selectedData" @click="handleConfirm()">
        Confirm
      </button>
    </div>

    <div v-if="showFileViewer" class="flex justify-between items-center  w-[80%] h-[85%]">
        <ImgPreviewModal :isOpen="true" :imageUrl="previewImgUrl"   @close="handleClose"/>
    </div>
  </div>
</template>
