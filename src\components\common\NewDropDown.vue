<script setup>
import {  closeIcon, filterIconActive, filterIconInActive, searchIcon} from '@/helpers/icons';
import { computed, ref, watch} from 'vue';
// import DnDFileUploader from './DnDFileUploader.vue';
import { onClickOutside } from '@vueuse/core';
import { UserStore } from '@/store';
import { uiOperations } from '@/store/uiOperations';
// import { useRoute } from 'vue-router';

const props = defineProps({
  options: {
    type: Object,
    required: true,
  },
  title: {
    type: Array,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  inputType: {
    type: String,
  },
  width: {
    type: String,
  },
  initialValue: {
    type: String,
  },
  searchBar: {
    type: Boolean,
  },
  outerWidth: {
    type: String,
  },
  // fileUploader: {
  //   type: Boolean,
  // },
});

const emit = defineEmits([
  'optionSelected',
  'experienceOptionSelected',
  'locationOptionSelected',
  'roleOptionSelected',
  'projectOptionSelected',
  'agentOptionSelected',
  'resetFilters',
]);

// const route = useRoute();
const initialData = ref(props.initialValue);
const tempTitle = ref('');
const dropDownModalRef = ref();
const searchText = ref('');
const openModal = ref();
const selectedOptions = ref([]);
const selectedLocationOptions = ref([]);
const selectedExperienceOptions = ref([]);
const selectedRoleOptions = ref([]);
const selectedProjectOptions = ref([]);
const selectedAgentOptions = ref([]);
const inputRef = ref(null);
const Store = UserStore();
const userStore = uiOperations();
const activeTab = ref(props.title[0]);
const svg =
    {
      open:
        `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M7.0147 4C6.68056 4.00007 6.36012 4.12277 6.12388 4.34114L1.08388 8.99826C0.963539 9.10566 0.86755 9.23414 0.801514 9.37618C0.735479 9.51823 0.70072 9.67101 0.699266 9.8256C0.697813 9.98019 0.729692 10.1335 0.793046 10.2766C0.8564 10.4197 0.949959 10.5497 1.06826 10.659C1.18657 10.7683 1.32725 10.8548 1.4821 10.9133C1.63695 10.9718 1.80287 11.0013 1.97017 11C2.13747 10.9986 2.30281 10.9665 2.45653 10.9055C2.61026 10.8445 2.74929 10.7558 2.86552 10.6446L7.0147 6.81058L11.1639 10.6446C11.4015 10.8566 11.7198 10.974 12.0502 10.9713C12.3805 10.9687 12.6966 10.8462 12.9302 10.6304C13.1638 10.4145 13.2963 10.1225 13.2992 9.81722C13.302 9.51195 13.175 9.21785 12.9455 8.99826L7.90552 4.34114C7.66928 4.12277 7.34885 4.00007 7.0147 4Z" fill="#1C64F2"/>
        </g>
        </svg>`,
      close:
        `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M7.0147 11C6.68056 10.9999 6.36012 10.8772 6.12388 10.6589L1.08388 6.00174C0.963539 5.89434 0.86755 5.76586 0.801514 5.62382C0.735479 5.48177 0.70072 5.32899 0.699266 5.1744C0.697813 5.01981 0.729692 4.86649 0.793046 4.72341C0.8564 4.58032 0.949959 4.45033 1.06826 4.34101C1.18657 4.23169 1.32725 4.14524 1.4821 4.0867C1.63695 4.02816 1.80287 3.9987 1.97017 4.00004C2.13747 4.00139 2.30281 4.03351 2.45653 4.09452C2.61026 4.15554 2.74929 4.24424 2.86552 4.35544L7.0147 8.18942L11.1639 4.35544C11.4015 4.14336 11.7198 4.02601 12.0502 4.02866C12.3805 4.03131 12.6966 4.15376 12.9302 4.36962C13.1638 4.58549 13.2963 4.87751 13.2992 5.18278C13.302 5.48805 13.175 5.78215 12.9455 6.00174L7.90552 10.6589C7.66928 10.8772 7.34885 10.9999 7.0147 11Z" fill="#1F2A37"/>
        </g>
        </svg>`,
    };

const typeContent = [
  {
    name: 'Experience',
    style: {
      textColor: 'text-black',
      onFocusTextColor: 'text-[#1c64f2]',
    },
    svg:
    {
      open:
        `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M7.0147 4C6.68056 4.00007 6.36012 4.12277 6.12388 4.34114L1.08388 8.99826C0.963539 9.10566 0.86755 9.23414 0.801514 9.37618C0.735479 9.51823 0.70072 9.67101 0.699266 9.8256C0.697813 9.98019 0.729692 10.1335 0.793046 10.2766C0.8564 10.4197 0.949959 10.5497 1.06826 10.659C1.18657 10.7683 1.32725 10.8548 1.4821 10.9133C1.63695 10.9718 1.80287 11.0013 1.97017 11C2.13747 10.9986 2.30281 10.9665 2.45653 10.9055C2.61026 10.8445 2.74929 10.7558 2.86552 10.6446L7.0147 6.81058L11.1639 10.6446C11.4015 10.8566 11.7198 10.974 12.0502 10.9713C12.3805 10.9687 12.6966 10.8462 12.9302 10.6304C13.1638 10.4145 13.2963 10.1225 13.2992 9.81722C13.302 9.51195 13.175 9.21785 12.9455 8.99826L7.90552 4.34114C7.66928 4.12277 7.34885 4.00007 7.0147 4Z" fill="#1C64F2"/>
        </g>
        </svg>`,
      close:
        `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M7.0147 11C6.68056 10.9999 6.36012 10.8772 6.12388 10.6589L1.08388 6.00174C0.963539 5.89434 0.86755 5.76586 0.801514 5.62382C0.735479 5.48177 0.70072 5.32899 0.699266 5.1744C0.697813 5.01981 0.729692 4.86649 0.793046 4.72341C0.8564 4.58032 0.949959 4.45033 1.06826 4.34101C1.18657 4.23169 1.32725 4.14524 1.4821 4.0867C1.63695 4.02816 1.80287 3.9987 1.97017 4.00004C2.13747 4.00139 2.30281 4.03351 2.45653 4.09452C2.61026 4.15554 2.74929 4.24424 2.86552 4.35544L7.0147 8.18942L11.1639 4.35544C11.4015 4.14336 11.7198 4.02601 12.0502 4.02866C12.3805 4.03131 12.6966 4.15376 12.9302 4.36962C13.1638 4.58549 13.2963 4.87751 13.2992 5.18278C13.302 5.48805 13.175 5.78215 12.9455 6.00174L7.90552 10.6589C7.66928 10.8772 7.34885 10.9999 7.0147 11Z" fill="#1F2A37"/>
        </g>
        </svg>`,
    },
  },
  {
    name: 'Location',
    style: {
      textColor: 'text-black',
      onFocusTextColor: 'text-[#1c64f2]',
    },
    svg:
    {
      open:
        `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M7.0147 4C6.68056 4.00007 6.36012 4.12277 6.12388 4.34114L1.08388 8.99826C0.963539 9.10566 0.86755 9.23414 0.801514 9.37618C0.735479 9.51823 0.70072 9.67101 0.699266 9.8256C0.697813 9.98019 0.729692 10.1335 0.793046 10.2766C0.8564 10.4197 0.949959 10.5497 1.06826 10.659C1.18657 10.7683 1.32725 10.8548 1.4821 10.9133C1.63695 10.9718 1.80287 11.0013 1.97017 11C2.13747 10.9986 2.30281 10.9665 2.45653 10.9055C2.61026 10.8445 2.74929 10.7558 2.86552 10.6446L7.0147 6.81058L11.1639 10.6446C11.4015 10.8566 11.7198 10.974 12.0502 10.9713C12.3805 10.9687 12.6966 10.8462 12.9302 10.6304C13.1638 10.4145 13.2963 10.1225 13.2992 9.81722C13.302 9.51195 13.175 9.21785 12.9455 8.99826L7.90552 4.34114C7.66928 4.12277 7.34885 4.00007 7.0147 4Z" fill="#1C64F2"/>
        </g>
        </svg>`,
      close:
        `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="chevron-down">
        <path id="Vector" d="M7.0147 11C6.68056 10.9999 6.36012 10.8772 6.12388 10.6589L1.08388 6.00174C0.963539 5.89434 0.86755 5.76586 0.801514 5.62382C0.735479 5.48177 0.70072 5.32899 0.699266 5.1744C0.697813 5.01981 0.729692 4.86649 0.793046 4.72341C0.8564 4.58032 0.949959 4.45033 1.06826 4.34101C1.18657 4.23169 1.32725 4.14524 1.4821 4.0867C1.63695 4.02816 1.80287 3.9987 1.97017 4.00004C2.13747 4.00139 2.30281 4.03351 2.45653 4.09452C2.61026 4.15554 2.74929 4.24424 2.86552 4.35544L7.0147 8.18942L11.1639 4.35544C11.4015 4.14336 11.7198 4.02601 12.0502 4.02866C12.3805 4.03131 12.6966 4.15376 12.9302 4.36962C13.1638 4.58549 13.2963 4.87751 13.2992 5.18278C13.302 5.48805 13.175 5.78215 12.9455 6.00174L7.90552 10.6589C7.66928 10.8772 7.34885 10.9999 7.0147 11Z" fill="#1F2A37"/>
        </g>
        </svg>`,
    },
  },
  {
    name: 'Font',
    style: {
      textColor: 'text-black',
      onFocusTextColor: 'text-[#1c64f2]',
    },
    svg:
    {
      open:
        `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="pencil-edit-01">
        <path id="Vector" d="M10.1421 4.48834L11.0765 3.55383C11.5927 3.03772 12.4295 3.03772 12.9456 3.55383C13.4617 4.06995 13.4617 4.90673 12.9456 5.42285L12.0111 6.35735M10.1421 4.48834L4.65284 9.97761C3.95597 10.6745 3.60752 11.0229 3.37026 11.4475C3.133 11.8721 2.89428 12.8747 2.66602 13.8334C3.62474 13.6051 4.62734 13.3664 5.05194 13.1291C5.47654 12.8919 5.82498 12.5435 6.52185 11.8466L12.0111 6.35735M10.1421 4.48834L12.0111 6.35735" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path id="Vector_2" d="M7.33398 13.8333H11.334" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
        </g>
        </svg>`,
      close:
        `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="pencil-edit-01">
        <path id="Vector" d="M10.1421 4.48834L11.0765 3.55383C11.5927 3.03772 12.4295 3.03772 12.9456 3.55383C13.4617 4.06995 13.4617 4.90673 12.9456 5.42285L12.0111 6.35735M10.1421 4.48834L4.65284 9.97761C3.95597 10.6745 3.60752 11.0229 3.37026 11.4475C3.133 11.8721 2.89428 12.8747 2.66602 13.8334C3.62474 13.6051 4.62734 13.3664 5.05194 13.1291C5.47654 12.8919 5.82498 12.5435 6.52185 11.8466L12.0111 6.35735M10.1421 4.48834L12.0111 6.35735" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path id="Vector_2" d="M7.33398 13.8333H11.334" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
        </g>
        </svg>`,
    },
  },
];

const filteredList = computed(() => {
  if (!Store.isMobile){
    return props.options.filter((item) => {
      return item.toLowerCase().includes(searchText.value.toLowerCase());
    });
  }
  if (props.options.location){
    return props.options.location.filter((item) => {
      return item.toLowerCase().includes(searchText.value.toLowerCase());
    });
  } else if (props.options.project){
    return props.options.project.filter((item) => {
      return item.toLowerCase().includes(searchText.value.toLowerCase());
    });
  }
  return [];

});
const filteredAgents = computed(() => {
  return props.options.agent.filter((item) => {
    return item.toLowerCase().includes(searchText.value.toLowerCase());
  });
});
const filteredExperience = computed(() => {
  return props.options.experience.filter((item) => {
    return item.toLowerCase().includes(searchText.value.toLowerCase());
  });
});
const filteredRole = computed(() => {
  return props.options.role.filter((item) => {
    return item.toLowerCase().includes(searchText.value.toLowerCase());
  });
});

const svgContent = computed(() => {
  return openModal.value ? svg?.open : svg?.close;
});
const testColorClass = computed(() => {
  const typeStyle = typeContent.find((item) => props.title === item.name)?.style;
  return openModal.value ? typeStyle?.onFocusTextColor : typeStyle?.textColor;
});

onClickOutside(dropDownModalRef, () => {
  console.log("onClickOutside");
  if (!Store.isMobile){
    searchText.value= '';
    openModal.value = false;
  }
});

watch(activeTab, () => {
  searchText.value = '';
});
const emitOption = (selectedOption, type) => {
  console.log("inside emitoption", type, "???", selectedOption, "::", props.inputType);
  if (props.type === 'edit' && props.inputType === 'radio'){
    if (!selectedOptions.value.some((item) => item === selectedOption)){
      initialData.value  = false;
      tempTitle.value = selectedOption;
    } else {
      initialData.value  = props.initialValue;
    }
  }
  if (props.inputType === 'checkbox'){
    if (Store.isMobile){
      if (props.options.location?.includes(selectedOption)){
        console.log("for location");
        if (!selectedLocationOptions.value.some((item) => item === selectedOption)){
          console.log("value added");
          selectedLocationOptions.value.push(selectedOption);
          inputRef.value = 1;
          console.log("added data", selectedLocationOptions.value);
        } else {
          console.log("removing value");
          const index = selectedLocationOptions.value.indexOf(selectedOption);
          if (index !== -1) {
            selectedLocationOptions.value.splice(index, 1);
          }
        }
        // emitLocation.value = true;
        emit('locationOptionSelected', selectedLocationOptions.value);

      } else if (props.options.experience?.includes(selectedOption)){
        if (!selectedExperienceOptions.value.some((item) => item === selectedOption)){
          console.log("value added");
          selectedExperienceOptions.value.push(selectedOption);
          inputRef.value = 1;
          console.log("added data", selectedExperienceOptions.value);
        } else {
          console.log("removing value");
          const index = selectedExperienceOptions.value.indexOf(selectedOption);
          if (index !== -1) {
            selectedExperienceOptions.value.splice(index, 1);
          }
        }
        // emitExperience.value = true;
        emit('experienceOptionSelected', selectedExperienceOptions.value);
      } else if (props.options.role?.includes(selectedOption)){
        if (!selectedRoleOptions.value.some((item) => item === selectedOption)){
          console.log("value added");
          selectedRoleOptions.value.push(selectedOption);
          inputRef.value = 1;
          console.log("added data", selectedRoleOptions.value);
        } else {
          console.log("removing value");
          const index = selectedRoleOptions.value.indexOf(selectedOption);
          if (index !== -1) {
            selectedRoleOptions.value.splice(index, 1);
          }
        }
        // emitExperience.value = true;
        emit('roleOptionSelected', selectedRoleOptions.value);
      }
    } else {
      if (!selectedOptions.value.some((item) => item === selectedOption)){
        console.log("value added");
        selectedOptions.value.push(selectedOption);
        inputRef.value = 1;
        console.log("added data", selectedOptions.value);
      } else {
        console.log("removing value");
        const index = selectedOptions.value.indexOf(selectedOption);
        if (index !== -1) {
          selectedOptions.value.splice(index, 1);
        }
      }
      emit('optionSelected', selectedOptions.value);
    }

  } else if (props.inputType === 'radio'){
    if (Store.isMobile){
      if (props.options.project?.includes(selectedOption)){
        console.log("********");
        if (!selectedProjectOptions.value.some((item) => item === selectedOption)){
          console.log("value added");
          selectedProjectOptions.value.push(selectedOption);
          inputRef.value = 1;
          console.log("added data", selectedProjectOptions.value);
          emit('projectOptionSelected', selectedOption);
        } else {
          console.log("removing value");
          const index = selectedProjectOptions.value.indexOf(selectedOption);
          if (index !== -1) {
            selectedProjectOptions.value.splice(index, 1);
          }
          emit('projectOptionSelected', null);
        }
        // emitLocation.value = true;

      } else if (props.options.agent?.includes(selectedOption)){
        if (!selectedAgentOptions.value.some((item) => item === selectedOption)){
          console.log("value added");
          selectedAgentOptions.value.push(selectedOption);
          inputRef.value = 1;
          console.log("added data", selectedAgentOptions.value);
          emit('agentOptionSelected', selectedOption);
        } else {
          console.log("removing value");
          const index = selectedAgentOptions.value.indexOf(selectedOption);
          if (index !== -1) {
            selectedAgentOptions.value.splice(index, 1);
          }
          emit('agentOptionSelected', null);
        }
        // emitExperience.value = true;
        // emit('agentOptionSelected', selectedAgentOptions.value);
      }
    } else {
      console.log("sss", selectedOptions.value);
      if (selectedOptions.value.some((item) => item === selectedOption)){
        const index = selectedOptions.value.indexOf(selectedOption);
        if (index !== -1) {
          selectedOptions.value.splice(index, 1);
        }
        emit('optionSelected', null);
      } else {
        selectedOptions.value = [selectedOption];
        console.log("item to remove:", selectedOption, "List:", selectedOptions.value);
        emit('optionSelected', selectedOption);
      }
    }
  }
};
function handleModal (){
  searchText.value= '';
  if (openModal.value){
    openModal.value = false;
    userStore.disableToaster = false;
    userStore.disableAddSessionIcon = false;
  } else {
    openModal.value = true;
    userStore.disableToaster = true;
    userStore.disableAddSessionIcon = true;
  }

}
function handleResetFilters (){
  selectedRoleOptions.value = [];
  selectedExperienceOptions.value = [];
  selectedLocationOptions.value = [];
  selectedProjectOptions.value = [];
  selectedAgentOptions.value = [];
  emit('resetFilters');
}

</script>

<template>
    <div v-if="!Store.isMobile"  class="relative" ref="dropDownModalRef">
        <div  :class="[openModal ? ' border !border-[#1c64f2] text-[#1c64f2]':'border hover:bg-gray-100 ', outerWidth ? `${outerWidth}`: ' w-[7.8rem]' ]" class=" h-[2.5rem]    active:bg-white  flex items-center justify-center gap-2 rounded-lg cursor-pointer select-none"
             @click="handleModal"
             >

                <div class="h-full w-[70%] flex items-center justify-start bg-transparent" :class="type === 'edit'?'border-r-2':''">
                    <p class=" text-sm font-medium pl-2"
                :class="testColorClass">{{ initialData ? initialData : tempTitle ? tempTitle : title }}</p>
                </div>
                <div class="w-[30%] h-full flex justify-center items-center bg-transparent">
                    <span v-html="svgContent">
                    </span>
                </div>

        </div>
        <div v-if="openModal"
        :class="[width ?`${width}`:'w-[9rem]',type === 'edit'?'top-[-300%] left-[100%]':'']"
        class="z-20 pt-2 pb-1 px-1 absolute bg-white rounded-lg shadow-2xl border-b border-r border-l border-gray-50 select-none flex flex-col gap-2 items-center"
        >
            <div v-if="searchBar" class="flex w-[90%] justify-center relative">
                <span v-html="searchIcon" class="absolute top-[9px] left-[20px]"></span>
                <input type="search"
                v-model="searchText"
                class="w-full bg-gray-100  text-gray-500 text-sm font-normal border rounded-lg pr-[0.5rem] pl-[2.6rem] placeholder-start py-2"
                placeholder="Search"/>
            </div>
            <ul v-if="!searchText" :class="width? 'w-full':'w-[95%]'" class="flex flex-col gap-3 max-h-[100px] overflow-y-scroll pb-2">
                <li v-for="item in options" :key="item"
                class="bg-white w-full flex gap-3 text-sm text-bg-50 font-semibold px-2.5 py-0 cursor-pointer"
                @click="emitOption(item,'')" >
                  <input :type="inputType" :name="type" :value="true" :checked="selectedOptions.some(val=>val === item)"/>
                  <p class="w-full text-xs capitalize">{{ item }}</p>
                </li>
            </ul>
            <ul v-if="searchText && filteredList" class="w-[90%] flex flex-col gap-3 max-h-[100px] overflow-y-scroll pb-2">
                <li v-for="item,index in filteredList" :key="index"
                class="bg-white w-full flex gap-3 text-sm text-bg-50 font-semibold px-2.5 py-0 cursor-pointer"
                @click="emitOption(item,'')" >
                  <input :type="inputType" :name="type" :value="true" :checked="selectedOptions.some(val=>val === item)"/>
                  <p class="w-full text-xs capitalize">{{ item }}</p>
                </li>
            </ul>
            <!-- <div v-if="fileUploader" class="w-full relative">
                <span class="absolute top-[0.7rem] left-8" v-html="uploadFileIcon"></span>
                <DnDFileUploader inputPlaceholder="Import Font" />
            </div> -->
        </div>

    </div>

    <div v-else  class="z-30">
    <div
      :class="openModal ? 'border !border-[#1c64f2] bg-[#ebf5ff]' : 'bg-gray-200 border hover:bg-gray-100'"
      class="w-[41px] h-[41px] flex items-center justify-center rounded-lg cursor-pointer select-none"
      @click="handleModal"
    >
      <span v-html="openModal ? filterIconActive : filterIconInActive"></span>
    </div>

    <div
      v-if="openModal"
      ref="dropDownModalRef"
      :class="[width ?`${width}`:'w-[9rem]',type === 'edit'?'top-[-300%] left-[100%]':'bottom-0 left-0 !w-full']"
      class="absolute h-[60%] z-20 bg-white shadow-md rounded-lg border border-gray-200 flex flex-col justify-between pt-2"
    >
      <div class="h-[10%] flex items-center justify-between px-4 pb-2 border-b border-gray-200">
        <p class="text-gray-800 text-lg font-medium">Filter</p>
        <span v-html="closeIcon" class="cursor-pointer" @click="handleModal"></span>
      </div>

      <!-- Sidebar Tabs -->
      <div class="h-[72%] flex">
        <div class="w-1/3 border-r border-gray-200 flex flex-col">
          <div

            v-for="tab in props.title"
            :key="tab"
            @click="activeTab = tab"
            :class="[
              'px-4 py-2 cursor-pointer font-medium text-sm',
              activeTab === tab ? 'bg-blue-50 text-blue-600  border-blue-600' : 'hover:bg-gray-100 text-gray-700'
            ]"
          >
            {{ tab }}
          </div>
        </div>

        <div class="w-2/3 px-4 py-2">
          <!-- Common Search bar -->
          <div class="relative mb-2">
            <span v-html="searchIcon" class="absolute top-2.5 left-3"></span>
            <input
              v-model="searchText"
              type="search"
              placeholder="Search"
              class="w-full bg-gray-100 text-sm text-gray-600 py-2 pl-10 pr-2 rounded-md border placeholder:text-left"
            />
          </div>
          {{ console.log("filtered",filteredList) }}
          <!-- Scrollable list -->
           <div v-if="activeTab === 'Project'">
              <ul v-if="!searchText" class="max-h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                  <li
                    v-for="(item, index) in  options.project"
                    :key="index"
                    class="flex items-center gap-2 cursor-pointer"
                    @click="emitOption(item,'project')"
                  >
                    <input
                      :type="inputType"
                      name="project"
                      :value="true"
                      :checked="selectedProjectOptions.some(val=>val === item)"
                    />
                    <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                  </li>
                </ul>
              <ul v-if="searchText && filteredList" class="max-h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  filteredList"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'project')"
                >
                  <input
                    :type="inputType"
                    name="project"
                    :value="true"
                    :checked="selectedProjectOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
           </div>
           <div v-if="activeTab === 'Agent'">
              <ul v-if="!searchText" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  options.agent"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'agent')"
                >
                  <input
                    :type="inputType"
                    name="agent"
                    :value="true"
                    :checked="selectedAgentOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
              <ul v-if="searchText && filteredAgents" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  filteredAgents"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'agent')"
                >
                  <input
                    :type="inputType"
                    name="agent"
                    :value="true"
                    :checked="selectedAgentOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
           </div>
           <div v-if="activeTab === 'Experience'">
              <ul v-if="!searchText" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  options.experience"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'experience')"
                >
                  <input
                    :type="inputType"
                    name="experience"
                    :value="true"
                    :checked="selectedExperienceOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
              <ul v-if="searchText && filteredExperience" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  filteredExperience"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'experience')"
                >
                  <input
                    :type="inputType"
                    name="experience"
                    :value="true"
                    :checked="selectedExperienceOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
           </div>
           <div v-if="activeTab === 'Location'">
              <ul v-if="!searchText" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  options.location"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'location')"
                >
                  <input
                    :type="inputType"
                    name="location"
                    :value="true"
                    :checked="selectedLocationOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
              <ul v-if="searchText && filteredList" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  filteredList"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'location')"
                >
                  <input
                    :type="inputType"
                    name="location"
                    :value="true"
                    :checked="selectedLocationOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
           </div>
           <div v-if="activeTab === 'Role'">
              <ul v-if="!searchText" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  options.role"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'role')"
                >
                  <input
                    :type="inputType"
                    name="role"
                    :value="true"
                    :checked="selectedRoleOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
              <ul v-if="searchText && filteredRole" class="max-h-[180px] h-[180px] overflow-y-auto flex flex-col gap-2 pr-1">
                <li
                  v-for="(item, index) in  filteredRole"
                  :key="index"
                  class="flex items-center gap-2 cursor-pointer"
                  @click="emitOption(item,'role')"
                >
                  <input
                    :type="inputType"
                    name="role"
                    :value="true"
                    :checked="selectedRoleOptions.some(val=>val === item)"
                  />
                  <p class="text-sm font-medium text-gray-800">{{ item }}</p>
                </li>
              </ul>
           </div>

        </div>
      </div>

      <!-- Footer Buttons -->
      <div class="h-[18%] flex justify-end items-center gap-2 px-4 py-2 border-t border-gray-200 bg-gray-50">
        <button
          class="border px-4 py-1 rounded-lg text-gray-700 active:!bg-gray-200"
          @click="handleResetFilters"
        >
          Reset
        </button>
        <!-- <button
          class="bg-[#1c64f2] hover:bg-[#1a56db] text-white px-4 py-1 rounded-lg"
          @click="applyFilters"
        >
          Apply filters
        </button> -->
      </div>
    </div>
    </div>
</template>

<style>
  .placeholder-start::placeholder {
    font-size: 14px;
    color: #6B7280;
    position: absolute;
  }
.scroller::-webkit-scrollbar {
    width: 0.3rem;
    height: 0.2rem;
}
</style>
