<script setup>
import Join<PERSON>ow<PERSON>utton from './JoinNowButton.vue';
import CopyLinkButton from './CopyLinkButton.vue';
import { formatDate, formatTime } from '../../helpers/domhelper';
defineEmits(['toggleMenu']);
defineProps({
  data: Object,
  toggleMenuId: String,
});

function copyurl (url){
  navigator.clipboard.writeText(url)
    .then(() => {
      // Handle success
      // Console.log('Link copied to clipboard:', url);
      // You can also show a success message to the user if needed
    })
    .catch((err) => {
      // Handle error
      console.error('Failed to copy link:', err);
      // You can also show an error message to the user if needed
    });
}
</script>

<template>
    <div class="bg-bg-1000 h-fit dark:bg-bg-default rounded-2xl border border-[#FFFFFF] w-[464px]">
        <div class="flex flex-col gap-4 p-4">
            <div class="flex justify-between">
                <div class="flex flex-col gap-2">
                    <div class="text-txt-400 text-sm">
                        Project-ID
                    </div>
                    <div class="text-txt-50 dark:text-txt-950">
                        {{ data.project_id }}
                    </div>
                </div>
                <div class="flex gap-5 items-center">
                    <!-- <div :class="[ (data.status.toLowerCase() === tableViewStatusTypes[0] && 'bg-green-100 border-lime-600'), (data.status.toLowerCase() === tableViewStatusTypes[1] && ' bg-orange-100 border-orange-500') , (data.status.toLowerCase() === tableViewStatusTypes[2] && ' bg-cyan-50 border-cyan-500'),((data.status.toLowerCase() === tableViewStatusTypes[3] && 'bg-orange-100 border-amber-400 ')),'w-fit h-fit rounded-[60px] text-txt-50  border-[1px] px-4 py-2 capitalize']"> -->
                    <div class="text-txt-50 dark:text-txt-950 w-fit h-fit rounded-[60px] border-[1px] px-4 py-2 capitalize">
                        {{ data.status}}
                    </div>
                    <div class="menu w-fit relative hover:cursor-pointer select-none" @click="$emit('toggleMenu',$event,data._id)">
                        <svg class="button w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path class="fill-bg-50 dark:fill-bg-950" d="M13.5607 2.71595C14.1464 3.30174 14.1464 4.25149 13.5607 4.83727C12.9749 5.42306 12.0251 5.42306 11.4393 4.83727C10.8536 4.25149 10.8536 3.30174 11.4393 2.71595C12.0251 2.13016 12.9749 2.13016 13.5607 2.71595Z"/>
                            <path class="fill-bg-50 dark:fill-bg-950" d="M13.5607 10.716C14.1464 11.3017 14.1464 12.2515 13.5607 12.8373C12.9749 13.4231 12.0251 13.4231 11.4393 12.8373C10.8536 12.2515 10.8536 11.3017 11.4393 10.716C12.0251 10.1302 12.9749 10.1302 13.5607 10.716Z"/>
                            <path class="fill-bg-50 dark:fill-bg-950" d="M13.5607 18.716C14.1464 19.3017 14.1464 20.2515 13.5607 20.8373C12.9749 21.4231 12.0251 21.4231 11.4393 20.8373C10.8536 20.2515 10.8536 19.3017 11.4393 18.716C12.0251 18.1302 12.9749 18.1302 13.5607 18.716Z"/>
                        </svg>
                        <div v-show="toggleMenuId !== null && toggleMenuId == data._id" class="absolute flex flex-col gap-4 w-44 top-8 left-1 p-3 bg-bg-1000 border border-bg-850 dark:bg-bg-50 dark:text-txt-950 rounded-xl">
                            <div class="flex gap-2 items-center">
                                <div class="flex justify-center items-center w-fit h-fit">
                                    <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M21.3113 6.87846L17.1216 2.68971C16.9823 2.55038 16.8169 2.43986 16.6349 2.36446C16.4529 2.28905 16.2578 2.25024 16.0608 2.25024C15.8638 2.25024 15.6687 2.28905 15.4867 2.36446C15.3047 2.43986 15.1393 2.55038 15 2.68971L3.43969 14.25C3.2998 14.3888 3.18889 14.554 3.11341 14.736C3.03792 14.9181 2.99938 15.1133 3.00001 15.3103V19.5C3.00001 19.8978 3.15804 20.2794 3.43935 20.5607C3.72065 20.842 4.10218 21 4.50001 21H20.25C20.4489 21 20.6397 20.921 20.7803 20.7803C20.921 20.6397 21 20.4489 21 20.25C21 20.0511 20.921 19.8603 20.7803 19.7197C20.6397 19.579 20.4489 19.5 20.25 19.5H10.8113L21.3113 9.00002C21.4506 8.86073 21.5611 8.69535 21.6365 8.51334C21.7119 8.33133 21.7507 8.13625 21.7507 7.93924C21.7507 7.74222 21.7119 7.54714 21.6365 7.36513C21.5611 7.18312 21.4506 7.01775 21.3113 6.87846ZM8.68969 19.5H4.50001V15.3103L12.75 7.06033L16.9397 11.25L8.68969 19.5ZM18 10.1897L13.8113 6.00002L16.0613 3.75002L20.25 7.93971L18 10.1897Z" fill="#5B616E"/>
                                    </svg>
                                </div>
                                <div class="font-medium">
                                    Edit
                                </div>
                            </div>
                            <div class="flex gap-2 items-center">
                                <div class="flex justify-center items-center w-fit h-fit">
                                    <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20.25 4.5H16.5V3.75C16.5 3.15326 16.2629 2.58097 15.841 2.15901C15.419 1.73705 14.8467 1.5 14.25 1.5H9.75C9.15326 1.5 8.58097 1.73705 8.15901 2.15901C7.73705 2.58097 7.5 3.15326 7.5 3.75V4.5H3.75C3.55109 4.5 3.36032 4.57902 3.21967 4.71967C3.07902 4.86032 3 5.05109 3 5.25C3 5.44891 3.07902 5.63968 3.21967 5.78033C3.36032 5.92098 3.55109 6 3.75 6H4.5V19.5C4.5 19.8978 4.65804 20.2794 4.93934 20.5607C5.22064 20.842 5.60218 21 6 21H18C18.3978 21 18.7794 20.842 19.0607 20.5607C19.342 20.2794 19.5 19.8978 19.5 19.5V6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5ZM9 3.75C9 3.55109 9.07902 3.36032 9.21967 3.21967C9.36032 3.07902 9.55109 3 9.75 3H14.25C14.4489 3 14.6397 3.07902 14.7803 3.21967C14.921 3.36032 15 3.55109 15 3.75V4.5H9V3.75ZM18 19.5H6V6H18V19.5ZM10.5 9.75V15.75C10.5 15.9489 10.421 16.1397 10.2803 16.2803C10.1397 16.421 9.94891 16.5 9.75 16.5C9.55109 16.5 9.36032 16.421 9.21967 16.2803C9.07902 16.1397 9 15.9489 9 15.75V9.75C9 9.55109 9.07902 9.36032 9.21967 9.21967C9.36032 9.07902 9.55109 9 9.75 9C9.94891 9 10.1397 9.07902 10.2803 9.21967C10.421 9.36032 10.5 9.55109 10.5 9.75ZM15 9.75V15.75C15 15.9489 14.921 16.1397 14.7803 16.2803C14.6397 16.421 14.4489 16.5 14.25 16.5C14.0511 16.5 13.8603 16.421 13.7197 16.2803C13.579 16.1397 13.5 15.9489 13.5 15.75V9.75C13.5 9.55109 13.579 9.36032 13.7197 9.21967C13.8603 9.07902 14.0511 9 14.25 9C14.4489 9 14.6397 9.07902 14.7803 9.21967C14.921 9.36032 15 9.55109 15 9.75Z" fill="#5B616E"/>
                                    </svg>
                                </div>
                                <div class="font-medium">
                                    Delete
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div>
                <div class="flex flex-col gap-2">
                    <div class="text-txt-400 text-sm">
                        Interested Unit
                    </div>
                    <div>
                        <span v-if="data['Interested Unit'] === null || data['Interested Unit'] === 'null' || data['Interested Unit'].length === 0">
                                          -
                        </span>
                        <span v-else class="flex flex-wrap justify-start items-center flex-row gap-3">

                                        <span  v-if="data['Interested Unit'] !== undefined" class="px-4 py-2 text-xs bg-gray-50 dark:bg-bg-150 rounded-[21px] border border-gray-200 text-txt-50 dark:text-txt-950">
                                            Unit {{data['Interested Unit'][0]}}
                                        </span>

                                        <span v-if="data['Interested Unit'] !== undefined" class=" px-4 py-2 text-xs bg-gray-50 dark:bg-bg-150 rounded-[21px] border border-gray-200 text-txt-50 dark:text-txt-950">
                                            Unit {{data['Interested Unit'][1]}}
                                        </span>

                                        <span v-if="data['Interested Unit'].length > 2 && !toggleItems" class="cursor-pointer text-xs bg-gray-50 dark:bg-bg-150 rounded-[50%] flex justify-center items-center flex-shrink-0 w-[36px] h-[36px] border border-gray-200 select-none text-txt-50 dark:text-txt-950" @click="addAndremoveToggleUnitsItem()">
                                              +{{data['Interested Unit'].length - 2}}
                                        </span>

                                        <span v-for="unit,unitsId in data['Interested Unit']" v-show="unitsId !== 0 && unitsId !== 1 && toggleItems"  class=" px-4 py-2 text-xs bg-gray-50 dark:bg-bg-150 rounded-[21px] border border-gray-200 text-txt-50 dark:text-txt-950">
                                                    Unit {{unit}}
                                        </span>

                                        <span v-show="data['Interested Unit'].length > 2 && toggleItems" class="underline cursor-pointer text-txt-default dark:text-txt-950"  @click="addAndremoveToggleUnitsItem()">
                                                    show less
                                        </span>

                                    </span>
                    </div>
                </div>
            </div> -->
            <div class="flex gap-7 items-center">
                <div class="flex flex-col gap-2">
                    <div class="text-txt-400 text-sm">
                        Topic
                    </div>
                    <div class="text-txt-50 dark:text-txt-950">
                        -
                    </div>
                </div>
                <div>
                    <svg width="1" height="21" viewBox="0 0 1 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <line x1="0.5" x2="0.5" y2="21" stroke="#D8DBDF"/>
                    </svg>
                </div>
                <div class="flex flex-col gap-2">
                    <div class="text-txt-400 text-sm">
                        Lead Name
                    </div>
                    <div class="text-txt-50 dark:text-txt-950">
                        -
                    </div>
                </div>
                <div>
                    <svg width="1" height="21" viewBox="0 0 1 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <line x1="0.5" x2="0.5" y2="21" stroke="#D8DBDF"/>
                    </svg>
                </div>
                <div class="flex flex-col gap-2">
                    <div class="text-txt-400 text-sm">
                        Agent
                    </div>
                    <div class="text-txt-50 dark:text-txt-950">
                        -
                    </div>
                </div>
            </div>
        </div>
        <div class="w-full bg-bg-950 py-3 pl-4 pr-4 flex justify-between rounded-bl-2xl rounded-br-2xl mt-auto">
                <div class="flex gap-4">
                    <div class="flex gap-1 items-center">
                        <div class="flex items-center">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10 1.875C8.39303 1.875 6.82214 2.35152 5.486 3.24431C4.14985 4.1371 3.10844 5.40605 2.49348 6.8907C1.87852 8.37535 1.71762 10.009 2.03112 11.5851C2.34463 13.1612 3.11846 14.6089 4.25476 15.7452C5.39106 16.8815 6.8388 17.6554 8.4149 17.9689C9.99099 18.2824 11.6247 18.1215 13.1093 17.5065C14.594 16.8916 15.8629 15.8502 16.7557 14.514C17.6485 13.1779 18.125 11.607 18.125 10C18.1227 7.84581 17.266 5.78051 15.7427 4.25727C14.2195 2.73403 12.1542 1.87727 10 1.875ZM10 16.875C8.64026 16.875 7.31105 16.4718 6.18046 15.7164C5.04987 14.9609 4.16868 13.8872 3.64833 12.6309C3.12798 11.3747 2.99183 9.99237 3.2571 8.65875C3.52238 7.32513 4.17716 6.10013 5.13864 5.13864C6.10013 4.17716 7.32514 3.52237 8.65876 3.2571C9.99238 2.99183 11.3747 3.12798 12.631 3.64833C13.8872 4.16868 14.9609 5.04987 15.7164 6.18045C16.4718 7.31104 16.875 8.64025 16.875 10C16.8729 11.8227 16.1479 13.5702 14.8591 14.8591C13.5702 16.1479 11.8227 16.8729 10 16.875ZM15 10C15 10.1658 14.9342 10.3247 14.8169 10.4419C14.6997 10.5592 14.5408 10.625 14.375 10.625H10C9.83424 10.625 9.67527 10.5592 9.55806 10.4419C9.44085 10.3247 9.375 10.1658 9.375 10V5.625C9.375 5.45924 9.44085 5.30027 9.55806 5.18306C9.67527 5.06585 9.83424 5 10 5C10.1658 5 10.3247 5.06585 10.4419 5.18306C10.5592 5.30027 10.625 5.45924 10.625 5.625V9.375H14.375C14.5408 9.375 14.6997 9.44085 14.8169 9.55806C14.9342 9.67527 15 9.83424 15 10Z" fill="#5B616E"/>
                            </svg>
                        </div>
                        <div>
                            {{ formatTime(data['start']) }}
                        </div>
                    </div>
                    <div class="flex gap-1 items-center">
                        <div class="flex items-center">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2.57715 7.83691H17.4304" stroke="#5B616E" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M13.7021 11.0916H13.7099" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M10.0039 11.0916H10.0116" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M6.29785 11.0916H6.30557" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M13.7021 14.3303H13.7099" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M10.0039 14.3303H10.0116" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M6.29785 14.3303H6.30557" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M13.3701 1.66675V4.40906" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M6.6377 1.66675V4.40906" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M13.5319 2.98267H6.4758C4.02856 2.98267 2.5 4.34594 2.5 6.85185V14.3932C2.5 16.9385 4.02856 18.3333 6.4758 18.3333H13.5242C15.9791 18.3333 17.5 16.9622 17.5 14.4563V6.85185C17.5077 4.34594 15.9868 2.98267 13.5319 2.98267Z" stroke="#5B616E" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div>
                            {{ formatDate(data['start']) }}
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                        <JoinNowButton/>
                        <CopyLinkButton @copy-url="copyurl"  :url="data.invite_link"/>
                </div>
        </div>
    </div>
</template>

<style scoped>

</style>
