<script setup>
import Spinner from '../../common/Spinner.vue';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { landmarkSchema } from '../../../validationSchema/landmark';
import { getCookie } from '../../../helpers/domhelper';
import { categoryItems } from '../../../helpers/constants';
import { createLandmark } from '../../../api/projects/landmarks/index';
import { useRoute, useRouter } from 'vue-router';
import { ref } from 'vue';
import Modal from '../../common/Modal/Modal.vue';
const router = useRouter();

const loader = ref(false);
const route = useRoute();
const projectId = ref(route.params.project_id);

const handleCreateProjectLandmark = (formData) => {
  loader.value = true;
  createLandmark(formData)
    .then(() => {
      document.dispatchEvent(new Event('refreshProjectLandmark'));
      router.go(-1);
    })
    .catch((err) => {
      console.log('output->err create landmark : ', err);
    })
    .finally(() => {
      loader.value = false;
    });
};

const handleSubmitForm = (values) => {
  const organization = getCookie('organization');
  if (organization) {
    const formData = new FormData();

    formData.append('organization_id', organization);
    formData.append('thumbnail', values.thumbnail);
    values.icon ?formData.append('icon', values.icon):{};
    formData.append('name', values.landmarkName);
    values.description ? formData.append('description', values.description):{};
    formData.append('category', values.landmarkCategory);
    values.distance && formData.append('distance', values.distance);
    values.walkTiming && formData.append('walk_timing', values.walkTiming);
    values.transitTiming && formData.append('transit_timing', values.transitTiming);
    values.carTiming && formData.append('car_timing', values.carTiming);
    formData.append('project_id', projectId.value);
    values.latitude ? formData.append('lat', values.latitude) : '';
    values.longitude ? formData.append('long', values.longitude) : '';

    handleCreateProjectLandmark(formData);
  }
  // Console.log(`output->landmarkvalues`,values)
};
</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary sm:max-w-3xl">
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Create Project Landmark</h1>
          <p class="modal-subheading-primary">
            Fill details below to create Project landmark.
          </p>
        </div>
        <Form
          @submit="handleSubmitForm"
          :validation-schema="landmarkSchema"
          class="flex flex-col justify-center"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label for="landmarkName" class="label-primary"> Name</label>
              <div class="mt-2">
                <Field
                  type="text"
                  name="landmarkName"
                  id="landmarkName"
                  class="input-primary"
                  placeholder="Enter Landmark Name"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="landmarkName"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="landmarkCategory" class="label-primary">
                Category</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="landmarkCategory"
                  id="landmarkCategory"
                  class="select-primary"
                  placeholder="Category"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="!categoryItems">
                    no category!
                  </option>
                  <option
                    v-else
                    :value="option"
                    v-for="(option, index) in categoryItems"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="landmarkCategory"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="thumbnail" class="label-primary">
                Thumbnail Image</label
              >
                <Field
                  type="file"
                  name="thumbnail"
                  id="thumbnail"
                  autocomplete="thumbnail"
                  class="input-primary"
                  placeholder="Upload Thumbnail"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="thumbnail"
                />
            </div>
            <div class="col-span-auto">
              <label for="icon" class="label-primary">
                icon</label
              >
                <Field
                  type="file"
                  name="icon"
                  id="icon"
                  autocomplete="icon"
                  class="input-primary"
                  placeholder="Upload Thumbnail"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="icon"
                />
            </div>
            <div class="col-span-auto">
                <label for="description" class="label-primary"
                  >Description</label
                >
                <Field
                  name="description"
                  class="input-primary"
                  placeholder="Write Description here..."
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="description"
                />
              </div>
            <div class="col-span-auto">
              <label for="distance" class="label-primary"> Distance (km)</label>
              <div class="mt-2">
                <Field
                  type="number"
                  name="distance"
                  id="distance"
                  autocomplete="distance"
                  class="h-11 input-primary"
                  placeholder="Distance"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="distance"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="walkTiming" class="label-primary"> Walk Timing (min)</label>
              <div class="mt-2">
                <Field
                  type="number"
                  name="walkTiming"
                  id="walkTiming"
                  autocomplete="walkTiming"
                  class="h-11 input-primary"
                  placeholder="Walk Timing"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="walkTiming"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="transitTiming" class="label-primary">
                Transit Timing (min)</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  name="transitTiming"
                  id="transitTiming"
                  autocomplete="transitTiming"
                  class="h-11 input-primary"
                  placeholder="Transit Timing"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="transitTiming"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="carTiming" class="label-primary"> Car Timing (min)</label>
              <div class="mt-2">
                <Field
                  type="number"
                  name="carTiming"
                  id="carTiming"
                  autocomplete="carTiming"
                  class="h-11 input-primary"
                  placeholder="Car Timing"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="carTiming"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="latitude" class="label-primary"> Latitude</label>
              <div class="mt-2">
                <Field
                  type="number"
                  name="latitude"
                  id="latitude"
                  autocomplete="latitude"
                  class="h-11 input-primary"
                  placeholder="latitude"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="latitude"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="longitude" class="label-primary"> Longitude</label>
              <div class="mt-2">
                <Field
                  type="number"
                  name="longitude"
                  id="longitude"
                  autocomplete="longitude"
                  class="h-11 input-primary"
                  placeholder="longitude"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="longitude"
                />
              </div>
            </div>
          </div>
          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="router.go(-1)"
            >
              Cancel
            </button>
            <button type="submit" class="proceed-btn-primary">
              Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
