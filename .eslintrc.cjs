/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')

module.exports = {
  root: true,
  'extends': [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    "prettier",
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting',
    
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  rules: {
        'vue/multi-word-component-names': 'off',
        'no-console': 'off',
        'no-unused-vars': 'off',
        "@typescript-eslint/no-unused-vars": ["error"],
        'indent': ['error', 2, { 'SwitchCase': 1 }],
        'semi': ['error', 'always'],
        'comma-dangle': ['error', 'always-multiline'],
        'eol-last': ['error', 'always'],
        'no-multiple-empty-lines': ['error', { 'max': 1, 'maxEOF': 0 }],
        'arrow-parens': ['error', 'always'],
        // 'camelcase': ['error', { 'properties': 'always' }],
        // 'no-underscore-dangle': 'error',
        // 'complexity': ['error', 10],
        // 'max-depth': ['error', 4],
        // 'max-len': ['error', { 'code': 120 }],
        'space-before-function-paren': ['error', 'always'],
        'key-spacing': ['error', { 'beforeColon': false }],
        'keyword-spacing': ['error', { 'before': true }],
        'brace-style': 'error',
        'no-trailing-spaces': 'error',
        'no-debugger': 'warn',
        'max-nested-callbacks': [2, 5],
        'prefer-const': ['error'],
        'no-use-before-define': ['error'],
        'constructor-super': ['error'],
        'array-callback-return': ['error'],
        'for-direction': ['error'],
        'no-cond-assign': ['error'],
        'no-const-assign': ['error'],
        'no-constant-condition': ['error'],
        'no-dupe-else-if': ['error'],
        'no-dupe-keys': ['error'],
        'no-duplicate-case': ['error'],
        'no-irregular-whitespace': ['error'],
        'no-unexpected-multiline': ['error'],
        'valid-typeof': ['error'],
        'block-scoped-var': ['error'],
        // 'capitalized-comments': ['error'],
        //'consistent-return': ['error'],
        'consistent-this': ['error'],
        'curly': ['error'],
        'default-param-last': ['error'],
        'dot-notation': ['error'],
        'eqeqeq': ['error'],
        'no-confusing-arrow': ['error'],
        'no-else-return': ['error'],
        'no-empty': ['error'],
        'no-eq-null': ['error'],
        'no-extra-semi': ['error'],
        'no-global-assign': ['error'],
        'no-implicit-globals': ['error'],
        'no-mixed-operators': ['error'],
        'no-multi-str': ['error'],
        'spaced-comment': ['error'],
        'vars-on-top': ['error'],
        'arrow-spacing': ['error'],
        'block-spacing': ['error'],
        'comma-spacing': ['error'],
        'comma-style': ['error'],
        'computed-property-spacing': ['error'],
        'switch-colon-spacing': ['error'],
        "vue/no-v-text-v-html-on-component": 0,
        "vue/require-component-is":0,
        "vue/attribute-hyphenation":0
    },
}
