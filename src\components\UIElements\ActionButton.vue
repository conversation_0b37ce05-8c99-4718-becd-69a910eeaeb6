<script setup>
import { defineProps, defineEmits } from 'vue';

defineProps({
  array: Array,
  active: String,
});

defineEmits(['click']);
</script>

<template>
    <div
        class="flex sm:items-center sm:gap-9 w-full sm:w-auto bg-gray-100 dark:bg-bg-200 sm:bg-transparent sm:dark:bg-transparent rounded-xl">
        <button v-for="(option, index) in array" :key="index" @click="$emit('click', option)" :class="[
            'flex-1 sm:flex-initial py-2 px-4 sm:px-0 sm:pb-2 transition-colors duration-200 ease-in-out capitalize rounded-lg sm:rounded-none',
            'font-roboto font-medium text-center',
            'text-[13px] sm:text-xl leading-[20px] sm:leading-normal tracking-[-0.08px] sm:tracking-normal',
            'relative',
            active === option
                ? 'bg-white dark:bg-bg-150 sm:bg-transparent sm:dark:bg-transparent text-black dark:text-white shadow-sm sm:shadow-none sm:border-b-[3px] sm:border-current'
                : 'text-gray-500 dark:text-gray-400'
        ]">
            {{ option }}
        </button>
    </div>
</template>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@500&display=swap');

.font-roboto {
    font-family: 'Roboto', sans-serif;
}
</style>
