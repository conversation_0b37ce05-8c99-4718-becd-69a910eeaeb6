<script setup>
import { computed } from 'vue';

const props = defineProps({
  eventData: {
    type: Array,
    default: () => [],
  },
  eventName: {
    type: String,
    required: true,
  },
});

const columns = computed(() => {
  if (props.eventData.length === 0) {
    return [];
  }
  return Object.keys(props.eventData[0]);
});

// For debugging
console.log(`Received event data for ${props.eventName}:`, props.eventData);
</script>

<template>
  <div class="h-64 overflow-auto p-2">
    <table v-if="eventData.length > 0" class="w-full text-xs text-left text-gray-500">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 sticky top-0">
        <tr>
          <th v-for="column in columns" :key="column" class="px-2 py-2">{{ column }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in eventData" :key="index" class="bg-white border-b">
          <td v-for="column in columns" :key="column" class="px-2 py-1">
            {{ item[column] }}
          </td>
        </tr>
      </tbody>
    </table>
    <div v-else class="h-full flex items-center justify-center text-gray-500">
      No data available for {{ eventName }}.
    </div>
  </div>
</template>
