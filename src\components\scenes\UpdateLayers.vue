<script setup>
import * as slider from '@zag-js/slider';
import { normalizeProps, useMachine } from '@zag-js/vue';
import Spinner from '../common/Spinner.vue';
import { useRoute } from 'vue-router';
import Multiselect from 'vue-multiselect';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { onMounted, ref, watch, computed } from 'vue';
import projectSchema from '../../validationSchema/scene/updateProjectLayerSchema';
import masterFormSchema from '../../validationSchema/scene/updateMasterLayerSchema';
import { deepzoomUpdateLayersValid } from '../../validationSchema/scene/updateProjectLayerSchema';
import { deepzoomUpdateLayersValid as masterDeepDeepzoomUpdateLayersValid } from '../../validationSchema/scene/updateMasterLayerSchema';
import { Org_Store } from '../../store/organization';
import { ProjectStore } from '../../store/project';
import { updateLayers, updateLayersVideoTag } from '../../api/projects/scene/svg/index';
import { updateLayers as updateMasterLayers, updateLayersVideoTag as updateMasterLayersVideoTag } from '../../api/masterScene/svg/index';
import { isMasterScenePath } from '../../helpers/helpers';
import { unitplanTypeList } from '@/helpers/constants';

const emit = defineEmits(['closeModal', 'updateResize', 'updateScale']);
const route = useRoute();
const props = defineProps({
  landmarks: Object,
  zoomLevel: String,
  svgData: Object,
  scenes: Object,
  projects: Object,
  projectId: String,
  defaultPostion: Object,
  categoryList: Array,
});
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const formSchema = isMasterScene.value ? masterFormSchema : projectSchema;
console.log("isMasterScene", isMasterScene.value);
console.log("first data", formSchema);

const loader = ref(false);
const svgId = ref(route.query.svgId);
const sceneId = ref(route.params.scene_id);
const layerId = ref(route.query.layerId);
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const currentZooomLevel = ref(0);
const previousData = ref({});
const name = ref(null);
const xposition = ref(null);
const yposition = ref(null);
const reSize = ref(null);
const showLabel = ref(false);
const groupedUnitsName = ref('');
const bedrooms = ref('');
const placement = ref(null);
const listOfPlacements = ref(['CENTER', 'TOP_LEFT', 'TOP', 'TOP_RIGHT', 'RIGHT', 'BOTTOM_RIGHT', 'BOTTOM', 'BOTTOM_LEFT', 'LEFT']);
const scaleX = ref(); // X current value from the slider states
const scaleY = ref(); // Y current value from the slider states
const scaleXRef = ref(null);
const [scaleXstate, scaleXsend] = useMachine(
  slider.machine({
    id: 'scalex',
    name: 'scalex',
    value: [1], // Default value
    min: 1,
    max: 100,
    step: 0.1,
    onValueChangeEnd (details) {
      console.log(scaleY.value[0]);
      emit('updateScale', {x: details.value[0], y: Array.isArray(scaleY.value) ? scaleY.value[0] : Number(scaleY.value)});
    },
  }),
);

const scaleYRef = ref(null);
const [scaleYstate, scaleYsend] = useMachine(
  slider.machine({
    id: 'scaley',
    name: 'scaley',
    value: [1], // Default value
    min: 1,
    max: 100,
    step: 0.1,
    onValueChangeEnd (details) {
      console.log(scaleX.value);
      emit('updateScale', {x: Array.isArray(scaleX.value) ? scaleX.value[0] : Number(scaleX.value), y: details.value[0]});
    },
  }),
);
const minandmaxRef = ref(null);
const [minandmaxState, minandmaxsend] = useMachine(
  slider.machine({
    id: 'minandmax',
    name: 'minandmax',
    value: [0, 100], // Default value
    min: 0,
    max: 100,
  }),
);
const zIndexRef = ref(null);
const [zindexstate, zindexsend] = useMachine(
  slider.machine({
    id: 'zindex',
    name: 'zindex',
    value: [1], // Default value
    min: 1,
    max: 5,
  }),
);
/* Video-tag */
const fileKey = ref(0);
const showOutputVideoTag = ref(false);
const valueInVideoTag = ref();
const showCustomError = ref(false);
const setToNull = ref(false);
const finishedUploading = ref(false);
// const videoTagUploadRef = ref(null);
/* ShowLabel */
const showGroupedUnitsName = ref(false);
const project_id  = route.params.project_id;
const videoUrl = ref(null);

/* Methods */

// Computed scaleX method
const scaleXComputed = computed(() => slider.connect(scaleXstate.value, scaleXsend, normalizeProps));
// Computed scaleY method
const scaleYComputed = computed(() => slider.connect(scaleYstate.value, scaleYsend, normalizeProps));
// Computed range slider method (min and Max)
const minandmaxComputed = computed(() => slider.connect(minandmaxState.value, minandmaxsend, normalizeProps));
// Computed range slider method (zIndex)
const zindexComputed = computed(() => slider.connect(zindexstate.value, zindexsend, normalizeProps));
const minandmaxZoomLevel = computed(() => minandmaxState.value.context.value);
const zIndexRatio = computed(() => zindexstate.value.context.value);

// ScaleX
watch(() => scaleXstate.value.context.value, (val) => {
  scaleX.value = val;
  console.log(scaleX.value);
}, {immediate: true});

// ScaleY
watch(() => scaleYstate.value.context.value, (val) => {
  scaleY.value = val;
  console.log(scaleY.value);
}, {immediate: true});

// Find the route new
const findTypeRoute = () => {
  const routeList = [];
  Object.values(props.svgData).forEach((svg) => {

    Object.values(svg.layers).forEach((layer) => {
      if (layer.type === 'route') {
        routeList.push({ _id: layer.layer_id, name: layer.name });
      }
    });
  });
  return routeList;
};

const handleChange = (val) => {
  if (val === 'project') {
    if (formSchema.project_id.options.length === 0) {
      formSchema.project_id.options = Object.values(newOrganizationStore.projects).map((project) => ({ '_id': project._id, 'name': project.name }));
    }
  }
  if (val === 'scene' || val === 'pin' || val === 'building' || val === 'floor' || val==='community') {
    if (formSchema.scene_id.options.length === 0) {
      formSchema.scene_id.options = Object.values(props.scenes).map(({ sceneData }) => ({ '_id': sceneData._id, 'name': sceneData.name }));
    }
  }

  if (!isMasterScene.value){
    if (val === 'landmark') {
      if (formSchema.landmark_id.options.length === 0) {
        formSchema.landmark_id.options = Object.values(props.landmarks).map((landmark) => ({ '_id': landmark._id, 'name': landmark.name }));
      // Console.log(`output->landmark`,props.landmarks)
      }
      // Finding layers with type route
      formSchema.route_id.options = findTypeRoute();

    }
    if (val === 'building') {

      formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
    }
    if (val === 'community') {
      console.log(projectStore.communities);

      formSchema.community_id.options = Object.values(projectStore.communities).map((community) => ({ '_id': community._id, 'name': community.name }));
    }
    if (val === 'floor') {
      if (formSchema.building_id.options.length === 0) {
        console.log("Building options are empty, fetching buildings", projectStore.buildings);
        formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
      // Console.log(`output->landmark`,props.landmarks)
      }
      watch(formSchema.building_id.ref, (val) => {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[val].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      });
      if (formSchema.building_id.ref.value) {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[formSchema.building_id.ref.value].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      }

    }
    if (val === 'units') {
      if (formSchema.units.options.length === 0) {
        console.log(projectStore.units);
        console.log(projectStore.buildings);
        formSchema.units.options = Object.values(projectStore.units).map((unit) => ({ '_id': unit._id, 'name': (unit.name + (projectStore.buildings[unit.building_id]?projectStore.buildings[unit.building_id].name:'')) }));
      // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenity') {
      if (formSchema.amenity_id.options.length === 0) {
        formSchema.amenity_id.options = Object.values(projectStore.amenities).map((amenity) => ({ '_id': amenity._id, 'name': amenity.name }));
      // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenitycategory') {
      if (formSchema.amenity_category.options.length === 0) {
        formSchema.amenity_category.options = Object.values(props.categoryList).map((elem) => ({ '_id': elem.name, 'name': elem.name }));
      // Console.log(`output->landmark`,props.landmarks)
      }
    }
    console.log("svg type", val);
    if (val === 'grouped_units'){
      if (formSchema.showLabel.ref.value || showLabel.value){
        showGroupedUnitsName.value = true;
      } else {
        showGroupedUnitsName.value = false;
      }
    } else {
      showGroupedUnitsName.value = false;
    }
  }

};

const handleInitialValues = () => {
  layerId.value = route.query.layerId;

  console.log(props.svgData);

  if (props.svgData[svgId.value]?.layers[layerId.value]){
    const layer = props.svgData[svgId.value].layers[layerId.value];

    previousData.value = layer;
    xposition.value =  layer.x;
    yposition.value =  layer.y;

    if (layer.name !== undefined){
      name.value = layer.name;
    }

    if (layer.minZoomLevel !== undefined && layer.maxZoomLevel !== undefined){
      minandmaxsend({ type: 'SET_VALUE', value: [layer.minZoomLevel, layer.maxZoomLevel] }); // Update the min and max value
    }

    if (layer.zIndex !== undefined){
      zindexsend({ type: 'SET_VALUE', value: [layer.zIndex] }); // Update the min value
    }

    if (layer.scale !== undefined){
      scaleXsend({ type: 'SET_VALUE', value: [layer.scale.x] });  // Scale x
      scaleYsend({ type: 'SET_VALUE', value: [layer.scale.y] });  // Scale y
    }

    if (layer.placement !== undefined && layer.placement !== null){
      placement.value = layer.placement;
    } else {
      placement.value = 'BOTTOM';
    }

    if (layer.reSize !== undefined){
      reSize.value = layer.reSize;
    } else {
      reSize.value = false;
    }
    if (layer.showLabel !== undefined){
      showLabel.value = layer.showLabel;
    } else {
      showLabel.value = false;
    }
    if (layer.showLabel){
      showGroupedUnitsName.value = true;
    } else {
      showGroupedUnitsName.value = false;
    }

    if (layer.group_name !== ''){
      groupedUnitsName.value = layer.group_name;
    } else {
      groupedUnitsName.value = '';
    }

    if (layer.bedrooms !== null){
      bedrooms.value = layer.bedrooms;
    } else {
      bedrooms.value = null;
    }

    formSchema.type.ref.value = layer.type ? layer.type : props.svgData[svgId.value].type;
    formSchema.project_id.ref.value = layer.project_id ? layer.project_id : null;
    formSchema.scene_id.ref.value = layer.scene_id ? layer.scene_id : null;
    formSchema.image_id.ref.value = layer.image_id ? layer.image_id : null;

    if (!isMasterScene.value){

      formSchema.landmark_id.ref.value = layer.landmark?.landmark_id ? layer.landmark.landmark_id : null;
      formSchema.route_id.ref.value = layer.landmark?.route_id ? layer.landmark.route_id : null;
      formSchema.building_id.ref.value = layer.building_id ? layer.building_id : props.svgData[svgId.value].building_id? props.svgData[svgId.value].building_id : null;
      formSchema.community_id.ref.value = layer.community_id ? layer.community_id : null;
      formSchema.floor_id.ref.value = layer.floor_id ? layer.floor_id : null;
      formSchema.amenity_id.ref.value = layer.amenity_id ? layer.amenity_id : null;
      formSchema.amenity_category.ref.value = layer.amenity_category ? layer.amenity_category : null;
      formSchema.units.ref.value = layer.units ? layer.units.map((elem) => {
        return { '_id': elem, 'name': projectStore.units[elem].name };
      }) : null;

    }
    formSchema.title.ref.value = layer.title ? layer.title : null;
    formSchema.category.ref.value = layer.category ? layer.category : null;
    formSchema.outputVideoTag.outputRef.value = layer.video_tag ? layer.video_tag : undefined;
    valueInVideoTag.value = formSchema.outputVideoTag.outputRef.value ? formSchema.outputVideoTag.outputRef.value : false;
    videoUrl.value = formSchema.outputVideoTag.outputRef.value ? formSchema.outputVideoTag.outputRef.value : false;
    if (formSchema.outputVideoTag.outputRef.value !== undefined){
      console.log("Video Tag is Present in ref");
      console.log("value inside outputref", formSchema.outputVideoTag.outputRef.value);
      console.log("value inside ref", valueInVideoTag.value);
      showOutputVideoTag.value = true;
    } else {
      console.log("No Video Tag in ref");
      showOutputVideoTag.value = false;
    }
    if (formSchema.video_tag.ref.value){
      console.log("Need to clear file button");
      formSchema.video_tag.ref.value = null;
      fileKey.value += 1;   // reset the file upload button
    } else {
      console.log("No need oto clear file button");
    }
    setToNull.value = false;
    finishedUploading.value = false;

  }

  /*   If(props.defaultPostion){
        // position initial values
  } */
  handleChange(formSchema.type.ref.value);

};
const  extractFileName=(url) => {
  // if (!url) {
  //   console.error("URL is undefined or null");
  //   return null;
  // }

  // Match only the filename portion, immediately before the final underscore and ".mp4"
  const match = url.match(/([^/]+?)_\d+\.mp4/);

  if (match) {
    const decodedFileName = decodeURIComponent(match[1]); // Decode %20 to spaces
    // console.log("Extracted File Name:", decodedFileName);
    const fileName = decodedFileName.match(/[^/]+$/)[0];
    return `${fileName}.mp4`;
  }

  console.log("No matching file name found in the URL");
  return null;
};
const comparePreviousValues = (compareObj, sourceObj) => {
  const newObject = {}; // Return Object
  const newSourceObj = {...sourceObj};
  const comparisonObj = {...compareObj};

  delete newSourceObj.outputVideoTag;
  delete comparisonObj.outputVideoTag;
  delete comparisonObj.layer_id; // Remove the layer_id
  delete comparisonObj.g; // Remove the g tag
  delete comparisonObj.width; // Remove the width
  delete comparisonObj.height; // Remove the width
  const others = {}; // Frame the type and related values before comparison
  Object.keys(comparisonObj).forEach((key) => {
    if (key !== 'reSize' && key !== 'showLabel' && key !== 'group_name' && key !== 'bedrooms' && key !== 'outputVideoTag' && key !== 'video_tag' && key !== 'placement' && key !== 'zIndex' && key !== 'x' && key !== 'y' && key !== 'maxZoomLevel' && key !== 'minZoomLevel' && key !== 'name' && key !== 'type' && key !== 'scale' ){
      if (comparisonObj[key] !== null && typeof comparisonObj[key] === 'object'){
        // Extract the object and get the keys and values
        Object.keys(comparisonObj[key]).forEach((secondKey) => {
          others[secondKey] = comparisonObj[key][secondKey]; // Append
        });
      } else {
        others[key] = comparisonObj[key]; // Append
      }
    }
  });
  const comparisonObject = {
    ...( comparisonObj.name !== undefined && {name: comparisonObj.name}),
    ...( comparisonObj.video_tag !== undefined && {video_tag: comparisonObj.video_tag ? extractFileName(comparisonObj.video_tag):null}),
    ...( comparisonObj.reSize !== undefined && {reSize: comparisonObj.reSize}),
    ...( comparisonObj.showLabel !== undefined && {showLabel: comparisonObj.showLabel}),
    ...( comparisonObj.group_name !== undefined && {group_name: comparisonObj.group_name}),
    ...( comparisonObj.bedrooms !== undefined && {bedrooms: comparisonObj.bedrooms}),
    ...( comparisonObj.placement !== undefined && {placement: comparisonObj.placement}),
    ...( comparisonObj.zIndex !== undefined && {zIndex: comparisonObj.zIndex}),
    ...( comparisonObj.x !== undefined && {x: comparisonObj.x}),
    ...( comparisonObj.y !== undefined && {y: comparisonObj.y}),
    ...( comparisonObj.maxZoomLevel !== undefined && {maxZoomLevel: comparisonObj.maxZoomLevel}),
    ...( comparisonObj.minZoomLevel !== undefined && {minZoomLevel: comparisonObj.minZoomLevel}),
    ...( comparisonObj.scale !== undefined && {scalex: comparisonObj.scale.x, scaley: comparisonObj.scale.y}),
    others: {
      ...others,
      ...(comparisonObj.type !== undefined && {type: comparisonObj.type}),
    },
  };
  console.log("newSource", newSourceObj);
  console.log("comparisonObj", comparisonObj);
  if (newSourceObj.video_tag){
    newSourceObj.video_tag = newSourceObj.video_tag.name;
    console.log("new video", newSourceObj.video_tag);
  } else {
    delete comparisonObj.video_tag; // Do not compare video_tag if not updating
  }
  // console.log("comparisonObject",comparisonObject);

  // Compare
  Object.keys(newSourceObj).forEach((key) => {
    if (key !== 'others'){
      if (comparisonObject[key] !== newSourceObj[key]){
        if (key === 'scalex'  || key === 'scaley'){
          if (!newObject.scale){
            newObject.scale = {};
          }
          newObject.scale[key === 'scalex' ? 'x' : 'y'] = newSourceObj[key];
        } else {
          newObject[key] = newSourceObj[key];
        }
      }
    } else {
      // Others (type & its related)
      if (newSourceObj[key].type) {
        if (comparisonObject[key].type === newSourceObj[key].type) {
          let anyChange = false;
          Object.keys(newSourceObj[key]).forEach((secondKey) => {
            if (comparisonObject[key][secondKey] !== newSourceObj[key][secondKey]) {
              newObject[secondKey] = newSourceObj[key][secondKey];
              anyChange = true;
            }
          });
          if (anyChange) {
            newObject.type = newSourceObj[key].type;
          }
        } else {
          // Type is not same or undefined
          Object.keys(newSourceObj[key]).forEach((querykey) => {
            newObject[querykey] = newSourceObj[key][querykey];
          });
          newObject.type = newSourceObj[key].type;
        }
      }
    }
  });
  return newObject;
};

const frameParms = (values) => {
  console.log(values);
  const formObject = {}; // Create a new form object with empty values
  if (!isMasterScene.value) {
    formObject.project_id = props.projectId;
  }
  formObject.svg_id = svgId.value;
  formObject.layer_id = layerId.value;
  formObject.query = values;
  return formObject;
};

watch(() => route.query.svgId, () => {
  svgId.value = route.query.svgId;
});

watch(() => route.query.layerId, () => {
  handleInitialValues();
});
watch(() => {
  const sceneData = !isMasterScene.value ? projectStore.scenes[sceneId.value].svgData : newOrganizationStore.masterScenes[sceneId.value].svgData;
  return sceneData;
}, () => {
  handleInitialValues();
});
watch(formSchema.type.ref, (val) => {
  handleChange(val);
});

watch([reSize, placement], (arry, previous) => {
  console.log(previous);
  if (previous[0] !== null){
    console.log(previous);
    console.log(arry);
    emit('updateResize', {reSize: arry[0], placement: !arry[0] ? arry[1] : null});
  }

}, {deep: true});

// Watch position
watch(() => props.defaultPostion, () => {
  console.log(props.defaultPostion);
  if (Object.keys(props.defaultPostion).length > 0){
    xposition.value = props.defaultPostion.px;
    yposition.value = props.defaultPostion.py;
  }

}, {deep: true});

// Watch zoom levels
watch(() => props.zoomLevel, () => {
  console.log(props.zoomLevel, "111111111111111111");
  if (props.zoomLevel !== null){
    currentZooomLevel.value = props.zoomLevel;
  }
}, {deep: true});
const areValuesEqual = (obj1, obj2) => {
  if (!obj1){
    return true;
  }
  if (!obj2){
    return true;
  }
  const keys1 = Object.keys(obj1).sort();
  const keys2 = Object.keys(obj2).sort();
  if (keys1.length !== keys2.length) {
    return false;
  }
  for (let i = 0; i < keys1.length; i++) {
    // console.log(keys1[i],"===",keys2[i]);
    if (keys1[i] !== keys2[i]) {
      return false;
    }
  }
  // Compare values by sorted keys
  for (let i = 0; i < keys1.length; i++) {
    const key = keys1[i];
    // console.log(obj1[key],"===",obj2[key]);
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }
  return true;
};
// Preventing from uploading the same file again
watch(
  () => formSchema.video_tag.ref.value,
  (newFile) => {
    if (newFile instanceof File) {
      const layer = isMasterScene.value ? newOrganizationStore.masterScenes[sceneId.value].svgData[svgId.value].layers[layerId.value]  : projectStore.scenes[sceneId.value].svgData[svgId.value].layers[layerId.value];
      const { video_tag } = layer;
      if (video_tag){
        const OldVideoTag = extractFileName(video_tag);
        console.log("OldVideoTag", OldVideoTag);
        console.log("NewVideoTag", newFile.name);
        console.log("Are equal", areValuesEqual(OldVideoTag, newFile.name));

        if (areValuesEqual(OldVideoTag, newFile.name)) {
        // formSchema.videoTag.ref.value = null;
          showCustomError.value = true;
        } else {
          showCustomError.value = false;
        }
      }

    }
  },
);
const  updateLayersFiles = (video_tag) => {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('layer_id', route.query.layerId);
    if (!isMasterScene.value){
      formData.append('project_id', route.params.project_id);
    }
    formData.append('scene_id', sceneId.value);
    formData.append('svg_id', route.query.svgId);
    formData.append('video_tag', video_tag);

    const apiCall = isMasterScene.value ? updateMasterLayersVideoTag : updateLayersVideoTag;
    apiCall(formData)
      .then(() => {
        console.log("Video upload successful");
        resolve(true);
      }).catch((err) => {
        console.error('Error in video upload:', err);
        reject(false);
      });
  });
};
// Watch for initial video URL
watch(() => formSchema.outputVideoTag.outputRef.value, (newUrl) => {

  if (newUrl) {
    videoUrl.value = newUrl;
  } else {
    videoUrl.value = null;
  }
}, { immediate: true });

const handleChangeVideo = () => {
  if (videoUrl.value) {
    window.URL.revokeObjectURL(videoUrl.value);
    videoUrl.value = null;

  }
  showCustomError.value = null;
  formSchema.video_tag.ref.value = null;
  formSchema.outputVideoTag.outputRef.value = null;
  setToNull.value = true;
  fileKey.value += 1;
};

const handleVideoUpload = (event) => {
  const file = event.target.files[0];
  if (file) {

    if (videoUrl.value) {
      window.URL.revokeObjectURL(videoUrl.value);
    }
    videoUrl.value = window.URL.createObjectURL(file);
    formSchema.video_tag.ref.value = file;
    setToNull.value = false;
  }
};

const handleSubmit = async (values) => {
  // Get layer data first
  const layer = isMasterScene.value ?
    newOrganizationStore.masterScenes[sceneId.value].svgData[svgId.value].layers[layerId.value] :
    projectStore.scenes[sceneId.value].svgData[svgId.value].layers[layerId.value];

  // Handle video tag value first
  if (formSchema.video_tag.ref.value instanceof File) {
    values.video_tag = formSchema.video_tag.ref.value;
    setToNull.value = false;
  } else if ( setToNull.value) {
    values.video_tag = null;
  }

  if (!isMasterScene.value && values.units && values.units.length !== 0) {
    values.units = values.units.map((elem) => elem._id);
  }

  loader.value = true;
  let refreshScenes = false;

  try {
    // Handle video updates first
    if (values.video_tag instanceof File || (setToNull.value && layer.video_tag !== null && layer.video_tag)) {
      await updateLayersFiles(values.video_tag);
      refreshScenes = true;
    }

    // Handle other fields
    const { zindex, reSize, placement, minandmax, x, y, name, scalex, scaley, outputVideoTag, video_tag, showLabel, group_name, bedrooms, ...others } = values;
    console.log(outputVideoTag, video_tag);
    const sourceObj = {
      ...(name !== null && { name: name }),
      x: x,
      y: y,
      maxZoomLevel: minandmax[1],
      minZoomLevel: minandmax[0],
      zIndex: zindex[0],
      placement: placement ? placement : null,
      reSize: reSize,
      showLabel: showLabel,
      ...(group_name && {group_name}),
      bedrooms: bedrooms,
      scalex: Array.isArray(scalex) ? scalex[0] : Number(scalex),
      scaley: Array.isArray(scaley) ? scaley[0] : Number(scaley),
      ...(others !== null && { others: { ...others } }),
    };

    const comparedValues = comparePreviousValues(previousData.value, sourceObj);
    const fields = Object.fromEntries(
      Object.entries(comparedValues).filter(([key]) => key !== 'video_tag' && key !== 'outputVideoTag'),
    );

    if (Object.keys(fields).length > 0) {
      const frameParamsOutput = frameParms(comparedValues);
      const updatedQuery = Object.fromEntries(
        Object.entries(frameParamsOutput.query).filter(([key]) => key !== 'video_tag'),
      );
      const updatedObject = { ...frameParamsOutput, query: updatedQuery };

      if (isMasterScene.value) {
        await updateMasterLayers(updatedObject);
      } else {
        await updateLayers(updatedObject);
      }
      refreshScenes = true;
    } else {
      refreshScenes = false;
    }

    // Only refresh scene data after all updates are complete
    if (refreshScenes) {
      finishedUploading.value = true;
      if (isMasterScene.value) {
        newOrganizationStore.ForceRefreshMasterScenes();
      } else {
        projectStore.ForceRefreshScenes(project_id);
      }
    }

  } catch (error) {
    console.error("Error during update:", error);
  } finally {
    loader.value = false;
  }
};

/* Hooks */
onMounted(() => {
  handleInitialValues();

});

// Add the new method for handling unit removal
const handleUnitRemoval = (field, index) => {
  const newValue = [...formSchema[field].ref.value];
  newValue.splice(index, 1);
  formSchema[field].ref.value = newValue;
};

watch(showLabel, (newVal) => {
  if (newVal && formSchema.type.ref.value === 'grouped_units'){
    showGroupedUnitsName.value = true;
  } else {
    showGroupedUnitsName.value = false;
  }
});
</script>

<template>
  <div
    class="h-full">
    <div class="overflow-y-scroll h-full">

      <Form @submit="handleSubmit"
        :validation-schema="isMasterScene ? masterDeepDeepzoomUpdateLayersValid : deepzoomUpdateLayersValid">
        <div >
          <div class="flex flex-col gap-2 text-black">

            <!-- name -->

            <div class="">
                      <label
                    class="mb-2 text-xs text-gray-500">
                     Name
                  </label>
                  <Field
                    v-model="name"
                    as="input"
                    type="text"
                    id="name"
                    class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-left placeholder:font-semibold"
                    name="name">
                  </Field>

                  <ErrorMessage
                    name="name"
                    as="p"
                    class="text-sm text-rose-500 mt-1" />
            </div>
            <!-- Type -->
            <div v-if="formSchema.type !== null"
              class="">
              <label :for="formSchema"
                class="">{{
                  formSchema.type.label }}</label>
              <Field v-model="formSchema.type.ref.value"
                v-if="formSchema.type.as.toLowerCase() === 'select'"
                :as="formSchema.type.as"
                :id="formSchema.type.name"
                :name="formSchema.type.name"
                class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-left">
                <option value="" disabled> Choose </option>
                <option value="" disabled
                  v-if="formSchema.type.options === null || formSchema.type.options.length === 0">
                  No Data found ! </option>
                <option v-else :value="option.value"
                  v-for="option, index in  formSchema.type.options"
                  :key="index" class="text-black"> {{
                    option.value }} </option>
              </Field>
              <ErrorMessage :name="formSchema.type.name"
                class="text-sm text-rose-500 mt-1" as="p" />
            </div>

            <!-- Others -->
            <div v-if="formSchema.type.ref.value" class="flex flex-col justify-start items-start gap-2 w-full">
              <!-- Video Tag Section - Moved outside the loop -->
              <div class="w-full">
                <div v-if="videoUrl" class="flex flex-col gap-2">
                  <label class="mb-2 text-xs text-gray-500">Video Tag</label>
                  <video
                    :src="videoUrl"
                    controls
                    class="w-full rounded-lg"
                    style="max-height: 200px; object-fit: contain;"
                  >
                    Your browser does not support the video tag.
                  </video>
                  <button
                    type="button"
                    @click="handleChangeVideo"
                    class="text-blue-500 hover:text-blue-700 text-xs underline w-fit"
                  >
                    Change Video
                  </button>
                  <p v-if="showCustomError" class="text-xs text-rose-500 mt-1">
                    This video is already being used. Please select a different video.
                  </p>
                </div>

                <div v-else-if="!videoUrl"  class="w-full">
                <label class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200 py-3 px-1 cursor-pointer">
                  <div class="h-full w-full flex justify-center items-center gap-2">
                    <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
                      <path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
                    </svg>
                    <p class="text-xs font-semibold text-gray-500">Upload Video</p>
                  </div>
                  <div>
                    <Field name="video_tag">
                    <input
                      type="file"
                      accept="video/*"
                      @change="handleVideoUpload"
                      :key="fileKey"
                      class="hidden"
                    />
                  </Field>
                  </div>
                </label>
                <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="video_tag" />
              </div>

              </div>

              <!-- Dynamic Fields Loop -->
              <div
                v-for="items in formSchema.type.options[formSchema.type.ref.value]?.toShow"
                :key="items.field"
                class="w-full"
              >
                <div
                  v-if="formSchema[items.field]?.type === 'multiselect'"
                  class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                  <label
                    class="mb-2 text-xs text-gray-500">{{
                      formSchema[items.field].label }}</label>
                  <Field as="input"
                    v-model="formSchema[items.field].ref.value"
                    class="sr-only"
                    :name="formSchema[items.field].name">
                  </Field>
                  <Multiselect
                    v-model="formSchema[items.field].ref.value"
                    :options="formSchema[items.field].options"
                    :searchable="true" :multiple="true"
                    :taggable="false" placeholder="units name?"
                    :close-on-select="false" label="name"
                    track-by="_id" open-direction="bottom"
                    deselectLabel="remove" selectLabel="">
                          <!-- Custom Option Template (showing just selected items count) -->
                                <template #selection="{ values, isOpen }">
        <span class=""
              v-if="values.length"
              v-show="!isOpen">{{ values.length }} units selected</span>

      </template>

      <!-- No Search Results Message -->
      <template v-slot:noResult>Oops! No Units found.</template>
                  </Multiselect>
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1" />
                     <!-- Selected Units Tag -->
                                <div name="unit_lists" class="">
                        <div class="outside-tags mt-2 flex flex-wrap gap-2 max-h-32 py-1 px-2 overflow-y-auto">
      <div
        class="bg-[#10b981] py-1 px-2 rounded-md text-white text-xs w-fit h-fit flex items-center gap-2"
        v-for="(unit, index) in formSchema[items.field].ref.value"
        :key="unit._id"
      >
        {{ unit.name }}
        <button class="remove-tag" @click="()=>handleUnitRemoval(items.field, index)">
<svg class="h-4 w-4 fill-[#f6f6f6]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
        </div>
    </div>
    </div>

                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'dropdown'"
                  class="">
                  <label :for="formSchema[items.field].name"
                    class="mb-2 text-xs text-gray-500">{{
                      formSchema[items.field].label }}</label>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    v-if="formSchema[items.field].as.toLowerCase() === 'select'"
                    :as="formSchema[items.field].as"
                    :id="formSchema[items.field].name"
                    :name="formSchema[items.field].name"
                    class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-left">
                    {{ formSchema[items.field].ref.value }}
                    <option value='' class="text-gray-500">
                      Choose </option>
                    <option value="" disabled
                      v-if="formSchema[items.field].options === null || formSchema[items.field].options.length === 0">
                      No Data found ! </option>
                    <option v-else
                      :value="option._id ? option._id : option"
                      v-for="option, index in formSchema[items.field].options"
                      :key="index" class="text-black"> {{
                        option.name ? option.name : option }}
                    </option>
                  </Field>
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'text'"
                  class="">
                  <label :for="formSchema[items.field].name"
                    class="mb-2 text-xs text-gray-500">{{
                      formSchema[items.field].label }}</label>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    :type="formSchema[items.field].ref.type"
                    v-if="formSchema[items.field].as.toLowerCase() === 'input'"
                    :name="formSchema[items.field].name"
                    :id="formSchema[items.field].name"
                    class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-xs placeholder:text-gray-500 placeholder:text-left placeholder:font-semibold"
                    :placeholder="`enter ${formSchema[items.field].label}`" />

                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>
              </div>
            </div>

            <!-- Positions -->
             <div class="relative">
                      <label
                    class="mb-2 text-xs text-gray-500">
                      x position
                  </label>
                  <Field
                    v-model="xposition"
                   disabled
                    as="input"
                    type="number"
                    id="x"
                    class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-left"
                    name="x">
                  </Field>

                  <ErrorMessage
                    name="x"
                    as="p"
                    class="text-sm text-rose-500 mt-1" />
            </div>

             <div class="relative">
                          <label
                        class="mb-2 text-xs text-gray-500">
                          y position
                      </label>
                      <Field
                      v-model="yposition"
                      disabled
                          as="input"
                          id="y"
                        class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-left"
                        name="y">
                      </Field>

                      <ErrorMessage
                        name="y"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />

                    </div>
           <!-- Scale -->
            <div class="relative ">
                          <label
                            class="mb-2 text-xs text-gray-500">
                            Scale X <br> <span class="italic font-semibold"> value: {{ Array.isArray(scaleX) ? scaleX[0] : scaleX}} </span>
                          </label>

                              <Field name="scalex" v-model="scaleX" v-slot="{ field }">

                                                    <div  v-bind="field">
                                                      <div ref="scaleXRef" v-bind="scaleXComputed.getRootProps()">
                                                          <div v-bind="scaleXComputed.getControlProps()">
                                                            <div v-bind="scaleXComputed.getTrackProps()">
                                                              <div v-bind="scaleXComputed.getRangeProps()" />
                                                            </div>
                                                            <div
                                                              v-for="(_, index) in scaleXComputed.value"
                                                              :key="index"
                                                              v-bind="scaleXComputed.getThumbProps({ index })"
                                                            >
                                                              <input v-bind="scaleXComputed.getHiddenInputProps({ index })" />
                                                            </div>
                                                          </div>
                                                    </div>
                                                  </div>

                                                    <ErrorMessage
                                                  name="scalex"
                                                  as="p"
                                                  class="text-sm text-rose-500 mt-1" />
                                  </Field>

                </div>

                <div class="relative ">
                          <label
                            class="mb-2 text-xs text-gray-500">
                            Scale Y <br> <span class="italic font-semibold"> value: {{ Array.isArray(scaleY) ? scaleY[0] : scaleY }}</span>
                          </label>

                              <Field name="scaley" v-model="scaleY" v-slot="{ field }">

                                                    <div  v-bind="field">
                                                      <div ref="scaleYRef" v-bind="scaleYComputed.getRootProps()">
                                                          <div v-bind="scaleYComputed.getControlProps()">
                                                            <div v-bind="scaleYComputed.getTrackProps()">
                                                              <div v-bind="scaleYComputed.getRangeProps()" />
                                                            </div>
                                                            <div
                                                              v-for="(_, index) in scaleYComputed.value"
                                                              :key="index"
                                                              v-bind="scaleYComputed.getThumbProps({ index })"
                                                            >
                                                              <input v-bind="scaleYComputed.getHiddenInputProps({ index })" />
                                                            </div>
                                                          </div>
                                                    </div>
                                                  </div>

                                                    <ErrorMessage
                                                  name="scaley"
                                                  as="p"
                                                  class="text-sm text-rose-500 mt-1" />
                                  </Field>

                </div>

            <!-- min and Max Zoom -->

    <div class="relative ">
                          <label
                            class="mb-2 text-xs text-gray-500">
                            min and max (zoom level) <br> <span class="italic font-semibold"> min: {{ minandmaxZoomLevel[0] }}</span>  <span class="italic font-semibold"> max: {{ minandmaxZoomLevel[1] }}</span>
                          </label>

                              <Field name="minandmax" v-model="minandmaxZoomLevel" v-slot="{ field }">

                                                    <div ref="minandmaxRef" v-bind="field">
                                                      <div  v-bind="minandmaxComputed.getRootProps()">
                                                          <div v-bind="minandmaxComputed.getControlProps()">
                                                            <div v-bind="minandmaxComputed.getTrackProps()">
                                                              <div v-bind="minandmaxComputed.getRangeProps()" />
                                                            </div>
                                                            <div
                                                              v-for="(_, index) in minandmaxComputed.value"
                                                              :key="index"
                                                              v-bind="minandmaxComputed.getThumbProps({ index })"
                                                            >
                                                              <input v-bind="minandmaxComputed.getHiddenInputProps({ index })" />
                                                            </div>
                                                          </div>
                                                    </div>
                                                  </div>

                                                    <ErrorMessage
                                                  name="minandmax"
                                                  as="p"
                                                  class="text-sm text-rose-500 mt-1" />
                                  </Field>

      </div>

      <div class="relative ">
                          <label
                            class="mb-2 text-xs text-gray-500">
                            zIndex <br> <span class="italic font-semibold"> value: {{ zIndexRatio[0] }}</span>
                          </label>

                              <Field name="zindex" v-model="zIndexRatio" v-slot="{ field }">

                                                    <div  v-bind="field">
                                                      <div ref="zIndexRef" v-bind="zindexComputed.getRootProps()">
                                                          <div v-bind="zindexComputed.getControlProps()">
                                                            <div v-bind="zindexComputed.getTrackProps()">
                                                              <div v-bind="zindexComputed.getRangeProps()" />
                                                            </div>
                                                            <div
                                                              v-for="(_, index) in zindexComputed.value"
                                                              :key="index"
                                                              v-bind="zindexComputed.getThumbProps({ index })"
                                                            >
                                                              <input v-bind="zindexComputed.getHiddenInputProps({ index })" />
                                                            </div>
                                                          </div>
                                                    </div>
                                                  </div>

                                                    <ErrorMessage
                                                  name="zindex"
                                                  as="p"
                                                  class="text-sm text-rose-500 mt-1" />
                                  </Field>

      </div>

            <!-- Placement -->
             <div v-if="!reSize" class="relative">
                  <label
                        class="mb-2 text-xs text-gray-500">
                         Placement
                      </label>
                  <Field
                    v-model="placement"
                     as="select"
                     type="dropdown"
                     id="placement"
                     name="placement"
                    class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-left">

                    <option
                      :value="option"
                      v-for="option in listOfPlacements"
                      :key="option" class="text-black">
                      {{ option }}
                    </option>

                  </Field>

                  <ErrorMessage
                    name="placement"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>

            <!-- Resize -->
            <div class="relative flex items-center gap-2">

                      <Field
                          v-model="reSize"
                          as="input"
                         type="checkbox"
                          :value="true"
                          :unchecked-value="false"
                          id="reSize"
                        class="input-primary !w-3 !h-3 text-black"
                        name="reSize">
                      </Field>

                      <label for="reSize"
                        class="mb-2 text-xs text-gray-500 select-none mt-1">
                          Resize
                      </label>

                      <ErrorMessage
                        name="reSize"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>
            <!-- ShowLabel -->
            <div v-if="formSchema.type.ref.value === 'building' || formSchema.type.ref.value === 'community' || formSchema.type.ref.value === 'grouped_units'" class="relative flex items-center gap-2">

                      <Field
                          v-model="showLabel"
                          as="input"
                         type="checkbox"
                          :value="true"
                          :unchecked-value="false"
                          id="showLabel"
                        class="input-primary !w-3 !h-3 text-black"
                        name="showLabel">
                      </Field>

                      <label for="showLabel"
                        class="mb-2 text-xs text-gray-500 select-none mt-1">
                        Show label
                      </label>

                      <ErrorMessage
                        name="showLabel"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>
            <!-- GroupedUnitsName -->
            <div v-if="showGroupedUnitsName && formSchema.type.ref.value === 'grouped_units'" class="">
              <label
               class="mb-2 text-xs text-gray-500">
                     Group Name
              </label>
                  <Field
                    v-model="groupedUnitsName"
                    as="input"
                    type="text"
                    id="group_name"
                    class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-bold text-gray-500 px-2 text-left placeholder:text-left placeholder:font-semibold placeholder:text-xs"
                    name="group_name"
                    placeholder="enter name">
                  </Field>

                  <ErrorMessage
                    name="group_name"
                    as="p"
                    class="text-sm text-rose-500 mt-1" />
            </div>
            <div v-if="formSchema.type.ref.value === 'grouped_units'">
              <label
                  class="mb-2 text-xs text-gray-500">
                  Bedrooms
              </label>
                  <Field
                    v-model="bedrooms"
                     as="select"
                     type="dropdown"
                     id="bedrooms"
                     name="bedrooms"
                    class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200  cursor-pointer text-xs font-semibold text-gray-500 px-2 text-left placeholder:text-left">

                    <option
                      :value="option"
                      v-for="option in unitplanTypeList"
                      :key="option" class="text-black">
                      {{ option }}
                    </option>

                  </Field>

                  <ErrorMessage
                    name="bedrooms"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
            </div>

            <!-- Button -->
            <div
              class="w-full text-center mt-1 flex items-center mb-3">
              <button
                id="submit"
                type="submit"
                :disabled="loader"
                class="inline-flex w-full justify-center items-center gap-2 h-9 sm:h-9 rounded-full sm:rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-3 text-xs text-white font-semibold leading-6">
                {{loader ? '' : 'Save Changes'}}
                <Spinner v-if="loader" class="text-slate-400 fill-white" />
              </button>
            </div>

            <div class="relative mb-4">
                          <label
                        class="mb-2 text-xs text-gray-500 !font-semibold text-grey w-full italic">
                          Current zoom level :  {{ currentZooomLevel }}
                      </label>

               </div>
          </div>
        </div>
      </Form>
    </div>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
/* width */
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>

<style>
/* Range Slider */
[data-scope="slider"][data-part="root"] {
  width: 100%;
}
[data-scope="slider"][data-part="root"] > div:first-child {
  display: flex;
  justify-content: space-between;
}
[data-scope="slider"][data-part="label"] {
  margin-right: 0.5rem;
}
[data-scope="slider"][data-part="control"] {
  display: flex;
  align-items: center;
  margin-top: 0rem;
  position: relative;
  padding-block: 0.625rem;
}
[data-scope="slider"][data-part="track"] {
  height: 8px;
  border-radius: 9999px;
  flex: 1;
  background: #A4CAFE;
}
[data-scope="slider"][data-part="range"] {
  height: 100%;
  border-radius: inherit;
  background: #1C64F2;
}
[data-scope="slider"][data-part="thumb"] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 9999px;
  background: #1C64F2;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
[data-scope="slider"][data-part="thumb"]:is(:focus, [data-focus]) {
  outline: 2px solid #1C64F2;
}
[data-scope="slider"][data-part="thumb"][data-disabled] {
  background: #e2e8f0;
}

</style>
