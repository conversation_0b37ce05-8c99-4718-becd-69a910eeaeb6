<script setup>
/* But<PERSON> followup by two colors - 'primary' and 'secondary' */
/* Props And Emits */
const { title, theme } = defineProps({
  title: String,
  theme: String,
});

defineEmits(['handleClick']);
</script>

<template>
  <button
    :class="[
      (theme ? (theme === 'primary' ? 'bg-[#1A56DB] text-white dark:text-txt-150' : 'bg-gray-200 text-txt-150' ) : 'bg-bg-50 text-txt-1000'),
      'rounded-lg flex flex-row justify-center items-center gap-2',
      'px-4 py-1 max-sm:px-2 sm:py-2',
      'h-8 sm:h-10',
      'text-xs sm:text-sm font-medium',
      'disabled:opacity-80 transition-all duration-200 ease-in-out'
    ]"
    @click="(event) => $emit('handleClick', event)"
  >
    <slot v-if="$slots.svg" name="svg">
    </slot>
    <span class="text-center text-inherit whitespace-nowrap text-sm">{{ title }}</span>

  </button>
</template>
