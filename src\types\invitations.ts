import { UserRole } from './organization';
export type UserInviteStatus = 'pending' | 'accepted' | 'rejected';

export type Invitation = {
    _id: string,
    email: string,
    role: string,
    organization_id: string,
    status: UserInviteStatus,
    created_at: string,
    updated_at: string
  };

export type CreateInvitationInput = {
    email: string,
    role: string,
    organization_id: string,
    status: UserInviteStatus,
    created_at: string,
    updated_at: string
  };

export type UserInvitation = {
  _id: string,
  email: string,
  organization_id: string,
  role: UserRole,
  status: string,
  created_at: string,
  updated_at: string
}

export type UserInvite = {
  organization_id: string;
  role: UserRole;
  email: string;
};

export type invitation={
  invitation_id:string,
  timeStamp:Date
}
