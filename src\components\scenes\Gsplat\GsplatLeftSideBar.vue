<script setup>
import { nextTick, ref, watch } from 'vue';
import router from '@/router';
import { useRoute } from 'vue-router';
import { Org_Store } from '../../../store/organization';
import { ProjectStore } from '../../../store/project';
import { getCategories as getAmenitiesCategories} from '../../../api/projects/amenties';
import { getAllScenes as getAllMasterScene, getScene as getMasterScene } from '../../../api/masterScene/index';
import { getListofScenes, getScene} from '@/api/projects/scene';
import AddSceneOptionsMenu from '../AddSceneOptionsMenu.vue';
import { getListofSidebarOptions } from '@/api/projects/sidebar/index';
import { GetAllTrash } from '@/api/trash';
import { isMasterScenePath } from '../../../helpers/helpers.ts';
import ClickToEditText from '../ClickToEditText.vue';
import Hotspot from '../Hotspot.vue';
import { cdn } from '../../../helpers';
import DesignSceneList from '../DesignSceneList.vue';
import { deleteLayer, updateLayers } from '@/api/projects/scene/svg';
import { updateLayers as updateMasterLayers, deleteLayers as deleteMasterLayer} from '../../../api/masterScene/svg/index';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';

const route = useRoute();
const hotSpotList = ref(null);
const projectStore = ProjectStore();
const newOrganizationStore = Org_Store();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));

const selectedSvg = ref();
const sceneDetails = ref(null);
const categoryList = ref();

const sceneList = ref();
const project_id = ref(route.params.project_id);
const TrashItems = ref({});
const openAddSceneOptionsMenu = ref();
const categorizedScenes = ref();
const openAddIcons = ref(false);
const selectedMenu = ref('pages');
const createSceneClick = ref({x: null,
  y: null,
});
const SceneReOrderData = ref(null);
const sidebarOptionList = ref();
const gsplatSvgId = ref();
const sceneDataLoading = ref(false);

const openLayerDeleteModal = ref(false);
const layerDeleteLoader = ref(false);
const layerToDelete = ref();
const parentSvgId = ref();

if (route.path.includes('/layers')) {
  selectedMenu.value = 'layers';
} else if (route.path.includes('/icons')) {
  selectedMenu.value = 'icons';
} else {
  selectedMenu.value = 'pages';
}

watch(
  () => route.path,
  (newPath) => {
    console.log('newPath ->', newPath);
    if (newPath.includes('/layers')) {
      selectedMenu.value = 'layers';
    } else if (newPath.includes('/icons')) {
      selectedMenu.value = 'icons';
    } else {
      selectedMenu.value = 'pages';
    }
  },
  { immediate: true },
);

/* Methods */
const handleAddHotspotClick = () => {
  if (route.query.layerId !== '00' || !route.query.layerId){
    router.push({path: route.path, query: {layerId: '00'} });
  } else {
    router.push({ path: route.path, query: {}});
  }

};

const handleAddPlaneClick =  () => {
  if (route.query.layerId !== '000' || !route.query.layerId){
    router.push({path: route.path, query: {layerId: '000'} });
  } else {
    router.push({ path: route.path, query: {}});
  }
};

const getSceneInfo = (sceneInfo) => {
  if (sceneInfo){
    hotSpotList.value = Object.values(sceneInfo[sceneId.value].svgData)[0]?.layers ? Object.values(sceneInfo[sceneId.value].svgData)[0].layers : [];
    gsplatSvgId.value = Object.values(sceneInfo[sceneId.value].svgData)[0]?._id;
  }
};

if (sceneId.value){
  if (isMasterScene.value){
    getSceneInfo(newOrganizationStore.masterScenes);
  } else {
    getSceneInfo(projectStore.scenes);
  }
}

console.log('routes', route.params);

const handleGetSidebarOptions = () => {
  getListofSidebarOptions(projectId.value).then((res) => {
    console.log(res);
    sidebarOptionList.value = res;
    projectStore.SyncMultipleSidebarOptions(res);
  });
};

if (!isMasterScene.value){
  handleGetSidebarOptions();
}

// Refresh projects
if (!isMasterScene.value){
  projectStore.RefreshScenes(projectId.value);
  projectStore.RefreshLandmarks(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshAmenities(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshCommunities(projectId.value);
}
newOrganizationStore.RefreshMasterScenes();
newOrganizationStore.RefreshProjects();

const handleGetScene = () => {
  sceneDataLoading.value = true;

  if (isMasterScene.value){
    getMasterScene(sceneId.value).then((res) => {

      const obj = {};
      obj[res.sceneData._id]= res,
      newOrganizationStore.SyncMultipleMasterScenes(obj);
      sceneDetails.value = res;
      sceneDataLoading.value = false;
    });
  } else {
    getScene(projectId.value, sceneId.value).then((res) => {
      const obj = {};
      obj[res.sceneData._id]= res,
      projectStore.SyncMultipleScenes(obj);
      sceneDetails.value = res;
      sceneDataLoading.value = false;
    });
  }
};

handleGetScene(); // Initialize

// const getSceneById = () => {
//   console.log("getSceneById called");
//   getScene(project_id.value, currentScene.value).then((res) => {
//     sceneDetails.value = res;
//   });
// };

// getSceneById();

document.addEventListener('refreshGetScene', () => { // After adding new svgs
  handleGetScene();
});

if (!isMasterScene.value){
  getAmenitiesCategories(projectId.value).then((res) => { // Get list of amenities categories
    console.log(res);
    categoryList.value = res.map((elem) => {
      return {name: elem.category};
    });
  }).catch((err) => {
    console.log('output->err', err);
  });
}

function customSort (items) {
  // First, sort the items
  const sortedItems = [...items].sort((a, b) => {
    // Case 1: If both items have no order property
    if (a.order === undefined && b.order === undefined) {
      // If one is 'OTHER', it comes last
      if (a.category === 'OTHER') {
        return 1;
      }
      if (b.category === 'OTHER') {
        return -1;
      }
      return 0; // Keep original order for items with no order
    }

    // Case 2: If only one item has no order property, it goes to the end
    if (a.order === undefined) {
      return 1;
    }
    if (b.order === undefined) {
      return -1;
    }

    // Case 3: Both have order property
    // If one is 'OTHER', it goes last regardless of order
    if (a.category === 'OTHER' && b.category !== 'OTHER') {
      return 1;
    }
    if (a.category !== 'OTHER' && b.category === 'OTHER') {
      return -1;
    }

    // Case 4: Both have order and neither is 'OTHER' (or both are)
    // Sort by order
    return a.order - b.order;
  });

  // Then reassign order values sequentially
  return sortedItems.map((item, index) => {
    return { ...item, order: index + 1 };
  });
}

watch(() => selectedSvg.value, () => {
  if (!selectedSvg.value) {
    router.push({ path: route.path, query: {} });
  } else {
    router.push({ path: route.path, query: { svgId: selectedSvg.value._id } });
  }
});

watch(() => route.params.params, (newId, oldId) => {
  console.log('Route param changed:', oldId, '=>', newId);
});

defineProps({
  updateHotSpotPosition: Object,
  updatePlaneScale: Object,
});

function categorizeScenes (data) {
  const categorizedData = {};

  // Loop through each scene in the input data
  for (const sceneId in data) {
    const scene = data[sceneId];
    const categoryId = scene.sceneData.category;

    // If there's a category, use it; otherwise, use 'uncategorized'
    const category = categoryId || 'uncategorized';

    // If the category doesn't exist in the result object, create it
    if (!categorizedData[category]) {
      categorizedData[category] = {};
    }

    // Add the scene to its corresponding category
    categorizedData[category][sceneId] = scene.sceneData;
  }

  return categorizedData;
}

function createCategoryBasedSceneData (data) {
  // Create a map for categories
  const categoryMap = {};
  const DEFAULT_CATEGORY = "OTHER";

  console.log(sidebarOptionList.value);
  sidebarOptionList.value && Object.values(sidebarOptionList.value).forEach((item) => {
    categoryMap[item._id] = {
      id: item._id,
      name: `Category ${item.name}`,
      category: item._id,
      linked_scenes: [],
      draggable: true,
      isCategory: true,
      order: item.order?item.order:null,
    };
  });
  categoryMap.OTHER = {
    id: 'UNCATEGORIZED',
    name: `UNCATEGORIZED`,
    category: `UNCATEGORIZED`,
    draggable: false,
    isCategory: true,
    linked_scenes: [],
  };

  // Process each scene and add to appropriate category
  for (const sceneId in data) {
    const sceneData = data[sceneId].sceneData;

    // Assign to OTHER category if no category exists
    const category = sceneData.category || DEFAULT_CATEGORY;

    // Create simplified scene object
    const sceneObj = {
      id: sceneId,
      name: sceneData.name,
      type: sceneData.type,
      draggable: true,
      category: category,
    };

    // Initialize category in categoryMap if it doesn't exist
    if (!categoryMap[category]) {
      categoryMap[category] = {
        id: category,
        name: category,
        category: category,
        linked_scenes: [],
      };
    }

    // Add scene to its category
    categoryMap[category].linked_scenes.push(sceneObj);
  }

  return customSort(Object.values(categoryMap));
}
/* Setup Data */

// setupData (Modify based on your needs)
const setupData = (data) => {
  console.log("setupData", data);
  if (data){
    // Structuring your data's
    const convertedData = createCategoryBasedSceneData(data);

    console.log(convertedData);

    // Update the your component reference
    SceneReOrderData.value = convertedData;
  }
};

const handleGetListOfScenes = () => {
  if (!isMasterScene.value){
    getListofScenes(projectId.value).then((res) => {
      sceneList.value = res;
      setupData(res);
      categorizedScenes.value = categorizeScenes(res);
      console.log("categorized scene -> ", categorizedScenes.value);
    });
  } else {
    getAllMasterScene().then((res) => {
      sceneList.value = res;
      setupData(res);
      categorizedScenes.value = categorizeScenes(res);
      console.log("categorized scene -> ", categorizedScenes.value);
    });
  }

};

handleGetListOfScenes();

const handleGetAllTrash = () => {
  GetAllTrash('scenes', project_id.value).then((res) => {
    console.log("trash scenes",  res.items);
    TrashItems.value = Object.values(res.items).filter((item) => {
      return item.type.split('_')[0]===projectId.value && item.type.split('_')[1]==='scenes';
    });
  });
};

handleGetAllTrash();

watch(() => selectedSvg.value, () => {
  if (!selectedSvg.value) {
    router.push({ path: route.path, query: {} });
  } else {
    router.push({ path: route.path, query: { svgId: selectedSvg.value._id } });
  }
});

const response = {
  type: 'gsplat',
  cameraPosition: {
    x: 2.107234919667583,
    y: 1.1339903035639423,
    z: -0.7236207464939785,
  },
};

const svgLayers = ref({});

const getSceneSource = (sceneInfo) => {
  if (sceneInfo){
    console.log(sceneInfo[route.params.scene_id].svgData);
    console.log(sceneInfo[route.params.scene_id].sceneData.gsplat_link);
    if (sceneInfo[route.params.scene_id]?.sceneData?.gsplat_link){

      response.source = cdn(sceneInfo[route.params.scene_id].sceneData.gsplat_link);
    }

    if (Object.values(sceneInfo[route.params.scene_id]?.svgData).length > 0){
      svgLayers.value = Object.values(sceneInfo[route.params.scene_id].svgData)[0].layers;
    }
  }
};

if (isMasterScene.value){
  getSceneSource(newOrganizationStore.masterScenes);
} else {
  getSceneSource(projectStore.scenes);
}

const enableEditLayerName = ref(null);

const handleTextClick = (id) => {
  enableEditLayerName.value = id;
};

const updateLayersName = (svgId, layerToUpdate, value) => {
  return new Promise((resolve, reject) => {
    console.log(value);
    const objectToUpdate = {
      layer_id: layerToUpdate,
      ...( !isMasterScene.value && {project_id: route.params.project_id}),
      svg_id: svgId,
      query: {name: value},
    };

    if (isMasterScene.value){
      updateMasterLayers(objectToUpdate).then((res) => {
        console.log(res);
        resolve(true);
      }).catch((err) => {
        console.log('Error');
        console.log('output->err in updateLayersData', err);
        reject(false);
      });
    } else {
      updateLayers(objectToUpdate).then((res) => {
        console.log(res);
        resolve(true);
      }).catch((err) => {
        console.log('Error');
        console.log('output->err in updateLayersData', err);
        reject(false);
      });
    }

  });
};

const handleEditLayerName = (svg_id, layer_id, updatedText) => {
  updateLayersName(svg_id, layer_id, updatedText).then(() => {
    handleGetScene();
    enableEditLayerName.value = null;
  }).catch(() => {
    enableEditLayerName.value = null;
  });
  enableEditLayerName.value = null;

  console.log(updatedText);

  // try {

};

watch(() => sceneDetails.value, () => {
  if (isMasterScene.value){
    getSceneInfo(newOrganizationStore.masterScenes);
  } else {
    getSceneInfo(projectStore.scenes);
  }
});

const gsplatLayersUpdateRef = ref(null);
const HandleUpdateGsplatLayers = (val) => {
  gsplatLayersUpdateRef.value = val;
};

const handleLayerSelection = (id) => {
  if (!route.query.layerId || route.query.layerId !== id){
    router.push({ path: route.path, query: {layerId: id}});
  } else {
    router.push({ path: route.path, query: {}});
  }
};

function scrollToSection (id) {
  nextTick(() => {
    const el = document.getElementById(id);
    if (!el) {
      console.warn(`Element with id "${id}" not found.`);
      return;
    }

    console.log('scrolling to element', el);

    el.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  });
}

if (route.query?.layerId){
  scrollToSection(`layer-${route.query.layerId}`);
}

watch(() => route.query.layerId, () => {
  if (route.query.layerId) {
    scrollToSection(`layer-${route.query.layerId}`);
  }
});

const handleMoveLayerToTrash = () => {
  layerDeleteLoader.value = true;
  const obj = {
    layer_id: layerToDelete.value,
    svg_id: parentSvgId.value,
    ...(!isMasterScene.value && {project_id: project_id.value}),
  };

  if (isMasterScene.value){
    deleteMasterLayer(obj).then(() => {
      layerDeleteLoader.value = false;
      openLayerDeleteModal.value = false;
      window.location = `/masterscenes/${sceneId.value}`;
    }).catch((err) => {
      layerDeleteLoader.value = false;
      console.log(err);
    });
  } else {
    deleteLayer(obj).then(() => {
      layerDeleteLoader.value = false;
      openLayerDeleteModal.value = false;
      window.location = `/projects/${project_id.value}/design/scenes/${sceneId.value}`;
    }).catch((err) => {
      layerDeleteLoader.value = false;
      console.log(err);
    });
  }
};

</script>

<template>
    <!-- Pages -->
    <div class="h-full w-full bg-gray-100  flex justify-between">

      <DesignSceneList v-if="selectedMenu==='pages'"/>
    <div v-else-if="selectedMenu==='layers'" class="h-full  w-full  bg-white py-3 flex flex-col">
        <div class="flex justify-between items-center px-3">
          <h2 class=" text-gray-500 text-lg font-semibold ml-1">
            Add Icon
          </h2>
          <button class="relative flex justify-center items-center w-6 h-6 rounded-md cursor-pointer" :class="$route.query.layerId==='00'?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'" @click="handleAddHotspotClick" >
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>
        </div>
        <div class="flex justify-between items-center px-3">
          <h2 class=" text-gray-500 text-lg font-semibold ml-1">
            Add plane
          </h2>
          <button class="relative flex justify-center items-center w-6 h-6 rounded-md cursor-pointer" :class="$route.query.layerId==='000'?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'" @click="handleAddPlaneClick">
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>
        </div>
        <div class="w-full py-2 mt-2 px-3">
          <!-- scenen Name Heading Skeleton -->
          <div v-if="sceneDataLoading" class="flex items-center gap-2 ">
            <div class="w-5 h-5 svg-skeleton-loader rounded-md"></div>

                <div class="h-5 w-28 svg-skeleton-loader rounded-md">
                </div>
          </div>
          <div v-else class="flex items-center gap-2 ">
            <svg class="w-5 h-5 fill-gray-500" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_723_21115)">
            <path d="M16.4 2.6665H3.6C3.17565 2.6665 2.76869 2.8382 2.46863 3.14381C2.16857 3.44943 2 3.86393 2 4.29613V15.7035C2 16.1357 2.16857 16.5502 2.46863 16.8559C2.76869 17.1615 3.17565 17.3332 3.6 17.3332H16.4C16.8243 17.3332 17.2313 17.1615 17.5314 16.8559C17.8314 16.5502 18 16.1357 18 15.7035V4.29613C18 3.86393 17.8314 3.44943 17.5314 3.14381C17.2313 2.8382 16.8243 2.6665 16.4 2.6665ZM12 5.92576C12.2373 5.92576 12.4693 5.99745 12.6667 6.13174C12.864 6.26604 13.0178 6.45693 13.1087 6.68026C13.1995 6.90359 13.2232 7.14934 13.1769 7.38643C13.1306 7.62352 13.0164 7.8413 12.8485 8.01223C12.6807 8.18316 12.4669 8.29956 12.2341 8.34672C12.0013 8.39388 11.7601 8.36968 11.5408 8.27717C11.3215 8.18466 11.1341 8.02801 11.0022 7.82702C10.8704 7.62602 10.8 7.38972 10.8 7.14799C10.8 6.82383 10.9264 6.51295 11.1515 6.28374C11.3765 6.05453 11.6817 5.92576 12 5.92576ZM15.5008 14.4658C15.4319 14.5938 15.3305 14.7005 15.2072 14.7749C15.0839 14.8493 14.9433 14.8886 14.8 14.8887H5.2C5.06362 14.8888 4.92949 14.8533 4.81034 14.7858C4.6912 14.7182 4.591 14.6207 4.51925 14.5025C4.44751 14.3844 4.40662 14.2495 4.40045 14.1108C4.39428 13.972 4.42304 13.8339 4.484 13.7097L7.284 8.00599C7.34846 7.87442 7.44666 7.76307 7.56821 7.68375C7.68976 7.60442 7.83011 7.56007 7.9744 7.55539C8.11993 7.54715 8.26478 7.58103 8.3922 7.65313C8.51962 7.72523 8.62439 7.83259 8.6944 7.9628L10.9144 11.8389L12.1512 10.3013C12.2315 10.2017 12.3339 10.1228 12.45 10.0714C12.5661 10.0199 12.6925 9.99715 12.8188 10.0051C12.9452 10.0131 13.0679 10.0514 13.1769 10.1171C13.2858 10.1828 13.3779 10.2738 13.4456 10.3828L15.4784 13.6421C15.5537 13.7649 15.5955 13.9061 15.5994 14.0509C15.6033 14.1956 15.5693 14.3389 15.5008 14.4658Z"/>
            </g>
            <defs>
            <clipPath id="clip0_723_21115">
            <rect width="16" height="16" fill="white" transform="translate(2 2)"/>
            </clipPath>
            </defs>
            </svg>

                <h3 class=" text-gray-500 text-sm font-semibold">
            {{sceneDetails?.sceneData.name}}
          </h3>
          </div>
        </div>

         <!-- No Data  -->
        <div class="py-10 px-2 flex flex-col justify-center items-center gap-3" v-if="!sceneDetails?.svgData || Object.keys(sceneDetails?.svgData).length === 0 && !sceneDataLoading">
          <div class="flex justify-center items-center w-full">
<svg class="w-8 h-8 fill-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="alert-circle"><rect width="24" height="24" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"/><circle cx="12" cy="16" r="1"/><path d="M12 7a1 1 0 0 0-1 1v5a1 1 0 0 0 2 0V8a1 1 0 0 0-1-1z"/></g></g></svg></div>
<p class="text-center text-gray-500 text-md font-semibold ">No data available!</p>
        </div>
        <!-- Svg Skeleton Loader -->
         <div v-if="sceneDataLoading"  class="mt-3 max-h-[inherit] overflow-y-auto mb-16 px-3">

         <div v-for="i in 6" class="my-2.5 h-6 w-full flex items-center gap-1.5" :key="i">
            <div class="h-4 w-4 rounded-full svg-skeleton-loader ">

              </div>

              <div class="flex-1 w-full rounded-md h-full svg-skeleton-loader">
              </div>
                 <div class="h-4 w-7 rounded-2xl mr-2  svg-skeleton-loader">

                </div>
                 <div class="h-full w-4 rounded-md  svg-skeleton-loader">

                </div>

          </div>
          </div>
        <!-- List -->
         <div v-else class="mt-3 overflow-y-auto pb-8">
          <div v-for="svg, svgId  in sceneDetails?.svgData" :key="svgId" class="relative">
            <div  class="w-1 h-[95%] absolute border-l border-b border-gray-500 left-6 top-4 z-10"></div>
        <div class="h-8 w-full flex gap-2 my-1 cursor-pointer px-3">
            <div class="flex justify-center items-center" >
              <svg  width="16" height="17" class="fill-gray-500 w-4.5 h-4.5" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.99399 6.30021L7.21405 12.1912C7.42287 12.3889 7.70604 12.5 8.0013 12.5C8.29656 12.5 8.57974 12.3889 8.78855 12.1912L15.0086 6.30021C15.1643 6.15272 15.2703 5.96483 15.3133 5.76028C15.3562 5.55574 15.3341 5.34373 15.2499 5.15106C15.1656 4.95838 15.0229 4.79369 14.8399 4.6778C14.6568 4.56192 14.4416 4.50004 14.2214 4.5H1.78124C1.56104 4.50004 1.3458 4.56192 1.16273 4.6778C0.979661 4.79369 0.836976 4.95838 0.752717 5.15106C0.668458 5.34373 0.646407 5.55574 0.689352 5.76028C0.732297 5.96483 0.83831 6.15272 0.99399 6.30021Z"/>
</svg>

              </div>
              <div class="flex-1 flex items-center" >
                <h2 class=" max-w-16 text-sm leading-4 text-gray-900 font-semibold ml-1 overflow-hidden text-ellipsis whitespace-nowrap">{{ svg.type }}</h2>
              </div>
              <div class="w-16 flex-0 flex justify-between items-center  h-full">
                <div class="flex justify-center items-center p-1 cursor-pointer">
                  <svg class="h-5" width="24" height="21" viewBox="0 0 24 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.5 17.75H16C18.7614 17.75 21 15.7912 21 13.375C21 10.9588 18.7614 9 16 9H12.5M9.5 17.75H6C3.23858 17.75 1 15.7912 1 13.375C1 10.9588 3.23858 9 6 9H9.5" stroke="#1C64F2" stroke-width="1.5" stroke-linecap="round"/>
<path d="M8 13.375H14" stroke="#1C64F2" stroke-width="1.5" stroke-linecap="round"/>
<path d="M20 2L20.2211 2.59745C20.511 3.38088 20.656 3.77259 20.9417 4.05834C21.2274 4.34409 21.6191 4.48903 22.4025 4.77892L23 5L22.4025 5.22108C21.6191 5.51097 21.2274 5.65592 20.9417 5.94166C20.656 6.22741 20.511 6.61912 20.2211 7.40255L20 8L19.7789 7.40255C19.489 6.61913 19.344 6.22741 19.0583 5.94166C18.7725 5.65591 18.3809 5.51097 17.5975 5.22108L17 5L17.5975 4.77892C18.3809 4.48903 18.7725 4.34409 19.0583 4.05834C19.344 3.77259 19.489 3.38088 19.7789 2.59745L20 2Z" fill="#1C64F2" stroke="#1C64F2" stroke-linejoin="round"/>
</svg>

              </div>

              </div>
          </div>
          <div  class="max-h-full overflow-scroll">

          <div class="pl-3 my-1 rounded-md ml-3" :id="`layer-${id}`" :class="$route.query.layerId===id?'bg-blue-50':''" v-for="item,id,index in hotSpotList" :key="index" >
          <div class="h-8 px-1 w-full flex items-center gap-1.5 cursor-pointer " >
              <div class="flex-1 flex items-center">

    <ClickToEditText  v-if="enableEditLayerName===item.layer_id" :text="item.name ? item.name:item.layer_id" @submitEditedText="(text)=>handleEditLayerName(gsplatSvgId,item.layer_id,text)" />
    <h2 v-else @click="handleTextClick(item.layer_id)"  class="text-xs leading-4 text-gray-900 font-semibold ml-1 overflow-hidden text-ellipsis whitespace-nowrap max-w-20">{{ index + 1 + ')' }} {{  item.name?item.name:id }}</h2>

              </div>
              <div class="w-14 flex-0 flex items-center justify-between  h-full">
                <div class="flex justify-center items-center p-1 cursor-pointer">
                  <button class="flex justify-center items-center w-6 h-6 rounded-md cursor-pointer"  @click.stop="()=>{openLayerDeleteModal=true;layerToDelete=item.layer_id;parentSvgId=svg._id}">
                <svg class="fill-red-500 w-4.5 h-4.5" width="14" height="15" viewBox="0 0 14 15" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1268_12790)">
<path d="M12.7024 3.44737H9.85055V1.97368C9.85055 1.58284 9.70032 1.208 9.43291 0.931632C9.16549 0.655263 8.8028 0.5 8.42462 0.5H5.57277C5.19459 0.5 4.8319 0.655263 4.56449 0.931632C4.29708 1.208 4.14685 1.58284 4.14685 1.97368V3.44737H1.29499C1.1059 3.44737 0.92456 3.525 0.790853 3.66318C0.657147 3.80137 0.582031 3.98879 0.582031 4.18421C0.582031 4.37963 0.657147 4.56705 0.790853 4.70524C0.92456 4.84342 1.1059 4.92105 1.29499 4.92105H2.00796V13.0263C2.00796 13.4172 2.15819 13.792 2.4256 14.0684C2.69301 14.3447 3.0557 14.5 3.43388 14.5H10.5635C10.9417 14.5 11.3044 14.3447 11.5718 14.0684C11.8392 13.792 11.9894 13.4172 11.9894 13.0263V4.92105H12.7024C12.8915 4.92105 13.0728 4.84342 13.2065 4.70524C13.3403 4.56705 13.4154 4.37963 13.4154 4.18421C13.4154 3.98879 13.3403 3.80137 13.2065 3.66318C13.0728 3.525 12.8915 3.44737 12.7024 3.44737ZM5.57277 1.97368H8.42462V3.44737H5.57277V1.97368ZM6.28574 11.5526C6.28574 11.7481 6.21062 11.9355 6.07691 12.0737C5.94321 12.2118 5.76186 12.2895 5.57277 12.2895C5.38368 12.2895 5.20234 12.2118 5.06863 12.0737C4.93492 11.9355 4.85981 11.7481 4.85981 11.5526V6.39474C4.85981 6.19931 4.93492 6.0119 5.06863 5.87371C5.20234 5.73553 5.38368 5.65789 5.57277 5.65789C5.76186 5.65789 5.94321 5.73553 6.07691 5.87371C6.21062 6.0119 6.28574 6.19931 6.28574 6.39474V11.5526ZM9.13759 11.5526C9.13759 11.7481 9.06247 11.9355 8.92877 12.0737C8.79506 12.2118 8.61371 12.2895 8.42462 12.2895C8.23553 12.2895 8.05419 12.2118 7.92048 12.0737C7.78678 11.9355 7.71166 11.7481 7.71166 11.5526V6.39474C7.71166 6.19931 7.78678 6.0119 7.92048 5.87371C8.05419 5.73553 8.23553 5.65789 8.42462 5.65789C8.61371 5.65789 8.79506 5.73553 8.92877 5.87371C9.06247 6.0119 9.13759 6.19931 9.13759 6.39474V11.5526Z"/>
</g>
<defs>
<clipPath id="clip0_1268_12790">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
</button>
                  <svg v-if="item.type" width="10" height="10" class="h-3 w-3 fill-green-500" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                   <g clip-path="url(#clip0_723_21212)">
                   <path d="M3.5777 9.1678C3.4145 9.16858 3.25752 9.09192 3.14033 8.95422L0.18872 5.48236C0.129889 5.41274 0.082947 5.32975 0.0505741 5.23814C0.0182012 5.14653 0.00103144 5.04808 4.5112e-05 4.94842C-0.00194686 4.74716 0.0621007 4.55317 0.178098 4.40915C0.294096 4.26512 0.452541 4.18285 0.618578 4.18044C0.784615 4.17803 0.944643 4.25566 1.06346 4.39627L3.5802 7.35538L8.9361 1.05014C9.05508 0.909535 9.21526 0.831977 9.38142 0.834534C9.54757 0.837091 9.70608 0.919552 9.82208 1.06378C9.93808 1.208 10.0021 1.40218 9.99995 1.60359C9.99784 1.805 9.92981 1.99714 9.81083 2.13775L4.01507 8.95422C3.89789 9.09192 3.74091 9.16858 3.5777 9.1678Z" fill="#0E9F6E"/>
                   </g>
                   <defs>
                   <clipPath id="clip0_723_21212">
                   <rect width="10" height="10" fill="white" transform="translate(0 0.000976562)"/>
                   </clipPath>
                   </defs>
                   </svg>

<svg v-else class="h-3 w-3 fill-orange-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="info"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm1 14a1 1 0 0 1-2 0v-5a1 1 0 0 1 2 0zm-1-7a1 1 0 1 1 1-1 1 1 0 0 1-1 1z"/></g></g></svg>

              </div>
                <button class="flex justify-center items-center w-6 h-6 rounded-md fill-gray-900 cursor-pointer"  @click="() => handleLayerSelection(id)" >
                 <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>
                </button>
              </div>
          </div>

          <!-- <UpdateProjectSvgLayers
         v-if="$route.query.layerId===layer.layer_id && sceneDetails.sceneData.type !== 'earth'"
         @closeModal="() => router.push({ path: route.path, query: { svgId: route.query.svgId } })"
         :landmarks="isMasterScene ? null : projectStore.landmarks"
         :categoryList="isMasterScene ? null : categoryList"
          /> -->
          <Hotspot v-if="route.query.layerId === item.layer_id"
                        @updateGsplatLayers = "HandleUpdateGsplatLayers"
                        :landmarks="isMasterScene ? false :projectStore.landmarks"
                        :loader="loader"
                        :svgData="isMasterScene ? newOrganizationStore.masterScenes[sceneId].svgData :projectStore.scenes[sceneId].svgData"
                        :scenes="isMasterScene ? newOrganizationStore.masterScenes : projectStore.scenes"
                        :projects="newOrganizationStore.projects"
                        :projectId="projectId"
                        :defaultPostion="updateHotSpotPosition"
                        :defaultScale="updatePlaneScale"
                        type="edit"
                        :isPlane="svgLayers?.[route.query.layerId]?.type !== 'plane' ? false  : true"
                        >

                </Hotspot>

          <!-- <RightSidebar v-if="isMasterScene && $route.query.layerId===layer.layer_id && sceneDetails.sceneData.type !== 'earth'"
      :open="$route.query.svgId && $route.query.layerId && newOrganizationStore.masterScenes?.[sceneId]"
      @closeModal="handleCloseUpdateSidebar">
      <UpdateMasterSvgLayers
        @closeModal="() => router.push({ path: route.path, query: { svgId: route.query.svgId } })"
        @UpdateSvgLayer="HandleUpdateSvgLayer"
        :loader="loader"
        :svgData="newOrganizationStore.masterScenes?.[sceneId].svgData"
        :scenes="newOrganizationStore.masterScenes" />
    </RightSidebar> -->
          </div>
        </div>
      </div>
        </div>
        <Modal :open="openLayerDeleteModal">
      <DeleteModalContent :trash="false" :loader="layerDeleteLoader" @closeModal="(e) => openLayerDeleteModal = false"
        @handleDelete="handleMoveLayerToTrash" :dataName="'Layer'" />
    </Modal>
      </div>
      <div v-else-if="selectedMenu==='icons'"
  class="h-full  w-[200px]  bg-white rounded-t-lg py-3 flex flex-col">

  <div class="flex justify-between items-center px-3">
          <h2 class=" text-gray-500 text-lg font-semibold ml-1">
            Icons
          </h2>
          <button class="relative flex justify-center items-center w-6 h-6 rounded-md cursor-pointer" :class="openAddIcons?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'"
          @click="openAddIcons=!openAddIcons"
          >
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>
                <CreateIcons v-if="openAddIcons" class="fixed left-1/2 top-1/2 transform -translate-y-1/2 z-100" @close="openAddIcons=false"/>
        </div>

</div>

      <AddSceneOptionsMenu :style="{top:`${createSceneClick.y}px` , left:`${createSceneClick.x}px`}" class="fixed left-36 top-8 z-100" v-if="openAddSceneOptionsMenu" :category="openAddSceneOptionsMenu==='UNCATEGORIZED'?'':openAddSceneOptionsMenu" @close="openAddSceneOptionsMenu=!openAddSceneOptionsMenu"/>
    </div>

</template>

<style scoped>
</style>
