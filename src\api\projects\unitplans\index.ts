import type { createHotspots, createUnitplanType, deleteHotspot, editHotspots, unitplanTrashType, unitplanType, updateUnitplanInterface } from '@/types/unitplan';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function createUnitplan (data:createUnitplanType) : Promise<unitplanType |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unitplan/createUnitplan`, body: data }).then((res) => {
      resolve(res as unitplanType);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function getListofUnitplan (project_id: string): Promise <Record<string, unitplanType>> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/unitplan/getListofUnitplan/${project_id}` }).then((res) => {
      resolve(res as Record<string, unitplanType>);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function getUnitplan (unitplan_id:string, project_id:string) : Promise<unitplanType | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unitplan/getUnitplan/${project_id}/${unitplan_id}`, body: {}}).then((res) => {
      resolve(res as unitplanType);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function  EditUnitplan (data:updateUnitplanInterface):Promise<unitplanType |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/unitplan/EditUnitplan`,
      body: data,
    })
      .then((res) => {
        resolve(res as unitplanType);

      })
      .catch((err) => {
        reject(err);
      });

  });
}

export async function moveUnitplanToTrash (data: unitplanTrashType, project_id:string) : Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unitplan/moveToTrash/${project_id}`, body: data }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function getStyles (projectId:string): Promise<string[] | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/unitplan/getStyles?project_id=${projectId}` }).then((res) => {
      resolve(res as string[]);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function getTypes (projectId:string) : Promise<string[] | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/unitplan/getTypes?project_id=${projectId}` }).then((res) => {
      resolve(res as string[]);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function createHotspots (data: createHotspots) : Promise<object>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unitplan/createHotspots`, body: data }).then((res) => {
      resolve(res as object);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function editHotspots (data:editHotspots) : Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unitplan/editHotspots`, body: data }).then((res) => {
      resolve(res as object);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function deleteHotspots (data:deleteHotspot):Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unitplan/deleteHotspots`, body: data }).then((res) => {
      resolve(res as object);
    }).catch((err) => {
      reject(err);
    });
  });
}
