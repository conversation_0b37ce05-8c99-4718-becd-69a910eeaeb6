import axios from 'axios';

export function cdn (path: string): string {
  if (path !== undefined && path.includes(import.meta.env.VITE_APP_BUCKET) ) {
    if (path.includes('?alt')) {
      path = path.substring(0, path.lastIndexOf('?alt'));
    }
    path = path.replace('firebasestorage.googleapis.com/v0/b/' + import.meta.env.VITE_APP_BUCKET + '/o', import.meta.env.VITE_APP_BUCKET_CDN);
    return path;
  }

  if (path !== undefined && path.includes('propvr-in-31420.appspot.com') ) {
    if (path.includes('?alt')) {
      path = path.substring(0, path.lastIndexOf('?alt'));
    }
    path = path.replace('firebasestorage.googleapis.com/v0/b/' + 'propvr-in-31420.appspot.com' + '/o', 'storagecdn.propvr.ai');
    return path;
  }
  return path;
}

export function  loadImageData (imageUrl: string, progresscallback: (progress: number) => void, loadedCallback: () => void) {
  return new Promise((resolve, reject) => {
    // Console.log(imageUrl)
    const xhr = new XMLHttpRequest();
    xhr.open('GET', imageUrl, true);
    xhr.responseType = 'blob';

    xhr.onprogress = (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded / event.total) * 100);
        // Console.log(progress)
        if (progresscallback) {
          progresscallback(progress);
        }
        xhr.dispatchEvent(new CustomEvent('progress', { detail: { progress } }));
      }
    };

    xhr.onload = function () {
      if (xhr.status === 200) {
        const blob = xhr.response;
        const reader = new FileReader();
        reader.onloadend = function () {
          resolve(reader.result);
          if (loadedCallback){
            loadedCallback();
          }
        };
        reader.readAsDataURL(blob);
      } else {
        reject(new Error(`Failed to fetch image. Status: ${xhr.status}`));
      }
    };

    xhr.onerror = function () {
      reject(new Error('Network error occurred while fetching image.'));
    };

    xhr.send();
  });
}

export async function GetRequest (url: string): Promise<string> {
  console.log(url);
  return new Promise((resolve, reject) => {
    const config = {
      method: 'GET',
      url: url,
    };
    axios(config)
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}
