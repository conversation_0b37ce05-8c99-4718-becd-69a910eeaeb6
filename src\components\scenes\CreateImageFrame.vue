<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../common/Spinner.vue';
import { Form, Field, ErrorMessage, FieldArray } from 'vee-validate';
import { CreateImageFrameSchema } from '../../validationSchema/scene';
import { getCookie } from '../../helpers/domhelper';

import { uiOperations } from '../../store/uiOperations';
import Modal from '../common/Modal/Modal.vue';
import { useRouter } from 'vue-router';
import { createScene, getAllScenesFrames } from '../../api/projects/scene/index';
import {createMasterScene, getAllScenesFrames as getAllMasterScenesFrames} from '../../api/masterScene/index';
import { isMasterScenePath, resizeImage } from '../../helpers/helpers';

const uiStore = uiOperations();
const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const loader = ref(false);
const totalExistingFrames = ref(null);

const uploadedFiles = ref();
const initialData = ref();

if (isMasterScene.value){
  getAllMasterScenesFrames(sceneId.value).then((res) => {
    console.log(Object.keys(res).length);
    totalExistingFrames.value = Object.keys(res).length;
  });
} else {
  getAllScenesFrames(projectId.value, sceneId.value).then((res) => {
    console.log(Object.keys(res).length);
    totalExistingFrames.value = Object.keys(res).length;
  });
}

watch(uploadedFiles, async (values) => {
  console.log(values);
  const Frame = await Promise.all(values.map(async (file, index) => {
    console.log(index);
    if (!file) {
      return null;
    }

    // Finding the file type and name
    const fileType = file.type.split('/').slice(0, -1).join('');
    const fileName = file.name.split('.').slice(0, -1).join('.');
    const fileSize = (file.size / (1024 * 1024)).toFixed(3); // Convert to MB

    let resizedThumbnail = null;
    // Image resize on type image
    if (fileType==='image'){
      resizedThumbnail = await resizeImage(file, 1280, 720);
    }
    // Function to read file and return a promise that resolves to a data URL for preview image
    const readFileAsDataURL = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsDataURL(file);
      });
    };
    const preview = await readFileAsDataURL(resizedThumbnail?resizedThumbnail:file);

    return {file: file, thumbnail: resizedThumbnail, preview: preview, type: 'rotatable_image_frame', name: fileName, size: fileSize};
  }));

  const obj = { Frame: Frame.filter((item) => !!item) }; // Filter out null results if any
  initialData.value = obj;
});

const handleSubmit = async (values) => {
  console.log(values);

  const organization = getCookie('organization');
  return new Promise((outerResolve, outerReject) => {
    loader.value = true;
    Promise.all(values.Frame.map(async (elem, index) => {
      const formData = new FormData();
      formData.append('organization_id', organization);
      formData.append('type', 'rotatable_image_frame');
      formData.append('name', elem.name);
      formData.append('order', totalExistingFrames.value+index+1);
      formData.append('highRes', elem.file);
      if (!isMasterScene.value){
        formData.append('project_id', projectId.value);
      }
      formData.append('parent', sceneId.value);
      formData.append('lowRes', elem.thumbnail);

      if (isMasterScene.value){
        await createMasterScene(formData).then(() => {
        });
      } else {
        await createScene(formData).then(() => {
        });
      }
    })).then(() => {
      loader.value = false;
      document.dispatchEvent(new Event('getRotatableImageFrames'));
      router.go(-1);

      outerResolve();
    }).catch((err) => {
      loader.value = false;
      console.log(err);
      uiStore.handleApiErrorMessage(err.message._message);
      outerReject();
    });
  });
};

</script>

<template>
    <Modal :open="true">
        <div
            class=" relative transform overflow-hidden rounded-t-2xl rounded-b-none sm:rounded-t-lg sm:rounded-b-lg text-left shadow-xl transition-all sm:max-w-3xl bg-white border border-gray-200 w-[536px] rounded-lg">
            <div class="h-9  flex justify-between items-center px-2.5 border-b border-gray-200 rounded-t-lg">
                    <p class="text-sm text-gray-900 font-semibold">
                        Upload From PC</p>
                        <button  class="fill-gray-400" @click="() => router.go(-1)">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
                </div>
            <div class="p-3 sm:p-6 h-full overflow-y-scroll">
                <div v-if="!initialData"
                            class="relative w-full h-fit flex-col col-span-2 justify-start items-start inline-flex bg-inherit row-span-2">
                            <label for="file"
                                class="text-sm font-medium text-gray-900">Upload the Images</label>
                            <div class="w-full">
                                    <label class="mb-0 w-full h-28 rounded-lg border-2 border-dashed border-gray-200 py-3 px-1 cursor-pointer">
              <div class="flex justify-center items-center gap-2">
                <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_22140)">
<path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
<path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
</g>
<defs>
<clipPath id="clip0_723_22140">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
           </svg>
           <p class="text-xs font-medium text-gray-500 mt-2">Upload</p>
</div>
<div>
  <p class="text-xs font-medium text-gray-500 text-center">16:9 Resolution 3840 x 2160px Sequence of 200 images (Jepg,Webp)</p>
  <Field   v-if="totalExistingFrames!==null" type="file"
                                v-model="uploadedFiles"
                                   multiple
                                    name="files"
                                    id="files"
                                    autocomplete="files"
                                    placeholder="Upload multiple files"
                                    class="hidden"/>
</div>
            </label>

                                <ErrorMessage as="p"
                                    class="ml-1 text-xs text-rose-500 mt-1"
                                    name="file" />
                            </div>
                            <div class="mt-2" v-if="uploadProgess">
                            <div  v-if="!isNaN(uploadProgess)" class="w-full h-[13px] overflow-hidden bg-slate-300 rounded">
                                <div class="h-fit text-[9.5px] flex justify-center items-center text-white bg-[#36f] hover:bg-[#4572fc]"
                                        :style="{ width: uploadProgess + '%' }">
                                       {{ uploadProgess }}%
                                 </div>
                            </div>
                            <div v-else class=" w-full h-fit text-xs text-start text-success bg-transparent">
                                     {{ uploadProgess }}
                               </div>

                            </div>
                        </div>
                <Form class="mb-2" v-else :validation-schema="CreateImageFrameSchema" :initial-values="initialData"
                    @submit="handleSubmit">
                    <div class="h-fit">
                      <div class="py-2 sm:p-6 h-full">Existing Frames : <span class="font-semibold">{{totalExistingFrames }}</span></div>
                <div class="max-h-60 w-full col-span-2 flex flex-col gap-1 overflow-scroll p-2">

                    <FieldArray name="Frame" v-slot="{ fields, remove }">
                                <div  v-for="(field, idx) in fields"
                                :key="field.key" >
                                <div class="h-12 w-full flex">
                                <div class="flex justify-center items-center"><p class="w-4 text-gray-900 text-sm font-semibold leading-relaxed">{{ totalExistingFrames+idx+1 }}</p>
                                </div>
                                <div class="h-full px-2 flex justify-center items-center w-fit"><svg width="18" height="21" class="fill-gray-400" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.3188 0.5H7.95834V5.5C7.95834 6.603 7.02396 7.5 5.875 7.5H0.666672V18.5C0.666672 19.603 1.5698 20.5 2.68126 20.5H15.3198C16.4302 20.5 17.3333 19.603 17.3333 18.5V2.5C17.3333 1.397 16.4302 0.5 15.3188 0.5ZM11.6042 6.5C12.4667 6.5 13.1667 7.172 13.1667 8C13.1667 8.828 12.4667 9.5 11.6042 9.5C10.7417 9.5 10.0417 8.828 10.0417 8C10.0417 7.172 10.7417 6.5 11.6042 6.5ZM13.9177 17.015C13.7292 17.316 13.3906 17.5 13.024 17.5H4.69063C4.33334 17.5 4.00105 17.324 3.80938 17.034C3.61771 16.744 3.59584 16.38 3.75001 16.07L6.49688 10.539C6.675 10.181 7.03126 9.958 7.47292 9.97C7.88646 9.984 8.25313 10.231 8.40626 10.601L9.63751 13.582L10.201 12.585C10.3844 12.262 10.6865 12.06 11.1208 12.063C11.5042 12.064 11.8552 12.268 12.0365 12.593L13.9448 16.031C14.1156 16.34 14.1052 16.713 13.9177 17.015Z"/>
<path d="M5.875 5.5V0.63C5.37292 0.768 4.9073 1.014 4.52813 1.379L1.5823 4.207C1.20313 4.571 0.94688 5.018 0.802089 5.5H5.875Z" />
</svg>
</div>
                                <!-- <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <Field :id="`name_${idx}`" :name="`Frame[${idx}].name`" style="width: auto;" class="input-primary" />
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Frame[${idx}].name`" />
                                </td> -->

                                <div class="flex-1 px-2 flex flex-col justify-center items-start">
                                  <Field :id="`name_${idx}`" :name="`Frame[${idx}].name`" class="text-gray-900 text-sm font-semibold leading-relaxed" />
                                <p class="text-gray-500 text-xs font-semibold leading-tight">{{ field.value.size }} MB</p>

                            </div>

                                <button type="button" @click="()=>{initialData.Frame.splice(idx, 1);remove(idx)}" class="h-full px-2 flex justify-center items-center w-fit">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 fill-gray-500" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close-circle"><rect width="24" height="24" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm2.71 11.29a1 1 0 0 1 0 1.42 1 1 0 0 1-1.42 0L12 13.41l-1.29 1.3a1 1 0 0 1-1.42 0 1 1 0 0 1 0-1.42l1.3-1.29-1.3-1.29a1 1 0 0 1 1.42-1.42l1.29 1.3 1.29-1.3a1 1 0 0 1 1.42 1.42L13.41 12z"/></g></g></svg>
                            </button>
                              </div>
                              <ErrorMessage  class="text-xs text-rose-500 mt-1" :name="`Frame[${idx}].file`" />
                              </div>
                            </FieldArray>
                    </div>
                    </div>

                    <div
                        class="mt-2 flex justify-center">
                        <button type="submit"
                           :disabled="loader"
                            class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">Add Scene
                            <Spinner v-if="loader" />
                        </button>
                    </div>
                </Form>

            </div>
        </div>
    </Modal>
</template>

<style>
</style>
