
type Unit = string;
export enum BuildingType {
  TOWER = 'tower',
  VILLA = 'villa'
}
export type Floor = {
    floor_id:string;
    floor_name: string;
    units:Array<Unit>;
 }
export type Building = {
  _id: string
  project_id: string;
  name: string;
  type:string;
  total_floors:number;
  floors: Record<string, Floor>;
  community_id:string;
};

export type FloorsRange = {
  minFloor: number,
  maxFloor: number
}

export type FilterBuilding = {
  id:string,
  name:string,
  floors: FloorsRange
}

export type transformedFilterBuilding = {
  [key:string]:FilterBuilding
}
export type updateBuilding = {
  name?:string,
  total_floors?:number,
  type?:string,
  floors?:Array<string>,
  community_id?:string
}

export type deleteBuilding = {
  building_id : string[],
  timetimeStamp: string
}

export type bulkFloorUpdateQuery = {
  floor_id:string
  order?: number,
}

export type bulkFloorUpdateType = {
    query:bulkFloorUpdateQuery[],
    project_id:string
}

export type deleteFloorType = {
  project_id:string
  floor_id:string
  building_id : string[],
}
export type addNewFloorType = {
  project_id:string
  floor_name:string
  building_id : string[],
}

export type renameFloorType = {
  project_id:string
  floor_id:string
  building_id : string[]
  name:string,
}
