<script setup>
import Modal from '@/components/common/Modal/Modal.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { projectSchema } from '../../validationSchema/project';
import { countryCity } from '../../helpers/constants';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { EditProject} from '@/api/projects';
import Spinner from '@/components/common/Spinner.vue';
import DnDFileUploader from '@/components/common/DnDFileUploader.vue';
import { Org_Store } from '../../store/organization';
import { processFirebaseFile } from '@/helpers/helpers';
import Multiselect from 'vue-multiselect';
import { UserStore } from '@/store';

const Store = UserStore();
const OrganizationStore = Org_Store();
const router = useRouter();
const route = useRoute();
// const propertyTypeOptions = ref(['Villa', 'Tower', 'Both']);
const experienceOptions = ref([{name: 'RT Application', value: 'rt_application'}, {name: 'PropVR 360', value: 'propvr_360'}, {name: 'Embed V1', value: 'embed_v1'}]);
const propertyTypeOptions = ref([{name: 'Villa', value: 'villa'}, {name: 'Tower', value: 'tower'}, {name: 'Both', value: 'both'}]);
const updatedfile = ref();
const successModal = ref(false);
const openModal = ref(true);
const loader = ref(false);
const ProjectName  = ref('');
const Country  = ref('');
const City  = ref();
const Experience  = ref([]);
const propertyType  = ref({});
const projectThumbnail  = ref();
const initialValue  = ref({});
onMounted(async () => {
  console.log("route", route.params.project_id);

  if (route.params.project_id){
    console.log("store", OrganizationStore.projects[route.params.project_id]);
    const data = OrganizationStore.projects[route.params.project_id];
    ProjectName.value = data.name;
    Country.value = data.country;
    City.value = data.city;
    experienceOptions.value.map((item) => {
      if (data.experience?.includes(item.value)){
        Experience.value.push(item);
      }
      return 0;
    });
    propertyType.value.name = data.property_type.charAt(0).toUpperCase() + data.property_type.slice(1);
    propertyType.value.value =  data.property_type;
    await processFirebaseFile(data.project_thumbnail).then((imageBlob) => {
      projectThumbnail.value =  imageBlob;
    });
    initialValue.value = {
      projectName: data.name,
      country: data.country,
      city: data.city,
      experience: data.experience,
      propertyType: data.property_type,
    };
  }
});
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};
const exportExperience = [];
const handleForm = (values) => {

  const prevData = initialValue.value;
  console.log("ini", initialValue.value);
  //   const { project_thumbnail, ...newCompareObj } = values;
  const newCompareObj = Object.fromEntries(
    Object.entries(values).filter(([key]) => key !== 'projectLogo'),
  );
  for (const key in values.experience){
    exportExperience[key] = values.experience[key].value;
  }
  newCompareObj.experience = exportExperience;
  // newCompareObj.Property_Type = String(propertyType.name).toLowerCase();
  console.log("newCompareObj", newCompareObj);

  const formData = new FormData(); // FormData
  formData.append('id', route.params.project_id );

  const params = frameParms(prevData, newCompareObj);
  console.log("params", params);
  if (Object.keys(params).length > 0 || updatedfile.value){
    loader.value = true;
    params.projectName && formData.append('name', params.projectName);
    params.experience && formData.append('experience', params.experience);
    params.propertyType && formData.append('property_type', params.propertyType);
    params.country && formData.append('country', params.country);
    params.city && formData.append('city', params.city);
    updatedfile.value && formData.append('project_thumbnail', updatedfile.value);

    EditProject(formData).then(() => {
      openModal.value = false;
      successModal.value = true;
    }).catch((err) => {
      console.log("Error", err);

    }).finally(() => {
      loader.value = false;
    });
  }

};

</script>

<template>
    <div v-if="!Store.isMobile" class="select-none">
        <Modal :open="openModal">
            <div class="modal-content-primary sm:max-w-3xl p-3 sm:p-6">
                <div class="">
                    <h1 class="text-xl font-bold">Edit Project</h1>
                </div>
                <div class="absolute top-6 right-6 cursor-pointer" @click="router.go(-1)">
                    <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="x" clip-path="url(#clip0_707_6588)">
                        <path id="Vector" d="M5.90741 5L9.30409 1.60332C9.36538 1.54412 9.41427 1.47331 9.4479 1.39502C9.48153 1.31672 9.49924 1.23252 9.49998 1.14731C9.50072 1.0621 9.48448 0.977595 9.45221 0.898729C9.41995 0.819863 9.3723 0.748212 9.31204 0.687958C9.25179 0.627705 9.18014 0.580054 9.10127 0.547787C9.0224 0.515521 8.9379 0.499284 8.85269 0.500024C8.76748 0.500765 8.68328 0.518468 8.60498 0.5521C8.52669 0.585733 8.45588 0.634621 8.39668 0.695913L5 4.09259L1.60332 0.695913C1.48229 0.579017 1.32019 0.514333 1.15193 0.515796C0.983666 0.517258 0.822712 0.584748 0.70373 0.70373C0.584748 0.822712 0.517258 0.983666 0.515796 1.15193C0.514333 1.32019 0.579017 1.48229 0.695913 1.60332L4.09259 5L0.695913 8.39668C0.634621 8.45588 0.585733 8.52669 0.5521 8.60498C0.518468 8.68328 0.500765 8.76748 0.500024 8.85269C0.499284 8.9379 0.515521 9.0224 0.547787 9.10127C0.580054 9.18014 0.627705 9.25179 0.687958 9.31204C0.748212 9.3723 0.819863 9.41995 0.898729 9.45221C0.977595 9.48448 1.0621 9.50072 1.14731 9.49998C1.23252 9.49924 1.31672 9.48153 1.39502 9.4479C1.47331 9.41427 1.54412 9.36538 1.60332 9.30409L5 5.90741L8.39668 9.30409C8.51771 9.42098 8.67981 9.48567 8.84807 9.4842C9.01633 9.48274 9.17729 9.41525 9.29627 9.29627C9.41525 9.17729 9.48274 9.01633 9.4842 8.84807C9.48567 8.67981 9.42098 8.51771 9.30409 8.39668L5.90741 5Z" fill="#6B7280"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_707_6588">
                        <rect width="10" height="10" fill="white"/>
                        </clipPath>
                        </defs>
                    </svg>
                </div>
        <Form :validation-schema="projectSchema" @submit="handleForm"  v-slot="{ values }" class="flex flex-col justify-center mt-[-8px]">
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-4">
            <div class="col-span-auto">
              <label for="projectLogo" class="text-sm font-medium text-black">Upload Project Logo / Image</label>
              <div class="h-[220px] relative flex justify-center items-center bg-gray-50 !border-[1px] border-gray-400 border-opacity-100 rounded-md">
            <Field name="projectLogo" id="projectLogo" :model-value="updatedfile || projectThumbnail" >
                    <DnDFileUploader  inputType="image/*" class="!w-full !h-full"
                    inputPlaceholder="Click to upload or drag & drop files here" @fileData="(val)=>{ updatedfile = val}"
                    :previousFileData="projectThumbnail" :key="projectThumbnail"/>
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 absolute bottom-[-20px] capitalize" name="projectLogo" />
              </div>
            </div>
            <div class="col-span-auto h-[270px] max-h-[270px] flex flex-col justify-between">
                <div class="h-[90px]">
                    <label for="projectName" class="label-primary">Project Name*</label>
                    <div class="mt-1">
                        <Field type="text" v-model="ProjectName" name="projectName" id="projectName" autocomplete="projectName" class="input-primary w-full !bg-gray-50"
                        placeholder="Enter Project Name" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="projectName" />
                    </div>
                </div>
                <div class="h-[90px]">
                    <label for="country" class="label-primary">Country*</label>
                    <div class="mt-1">
                        <Field name="country" v-model="Country"  v-slot="{field}"
                         class="!bg-gray-50" >
    <multiselect v-model="Country" @select="()=>{City = null;values.City=null}" :options="Object.keys(countryCity)" :searchable="false" :close-on-select="true" :show-labels="false"
                 placeholder="Select a Country" aria-label="Select a Country" v-bind="field" class="!bg-gray-50" maxHeight="150"></multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="country" />
                    </div>
                </div>
                <div class="h-[90px]">
                    <label for="city" class="label-primary">City/Area*</label>
                    <div class="mt-1">
                        <Field name="city" :model-value="City" v-slot="{field}" >
    <multiselect v-model="City" :options="Country ?countryCity[Country]?.cities:[]" :searchable="false" :close-on-select="true" :show-labels="false"
                 placeholder="Select a City" aria-label="Select a City" v-bind="field" class="!bg-gray-50" maxHeight="150"></multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="city" />
                    </div>
                </div>
            </div>
            <div class="col-span-auto">
                <div class="h-[90px]">
                    <label for="propertyType" class="label-primary">Property Type*</label>
                    <div class="mt-2">
                        <Field name="propertyType"  :model-value="propertyType.value" v-slot="{ field }" class="!bg-gray-50">
                        <multiselect v-model="propertyType" deselect-label="Can't remove this value" track-by="name" label="name"
                                    placeholder="Select one" aria-label="Select one" :options="propertyTypeOptions" :searchable="false" :allow-empty="false"
                                     @select="(val) =>propertyType = val" v-bind="field" class="!bg-gray-50">
                       <template v-slot:singleLabel="{ option }">
                        <div class="relative top-[2px] !bg-gray-50">
                            <p v-if="option.name" class="text-black">{{ option.name }}</p>
                            <p v-else class="text-gray-500 ">Select one</p>
                        </div>
                        </template>
                        </multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="propertyType" />
                    </div>
                </div>
            </div>
            <div class="col-span-auto">
                <div class="h-[90px]">
                    <label for="experience" class="label-primary">Experience*</label>
                    <div class="mt-2">
                         <Field  name="experience" :model-value="Experience" v-slot="{ field }">
                        <Multiselect
                        v-model="Experience"
                        :allow-empty="true"
                        :multiple="true"
                        v-bind="field"
                        track-by="name" label="name"
                        :searchable="false"
                        :taggable="true"
                        deselectLabel="remove"
                        :close-on-select="false" :clear-on-select="false"
                         class="!bg-gray-50"
                         placeholder="Choose"
                         :options="experienceOptions" maxHeight="250">
                        </Multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="experience" />
                    </div>
                </div>
            </div>
          </div>
          <div class="mt-4 sm:mt-4 flex justify-end gap-x-3">
            <!-- <button type="button" class="cancel-btn-primary !bg-white !border" @click="router.go(-1)" ref="cancelButtonRef">
              Clear
            </button> -->
            <button type="submit" class="hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 h-8 sm:h-10
            rounded-lg flex flex-row justify-center items-center gap-2">
              <span class="text-center text-inherit whitespace-nowrap">Save changes</span>
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>

    </div>
        </Modal>
        <Modal :open="successModal">
         <div class="w-[20%] h-[25%] relative flex flex-col justify-center bg-white rounded-lg  shadow-xl gap-4">
            <div @click="()=>router.go(-1)" class="absolute top-4 right-3 cursor-pointer">
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="x">
                <path id="Vector" d="M7.0885 6.50001L11.1645 2.42399C11.2381 2.35295 11.2967 2.26798 11.3371 2.17403C11.3774 2.08007 11.3987 1.97902 11.3996 1.87677C11.4005 1.77452 11.381 1.67312 11.3423 1.57848C11.3035 1.48384 11.2464 1.39786 11.1741 1.32556C11.1018 1.25325 11.0158 1.19607 10.9211 1.15735C10.8265 1.11863 10.7251 1.09915 10.6228 1.10004C10.5206 1.10092 10.4195 1.12217 10.3256 1.16253C10.2316 1.20289 10.1467 1.26155 10.0756 1.3351L5.99961 5.41112L1.92359 1.3351C1.77835 1.19483 1.58383 1.11721 1.38192 1.11896C1.18001 1.12072 0.986864 1.2017 0.844085 1.34448C0.701307 1.48726 0.620319 1.68041 0.618564 1.88232C0.616809 2.08423 0.694429 2.27875 0.834705 2.42399L4.91072 6.50001L0.834705 10.576C0.761155 10.6471 0.702488 10.732 0.662129 10.826C0.621771 10.9199 0.600527 11.021 0.599638 11.1232C0.59875 11.2255 0.618234 11.3269 0.656954 11.4215C0.695674 11.5162 0.752855 11.6021 0.825159 11.6745C0.897464 11.7468 0.983445 11.8039 1.07808 11.8427C1.17272 11.8814 1.27413 11.9009 1.37638 11.9C1.47863 11.8991 1.57968 11.8778 1.67363 11.8375C1.76758 11.7971 1.85256 11.7385 1.92359 11.6649L5.99961 7.58889L10.0756 11.6649C10.2209 11.8052 10.4154 11.8828 10.6173 11.8811C10.8192 11.8793 11.0124 11.7983 11.1551 11.6555C11.2979 11.5127 11.3789 11.3196 11.3807 11.1177C11.3824 10.9158 11.3048 10.7213 11.1645 10.576L7.0885 6.50001Z" fill="#6B7280"/>
                </g>
                </svg>

            </div>
            <div class="w-full flex items-center justify-center">
                <span class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="check">
                    <path id="Vector" d="M7.15541 18.3333C6.82899 18.3349 6.51503 18.1816 6.28067 17.9061L0.37744 10.9624C0.259778 10.8232 0.165894 10.6572 0.101148 10.474C0.0364024 10.2908 0.00206288 10.0939 9.0224e-05 9.89456C-0.00389373 9.49202 0.124201 9.10406 0.356196 8.81601C0.588191 8.52796 0.905082 8.36342 1.23716 8.35859C1.56923 8.35376 1.88929 8.50904 2.12691 8.79026L7.1604 14.7085L17.8722 2.098C18.1102 1.81678 18.4305 1.66167 18.7628 1.66678C19.0951 1.67189 19.4122 1.83682 19.6442 2.12527C19.8762 2.41372 20.0041 2.80207 19.9999 3.20488C19.9957 3.6077 19.8596 3.99199 19.6217 4.27321L8.03014 17.9061C7.79578 18.1816 7.48182 18.3349 7.15541 18.3333Z" fill="#0E9F6E"/>
                    </g>
                    </svg>
                </span>
            </div>
            <span class="w-full text-center"><p class="text-lg font-semibold">Project Edited Successfully</p></span>
            <button @click="()=>router.go(-1)" class="w-32 bg-[#1a56db] text-white rounded-lg h-10 p-2 mx-auto border !border-blue-500">Okay</button>
        </div>
        </Modal>

    </div>
    <div v-if="Store.isMobile" class="select-none">
        <Modal :open="openModal">
            <div class="modal-content-primary h-full flex flex-col gap-4 px-3 py-2 ">
              <div class="w-full h-[6%] flex gap-4 items-center">
                <div class="cursor-pointer " @click="router.go(-1)">
<svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.925781 10.0273C0.925781 9.66016 1.06641 9.33984 1.34766 9.06641L9.05859 1.36719C9.19922 1.22656 9.34766 1.12109 9.50391 1.05078C9.66016 0.980469 9.82422 0.945312 9.99609 0.945312C10.3555 0.945312 10.6562 1.0625 10.8984 1.29688C11.1406 1.53125 11.2617 1.82422 11.2617 2.17578C11.2617 2.36328 11.2266 2.53516 11.1562 2.69141C11.0859 2.83984 10.9922 2.97266 10.875 3.08984L8.25 5.75L3.89062 9.74609L3.46875 8.98438L7.53516 8.73828H21.7734C22.1641 8.73828 22.4766 8.85938 22.7109 9.10156C22.9531 9.33594 23.0742 9.64453 23.0742 10.0273C23.0742 10.4023 22.9531 10.7109 22.7109 10.9531C22.4766 11.1875 22.1641 11.3047 21.7734 11.3047H7.53516L3.46875 11.0703L3.89062 10.3203L8.25 14.3047L10.875 16.9531C10.9922 17.0703 11.0859 17.207 11.1562 17.3633C11.2266 17.5195 11.2617 17.6875 11.2617 17.8672C11.2617 18.2188 11.1406 18.5117 10.8984 18.7461C10.6562 18.9805 10.3555 19.0977 9.99609 19.0977C9.65234 19.0977 9.34375 18.9648 9.07031 18.6992L1.34766 10.9883C1.06641 10.7148 0.925781 10.3945 0.925781 10.0273Z" fill="#747577"/>
</svg>
                </div>
                <div class="">
                    <h1 class="text-xl font-bold">Edit Project</h1>
                </div>
              </div>
        <Form :validation-schema="projectSchema" @submit="handleForm"  v-slot="{ values }" class=" h-[94%] w-[90%] mx-auto flex flex-col justify-center mt-[-8px]">
          <div class="flex flex-col overflow-y-scroll">
            <div class="col-span-auto">
              <label for="projectLogo" class="text-sm font-medium text-black">Upload Project Logo / Image</label>
              <div class="h-[220px] relative flex justify-center items-center bg-gray-50 !border-[1px] border-gray-400 border-opacity-100 rounded-md">
            <Field name="projectLogo" id="projectLogo" :model-value="updatedfile || projectThumbnail" >
                    <DnDFileUploader  inputType="image/*" class="!w-full !h-full"
                    inputPlaceholder="Click to upload or drag & drop files here" @fileData="(val)=>{ updatedfile = val}"
                    :previousFileData="projectThumbnail" :key="projectThumbnail"/>
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 absolute bottom-[-20px] capitalize" name="projectLogo" />
              </div>
            </div>
            <div class="col-span-auto h-[270px] max-h-[270px] flex flex-col justify-between">
                <div class="h-[90px]">
                    <label for="projectName" class="label-primary">Project Name*</label>
                    <div class="mt-1">
                        <Field type="text" v-model="ProjectName" name="projectName" id="projectName" autocomplete="projectName" class="input-primary !bg-gray-50"
                        placeholder="Enter Project Name" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="projectName" />
                    </div>
                </div>
                <div class="h-[90px]">
                    <label for="country" class="label-primary">Country*</label>
                    <div class="mt-1">
                        <Field name="country" v-model="Country"  v-slot="{field}"
                         class="!bg-gray-50" >
    <multiselect v-model="Country" @select="()=>{City = null;values.City=null}" :options="Object.keys(countryCity)" :searchable="false" :close-on-select="true" :show-labels="false"
                 placeholder="Select a Country" aria-label="Select a Country" v-bind="field" class="!bg-gray-50" maxHeight="150"></multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="country" />
                    </div>
                </div>
                <div class="h-[90px]">
                    <label for="city" class="label-primary">City/Area*</label>
                    <div class="mt-1">
                        <Field name="city" :model-value="City" v-slot="{field}" >
    <multiselect v-model="City" :options="Country ?countryCity[Country]?.cities:[]" :searchable="false" :close-on-select="true" :show-labels="false"
                 placeholder="Select a City" aria-label="Select a City" v-bind="field" class="!bg-gray-50" maxHeight="150"></multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="city" />
                    </div>
                </div>
            </div>
            <div class="col-span-auto">
                <div class="h-[90px]">
                    <label for="propertyType" class="label-primary">Property Type*</label>
                    <div class="mt-2">
                        <Field name="propertyType"  :model-value="propertyType.value" v-slot="{ field }" class="!bg-gray-50">
                        <multiselect v-model="propertyType" deselect-label="Can't remove this value" track-by="name" label="name"
                                    placeholder="Select one" aria-label="Select one" :options="propertyTypeOptions" :searchable="false" :allow-empty="false"
                                     @select="(val) =>propertyType = val" v-bind="field" class="!bg-gray-50">
                       <template v-slot:singleLabel="{ option }">
                        <div class="relative top-[2px] !bg-gray-50">
                            <p v-if="option.name" class="text-black capitalize">{{ option.name }}</p>
                            <p v-else class="text-gray-500 ">Select one</p>
                        </div>
                        </template>
                        </multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="propertyType" />
                    </div>
                </div>
            </div>
            <div class="col-span-auto">
                <div class="h-[90px]">
                    <label for="experience" class="label-primary">Experience*</label>
                    <div class="mt-2">
                         <Field  name="experience" :model-value="Experience" v-slot="{ field }">
                        <Multiselect
                        v-model="Experience"
                        :allow-empty="true"
                        :multiple="true"
                        v-bind="field"
                        track-by="name" label="name"
                        :searchable="false"
                        :taggable="true"
                        deselectLabel="remove"
                        :close-on-select="false" :clear-on-select="false"
                         class="!bg-gray-50"
                         placeholder="Choose"
                         :options="experienceOptions" maxHeight="250">
                        </Multiselect>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="experience" />
                    </div>
                </div>
            </div>
          </div>
          <div class="mt-4 w-full flex justify-end gap-x-3">
            <button type="submit" class="w-full h-full hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1
            rounded-lg flex flex-row justify-center items-center gap-2">
              <span class="text-center text-inherit whitespace-nowrap">Save changes</span>
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>

    </div>
        </Modal>
        <Modal :open="successModal">
         <div class="w-[70%] h-[30%] relative flex flex-col justify-center bg-white rounded-lg  shadow-xl gap-4">
            <div @click="()=>router.go(-1)" class="absolute top-4 right-3 cursor-pointer">
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="x">
                <path id="Vector" d="M7.0885 6.50001L11.1645 2.42399C11.2381 2.35295 11.2967 2.26798 11.3371 2.17403C11.3774 2.08007 11.3987 1.97902 11.3996 1.87677C11.4005 1.77452 11.381 1.67312 11.3423 1.57848C11.3035 1.48384 11.2464 1.39786 11.1741 1.32556C11.1018 1.25325 11.0158 1.19607 10.9211 1.15735C10.8265 1.11863 10.7251 1.09915 10.6228 1.10004C10.5206 1.10092 10.4195 1.12217 10.3256 1.16253C10.2316 1.20289 10.1467 1.26155 10.0756 1.3351L5.99961 5.41112L1.92359 1.3351C1.77835 1.19483 1.58383 1.11721 1.38192 1.11896C1.18001 1.12072 0.986864 1.2017 0.844085 1.34448C0.701307 1.48726 0.620319 1.68041 0.618564 1.88232C0.616809 2.08423 0.694429 2.27875 0.834705 2.42399L4.91072 6.50001L0.834705 10.576C0.761155 10.6471 0.702488 10.732 0.662129 10.826C0.621771 10.9199 0.600527 11.021 0.599638 11.1232C0.59875 11.2255 0.618234 11.3269 0.656954 11.4215C0.695674 11.5162 0.752855 11.6021 0.825159 11.6745C0.897464 11.7468 0.983445 11.8039 1.07808 11.8427C1.17272 11.8814 1.27413 11.9009 1.37638 11.9C1.47863 11.8991 1.57968 11.8778 1.67363 11.8375C1.76758 11.7971 1.85256 11.7385 1.92359 11.6649L5.99961 7.58889L10.0756 11.6649C10.2209 11.8052 10.4154 11.8828 10.6173 11.8811C10.8192 11.8793 11.0124 11.7983 11.1551 11.6555C11.2979 11.5127 11.3789 11.3196 11.3807 11.1177C11.3824 10.9158 11.3048 10.7213 11.1645 10.576L7.0885 6.50001Z" fill="#6B7280"/>
                </g>
                </svg>

            </div>
            <div class="w-full flex items-center justify-center">
                <span class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="check">
                    <path id="Vector" d="M7.15541 18.3333C6.82899 18.3349 6.51503 18.1816 6.28067 17.9061L0.37744 10.9624C0.259778 10.8232 0.165894 10.6572 0.101148 10.474C0.0364024 10.2908 0.00206288 10.0939 9.0224e-05 9.89456C-0.00389373 9.49202 0.124201 9.10406 0.356196 8.81601C0.588191 8.52796 0.905082 8.36342 1.23716 8.35859C1.56923 8.35376 1.88929 8.50904 2.12691 8.79026L7.1604 14.7085L17.8722 2.098C18.1102 1.81678 18.4305 1.66167 18.7628 1.66678C19.0951 1.67189 19.4122 1.83682 19.6442 2.12527C19.8762 2.41372 20.0041 2.80207 19.9999 3.20488C19.9957 3.6077 19.8596 3.99199 19.6217 4.27321L8.03014 17.9061C7.79578 18.1816 7.48182 18.3349 7.15541 18.3333Z" fill="#0E9F6E"/>
                    </g>
                    </svg>
                </span>
            </div>
            <span class="w-full text-center"><p class="text-lg font-semibold">Project Edited Successfully</p></span>
            <button @click="()=>router.go(-1)" class="w-32 bg-[#1a56db] text-white rounded-lg h-10 p-2 mx-auto border !border-blue-500">Okay</button>
        </div>
        </Modal>

    </div>
</template>

<style >
*::-webkit-scrollbar {
    width: 0;
    height: 0;
}
.multiselect__single{
    background-color: #f9fafb;
}
</style>
