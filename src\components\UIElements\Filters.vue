<script setup>
import { defineProps, ref } from 'vue';
import LabelDropdown from '../common/LabelDropdown.vue';
defineProps({
  title: String,
  scheduleView: Number,
});
const emit = defineEmits(['switchView']);
// Let activeButton = ref(2);
const initialPosition = ref({});

function changeView (button) {
  // ActiveButton.value = button;
  emit('switchView', button);
}
const sort = ['New', 'Hot', 'Cold', 'Warm'];

// Function openDropdown (event) {
//   InitialPosition.value = event.currentTarget.getBoundingClientRect();
//   Console.log(event.currentTarget.getBoundingClientRect());
// }

const selectOrganisation = [
  {
    id: 1,
    name: 'Damac',
    logo: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="12" fill="#0F0F0F"/><path d="M16.8469 13.8896C17.5429 13.2057 18.0953 12.3743 18.4682 11.4492C19.0223 10.0423 18.975 8.94902 18.3263 8.16942C17.6775 7.3898 16.453 7 14.6527 7H8.56075L8.01004 8.40874H12.2053C14.3448 8.40874 14.9656 9.54007 14.2268 11.4111C13.801 12.499 13.423 13.4907 12.5319 13.9349C11.7179 14.341 10.8747 14.4607 9.37825 14.4607H7.43711L8.8857 10.8165H7.0523L5 16H10.5224C12.0707 16 13.2942 15.8482 14.1926 15.5449C15.1821 15.2084 16.0888 14.643 16.8469 13.8896Z" fill="white"/></svg>',
    isSelected: true,
  },
  {
    id: 2,
    name: 'Damac',
    logo: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="12" fill="#0F0F0F"/><path d="M16.8469 13.8896C17.5429 13.2057 18.0953 12.3743 18.4682 11.4492C19.0223 10.0423 18.975 8.94902 18.3263 8.16942C17.6775 7.3898 16.453 7 14.6527 7H8.56075L8.01004 8.40874H12.2053C14.3448 8.40874 14.9656 9.54007 14.2268 11.4111C13.801 12.499 13.423 13.4907 12.5319 13.9349C11.7179 14.341 10.8747 14.4607 9.37825 14.4607H7.43711L8.8857 10.8165H7.0523L5 16H10.5224C12.0707 16 13.2942 15.8482 14.1926 15.5449C15.1821 15.2084 16.0888 14.643 16.8469 13.8896Z" fill="white"/></svg>',
    isSelected: false,
  },
  {
    id: 2,
    name: 'Damac',
    logo: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="12" fill="#0F0F0F"/><path d="M16.8469 13.8896C17.5429 13.2057 18.0953 12.3743 18.4682 11.4492C19.0223 10.0423 18.975 8.94902 18.3263 8.16942C17.6775 7.3898 16.453 7 14.6527 7H8.56075L8.01004 8.40874H12.2053C14.3448 8.40874 14.9656 9.54007 14.2268 11.4111C13.801 12.499 13.423 13.4907 12.5319 13.9349C11.7179 14.341 10.8747 14.4607 9.37825 14.4607H7.43711L8.8857 10.8165H7.0523L5 16H10.5224C12.0707 16 13.2942 15.8482 14.1926 15.5449C15.1821 15.2084 16.0888 14.643 16.8469 13.8896Z" fill="white"/></svg>',
    isSelected: false,
  },
  {
    id: 2,
    name: 'Damac',
    logo: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="12" fill="#0F0F0F"/><path d="M16.8469 13.8896C17.5429 13.2057 18.0953 12.3743 18.4682 11.4492C19.0223 10.0423 18.975 8.94902 18.3263 8.16942C17.6775 7.3898 16.453 7 14.6527 7H8.56075L8.01004 8.40874H12.2053C14.3448 8.40874 14.9656 9.54007 14.2268 11.4111C13.801 12.499 13.423 13.4907 12.5319 13.9349C11.7179 14.341 10.8747 14.4607 9.37825 14.4607H7.43711L8.8857 10.8165H7.0523L5 16H10.5224C12.0707 16 13.2942 15.8482 14.1926 15.5449C15.1821 15.2084 16.0888 14.643 16.8469 13.8896Z" fill="white"/></svg>',
    isSelected: false,
  },

];

</script>

<template>
    <div class="w-full flex justify-between items-center gap-10 -z-100">
        <div class="text-txt-50 dark:text-txt-1000 font-medium txtWrap">
            {{ title }}
        </div>
        <div class="flex gap-10 no-scrollbar txtWrap overflow-x-auto">
            <div class="flex gap-3 items-center">
                <div class="text-txt-400 text-base dark:text-txt-1000">
                    Sort by:
                </div>
                <div class="flex gap-4">
                    <div v-for="(item, index) in sort" :key=index class="text-txt-50 px-4 py-[9px] font-normal text-base bg-opacity-50 rounded-[21px] bg-bg-900 dark:bg-bg-1000">
                        {{ item }}
                    </div>
                </div>
            </div>
            <div >
                <LabelDropdown title="Project" :selectOrganisation="selectOrganisation" :top="initialPosition.top" :left="initialPosition.left"/>
            </div>
            <div >
                <LabelDropdown title="Agent" :selectOrganisation="selectOrganisation" :top="initialPosition.top" :left="initialPosition.left"/>
            </div>
            <div class="flex items-center">
                <div @click="changeView(1)" :class="{ 'bg-bg-150': scheduleView === 1, 'bg-white': scheduleView !== 1 }" class="hover:cursor-pointer border border-black pl-[22px] pr-[21.5px] h-full flex items-center rounded-tl-lg rounded-bl-lg">
                    <svg class="w-[33px] h-[33px]" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path :class="scheduleView === 1 ? 'fill-bg-1000' : 'fill-bg-150'" d="M4.28711 26.288H28.2871C28.8175 26.288 29.3263 26.0773 29.7013 25.7022C30.0764 25.3271 30.2871 24.8184 30.2871 24.288V8.28796C30.2871 7.75753 30.0764 7.24882 29.7013 6.87375C29.3263 6.49868 28.8175 6.28797 28.2871 6.28797H4.28711C3.75668 6.28797 3.24797 6.49868 2.8729 6.87375C2.49783 7.24882 2.28711 7.75753 2.28711 8.28796V24.288C2.28711 24.8184 2.49783 25.3271 2.8729 25.7022C3.24797 26.0773 3.75668 26.288 4.28711 26.288ZM4.28711 24.288V21.288H28.2871V24.288H4.28711ZM4.28711 8.28796H28.2871V19.288H4.28711V8.28796ZM6.28711 11.288C6.28711 11.0227 6.39247 10.7684 6.58 10.5809C6.76754 10.3933 7.02189 10.288 7.28711 10.288H11.2871C11.5523 10.288 11.8067 10.3933 11.9942 10.5809C12.1818 10.7684 12.2871 11.0227 12.2871 11.288C12.2871 11.5532 12.1818 11.8075 11.9942 11.9951C11.8067 12.1826 11.5523 12.288 11.2871 12.288H7.28711C7.02189 12.288 6.76754 12.1826 6.58 11.9951C6.39247 11.8075 6.28711 11.5532 6.28711 11.288ZM14.2871 11.288C14.2871 11.0227 14.3925 10.7684 14.58 10.5809C14.7675 10.3933 15.0219 10.288 15.2871 10.288H17.2871C17.5523 10.288 17.8067 10.3933 17.9942 10.5809C18.1818 10.7684 18.2871 11.0227 18.2871 11.288C18.2871 11.5532 18.1818 11.8075 17.9942 11.9951C17.8067 12.1826 17.5523 12.288 17.2871 12.288H15.2871C15.0219 12.288 14.7675 12.1826 14.58 11.9951C14.3925 11.8075 14.2871 11.5532 14.2871 11.288Z"/>
                    </svg>
                </div>
                <div @click="changeView(2)" :class="{ 'bg-bg-150': scheduleView === 2, 'bg-white': scheduleView !== 2 }" class="hover:cursor-pointer border border-black pl-[22px] pr-[21.5px] h-full flex items-center rounded-tr-lg rounded-br-lg">
                    <svg class="w-[32px] h-[32px]" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path :class="scheduleView === 2 ? 'fill-bg-1000' : 'fill-bg-150'" d="M28 6H4C3.73478 6 3.48043 6.10536 3.29289 6.29289C3.10536 6.48043 3 6.73478 3 7V24C3 24.5304 3.21071 25.0391 3.58579 25.4142C3.96086 25.7893 4.46957 26 5 26H27C27.5304 26 28.0391 25.7893 28.4142 25.4142C28.7893 25.0391 29 24.5304 29 24V7C29 6.73478 28.8946 6.48043 28.7071 6.29289C28.5196 6.10536 28.2652 6 28 6ZM5 14H10V18H5V14ZM12 14H27V18H12V14ZM27 8V12H5V8H27ZM5 20H10V24H5V20ZM27 24H12V20H27V24Z"/>
                    </svg>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped>
.no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.txtWrap{
    text-wrap: nowrap;
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
</style>
