<script setup>
import { onMounted, ref, watch } from 'vue';
import OpenSeadragon from 'openseadragon';
import { cdn } from '../../../helpers';
import { useRoute } from 'vue-router';
import router from '../../../router';
import { isMasterScenePath } from '@/helpers/helpers';

const emits = defineEmits(['updateLayerPosition', 'updateCurrentZoomLevel']);
const route = useRoute();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const sceneId = ref(route.params.scene_id); // Scene id
const projectId = ref(route.params.project_id); // Project id
const props = defineProps({
  tileSource: {
    type: String,
    default () {
      return "";
    },
  },
  overlayLayers: {
    type: Object,
    default () {
      return {};
    },
  },
  resize: {
    type: Object,
    default () {
      return {};
    },
  },
  scale: {
    type: Object,
    default () {
      return {};
    },
  },
});
const viewerId = ref('openseadragon'); // Seadragon id
const viewer = ref(null); // Viewer
const trackCurrentOverlayIds = ref([]); // Current
const dragHandlerRef = ref(null);
const currentZoomLevel = ref(0);
const svgLayer = ref({});

// Get the Viewport Rect Points of Image points
function imageToViewportRectPoints (imageX, imageY) {
  var imagePoint = new OpenSeadragon.Point(imageX, imageY); // React point object from image coordinates
  return viewer.value.viewport.imageToViewportCoordinates(imagePoint);
}

// Reset the Zoom Level to min default
function zoomToMinDefault () {
  return new Promise ((resolve) => {
    const minZoom = viewer.value.viewport.getMinZoom();
    // Set the zoom to the minimum level
    viewer.value.viewport.zoomTo(minZoom, null, 1.0);
    resolve(true);
  });
}

// Get the element Information
async function getTheElementInfo (svgElement)  {
  return new Promise((resolve) => {
    const info = viewer.value.currentOverlays.find((o) => o.element === svgElement); // Find the element in currentOverlays in viewer
    resolve(info);
  });
}

// ResizeToggler
async function resizeToggler (resize, layerId, svgId) {
  const svgElement = document.getElementById(layerId);
  const resetZoomLevel = await zoomToMinDefault();  // Reset the zoom level to default min
  if (resetZoomLevel){
    setTimeout(function (){
      getTheElementInfo(svgElement).then((res) => {
        if (resize.reSize === false){
          console.log('yes the scale is true');
          svgElement.setAttribute('width', props.overlayLayers[svgId].layers[layerId].width  +'px' );
          svgElement.setAttribute('height', props.overlayLayers[svgId].layers[layerId].height + 'px');
          const imagePoint = viewer.value.viewport.viewportToImageCoordinates(res.location); // Get the image coordinates from currentLocation in viewer
          console.log(imagePoint);
          let currentPosition;
          if (res.scales === false){
            currentPosition = {
              px: imagePoint.x,
              py: imagePoint.y,
            };
            console.log(currentPosition);
            emits('updateLayerPosition', {px: imagePoint.x - (props.overlayLayers[svgId].layers[layerId].width / 2), py: imagePoint.y - props.overlayLayers[svgId].layers[layerId].height});
          } else {
            currentPosition = {
              px: imagePoint.x + (props.overlayLayers[svgId].layers[layerId].width / 2),
              py: imagePoint.y + props.overlayLayers[svgId].layers[layerId].height,
            };
            emits('updateLayerPosition', {px: imagePoint.x, py: imagePoint.y});
          }
          viewer.value.updateOverlay(svgElement, {
            location: imageToViewportRectPoints(currentPosition.px, currentPosition.py),
            placement: OpenSeadragon.Placement[resize.placement],
            checkResize: false,
            width: null,
            height: null,
          });

        } else {
          console.log('no the scale is false');
          svgElement.removeAttribute('width');
          svgElement.removeAttribute('height');
          const imagePoint = viewer.value.viewport.viewportToImageCoordinates(res.location); // Get the image coordinates from currentLocation in viewer
          console.log(imagePoint);
          const currentPosition = {
            px: imagePoint.x - (props.overlayLayers[svgId].layers[layerId].width / 2),
            py: imagePoint.y - props.overlayLayers[svgId].layers[layerId].height,
          };
          const item = viewer.value.world.getItemAt(0);
          const imageWidth = item.source.width;
          //   Const imageHeight = item.source.height;

          viewer.value.updateOverlay(svgElement, {
            location: imageToViewportRectPoints(currentPosition.px, currentPosition.py),
            placement: null,
            checkResize: true,
            width: props.overlayLayers[svgId].layers[layerId].width / imageWidth,
            height: props.overlayLayers[svgId].layers[layerId].height / imageWidth,
          });

          emits('updateLayerPosition', currentPosition);
        }
      });
    }, 200);
  }
}

// Reset the Position of Layer
async function resetPosition (layerId){
  return new Promise((resolve) => {
    let overlaysObject = {};
    Object.keys(props.overlayLayers).forEach((key) => {
      overlaysObject = {...overlaysObject, ...props.overlayLayers[key].layers};
    });
    const item = viewer.value.world.getItemAt(0);
    const imageWidth = item.source.width; // Get the width of the tilesource
    if (Object.keys(overlaysObject).length > 0){
      if (overlaysObject[layerId]){
        const overlayElement = document.getElementById(layerId);

        //  Viewer.value.updateOverlay(overlayElement, imageToViewportRectPoints(overlaysObject[layerId].x,overlaysObject[layerId].y)); // update overlay with viewport coordinates
        if (overlaysObject[layerId].reSize === undefined || overlaysObject[layerId].reSize === true){
          overlayElement.removeAttribute('width');
          overlayElement.removeAttribute('height');
          viewer.value.updateOverlay(overlayElement, {
            location: imageToViewportRectPoints(overlaysObject[layerId].x, overlaysObject[layerId].y),
            placement: null,
            checkResize: true,
            width: overlaysObject[layerId].width / imageWidth,
            height: overlaysObject[layerId].height / imageWidth,
          });
        } else {
          overlayElement.setAttribute('width', overlaysObject[layerId].width  +'px' );
          overlayElement.setAttribute('height', overlaysObject[layerId].height + 'px');
          viewer.value.updateOverlay(overlayElement, {
            location: imageToViewportRectPoints(overlaysObject[layerId].x + (overlaysObject[layerId].width / 2), overlaysObject[layerId].y + overlaysObject[layerId].height ),
            placement: OpenSeadragon.Placement[overlaysObject[layerId].placement],
            checkResize: false,
            width: null,
            height: null,
          });
        }

        overlayElement.style.border = 'none'; // Remove the border
        resolve();
      }
    }

  });
}

// Remove Draggable
async function removeDraggable (layerId){
  return new Promise((resolve) => {
    if (dragHandlerRef.value){
      dragHandlerRef.value.destroy(); // Destroy the drag handler
      dragHandlerRef.value = null;  // Set to null
      // Reset the positions
      resetPosition(layerId).then(() => {
        resolve();
      });
    }
  });
}

// Enable Draggable
function enableDraggable (layerId, elementInfo){
  console.log(elementInfo);
  const svgElement = document.getElementById(layerId);
  svgElement.style.border = '1px solid white'; // Add up some styles
  getTheElementInfo(svgElement).then((overlayInfo) => {
    dragHandlerRef.value = new OpenSeadragon.MouseTracker({
      element: svgElement,
      dragHandler: function (event) {
        console.log('Yes dragging');
        const delta = viewer.value.viewport.deltaPointsFromPixels(event.delta);
        let newX = overlayInfo.location.x + delta.x;
        let newY = overlayInfo.location.y + delta.y;

        // // Constrain to viewer bounds
        newX = Math.max(0, Math.min(1 - (overlayInfo.width / viewer.value.world.getContentFactor()), newX));
        newY = Math.max(0, Math.min(1 - (overlayInfo.height / viewer.value.world.getContentFactor()), newY));

        overlayInfo.location.x = newX;
        overlayInfo.location.y = newY;

        const imagePoint = viewer.value.viewport.viewportToImageCoordinates(overlayInfo.location); // Viewport coordinates to image coordinates
        console.log(imagePoint);
        const imageCoordinates = {
          px: imagePoint.x,
          py: imagePoint.y,
        };

        console.log(overlayInfo);
        viewer.value.updateOverlay(svgElement, imageCoordinates); // Update the overlay with image coordinates

        if (overlayInfo.scales){
          // True
          console.log('yes scales');
          emits('updateLayerPosition', imageCoordinates);
        } else {
          // False
          console.log('no scales');
          const newFormatedCoordinates = {
            px: imageCoordinates.px - (elementInfo.width / 2),
            py: imageCoordinates.py - elementInfo.height,
          };
          emits('updateLayerPosition', newFormatedCoordinates);
        }

      },
    }).setTracking(true);
  });
}

// Function to fetch the gtag content and append it to SVG
async function readLayersFromLink (cloudLink) {
  return new Promise((resolve) => {
    fetch(cloudLink)
      .then((response) => response.text())
      .then((res) => {
        resolve(res);
      });
  });
}

// Create a overlays
async function createOverlays (layers) {
  return new Promise((resolve) => {
    if (Object.values(layers).length > 0) {
      Object.values(layers).forEach((item) => {
        trackCurrentOverlayIds.value.push(item.layer_id); // Track id of add overlay
        const svgElement = document.createElementNS(
          'http://www.w3.org/2000/svg',
          'svg',
        );
        const gElement = document.createElementNS(
          'http://www.w3.org/2000/svg',
          'g',
        );
        svgElement.setAttribute('version', '1.1');
        svgElement.setAttribute('x', '0px');
        svgElement.setAttribute('y', '0px');
        svgElement.setAttribute('xml:space', 'preserve');
        svgElement.setAttribute('id', item.layer_id);
        svgElement.setAttribute(
          'viewBox',
          `0 0 ${item.width} ${item.height}`,
        );
        svgElement.classList.add('cursor-pointer');
        readLayersFromLink(cdn(item.g)).then((res) => {
          gElement.innerHTML = res;
          svgElement.appendChild(gElement);
          console.log(svgElement);

          // SvgElement.style.opacity = 0.8; // initial opacity

          if (item.zIndex !== undefined) {
            svgElement.style.zIndex = item.zIndex;
          }
          if (item.reSize === undefined || item.reSize === true) {
            // Undefined resize and with resize 'true' considered as a scalable elements
            viewer.value.addOverlay({
              element: svgElement,
              px: item.x,
              py: item.y,
              width: item.width,
              height: item.height,
              placement: null,
            });
          } else {
            // Un scalable the element
            svgElement.setAttribute('width', item.width + 'px');
            svgElement.setAttribute('height', item.height + 'px');

            viewer.value.addOverlay({
              element: svgElement,
              px: item.x + (item.width / 2),
              py: item.y + item.height,
              placement: String(item.placement),
              checkResize: item.reSize,
            });
          }
          svgLayer.value[item.layer_id] = {
            'element': svgElement,
            'minZoomLevel': item.minZoomLevel,
            'maxZoomLevel': item.maxZoomLevel,
          };
          // Apply the scale factor
          if (item.scale) {
            svgElement.style.scale = `${item.scale.x} ${item.scale.y}`;
          }

          new OpenSeadragon.MouseTracker({
            element: svgElement,
            clickHandler: function () {
              console.log('yes click handler');
              console.log(item.layer_id);
              let svgID;
              Object.values(props.overlayLayers).forEach((svgLayers) => {
                if (svgLayers.layers[item.layer_id]){
                  svgID = svgLayers._id;
                }

              });
              console.log(svgID);
              if (item.layer_id && svgID) {
                router.push({
                  path: isMasterScene.value?`/masterscenes/${sceneId.value}/layers`:`/projects/${projectId.value}/design/scenes/${sceneId.value}/layers`,
                  query: { svgId: svgID, layerId: item.layer_id },
                });
              }
            },
          });
          if (item.minZoomLevel && item.maxZoomLevel) {
            if (currentZoomLevel.value >= item.minZoomLevel
              && currentZoomLevel.value <= item.maxZoomLevel) {
              svgElement.classList.add('!visible');
              svgElement.classList.remove('!hidden');
            } else {
              svgElement.classList.remove('!visible');
              svgElement.classList.add('!hidden');
            }
          }
        });
      });
      resolve();
    }
  });
}

// Watch the overlayLayers prop changes
watch(
  () => props.overlayLayers,
  () => {
    if (props.overlayLayers && viewer.value) {
      let overlays = {};
      Object.values(props.overlayLayers).forEach((item) => {
        if (Object.values(item.layers).length > 0) {
          overlays = { ...overlays, ...item.layers };
        }
      });

      if (Object.values(overlays).length > 0) {
        // Check
        if (trackCurrentOverlayIds.value.length > 0) {
          // Delete already existing in current overlay objects by trackCurrentOverlayIds
          trackCurrentOverlayIds.value.forEach((layer_id) => {
            delete overlays[layer_id];
          });
        }
        createOverlays(overlays).then(() => { });
      }
    }
  },
  { deep: true },
);

// Watch the overlay
watch(
  () => route.query.layerId,
  (current, previous) => {
    console.log('yes change');
    console.log('current: ' + current);
    console.log('previous: ' + previous);
    let allOverlays =  {};
    Object.keys(props.overlayLayers).forEach((key) => {
      allOverlays = {...allOverlays, ...props.overlayLayers[key].layers};
    });
    if (previous === undefined && current){
      // If there is no previous layer, then create a new drag
      console.log('no previous layer, then create a new drag');
      console.log(current);
      enableDraggable(current, allOverlays[current]);
    }  else {
      // Previous and current ||  if there is a previous layer , then destroy the previous drag and create new ones
      console.log('if there is a previous layer , then destroy the previous drag and create new ones ');
      console.log(previous);

      // Remove the scale
      if (allOverlays[previous].scale) {
        document.getElementById(allOverlays[previous].layer_id).style.scale = `${allOverlays[previous].scale.x} ${allOverlays[previous].scale.y}`;
      } else {
        document.getElementById(allOverlays[previous].layer_id).style.scale = 'none';
      }

      // Remove the previous element drag and reset its position
      removeDraggable(previous).then(() => {
        if (current){
          enableDraggable(current, allOverlays[current]);
        }
      });
    }

  },
);

// Watch the resize
watch(() => props.resize, (val) => {
  const svgElement = document.getElementById(route.query.layerId);
  getTheElementInfo(svgElement).then((overlayInfo) => {
    if (val.reSize !== overlayInfo.scales || overlayInfo.placement === null ? val.placement !== overlayInfo.placement  : OpenSeadragon.Placement[val.placement] !== overlayInfo.placement ){
      resizeToggler(val, route.query.layerId, route.query.svgId);
    }
  });
});

// Watch the scale
watch(() =>  props.scale, (val) => {
  const svgElement = document.getElementById(route.query.layerId);
  svgElement.style.scale = `${val.x} ${val.y}`;
});

onMounted(() => {
  console.log(props.tileSource);
  console.log(props.overlayLayers);
  if (props.tileSource) {
    // Viewer
    viewer.value = OpenSeadragon({
      id: viewerId.value,
      showZoomControl: false,
      showHomeControl: false,
      showFullPageControl: false,
      showRotationControl: false,
      showSequenceControl: false,
      tileSources: [props.tileSource], // TileSources
      maxZoomLevel: 9,
      preserveViewport: true,
      visibilityRatio: 1,
      constrainDuringPan: true,
      homeFillsViewer: true,
      zoomPerScroll: 1.3, // Increase zoom speed
      springStiffness: 10, // Increase panning speed
      animationTime: 0.6, // Reduce animation time for faster panning,
    });

    // Zoom
    viewer.value.addHandler('zoom', (e) => {
      emits('updateCurrentZoomLevel', e.zoom);
      currentZoomLevel.value = e.zoom;
      if (screen.width < 600){
        if (e.zoom < viewer.value.viewport.minZoomLevel) {
          viewer.value.viewport.zoomTo(viewer.value.viewport.minZoomLevel);
        }
      }
    });

    // Ready
    viewer.value.addHandler('open', function () {
      viewer.value.viewport.minZoomLevel = viewer.value.viewport.getHomeZoom(); // Set the minZoomLevel
      // viewer disapatch
      window.viewer = viewer;
      window.dispatchEvent(new Event('dispatchViewerInfo'));
      // Layers
      if (props.overlayLayers) {
        // Console.log(initialFilterList);
        let overlays = {};
        Object.values(props.overlayLayers).forEach((item) => {
          if (Object.values(item.layers).length > 0) {
            overlays = { ...overlays, ...item.layers };
          }
        });

        if (Object.values(overlays).length > 0) {
          createOverlays(overlays).then(() => { });
        }
      }
    });
  }
});
function setActiveElem (svgElem, zoomLevel){
  Object.keys(svgElem).forEach((item) => {
    if (zoomLevel >= svgElem[item].minZoomLevel && zoomLevel <= svgElem[item].maxZoomLevel) {
      svgElem[item].element.classList.add('!visible');
      svgElem[item].element.classList.remove('!hidden');
    } else {
      svgElem[item].element.classList.add('!hidden');
      svgElem[item].element.classList.remove('!visible');
    }
  });
}
watch(currentZoomLevel, () => {
  setActiveElem(svgLayer.value, currentZoomLevel.value);
});
</script>

<template>
    <!-- openseadragon container -->
    <div :id="viewerId" class="w-full h-full"></div>
</template>

<style scoped></style>
