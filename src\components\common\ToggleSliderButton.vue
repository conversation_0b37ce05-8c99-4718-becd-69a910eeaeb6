<script setup>
import { UserStore } from '@/store';
import { onClickOutside } from '@vueuse/core';
import { ref, watch } from 'vue';

const emit = defineEmits(['update:modelValue', 'toggleData']);
const Store = UserStore();
const openSlider = ref(false);
const dropDownModalRef = ref();
const props = defineProps({
  modelValue: {
    type: String,
    default: 'card',
  },
  title: {
    type: Array,
  },
  position: {
    type: String,
  },
  size: {
    type: String,
  },
  enableDaySVG: {
    type: Boolean,
  },
});
const type = [
  {
    name: 'cardView',
    svg: {
      active: `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="credit-card">
<g id="Vector">
<path d="M14.4 2.5H1.6C1.17565 2.5 0.768687 2.68061 0.468629 3.0021C0.168571 3.32359 0 3.75963 0 4.21429V12.7857C0 13.2404 0.168571 13.6764 0.468629 13.9979C0.768687 14.3194 1.17565 14.5 1.6 14.5H14.4C14.8243 14.5 15.2313 14.3194 15.5314 13.9979C15.8314 13.6764 16 13.2404 16 12.7857V4.21429C16 3.75963 15.8314 3.32359 15.5314 3.0021C15.2313 2.68061 14.8243 2.5 14.4 2.5ZM1.6 12.7857V7.64286H14.4V12.7857H1.6Z" fill="#1C64F2"/>
<path d="M4.8 9.35714H3.2C2.98783 9.35714 2.78434 9.44745 2.63431 9.60819C2.48429 9.76894 2.4 9.98696 2.4 10.2143C2.4 10.4416 2.48429 10.6596 2.63431 10.8204C2.78434 10.9811 2.98783 11.0714 3.2 11.0714H4.8C5.01217 11.0714 5.21566 10.9811 5.36569 10.8204C5.51571 10.6596 5.6 10.4416 5.6 10.2143C5.6 9.98696 5.51571 9.76894 5.36569 9.60819C5.21566 9.44745 5.01217 9.35714 4.8 9.35714Z" fill="#1C64F2"/>
<path d="M11.2 9.35714H7.2C6.98783 9.35714 6.78434 9.44745 6.63432 9.60819C6.48429 9.76894 6.4 9.98696 6.4 10.2143C6.4 10.4416 6.48429 10.6596 6.63432 10.8204C6.78434 10.9811 6.98783 11.0714 7.2 11.0714H11.2C11.4122 11.0714 11.6157 10.9811 11.7657 10.8204C11.9157 10.6596 12 10.4416 12 10.2143C12 9.98696 11.9157 9.76894 11.7657 9.60819C11.6157 9.44745 11.4122 9.35714 11.2 9.35714Z" fill="#1C64F2"/>
</g>
</g>
</svg>
`,
      inActive: `<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="credit-card" clip-path="url(#clip0_1313_22094)">
<g id="Vector">
<path d="M14.9 2.5H2.1C1.67565 2.5 1.26869 2.68061 0.968629 3.0021C0.668571 3.32359 0.5 3.75963 0.5 4.21429V12.7857C0.5 13.2404 0.668571 13.6764 0.968629 13.9979C1.26869 14.3194 1.67565 14.5 2.1 14.5H14.9C15.3243 14.5 15.7313 14.3194 16.0314 13.9979C16.3314 13.6764 16.5 13.2404 16.5 12.7857V4.21429C16.5 3.75963 16.3314 3.32359 16.0314 3.0021C15.7313 2.68061 15.3243 2.5 14.9 2.5ZM2.1 12.7857V7.64286H14.9V12.7857H2.1Z" fill="#6B7280"/>
<path d="M5.3 9.35714H3.7C3.48783 9.35714 3.28434 9.44745 3.13431 9.60819C2.98429 9.76894 2.9 9.98696 2.9 10.2143C2.9 10.4416 2.98429 10.6596 3.13431 10.8204C3.28434 10.9811 3.48783 11.0714 3.7 11.0714H5.3C5.51217 11.0714 5.71566 10.9811 5.86569 10.8204C6.01571 10.6596 6.1 10.4416 6.1 10.2143C6.1 9.98696 6.01571 9.76894 5.86569 9.60819C5.71566 9.44745 5.51217 9.35714 5.3 9.35714Z" fill="#6B7280"/>
<path d="M11.7 9.35714H7.7C7.48783 9.35714 7.28434 9.44745 7.13432 9.60819C6.98429 9.76894 6.9 9.98696 6.9 10.2143C6.9 10.4416 6.98429 10.6596 7.13432 10.8204C7.28434 10.9811 7.48783 11.0714 7.7 11.0714H11.7C11.9122 11.0714 12.1157 10.9811 12.2657 10.8204C12.4157 10.6596 12.5 10.4416 12.5 10.2143C12.5 9.98696 12.4157 9.76894 12.2657 9.60819C12.1157 9.44745 11.9122 9.35714 11.7 9.35714Z" fill="#6B7280"/>
</g>
</g>
<defs>
<clipPath id="clip0_1313_22094">
<rect width="16" height="16" fill="white" transform="translate(0.5 0.5)"/>
</clipPath>
</defs>
</svg>
`,
    },
  },
  {
    name: 'listView',
    svg: {
      active: `<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="list" clip-path="url(#clip0_1313_22097)">
<g id="Vector">
<path d="M2 3.7H1C0.723858 3.7 0.5 3.9149 0.5 4.18V5.14C0.5 5.40509 0.723858 5.62 1 5.62H2C2.27614 5.62 2.5 5.40509 2.5 5.14V4.18C2.5 3.9149 2.27614 3.7 2 3.7Z" fill="#1C64F2"/>
<path d="M2 7.54H1C0.723858 7.54 0.5 7.7549 0.5 8.02V8.98C0.5 9.24509 0.723858 9.46 1 9.46H2C2.27614 9.46 2.5 9.24509 2.5 8.98V8.02C2.5 7.7549 2.27614 7.54 2 7.54Z" fill="#1C64F2"/>
<path d="M2 11.38H1C0.723858 11.38 0.5 11.5949 0.5 11.86V12.82C0.5 13.0851 0.723858 13.3 1 13.3H2C2.27614 13.3 2.5 13.0851 2.5 12.82V11.86C2.5 11.5949 2.27614 11.38 2 11.38Z" fill="#1C64F2"/>
<path d="M15.5 5.62H5.5C5.23478 5.62 4.98043 5.51885 4.79289 5.33882C4.60536 5.15878 4.5 4.9146 4.5 4.66C4.5 4.40539 4.60536 4.16121 4.79289 3.98117C4.98043 3.80114 5.23478 3.7 5.5 3.7H15.5C15.7652 3.7 16.0196 3.80114 16.2071 3.98117C16.3946 4.16121 16.5 4.40539 16.5 4.66C16.5 4.9146 16.3946 5.15878 16.2071 5.33882C16.0196 5.51885 15.7652 5.62 15.5 5.62Z" fill="#1C64F2"/>
<path d="M15.5 9.46H5.5C5.23478 9.46 4.98043 9.35885 4.79289 9.17882C4.60536 8.99878 4.5 8.7546 4.5 8.5C4.5 8.24539 4.60536 8.00121 4.79289 7.82117C4.98043 7.64114 5.23478 7.54 5.5 7.54H15.5C15.7652 7.54 16.0196 7.64114 16.2071 7.82117C16.3946 8.00121 16.5 8.24539 16.5 8.5C16.5 8.7546 16.3946 8.99878 16.2071 9.17882C16.0196 9.35885 15.7652 9.46 15.5 9.46Z" fill="#1C64F2"/>
<path d="M15.5 13.3H5.5C5.23478 13.3 4.98043 13.1989 4.79289 13.0188C4.60536 12.8388 4.5 12.5946 4.5 12.34C4.5 12.0854 4.60536 11.8412 4.79289 11.6612C4.98043 11.4811 5.23478 11.38 5.5 11.38H15.5C15.7652 11.38 16.0196 11.4811 16.2071 11.6612C16.3946 11.8412 16.5 12.0854 16.5 12.34C16.5 12.5946 16.3946 12.8388 16.2071 13.0188C16.0196 13.1989 15.7652 13.3 15.5 13.3Z" fill="#1C64F2"/>
</g>
</g>
<defs>
<clipPath id="clip0_1313_22097">
<rect width="16" height="16" fill="white" transform="translate(0.5 0.5)"/>
</clipPath>
</defs>
</svg>
`,
      inActive: `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="list">
<g id="Vector">
<path d="M1.5 3.7H0.5C0.223858 3.7 0 3.9149 0 4.18V5.14C0 5.40509 0.223858 5.62 0.5 5.62H1.5C1.77614 5.62 2 5.40509 2 5.14V4.18C2 3.9149 1.77614 3.7 1.5 3.7Z" fill="#6B7280"/>
<path d="M1.5 7.54H0.5C0.223858 7.54 0 7.7549 0 8.02V8.98C0 9.24509 0.223858 9.46 0.5 9.46H1.5C1.77614 9.46 2 9.24509 2 8.98V8.02C2 7.7549 1.77614 7.54 1.5 7.54Z" fill="#6B7280"/>
<path d="M1.5 11.38H0.5C0.223858 11.38 0 11.5949 0 11.86V12.82C0 13.0851 0.223858 13.3 0.5 13.3H1.5C1.77614 13.3 2 13.0851 2 12.82V11.86C2 11.5949 1.77614 11.38 1.5 11.38Z" fill="#6B7280"/>
<path d="M15 5.62H5C4.73478 5.62 4.48043 5.51885 4.29289 5.33882C4.10536 5.15878 4 4.9146 4 4.66C4 4.40539 4.10536 4.16121 4.29289 3.98117C4.48043 3.80114 4.73478 3.7 5 3.7H15C15.2652 3.7 15.5196 3.80114 15.7071 3.98117C15.8946 4.16121 16 4.40539 16 4.66C16 4.9146 15.8946 5.15878 15.7071 5.33882C15.5196 5.51885 15.2652 5.62 15 5.62Z" fill="#6B7280"/>
<path d="M15 9.46H5C4.73478 9.46 4.48043 9.35885 4.29289 9.17882C4.10536 8.99878 4 8.7546 4 8.5C4 8.24539 4.10536 8.00121 4.29289 7.82117C4.48043 7.64114 4.73478 7.54 5 7.54H15C15.2652 7.54 15.5196 7.64114 15.7071 7.82117C15.8946 8.00121 16 8.24539 16 8.5C16 8.7546 15.8946 8.99878 15.7071 9.17882C15.5196 9.35885 15.2652 9.46 15 9.46Z" fill="#6B7280"/>
<path d="M15 13.3H5C4.73478 13.3 4.48043 13.1989 4.29289 13.0188C4.10536 12.8388 4 12.5946 4 12.34C4 12.0854 4.10536 11.8412 4.29289 11.6612C4.48043 11.4811 4.73478 11.38 5 11.38H15C15.2652 11.38 15.5196 11.4811 15.7071 11.6612C15.8946 11.8412 16 12.0854 16 12.34C16 12.5946 15.8946 12.8388 15.7071 13.0188C15.5196 13.1989 15.2652 13.3 15 13.3Z" fill="#6B7280"/>
</g>
</g>
</svg>
`,
    },
  },
];
const dayListType = [
  {
    name: 'cardView',
    svg: {
      active: `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="calendar-day" clip-path="url(#clip0_5947_20770)">
<g id="Vector">
<path d="M16 3.7C16 3.27565 15.8314 2.86869 15.5314 2.56863C15.2313 2.26857 14.8243 2.1 14.4 2.1H12.8V1.3C12.8 1.08783 12.7157 0.884344 12.5657 0.734315C12.4157 0.584285 12.2122 0.5 12 0.5C11.7878 0.5 11.5843 0.584285 11.4343 0.734315C11.2843 0.884344 11.2 1.08783 11.2 1.3V2.1H8.8V1.3C8.8 1.08783 8.71571 0.884344 8.56569 0.734315C8.41566 0.584285 8.21217 0.5 8 0.5C7.78783 0.5 7.58434 0.584285 7.43431 0.734315C7.28429 0.884344 7.2 1.08783 7.2 1.3V2.1H4.8V1.3C4.8 1.08783 4.71571 0.884344 4.56569 0.734315C4.41566 0.584285 4.21217 0.5 4 0.5C3.78783 0.5 3.58434 0.584285 3.43431 0.734315C3.28429 0.884344 3.2 1.08783 3.2 1.3V2.1H1.6C1.17565 2.1 0.768687 2.26857 0.468629 2.56863C0.168571 2.86869 0 3.27565 0 3.7V5.3H16V3.7Z" fill="#1C64F2"/>
<path d="M0 14.9C0 15.3243 0.168571 15.7313 0.468629 16.0314C0.768687 16.3314 1.17565 16.5 1.6 16.5H14.4C14.8243 16.5 15.2313 16.3314 15.5314 16.0314C15.8314 15.7313 16 15.3243 16 14.9V6.9H0V14.9ZM4 8.5H12C12.2122 8.5 12.4157 8.58429 12.5657 8.73431C12.7157 8.88434 12.8 9.08783 12.8 9.3C12.8 9.51217 12.7157 9.71566 12.5657 9.86569C12.4157 10.0157 12.2122 10.1 12 10.1H4C3.78783 10.1 3.58434 10.0157 3.43431 9.86569C3.28429 9.71566 3.2 9.51217 3.2 9.3C3.2 9.08783 3.28429 8.88434 3.43431 8.73431C3.58434 8.58429 3.78783 8.5 4 8.5Z" fill="#1C64F2"/>
</g>
</g>
<defs>
<clipPath id="clip0_5947_20770">
<rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>


`,
      inActive: `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="calendar-day" clip-path="url(#clip0_5947_20554)">
<g id="Vector">
<path d="M16 3.7C16 3.27565 15.8314 2.86869 15.5314 2.56863C15.2313 2.26857 14.8243 2.1 14.4 2.1H12.8V1.3C12.8 1.08783 12.7157 0.884344 12.5657 0.734315C12.4157 0.584285 12.2122 0.5 12 0.5C11.7878 0.5 11.5843 0.584285 11.4343 0.734315C11.2843 0.884344 11.2 1.08783 11.2 1.3V2.1H8.8V1.3C8.8 1.08783 8.71571 0.884344 8.56569 0.734315C8.41566 0.584285 8.21217 0.5 8 0.5C7.78783 0.5 7.58434 0.584285 7.43431 0.734315C7.28429 0.884344 7.2 1.08783 7.2 1.3V2.1H4.8V1.3C4.8 1.08783 4.71571 0.884344 4.56569 0.734315C4.41566 0.584285 4.21217 0.5 4 0.5C3.78783 0.5 3.58434 0.584285 3.43431 0.734315C3.28429 0.884344 3.2 1.08783 3.2 1.3V2.1H1.6C1.17565 2.1 0.768687 2.26857 0.468629 2.56863C0.168571 2.86869 0 3.27565 0 3.7V5.3H16V3.7Z" fill="#6B7280"/>
<path d="M0 14.9C0 15.3243 0.168571 15.7313 0.468629 16.0314C0.768687 16.3314 1.17565 16.5 1.6 16.5H14.4C14.8243 16.5 15.2313 16.3314 15.5314 16.0314C15.8314 15.7313 16 15.3243 16 14.9V6.9H0V14.9ZM4 8.5H12C12.2122 8.5 12.4157 8.58429 12.5657 8.73431C12.7157 8.88434 12.8 9.08783 12.8 9.3C12.8 9.51217 12.7157 9.71566 12.5657 9.86569C12.4157 10.0157 12.2122 10.1 12 10.1H4C3.78783 10.1 3.58434 10.0157 3.43431 9.86569C3.28429 9.71566 3.2 9.51217 3.2 9.3C3.2 9.08783 3.28429 8.88434 3.43431 8.73431C3.58434 8.58429 3.78783 8.5 4 8.5Z" fill="#6B7280"/>
</g>
</g>
<defs>
<clipPath id="clip0_5947_20554">
<rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

`,
    },
  },
  {
    name: 'listView',
    svg: {
      active: `<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="list" clip-path="url(#clip0_1313_22097)">
<g id="Vector">
<path d="M2 3.7H1C0.723858 3.7 0.5 3.9149 0.5 4.18V5.14C0.5 5.40509 0.723858 5.62 1 5.62H2C2.27614 5.62 2.5 5.40509 2.5 5.14V4.18C2.5 3.9149 2.27614 3.7 2 3.7Z" fill="#1C64F2"/>
<path d="M2 7.54H1C0.723858 7.54 0.5 7.7549 0.5 8.02V8.98C0.5 9.24509 0.723858 9.46 1 9.46H2C2.27614 9.46 2.5 9.24509 2.5 8.98V8.02C2.5 7.7549 2.27614 7.54 2 7.54Z" fill="#1C64F2"/>
<path d="M2 11.38H1C0.723858 11.38 0.5 11.5949 0.5 11.86V12.82C0.5 13.0851 0.723858 13.3 1 13.3H2C2.27614 13.3 2.5 13.0851 2.5 12.82V11.86C2.5 11.5949 2.27614 11.38 2 11.38Z" fill="#1C64F2"/>
<path d="M15.5 5.62H5.5C5.23478 5.62 4.98043 5.51885 4.79289 5.33882C4.60536 5.15878 4.5 4.9146 4.5 4.66C4.5 4.40539 4.60536 4.16121 4.79289 3.98117C4.98043 3.80114 5.23478 3.7 5.5 3.7H15.5C15.7652 3.7 16.0196 3.80114 16.2071 3.98117C16.3946 4.16121 16.5 4.40539 16.5 4.66C16.5 4.9146 16.3946 5.15878 16.2071 5.33882C16.0196 5.51885 15.7652 5.62 15.5 5.62Z" fill="#1C64F2"/>
<path d="M15.5 9.46H5.5C5.23478 9.46 4.98043 9.35885 4.79289 9.17882C4.60536 8.99878 4.5 8.7546 4.5 8.5C4.5 8.24539 4.60536 8.00121 4.79289 7.82117C4.98043 7.64114 5.23478 7.54 5.5 7.54H15.5C15.7652 7.54 16.0196 7.64114 16.2071 7.82117C16.3946 8.00121 16.5 8.24539 16.5 8.5C16.5 8.7546 16.3946 8.99878 16.2071 9.17882C16.0196 9.35885 15.7652 9.46 15.5 9.46Z" fill="#1C64F2"/>
<path d="M15.5 13.3H5.5C5.23478 13.3 4.98043 13.1989 4.79289 13.0188C4.60536 12.8388 4.5 12.5946 4.5 12.34C4.5 12.0854 4.60536 11.8412 4.79289 11.6612C4.98043 11.4811 5.23478 11.38 5.5 11.38H15.5C15.7652 11.38 16.0196 11.4811 16.2071 11.6612C16.3946 11.8412 16.5 12.0854 16.5 12.34C16.5 12.5946 16.3946 12.8388 16.2071 13.0188C16.0196 13.1989 15.7652 13.3 15.5 13.3Z" fill="#1C64F2"/>
</g>
</g>
<defs>
<clipPath id="clip0_1313_22097">
<rect width="16" height="16" fill="white" transform="translate(0.5 0.5)"/>
</clipPath>
</defs>
</svg>
`,
      inActive: `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="list">
<g id="Vector">
<path d="M1.5 3.7H0.5C0.223858 3.7 0 3.9149 0 4.18V5.14C0 5.40509 0.223858 5.62 0.5 5.62H1.5C1.77614 5.62 2 5.40509 2 5.14V4.18C2 3.9149 1.77614 3.7 1.5 3.7Z" fill="#6B7280"/>
<path d="M1.5 7.54H0.5C0.223858 7.54 0 7.7549 0 8.02V8.98C0 9.24509 0.223858 9.46 0.5 9.46H1.5C1.77614 9.46 2 9.24509 2 8.98V8.02C2 7.7549 1.77614 7.54 1.5 7.54Z" fill="#6B7280"/>
<path d="M1.5 11.38H0.5C0.223858 11.38 0 11.5949 0 11.86V12.82C0 13.0851 0.223858 13.3 0.5 13.3H1.5C1.77614 13.3 2 13.0851 2 12.82V11.86C2 11.5949 1.77614 11.38 1.5 11.38Z" fill="#6B7280"/>
<path d="M15 5.62H5C4.73478 5.62 4.48043 5.51885 4.29289 5.33882C4.10536 5.15878 4 4.9146 4 4.66C4 4.40539 4.10536 4.16121 4.29289 3.98117C4.48043 3.80114 4.73478 3.7 5 3.7H15C15.2652 3.7 15.5196 3.80114 15.7071 3.98117C15.8946 4.16121 16 4.40539 16 4.66C16 4.9146 15.8946 5.15878 15.7071 5.33882C15.5196 5.51885 15.2652 5.62 15 5.62Z" fill="#6B7280"/>
<path d="M15 9.46H5C4.73478 9.46 4.48043 9.35885 4.29289 9.17882C4.10536 8.99878 4 8.7546 4 8.5C4 8.24539 4.10536 8.00121 4.29289 7.82117C4.48043 7.64114 4.73478 7.54 5 7.54H15C15.2652 7.54 15.5196 7.64114 15.7071 7.82117C15.8946 8.00121 16 8.24539 16 8.5C16 8.7546 15.8946 8.99878 15.7071 9.17882C15.5196 9.35885 15.2652 9.46 15 9.46Z" fill="#6B7280"/>
<path d="M15 13.3H5C4.73478 13.3 4.48043 13.1989 4.29289 13.0188C4.10536 12.8388 4 12.5946 4 12.34C4 12.0854 4.10536 11.8412 4.29289 11.6612C4.48043 11.4811 4.73478 11.38 5 11.38H15C15.2652 11.38 15.5196 11.4811 15.7071 11.6612C15.8946 11.8412 16 12.0854 16 12.34C16 12.5946 15.8946 12.8388 15.7071 13.0188C15.5196 13.1989 15.2652 13.3 15 13.3Z" fill="#6B7280"/>
</g>
</g>
</svg>
`,
    },
  },
];
const toggleView = ref(props.modelValue);

watch(
  () => props.modelValue,
  (newValue) => {
    toggleView.value = newValue;
  },
);
function toggleList (){
  if (toggleView.value === 'card'){
    toggleView.value = 'list';
  } else {
    toggleView.value = 'card';
  }
  emit('toggleData', toggleView.value);
}
onClickOutside(dropDownModalRef, () => {
  openSlider.value = false;
});

</script>

<template>
    <div v-if="!Store.isMobile">
        <div class="w-[7rem] h-[2.5rem] overflow-hidden flex border rounded-lg  cursor-pointer" @click="toggleList">
            <!-- Card  -->
            <div :class="toggleView === 'card'? 'w-full pr-2 bg-[#ebf5ff]':'w-[70px] hover:bg-gray-100'" class="rounded-tl-md rounded-bl-md flex items-center justify-center border-r-2 px-1">
                <span class="p-1" v-html="enableDaySVG ? toggleView === 'card' ? dayListType.find((item)=> item.name === 'cardView').svg.active:dayListType.find((item)=> item.name === 'cardView').svg.inActive : toggleView === 'card' ? type.find((item)=> item.name === 'cardView').svg.active:type.find((item)=> item.name === 'cardView').svg.inActive"></span>
                <p v-if="toggleView === 'card'" :class="toggleView === 'card'?'text-[#1C64F2]':'text-black '" class="text-sm font-medium">{{ title[0] }}</p>
            </div>

            <!-- List -->
            <div :class="toggleView === 'list' ? 'w-full bg-[#ebf5ff]':'w-[50px] hover:bg-gray-100'" class="rounded-tr-md rounded-br-md flex items-center justify-center  transition-transform duration-200 ">
                <span class="p-1" v-html="toggleView === 'list' ? type.find((item)=> item.name === 'listView').svg.active : type.find((item)=> item.name === 'listView').svg.inActive"></span>
                <p v-if="toggleView === 'list'" :class="toggleView === 'list'?'text-[#1C64F2]':'text-black '" class="text-sm font-medium">{{ title[1] }}</p>
            </div>
        </div>
    </div>
    <div v-else ref="dropDownModalRef">
      <div  :class="openSlider?'bg-[#ebf5ff]':'bg-gray-200 hover:bg-gray-100'"  class="w-[41px] h-[41px] flex justify-center items-center  rounded-md cursor-pointer" @click="()=>openSlider = !openSlider">
          <span v-if="!openSlider" class="h-auto w-auto" v-html="toggleView === 'card' ? type.find((item)=> item.name === 'cardView').svg.inActive:type.find((item)=> item.name === 'listView').svg.inActive"></span>
          <span v-if="openSlider" class="h-auto w-auto" v-html="toggleView === 'card' ? type.find((item)=> item.name === 'cardView').svg.active:type.find((item)=> item.name === 'listView').svg.active"></span>
      </div>
      <div v-if="openSlider" :class="[position?`${position}`:'right-[105px] top-[120px]',size?`${size}`:'w-[120px] h-[80px]']"  class=" flex flex-col absolute  rounded-lg border z-30">
        <div class="w-full h-[50%] flex border-b-2 rounded-t-lg cursor-pointer" :class="toggleView === 'card'?'bg-[#ebf5ff]':'bg-white'" @click="()=>{toggleView = 'card';openSlider=false;emit('toggleData', toggleView)}">
           <span class="p-1 w-[30%] flex justify-center items-center" v-html="toggleView === 'card' ? type.find((item)=> item.name === 'cardView').svg.active:type.find((item)=> item.name === 'cardView').svg.inActive"></span>
                <span class="w-[70%] flex justify-start items-center">
                  <p  :class="toggleView === 'card'?'text-[#1C64F2]':'text-gray-500 '" class="text-sm  items-center">{{ title[0] }}</p>
                </span>
        </div>
        <div class="w-full h-[50%] flex rounded-b-lg cursor-pointer"  :class="toggleView === 'list'?'bg-[#ebf5ff]':'bg-white'" @click="{toggleView = 'list';openSlider=false;emit('toggleData', toggleView)}">
          <span class="p-1 w-[30%] flex justify-center items-center" v-html="toggleView === 'list' ? type.find((item)=> item.name === 'listView').svg.active : type.find((item)=> item.name === 'listView').svg.inActive"></span>
                <span class="w-[70%] flex justify-start items-center">
                  <p  :class="toggleView === 'list'?'text-[#1C64F2]':'text-gray-500 '" class="text-sm">{{ title[1] }}</p>
                </span>
        </div>
      </div>
    </div>
</template>

<style>

</style>
