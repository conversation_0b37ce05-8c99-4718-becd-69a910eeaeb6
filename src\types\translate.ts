export enum SupportedLanguages {
    AB = 'ab',
    ACE = 'ace',
    ACH = 'ach',
    AF = 'af',
    SQ = 'sq',
    ALZ = 'alz',
    AM = 'am',
    AR = 'ar',
    HY = 'hy',
    AS = 'as',
    AWA = 'awa',
    AY = 'ay',
    AZ = 'az',
    BAN = 'ban',
    BM = 'bm',
    BA = 'ba',
    EU = 'eu',
    BTX = 'btx',
    BTS = 'bts',
    BBC = 'bbc',
    BE = 'be',
    BEM = 'bem',
    BN = 'bn',
    BEW = 'bew',
    BHO = 'bho',
    BIK = 'bik',
    BS = 'bs',
    BR = 'br',
    BG = 'bg',
    BUA = 'bua',
    YUE = 'yue',
    CA = 'ca',
    CEB = 'ceb',
    NY = 'ny',
    ZH_CN = 'zh-CN',
    ZH_TW = 'zh-TW',
    CV = 'cv',
    CO = 'co',
    CRH = 'crh',
    HR = 'hr',
    CS = 'cs',
    DA = 'da',
    DIN = 'din',
    DV = 'dv',
    DOI = 'doi',
    DOV = 'dov',
    NL = 'nl',
    DZ = 'dz',
    EN = 'en',
    EO = 'eo',
    ET = 'et',
    EE = 'ee',
    FJ = 'fj',
    FIL = 'fil',
    FI = 'fi',
    FR = 'fr',
    FR_FR = 'fr-FR',
    FR_CA = 'fr-CA',
    FY = 'fy',
    FF = 'ff',
    GAA = 'gaa',
    GL = 'gl',
    LG = 'lg',
    KA = 'ka',
    DE = 'de',
    EL = 'el',
    GN = 'gn',
    GU = 'gu',
    HT = 'ht',
    CNH = 'cnh',
    HA = 'ha',
    HAW = 'haw',
    HE = 'he',
    HIL = 'hil',
    HI = 'hi',
    HMN = 'hmn',
    HU = 'hu',
    HRX = 'hrx',
    IS = 'is',
    IG = 'ig',
    ILO = 'ilo',
    ID = 'id',
    GA = 'ga',
    IT = 'it',
    JA = 'ja',
    JW = 'jw',
    KN = 'kn',
    PAM = 'pam',
    KK = 'kk',
    KM = 'km',
    CGG = 'cgg',
    RW = 'rw',
    KTU = 'ktu',
    GOM = 'gom',
    KO = 'ko',
    KRI = 'kri',
    KU = 'ku',
    CKB = 'ckb',
    KY = 'ky',
    LO = 'lo',
    LTG = 'ltg',
    LA = 'la',
    LV = 'lv',
    LIJ = 'lij',
    LI = 'li',
    LN = 'ln',
    LT = 'lt',
    LMO = 'lmo',
    LUO = 'luo',
    LB = 'lb',
    MK = 'mk',
    MAI = 'mai',
    MAK = 'mak',
    MG = 'mg',
    MS = 'ms',
    MS_ARAB = 'ms-Arab',
    ML = 'ml',
    MT = 'mt',
    MI = 'mi',
    MR = 'mr',
    CHM = 'chm',
    MNI_MTEI = 'mni-Mtei',
    MIN = 'min',
    LUS = 'lus',
    MN = 'mn',
    MY = 'my',
    NR = 'nr',
    NEW = 'new',
    NE = 'ne',
    NSO = 'nso',
    NO = 'no',
    NUS = 'nus',
    OC = 'oc',
    OR = 'or',
    OM = 'om',
    PAG = 'pag',
    PAP = 'pap',
    PS = 'ps',
    FA = 'fa',
    PL = 'pl',
    PT = 'pt',
    PT_PT = 'pt-PT',
    PT_BR = 'pt-BR',
    PA = 'pa',
    PA_ARAB = 'pa-Arab',
    QU = 'qu',
    ROM = 'rom',
    RO = 'ro',
    RN = 'rn',
    RU = 'ru',
    SM = 'sm',
    SG = 'sg',
    SA = 'sa',
    GD = 'gd',
    SR = 'sr',
    ST = 'st',
    CRS = 'crs',
    SHN = 'shn',
    SN = 'sn',
    SCN = 'scn',
    SZL = 'szl',
    SD = 'sd',
    SI = 'si',
    SK = 'sk',
    SL = 'sl',
    SO = 'so',
    ES = 'es',
    SU = 'su',
    SW = 'sw',
    SS = 'ss',
    SV = 'sv',
    TG = 'tg',
    TA = 'ta',
    TT = 'tt',
    TE = 'te',
    TET = 'tet',
    TH = 'th',
    TI = 'ti',
    TS = 'ts',
    TN = 'tn',
    TR = 'tr',
    TK = 'tk',
    AK = 'ak',
    UK = 'uk',
    UR = 'ur',
    UG = 'ug',
    UZ = 'uz',
    VI = 'vi',
    CY = 'cy',
    XH = 'xh',
    YI = 'yi',
    YO = 'yo',
    YUA = 'yua',
    ZU = 'zu'
}

export type translateParams = {
    sourceLanguageCode: SupportedLanguages,
    targetLanguageCode: SupportedLanguages,
    text:string
}

export type addRemoveLanguageParams = {
    targetLanguageCode: SupportedLanguages
}

export type updateTranslationCodeParams = {
    translationId: string,
    targetLanguageCode: SupportedLanguages,
    translation: string
}

export type storeTranslationParams = {
    translations: Record<SupportedLanguages, string>
}
