<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
import UnitplanDetail from '../../../components/Projects/unitplans/UnitPlanDetail.vue';
// import SideBar from '../../../components/Projects/SideBar.vue';
import SideNavBar from '@/components/common/SideNavBar.vue';

</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar/>
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
                <UnitplanDetail />
                <router-view></router-view>
            </div>
        </div>
    </div></template>
