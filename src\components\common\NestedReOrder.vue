<script setup>
import { computed, ref, nextTick, onMounted } from 'vue';
import { VueDraggable } from 'vue-draggable-plus';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  isRoot: {
    type: Boolean,
    default: true,
  },
  uniqueKey: {
    type: String,
    default: 'id',
  },
  nestedChildKey: {
    type: String,
    default: 'children',
  },
  ghostClass: {
    type: String,
    default: 'defaultghost_nestedreorder',
  },
  animationMilliSec: {
    type: String,
    default: '350',
  },
  childSort: {
    type: Boolean,
    default: true,
  },
  childReparentingSort: {
    type: Boolean,
    default: true,
  },
  sortReferenceKey: {
    type: String,
    default: 'id',
  },
  referenceData: {
    type: Array,
    default: null,
  },
  groupName: {
    type: String,
    default: 'reOrder',
  },
  allowChildReparenting: {
    type: Boolean,
    default: true,
  },
  allowChildSort: {
    type: Boolean,
    default: true,
  },
  containerClasses: {
    type: String,
    default: '',
  },
  commonClasses: {
    type: String,
    default: 'pl-4',
  },

});

const emits = defineEmits(['update:modelValue', 'handleChildSort', 'handleChildReparentingSort', 'ready']);

onMounted(() => {
  nextTick(() => {
    emits('ready'); // Notify parent that rendering is done
  });
});

const type = {
  CHILDSORT: "childsort",
  CHILDREPARENTINGSORT: 'childreparentingsort',
};
const rootDataReference = ref(null);
const dragStatus = ref(false);

/* Methods */
// Computed ~ create's a two way binding from component v-model to props modelValue
const list = computed({
  get: () => props.modelValue,
  set: (value) => {
    rootDataReference.value = props.isRoot ? value : null; // Only rootItems Reference (i.e Great Grand Parents)
    emits('update:modelValue', value); // triggered, whenever the vue-draggable is updating the v-model value.
  },
});

const updateOrders = (list, sortRange, sortType) => {

  console.log("updateOrders");

  if (list && sortRange !== null){
    const cloneList = [...list];

    if (sortType === type.CHILDSORT){
      /* Child Sort */
      const updateItems = [];

      console.log(sortRange.start, "Start");
      console.log(sortRange.end, "End");

      // Sortout
      cloneList.forEach((item, index) => {
        if (index >= sortRange.start && index <= sortRange.end){
          console.log(index, item);
          console.log("yes", item);

          const cloneItem = {
            ...item,
          };
          cloneItem.order = index + 1;
          console.log(cloneItem);

          updateItems.push(cloneItem);
        }
      });
      return updateItems;
    }

    /* Child Reparenting Sort */
    const updateItems = [];

    console.log(sortRange, "Range");

    // Sortout
    cloneList.forEach((item, index) => {
      if (index >= sortRange ){ // Check for index equal & above the rangeindex
        const cloneItem = {
          ...item,
        };
        cloneItem.order = index + 1;
        console.log(cloneItem);

        updateItems.push(cloneItem);
      }
    });

    return updateItems;

  }
  return false;
};

// End
const handleEnd = (val) => {
  console.log(dragStatus.value);
  console.log(val);
  console.log(props.referenceData);
  console.log(props.isRoot);

  const isReparenting = val.item.addBaseParentReference && val.item.removeBaseParentReference;
  console.log(isReparenting);

  /* Child Sort */
  if ( props.allowChildSort && val.oldIndex !== val.newIndex && !isReparenting){
    console.log("Child Sort");

    // Step1: Get the information from the event
    const Indexes = [val.oldIndex, val.newIndex]; // extact the old & New Index
    const startRangeIndex = Math.min(...Indexes); // least as start
    const endRangeIndex = Math.max(...Indexes); // highest as end

    // Note: props.referenceData will a base Parent(Object) if it's not a root, If you dragged Item is root then props.referenceData will be not return base parent instead it will return null.

    // PickOut the list of direct-child for dragged Item (i.e is directly related to same parent of dragged Item) from baseParentReference.
    const list = !props.isRoot ? {...props.referenceData}[props.nestedChildKey] : [...rootDataReference.value];
    console.log(list);

    let sortedItemsOrder = updateOrders( list, {start: startRangeIndex, end: endRangeIndex}, type.CHILDSORT);  // Get the sortedItems
    console.log(sortedItemsOrder);

    // Split the Dragged Item in sortedItems Order
    let draggedItem; // Dragged Item
    sortedItemsOrder = sortedItemsOrder.filter((item) => {
      if (item[props.sortReferenceKey] !== val.data[props.sortReferenceKey]){
        return true;
      }
      draggedItem = item;
      return false;
    });

    emits('handleChildSort', {
      baseParentReference: !props.isRoot ? {...props.referenceData} : null,
      sortedItems: sortedItemsOrder,
      draggedItem: draggedItem,
    });

  }

  /* Child Reparenting Sort */
  if (props.allowChildReparenting && isReparenting) {
    console.log("Child Reparenting Sort");

    const removeRangeIndex = val.oldIndex; // oldIndex as removed from targeted parent
    const addRangeIndex = val.newIndex; // newIndex as add range for targeted parent

    // Add
    const addList = val.item.addBaseParentReference[props.nestedChildKey]; // PickOut the list of direct-child for dragged Item add source (i.e is directly related to new parent of dragged Item) from baseParentReference.
    console.log("AddBase", addList);
    let addSortedItems = updateOrders(addList, addRangeIndex, type.CHILDREPARENTINGSORT);
    console.log("addSortedItems", addSortedItems);

    // Remove
    const removeList = val.item.removeBaseParentReference[props.nestedChildKey]; // PickOut the list of direct-child for dragged Item remove source (i.e is directly related to old parent of dragged Item) from baseParentReference.
    console.log("RemoveBase", removeList);
    const removeSortedItems = updateOrders(removeList, removeRangeIndex, type.CHILDREPARENTINGSORT);
    console.log("removeSortedItems", removeSortedItems);

    // Split the Dragged Item in addSortedItems Order
    let draggedItem; // Dragged Item
    addSortedItems = addSortedItems.filter((item) => {
      if (item[props.sortReferenceKey] !== val.data[props.sortReferenceKey]){
        return true;
      }
      draggedItem = item;
      return false;
    });

    console.log(addSortedItems);

    emits('handleChildReparentingSort', {
      add: {
        baseParentReference: val.item.addBaseParentReference,
        sortedItems: addSortedItems,
      },
      remove: {
        baseParentReference: val.item.removeBaseParentReference,
        sortedItems: removeSortedItems,
      },
      draggedItem: {
        baseStatus: 'add',
        Item: draggedItem,
      },
    });

    // Reset the Reference inside the events
    delete val.item.addBaseParentReference;
    delete val.item.removeBaseParentReference;

  }

  // Update the Drag Status, After DOM Updation
  nextTick(() => {
    dragStatus.value = false;
  });
};

// Add & Remove
const handleReparentingEvents = (e) => {
  if (props.allowChildReparenting){
    console.log(e, props.referenceData);
    if (e.type !== 'add'){
      console.log("remove", e);
      e.item.removeBaseParentReference = {...props.referenceData};
    } else {
      console.log("add", e);
      e.item.addBaseParentReference = {...props.referenceData};
    }
  }
};

</script>

<template>
    <VueDraggable :class="[!isRoot && commonClasses]" v-model="list" :scroll="true" :scrollSensitivity="60" :scrollSpeed="10" :bubbleScroll="true" :group="allowChildReparenting && !isRoot ? groupName : false" target=".transition-nested_reOrder" :animation="animationMilliSec" :ghostClass="ghostClass" @start="() => dragStatus = true" @end="(e) => handleEnd(e)" @remove="(e) => handleReparentingEvents(e)" @add="(e) => handleReparentingEvents(e)" @move="(e) => e.data.draggable">
      <TransitionGroup type="transition" tag="div" :name="isRoot && !dragStatus ? 'nestedreorder_fade' : undefined" :class="['transition-nested_reOrder', containerClasses]">
          <div v-for="item in list" class="!w-full" :key="item[props.uniqueKey]">
                <slot :item="item"> </slot>
                <template v-if="item[nestedChildKey] && Array.isArray(item[nestedChildKey])">
                    <NestedReOrder :common-classes="commonClasses"  v-model="item[nestedChildKey]" :groupName="groupName" :allowChildReparenting="allowChildReparenting" :allowChildSort="allowChildSort" :isRoot="false" :key="uniqueKey" :nestedChildKey="nestedChildKey" :ghostClass="ghostClass" :referenceData="item" :sortReferenceKey="sortReferenceKey"  @handleChildSort="(val) => emits('handleChildSort',val)" @handleChildReparentingSort="(val) => emits('handleChildReparentingSort',val)">
                        <template #default="{ item }">
                            <slot :item="item"></slot>
                        </template>
                    </NestedReOrder>
                </template>
            </div>
      </TransitionGroup>
    </VueDraggable>
</template>

<style>
.defaultghost_nestedreorder {
  opacity: 0.5;
}

.nestedreorder_fade-move, .nestedreorder_fade-enter-active, .nestedreorder_fade-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
</style>
