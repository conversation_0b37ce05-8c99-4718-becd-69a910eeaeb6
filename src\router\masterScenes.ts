import type { RouteRecordRaw } from "vue-router";

export default [
  {
    path: '/masterscenes',
    name: 'master_scenes',
    component: () => import('../views/masterScenes/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'master_scenes_create',
        component: () => import('../components/scenes/CreateProjectScene.vue'),

      },
      {
        path: ':scene_id/convertdeepzoom',
        name: 'master_scenes_deepzoomconversion',
        component: () =>
          import('../components/scenes/ConvertToDeepzoom.vue'),

      },
    ],
  },
  {
    path: '/masterscenes/:scene_id',
    name: 'masterscene_scene',
    component: () => import('../views/masterScenes/MasterSceneById.vue'),
    children: [
      {
        path: 'createsvg',
        name: 'master_scene_create_svg',
        component: () => import('../components/scenes/AddSvg.vue'),
      },
      {
        path: 'edit',
        name: 'master_scene_edit',
        component: () =>
          import('../components/scenes/EditSceneSettingModal.vue'),
      },
      {
        path: 'createframe',
        name: 'master_scenes_create_frame',
        component: () =>
          import('../components/scenes/CreateImageFrame.vue'),
      },
    ],
  },
  {
    path: '/masterscenes/:scene_id/layers',
    name: 'masterscene_layers',
    component: () => import('../views/masterScenes/MasterSceneById.vue'),
  },
  {
    path: '/masterscenes/:scene_id/icons',
    name: 'masterscene_icons',
    component: () => import('../views/masterScenes/MasterSceneById.vue'),
  },
  {
    path: '/masterscenes/earth/:scene_id',
    name: 'masterscene_earth_scene',
    component: () => import('../views/masterScenes/MasterSceneById.vue'),
    children: [
      {
        path: 'edit',
        name: 'master_scene_earth_edit',
        component: () =>
          import('../components/scenes/EditSceneSettingModal.vue'),
      },
    ],
  },
  {
    path: '/masterscenes/:scene_id/deepzoom',
    name: 'masterscene_scenes_deepzoom',
    component: () => import('../views/masterScenes/Deepzoom.vue'),
    children: [
      {
        path: 'createlayers',
        name: 'masterscene_scene_create_layers',
        component: () => import('../components/scenes/CreateLayers.vue'),
      },
      {
        path: 'edit',
        name: 'masterscene_scene_deepzoom_edit',
        component: () =>
          import('../components/scenes/EditSceneSettingModal.vue'),
      },
      {
        path: 'createicons',
        name: 'masterscene_scene_create_icons',
        component: () => import('../components/scenes/CreateIcons.vue'),
      },
    ],
  },
  {
    path: '/masterscenes/:scene_id/gsplat',
    name: 'masterscene_scenes_gsplat',
    component: () => import('../views/masterScenes/Gsplat.vue'),
    children: [
      {
        path: 'edit',
        name: 'masterscene_scenes_gsplat_edit',
        component: () =>
          import('../components/scenes/EditSceneSettingModal.vue'),
      },
    ],
  },
] as Array<RouteRecordRaw>;
