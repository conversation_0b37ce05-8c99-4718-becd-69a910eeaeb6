<script setup>
import { ErrorMessage, Field, Form } from 'vee-validate';
import Modal from '../common/Modal/Modal.vue';
import { onMounted, ref } from 'vue';
import { closeIcon } from '@/helpers/icons';
import { useRouter } from 'vue-router';
import { addUserInOrganization } from '@/validationSchema/user';
import { AddUserToOrganization } from '@/api/organization';
import Spinner from '../common/Spinner.vue';
import { UserStore } from '@/store';
import { uiOperations } from '@/store/uiOperations';
import Multiselect from 'vue-multiselect';

const uiStore = uiOperations();
const Store = UserStore();
const roles = ref([
  { name: "Admin", value: "admin" },
  { name: "Reader", value: "reader" },
  { name: "Editor", value: "editor" },
]);
const selectedRole = ref({});
const successModal = ref(false);
const router = useRouter();

const loader = ref(false);
onMounted(() => {
  Store.callbackFunctionMonitorChanges();
});
function handleForm (values){
  loader.value =true;
  const userToAdd = {
    email: values.email,
    role: values.role ? values.role.toString().toLowerCase() : "",
  };

  // const formData = new FormData();
  // formData.append('email',values.email)
  // formData.append('role',values.role)
  console.log("values", values.email);

  AddUserToOrganization(userToAdd)
    .then(() => {
    })
    .catch((err) => {
      if (err?.error) {
        uiStore.handleApiErrorMessage(err?.error);
      }
    }).finally(() => {
      router.go(-1);
      loader.value = false;
    });
}
</script>

<template>
     <Modal v-if="!Store.isMobile" :open="true">
        <div  class="h-fit w-[35%] xl:w-[25%] bg-white px-3 py-3 rounded-lg relative top-[10px]">
            <div class="w-full h-[10%] flex items-center justify-start">
                <p class="text-[#111928] text-xl font-bold">Add Members</p>
            </div>
            <div class="absolute top-[20px] right-[20px] cursor-pointer" @click="router.go(-1)">
                <span v-html="closeIcon"></span>
            </div>
            <Form :validation-schema="addUserInOrganization"  @submit="handleForm" class="m-0">
                <div class="h-[90px]">
                    <label for="email" class="text-[14px] font-medium text-black">Email ID*</label>
                    <div class="mt-1">
                        <Field type="text" name="email" id="email" autocomplete="email" class="input-primary w-full !bg-gray-50"
                        placeholder="Enter email id" />
                         <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="email" />
                    </div>
                </div>
                <!-- <div class="h-[90px]">
                    <label for="phone" class="label-primary">Phone*</label>
                    <div class="mt-1">
                        <Field type="text" name="phone" id="phone" autocomplete="phone" class="input-primary !bg-gray-50"
                        placeholder="Phone" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="phone" />
                    </div>
                </div> -->
                <div class="h-[90px]">
                    <label for="role" class="text-[14px] font-medium text-black">Role*</label>
                    <div class="mt-1">
                        <Field name="role" :model-value="selectedRole.value" class="!bg-gray-50" v-slot="{field}">
                        <Multiselect v-model="selectedRole" deselect-label="Can't remove this value" track-by="name" label="name"
                                    placeholder="Select One" aria-label="Select one" :options="roles" :searchable="false" :allow-empty="false"
                                     @select="(val) => selectedRole = val" v-bind="field" class="!bg-gray-50">
                       <template v-slot:singleLabel="{ option }">
                        <div class="relative top-[2px] !bg-gray-50">
                            <p v-if="option.name" class="text-black  ">{{ option.name }}</p>
                            <p v-else class="text-gray-500 ">Select one</p>
                        </div>
                        </template>
                        </multiselect>
                        </Field>
                         <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="role" />
                    </div>
                </div>
                <!-- <div class="h-[90px]">
                    <label for="name" class="label-primary">Permission*</label>
                    <div class="mt-1">
                        <Field as="select" type="text" name="permission"  id="permission" autocomplete="permission"
                            class="select-primary !bg-gray-50 ">
                            <option value="" disabled>Select Permission</option>
                            <option value="" disabled v-if="!permission">
                                No Permissions Found!
                            </option>
                            <option v-else :value="option" v-for="(option, index) in permission"
                                :key="index" class="text-black">
                                {{ option }}
                            </option>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="permission" />
                    </div>
                </div> -->
                <div class="h-[60px] w-full flex items-center">
                    <button type="submit" class="w-full h-[90px] hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
                        rounded-lg flex flex-row justify-center items-center gap-2">
                        <span class="text-center text-inherit whitespace-nowrap">Send Invitation</span>
                        <Spinner v-if="loader" />
                    </button>
                </div>
            </Form>
        </div>
    </Modal>
     <Modal v-if="Store.isMobile" :open="true">
        <div  class="h-full relative w-full bg-white px-3 py-2 flex flex-col gap-3  top-0">
            <div class="flex w-full items-center gap-4">
                <div class="cursor-pointer " @click="router.go(-1)">
<svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.925781 10.0273C0.925781 9.66016 1.06641 9.33984 1.34766 9.06641L9.05859 1.36719C9.19922 1.22656 9.34766 1.12109 9.50391 1.05078C9.66016 0.980469 9.82422 0.945312 9.99609 0.945312C10.3555 0.945312 10.6562 1.0625 10.8984 1.29688C11.1406 1.53125 11.2617 1.82422 11.2617 2.17578C11.2617 2.36328 11.2266 2.53516 11.1562 2.69141C11.0859 2.83984 10.9922 2.97266 10.875 3.08984L8.25 5.75L3.89062 9.74609L3.46875 8.98438L7.53516 8.73828H21.7734C22.1641 8.73828 22.4766 8.85938 22.7109 9.10156C22.9531 9.33594 23.0742 9.64453 23.0742 10.0273C23.0742 10.4023 22.9531 10.7109 22.7109 10.9531C22.4766 11.1875 22.1641 11.3047 21.7734 11.3047H7.53516L3.46875 11.0703L3.89062 10.3203L8.25 14.3047L10.875 16.9531C10.9922 17.0703 11.0859 17.207 11.1562 17.3633C11.2266 17.5195 11.2617 17.6875 11.2617 17.8672C11.2617 18.2188 11.1406 18.5117 10.8984 18.7461C10.6562 18.9805 10.3555 19.0977 9.99609 19.0977C9.65234 19.0977 9.34375 18.9648 9.07031 18.6992L1.34766 10.9883C1.06641 10.7148 0.925781 10.3945 0.925781 10.0273Z" fill="#747577"/>
</svg>
                </div>
            <div class="w-full h-[10%] flex items-center justify-start">
                <p class="text-[#111928] text-xl font-bold">Add Members</p>
            </div>
            </div>

            <Form :validation-schema="addUserInOrganization"  @submit="handleForm" class="h-[95%] flex flex-col justify-between">
                <!-- <div class="h-[90px]">
                    <label for="name" class="label-primary">Enter Name*</label>
                    <div class="mt-1">
                        <Field type="text" name="name" id="name" autocomplete="name" class="input-primary !bg-gray-50"
                        placeholder="Enter Name" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="name" />
                    </div>
                </div> -->
                <div>
                    <div class="h-[90px]">
                        <label for="email" class="label-primary">Email ID*</label>
                        <div class="mt-1">
                            <Field type="text" name="email" id="email" autocomplete="email" class="input-primary !bg-gray-50"
                            placeholder="Enter email id" />
                            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="email" />
                        </div>
                    </div>
                <div class="h-[90px]">
                    <label for="role" class="label-primary">Role*</label>
                    <div class="mt-1">
                        <Field name="role" :model-value="selectedRole.value" class="!bg-gray-50" v-slot="{field}">
                        <Multiselect v-model="selectedRole" deselect-label="Can't remove this value" track-by="name" label="name"
                                    placeholder="Select One" aria-label="Select one" :options="roles" :searchable="false" :allow-empty="false"
                                     @select="(val) => selectedRole = val" v-bind="field" class="!bg-gray-50">
                       <template v-slot:singleLabel="{ option }">
                        <div class="relative top-[2px] !bg-gray-50">
                            <p v-if="option.name" class="text-black  ">{{ option.name }}</p>
                            <p v-else class="text-gray-500 ">Select one</p>
                        </div>
                        </template>
                        </multiselect>
                        </Field>
                         <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="role" />
                    </div>
                </div>
                </div>
          <div class="w-[95%] mx-auto h-12  flex justify-end gap-x-3">
            <button type="submit" class="w-full h-full  bg-[#1c64f2] hover:bg-[#1a56db] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
            rounded-lg flex flex-row justify-center items-center gap-2">
              <span class="text-center text-inherit whitespace-nowrap">Send Invitation</span>
              <Spinner v-if="loader" />
            </button>
          </div>
            </Form>
        </div>
    </Modal>
    <Modal :open="successModal">
         <div class="w-[30%] h-[30%] relative flex flex-col justify-center bg-white rounded-lg  shadow-xl gap-4">
            <div @click="()=>router.go(-1)" class="absolute top-4 right-3 cursor-pointer">
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="x">
                <path id="Vector" d="M7.0885 6.50001L11.1645 2.42399C11.2381 2.35295 11.2967 2.26798 11.3371 2.17403C11.3774 2.08007 11.3987 1.97902 11.3996 1.87677C11.4005 1.77452 11.381 1.67312 11.3423 1.57848C11.3035 1.48384 11.2464 1.39786 11.1741 1.32556C11.1018 1.25325 11.0158 1.19607 10.9211 1.15735C10.8265 1.11863 10.7251 1.09915 10.6228 1.10004C10.5206 1.10092 10.4195 1.12217 10.3256 1.16253C10.2316 1.20289 10.1467 1.26155 10.0756 1.3351L5.99961 5.41112L1.92359 1.3351C1.77835 1.19483 1.58383 1.11721 1.38192 1.11896C1.18001 1.12072 0.986864 1.2017 0.844085 1.34448C0.701307 1.48726 0.620319 1.68041 0.618564 1.88232C0.616809 2.08423 0.694429 2.27875 0.834705 2.42399L4.91072 6.50001L0.834705 10.576C0.761155 10.6471 0.702488 10.732 0.662129 10.826C0.621771 10.9199 0.600527 11.021 0.599638 11.1232C0.59875 11.2255 0.618234 11.3269 0.656954 11.4215C0.695674 11.5162 0.752855 11.6021 0.825159 11.6745C0.897464 11.7468 0.983445 11.8039 1.07808 11.8427C1.17272 11.8814 1.27413 11.9009 1.37638 11.9C1.47863 11.8991 1.57968 11.8778 1.67363 11.8375C1.76758 11.7971 1.85256 11.7385 1.92359 11.6649L5.99961 7.58889L10.0756 11.6649C10.2209 11.8052 10.4154 11.8828 10.6173 11.8811C10.8192 11.8793 11.0124 11.7983 11.1551 11.6555C11.2979 11.5127 11.3789 11.3196 11.3807 11.1177C11.3824 10.9158 11.3048 10.7213 11.1645 10.576L7.0885 6.50001Z" fill="#6B7280"/>
                </g>
                </svg>

            </div>
            <div class="w-full flex items-center justify-center">
                <span class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="check">
                    <path id="Vector" d="M7.15541 18.3333C6.82899 18.3349 6.51503 18.1816 6.28067 17.9061L0.37744 10.9624C0.259778 10.8232 0.165894 10.6572 0.101148 10.474C0.0364024 10.2908 0.00206288 10.0939 9.0224e-05 9.89456C-0.00389373 9.49202 0.124201 9.10406 0.356196 8.81601C0.588191 8.52796 0.905082 8.36342 1.23716 8.35859C1.56923 8.35376 1.88929 8.50904 2.12691 8.79026L7.1604 14.7085L17.8722 2.098C18.1102 1.81678 18.4305 1.66167 18.7628 1.66678C19.0951 1.67189 19.4122 1.83682 19.6442 2.12527C19.8762 2.41372 20.0041 2.80207 19.9999 3.20488C19.9957 3.6077 19.8596 3.99199 19.6217 4.27321L8.03014 17.9061C7.79578 18.1816 7.48182 18.3349 7.15541 18.3333Z" fill="#0E9F6E"/>
                    </g>
                    </svg>
                </span>
            </div>
            <span class="w-full text-center"><p class="text-lg font-semibold">Invitation Sent</p></span>
            <button @click="()=>router.go(-1)" class="w-32 bg-[#1a56db] text-white rounded-lg h-10 p-2 mx-auto border !border-blue-500">Okay</button>
        </div>
    </Modal>
</template>

<style>
.multiselect__single{
    background-color: #f9fafb;
}
</style>
